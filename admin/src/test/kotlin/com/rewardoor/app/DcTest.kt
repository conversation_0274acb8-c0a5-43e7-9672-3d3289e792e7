package com.rewardoor.app

import com.fasterxml.jackson.databind.ObjectMapper
import com.github.scribejava.apis.DiscordApi
import com.github.scribejava.core.builder.ServiceBuilder
import discord4j.common.JacksonResources
import discord4j.common.ReactorResources
import discord4j.common.util.Snowflake
import discord4j.core.DiscordClient
import discord4j.rest.http.ExchangeStrategies
import discord4j.rest.http.client.AuthorizationScheme
import discord4j.rest.request.*
import discord4j.rest.response.ResponseFunction
import discord4j.rest.route.Routes
import discord4j.rest.service.UserService
import org.junit.jupiter.api.Test
import reactor.netty.http.client.HttpClient


class DcTest {
    val appId = "1146414186566537288"
    val appSecret = "SFBIDtL-Nc1KQr_IJMiK4wGPvvcZBBk4"
    val mapper = ObjectMapper()
    @Test
    fun dcTest() {
        val service = ServiceBuilder(appId)
            .apiSecret(appSecret)
            .defaultScope("identify guilds.members.read guilds")
            .userAgent("ScribeJava")
            .callback("https://campaign-staging.tbook.com/dc_callback")
            .responseType("code")
            .build(DiscordApi.instance())
        val token = service.getAccessToken("YJDgYk7JJK8mjLuEjUBnOBOWxYEuDR")
        println(token.rawResponse)
    }

    private fun createDefaultRouter(token: String): Router {
        return DefaultRouter(
            RouterOptions(AuthorizationScheme.BEARER,
                token,
                ReactorResources.create(),
                ExchangeStrategies.jackson(JacksonResources.create().objectMapper),
                emptyList<ResponseFunction>(),
                BucketGlobalRateLimiter.create(),
                RequestQueueFactory.buffering(),
                Routes.BASE_URL
            )
        )
    }

    @Test
    fun getDcInfo() {
        val dcUserService = UserService(createDefaultRouter("m28oUliAD5e3MO6jS555xNL9Y9nJ5u"))

        val guilds = dcUserService.getCurrentUserGuilds(emptyMap()).collectList().block()
        println(guilds)
        /*val user = dcUserService.currentUser.block()
        println(mapper.writeValueAsString(user))*/
    }

    @Test
    fun testCoreClient() {
        val nettySystemProxyHttpClient = HttpClient.create().proxyWithSystemProperties()
        val resource = ReactorResources.builder().httpClient(nettySystemProxyHttpClient).build()
        val dcClient = DiscordClient.builder("").setReactorResources(resource).build()
        val gum = dcClient.getGuildById(Snowflake.of(1199994306350555167)).getMember(Snowflake.of(836415712008208426)).block()
        println(gum?.user())
        println(gum?.roles())
    }
}