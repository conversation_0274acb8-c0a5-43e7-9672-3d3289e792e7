package com.rewardoor.app

import com.fasterxml.jackson.databind.ObjectMapper
import com.google.gson.Gson
import com.rewardoor.enums.CredentialForms
import com.rewardoor.model.*
import org.junit.jupiter.api.Test
import java.net.URI
import java.time.Instant

class SignVerifyTest {

    @Test
    fun verify() {
//        val address = "0xadfda5e88d3642219427dc8278029b17a7df5ce0";
//        val sign =
//            "0x134f5ab3547eccd3df00b6a31c7fb6972fb4814efdbb3ed89604624ec2395be833dd6d679a11ee614512df77fb2266f104effc528d977d58ccb800a0944d55011c"
//        val nonce = "sign in to tbook 1669171336557"
//
//        Signs.getAddressUsedToSignHashedMessage(sign, nonce);
//
//        val add = Signs.getAddressUsedToSignHashedMessage(
//            "0xe115164b22713cca342ad82ffa7742450a5751f29ceacbea830644cb700cab9e7ec457ec30357f597b1b2005e2656289eead8f13431dbaa42bad9b64023fcf591c",
//            "21681550006 grant 1000 token to : 34964810195, grantId: 34974030214"
//        )
//        println(add)

//        val period = "[{\"credentialId\":179943370143,\"name\":\"Follow on Twitter\",\"picUrl\":\"\",\"projectId\":154283610009,\"creatorId\":154283420008,\"groupId\":0,\"eligibleCount\":0},{\"credentialId\":179943370144,\"name\":\"Twitter Retweet\",\"picUrl\":\"\",\"projectId\":154283610009,\"creatorId\":154283420008,\"groupId\":0,\"eligibleCount\":0}]"
//        //        val periodInfo = Gson().fromJson(period, PeriodInfo::class.java)
//        val periodInfo = Gson().fromJson(period,Array<Credential>::class.java).toList()
//        println(period)
//        println(periodInfo[0].groupId)

        val mapper = ObjectMapper()
        val point1 = Point(1, 2, 1, true, 1, 222, 333, 4444)
        val point2 = Point(2, 2, 1, true, 1, 222, 333, 4444)
        val point3 = Point(3, 2, 1, true, 1, 222, 333, 4444)
        val point4 = Point(4, 2, 1, true, 1, 222, 333, 4444)
        val pointList = listOf<Point>(point1, point2, point3, point4)


        val nft1 = NFT(
            nftId = 1,
            name = "My NFT 1",
            symbol = "NFT1",
            contract = "0x1234567890",
            chainId = 1,
            coverUrl = "https://example.com/nft1.png",
            projectId = 1,
            creatorId = 1,
            groupId = 1
        )

        val nft2 = NFT(
            nftId = 2,
            name = "My NFT 2",
            symbol = "NFT2",
            contract = "0x0987654321",
            chainId = 1,
            coverUrl = "https://example.com/nft2.png",
            projectId = 2,
            creatorId = 2,
            groupId = 2
        )

        val nft3 = NFT(
            nftId = 3,
            name = "My NFT 3",
            symbol = "NFT3",
            contract = "0xabcdef1234",
            chainId = 1,
            coverUrl = "https://example.com/nft3.png",
            projectId = 3,
            creatorId = 3,
            groupId = 3
        )
        val NFTList = listOf<NFT>(nft1, nft2, nft3)

        val credential1 = Credential(
            credentialId = 1,
            name = "Credential 1",
            picUrl = "https://example.com/credential1.png",
            projectId = 1,
            creatorId = 1,
            groupId = 1,
            eligibleCount = 10
        )

        val credential2 = Credential(
            credentialId = 2,
            name = "Credential 2",
            picUrl = "https://example.com/credential2.png",
            projectId = 2,
            creatorId = 2,
            groupId = 2,
            eligibleCount = 20
        )

        val credential3 = Credential(
            credentialId = 3,
            name = "Credential 3",
            picUrl = "https://example.com/credential3.png",
            projectId = 3,
            creatorId = 3,
            groupId = 3,
            eligibleCount = 30
        )
        val credentials = listOf<Credential>(credential1, credential2, credential3)

        val credentialGroup1 = CredentialGroup(
            groupType = 1,
            name = "Governance Group 1",
            id = 0,
            credentialList = credentials,

            projectId = 1,
            creatorId = 1,
            campaignId = 1,
            status = 1,
            nftList = NFTList,
            pointList = pointList
        )

        val credentialGroup2 = CredentialGroup(
            groupType = 1,
            name = "Governance Group 2",
            id = 0,
            credentialList = credentials,

            projectId = 1,
            creatorId = 1,
            campaignId = 1,
            status = 1,
            nftList = NFTList,
            pointList = pointList
        )

        val credentialGroup3 = CredentialGroup(
            groupType = 1,
            name = "Governance Group 3",
            id = 0,
//            credentialList = mapper.writeValueAsString(credentials),
            credentialList = credentials,

            projectId = 1,
            creatorId = 1,
            campaignId = 1,
            status = 1,
            nftList = NFTList,
            pointList = pointList
        )

        val followerUser = "test_user"
        val retweetUser = "re_user"
        val projectId = 153900040006
        val creatorId = 153900040007
        val followCredential =
            Credential(
                0,
                "Follow on Twitter",
                "Follow <%= $followerUser %> on Twitter",
                1,
                "",
                "https://rd-worker.xgamma.workers.dev/img/328d2ceabec948999d82c24888877b72",
                projectId,
                creatorId,
                0,
                0
            )
        val retweetCredential =
            Credential(
                1,
                "Twitter Retweet",
                "Retweet <%= $retweetUser %> on Twitter",
                2,
                "",
                "https://rd-worker.xgamma.workers.dev/img/328d2ceabec948999d82c24888877b72",
                projectId,
                creatorId,
                0,
                0
            )
        val testCredentials = listOf<Credential>(followCredential, retweetCredential)
        val testGroups = listOf<CredentialGroup>(
            CredentialGroup(
                2,
                "Twitter",
                0,
                testCredentials,
                0,
                creatorId,
                0,
                0,
                emptyList(),
                emptyList()
            )
        )
//        println(mapper.writeValueAsString(testGroups))
        val govGroups = listOf<CredentialGroup>(credentialGroup1, credentialGroup2, credentialGroup3)
        val campaign = Campaign(
            title = "tbook 666",
            picUrl = "https://rd-worker.xgamma.workers.dev/img/c761d3f0ac734a398999636e2e516512",
            description = "abc is abc",
            projectId = 153900040006
        )
        val campaignTotal = CampaignTotal(campaign, testGroups)

//        println(enumValues<Actions>().map { it.name })


        val link =
            "https://twitter.com/i/spaces/1ypKddpBgwVKW?s=20" // eg: https://twitter.com/i/spaces/1ypKddpBgwVKW?s=20  ->  1ypKddpBgwVKW

//        println(CampaignStatus.SCHEDULED.value)

//        val url = URI(link)
//        val segments = url.path.split("/")
//        val spaceId = segments.last()
//        println(spaceId)


//        println(mapper.writeValueAsString(campaignTotal))

        val twitterJson = """
    {
        "list": [
            {
                "name": "link",
                "label": "Tweet Link",
                "component": "Input",
                "componentProps": {
                    "placeholder": "Paste tweet link here"
                },
                "rules": [
                    {
                        "required": true,
                        "message": "Please input your tweet link"
                    },
                    {
                        "type": "url",
                        "message": "Please input a valid url"
                    }
                ]
            }
        ]
    }
"""
        val spaceJson = """
    {
        "list": [
            {
                "name": "link",
                "label": "Tweet Space Link",
                "component": "Input",
                "componentProps": {
                    "placeholder": "Paste twitter space link here"
                },
                "rules": [
                    {
                        "required": true,
                        "message": "Please input your tweet link"
                    },
                    {
                        "type": "url",
                        "message": "Please input a valid url"
                    }
                ]
            }
        ]
    }
"""
        val discordJson = """
    {
        "list": [
            {
                "name": "link",
                "label": "Discord Server URL",
                "component": "Input",
                "componentProps": {
                    "placeholder": "https://discord.gg/xxxx"
                },
                "rules": [
                    {
                        "required": true,
                        "message": "Please input the Discord Server URL"
                    },
                    {
                        "type": "url",
                        "message": "Please input a valid url"
                    }
                ]
            }
        ]
    }
"""
        val discordServerJson = """
    {
        "list": [
            {
                "component": "HTML",
                "html": "<a style='color:#1D9BF0' class='underline' href='https://app.gitbook.com/o/XmLEuzCUK0IIbhY5X44k/s/xLOTfURQ4EC9jmYQjFob/how-to-get-role-id-in-discord' target='_blank'>How to get Role ID in Discord</a>"
            },
            {
                "name": "link",
                "label": "Discord Server URL",
                "component": "Input",
                "componentProps": {
                    "placeholder": "https://discord.gg/xxxx"
                },
                "rules": [
                    {
                        "required": true,
                        "message": "Please input the Verify Discord Role"
                    },
                    {
                        "type": "url",
                        "message": "Please input a valid url"
                    }
                ]
            },
            {
                "name": "roleId",
                "label": "Role ID",
                "component": "Input",
                "componentProps": {
                    "placeholder": "Enter Role ID"
                },
                "rules": [
                    {
                        "required": true,
                        "message": "Please input the Role ID"
                    }
                ]
            },
            {
                "name": "roleName",
                "label": "Role Name",
                "component": "Input",
                "componentProps": {
                    "placeholder": "Enter Role Name"
                },
                "rules": [
                    {
                        "required": true,
                        "message": "Please input the Role Name"
                    }
                ]
            }
        ]
    }
"""
        val telegramGroupJson = """
    {
        "list": [
            {
                "component": "HTML",
                "html": "<p>Add TBOOK support bot as an admin to your group or channel</p> <a href='https://t.me/tbook_sign_bot' style='color:#1D9BF0' class='underline' target='_blank'> Invite bot </a >"
            },
            {
                "name": "link",
                "label": "Group Invite Link",
                "component": "Input",
                "componentProps": {
                    "placeholder": "Please paste the invite link to your telegram group"
                },
                "rules": [
                    {
                        "required": true,
                        "message": "Please input the invite link"
                    },
                    {
                        "type": "url",
                        "message": "Please input a valid url"
                    }
                ]
            }
        ]
    }
"""
        val telegramChannelJson = """
    {
        "list": [
            {
                "component": "HTML",
                "html": "<p>Add TBOOK support bot as an admin to your group or channel</p> <a href='https://t.me/tbook_sign_bot' style='color:#1D9BF0' class='underline' target='_blank'> Invite bot </a >"
            },
            {
                "name": "link",
                "label": "Channel Invite Link",
                "component": "Input",
                "componentProps": {
                    "placeholder": "Please paste the invite link to your telegram channel"
                },
                "rules": [
                    {
                        "required": true,
                        "message": "Please input the invite link"
                    },
                    {
                        "type": "url",
                        "message": "Please input a valid url"
                    }
                ]
            }
        ]
    }
"""
        val visitPageJson =
            """
    {
          "list": [
            {
              "name": "name",
              "label": "Name",
              "component": "Input",
              "componentProps": {
                "placeholder": "Please enter the page name or site name"
              },
              "rules": [
                {
                  "required": true,
                  "message": "Please input the name",
                  "type": null
                }
              ]
            },
            {
              "name": "link",
              "label": "Link",
              "component": "Input",
              "componentProps": {
                "placeholder": "Please paste the link the users need to visit"
              },
              "rules": [
                {
                  "required": true,
                  "message": "Please input the link",
                  "type": null
                },
                {
                  "required": false,
                  "message": "Please input a valid url",
                  "type": "url"
                }
              ]
            }
          ]
    }
"""
        val gson = Gson()
        val pageJson = gson.fromJson(CredentialForms.VISIT_PAGE_OR_SITE.json, FormList::class.java)
//        println(pageJson.list[0].componentProps)

        val template =
            """<p class='text-base' style=" display: inline;"> 
//            <a href=" "  style="color: #3A82F7;" target="_blank">
        %s %s
                    </p >"""
//        println(template)
        val link1 = "https://hub.snapshot.org/api/proposals/QmWbpCtwdLzxuLKnMW4Vv4MPFd2pdPX71YBKPasfZxqLUS"
        val link2 = "https://snapshot.org/#/keylasue.eth/proposal/0x6f7cc109170a98ec8a38a189dc2811c81297c2b7c5c9128ec92860581bf55731"
        val url = URI(link2)
        println(url.path)
        val segments = link2.split("/")
        val spaceId = segments.lastOrNull()
        println(spaceId)

    }


}
