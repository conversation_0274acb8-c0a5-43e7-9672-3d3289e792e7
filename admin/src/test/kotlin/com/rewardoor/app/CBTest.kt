package com.rewardoor.app

import com.auth0.jwt.JWT
import com.rewardoor.app.services.CallbackService
import com.rewardoor.app.utils.Hashs
import com.rewardoor.model.User
import com.rewardoor.model.UserEvm
import org.junit.jupiter.api.Test


class CBTest {
    @Test
    fun testCBGoPlus() {
        val evm = UserEvm(1, "0x8097bbb6DAf949A7430787d37b2042ea8371dD91")
        CallbackService.callbackGoPlus("https://test-secwarex-api.ansuzsecurity.com/open/v1/task/finish",
            User(1, "", "", "", evm = evm), 1, System.currentTimeMillis())
    }

    @Test
    fun testHkdf() {
        val token = JWT.decode("eyJhbGciOiJSUzI1NiIsImtpZCI6IjkxNDEzY2Y0ZmEwY2I5MmEzYzNmNWEwNTQ1MDkxMzJjNDc2NjA5MzciLCJ0eXAiOiJKV1QifQ.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.bXt5mAezkAc83KwsVLceuM7EAFRdec_2Bz751QtDsZ9AXZKhCUBZro92k_t7XtS3n62tVg95ZUnk4FKk3oODdnG3XGz6_tA6dD92mq7-vOLl1rWS6XPABpuJVzQpA9FHEd3fS5RMx0YNCSD5-prZHzDVQ8RiRL9-hp4L9rDfJIPxuySi0Fyh66DcL3TUGhslff6DFVwQJo5gUCM2T7eyQrzOM0EajHmSI_nJLmYIYGSLYRISpgNl47iY1pfonyR5sFoGqau6q0z3c8M1OvvAeeuLQamiz-sLcGt1cb2kLrcAPPhFY83oibLlO_Kxi8umYL2CfLyd6J1hhLNPY8CiTg")
        val salt = Hashs.hkdfJwt(token)
        println(salt)
    }
}