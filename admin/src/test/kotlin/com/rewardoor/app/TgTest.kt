package com.rewardoor.app

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.databind.node.ObjectNode
import com.pengrad.telegrambot.TelegramBot
import com.pengrad.telegrambot.request.GetChat
import com.pengrad.telegrambot.request.GetChatMember
import com.rewardoor.app.services.TelegramService
import org.junit.jupiter.api.Test
import java.security.MessageDigest
import java.util.*
import javax.crypto.Mac
import javax.crypto.spec.SecretKeySpec

class TgTest {
    val mapper = ObjectMapper()
    val tgToken = "6610421175:AAHDuSR_gDR3jsMy5jnjS1LT1iSu5HjKE4g"
    @Test
    fun test_sign_verify() {
        val authResult = "eyJpZCI6MzI1ODE0NDc5LCJmaXJzdF9uYW1lIjoiZ2FnIiwibGFzdF9uYW1lIjoid29uZyIsInVzZXJuYW1lIjoiZ2Fnd29uZyIsImF1dGhfZGF0ZSI6MTY5MzM3OTYwNiwiaGFzaCI6IjJhMmI2NjljYjUwM2ZhODc0MTg1ZDk0Yjg3OGY2YWE1ZTJjMGVlNmE4ZmU3MThjOTA0MWE2MGE3M2IxZWZlNTQifQ"
        val auth = String(Base64.getUrlDecoder().decode(authResult))
        val authObj = mapper.readTree(auth) as ObjectNode
        val hash = authObj.get("hash").asText()
        authObj.remove("hash")
        val paramMap = authObj.properties().associate { it.key to it.value.asText() }
        val joinString = paramMap.entries.sortedBy { it.key }.joinToString(separator = "\n") { "${it.key}=${it.value}" }
        println("joinString: $joinString")

        val sk = SecretKeySpec( // Get SHA 256 from telegram token
            MessageDigest.getInstance("SHA-256").digest(tgToken.toByteArray()), "HmacSHA256"
        )
        val mac: Mac = Mac.getInstance("HmacSHA256")
        mac.init(sk)

        val result = mac.doFinal(joinString.toByteArray())
        val resultStr = HexFormat.of().formatHex(result)

        println("hash: $hash")

        println("result: $resultStr")
    }

    @Test
    fun testMember(){
        val tgBot = TelegramBot(tgToken)
        val member = tgBot.execute(GetChatMember("@tokenpocket_channel", 6856010782))
        println(member)
    }

    @Test
    fun getChat() {
        val tgBot = TelegramBot(tgToken)
        val chat = tgBot.execute(GetChat("emacs_china"))
        println(chat)
    }

    @Test
    fun verifyMiniApp() {
        println(TelegramService.verifyMiniApp("query_id=AAHPiGsTAAAAAM-IaxPERm4P&user=%7B%22id%22%3A325814479%2C%22first_name%22%3A%22gag%22%2C%22last_name%22%3A%22wong%22%2C%22username%22%3A%22gagwong%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1711425640&hash=c6faa5a0e54e29a6de5375fb5ce20a422d6a2b09ff77a41c023f900b4086e788",
                "6364113882:AAGKqnqRqo5am22knQQ-mgDF_-J0YKGDzLY"))
    }
}