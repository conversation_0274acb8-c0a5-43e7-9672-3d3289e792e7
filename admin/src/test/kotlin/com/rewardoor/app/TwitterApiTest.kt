package com.rewardoor.app

import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.twitter.clientlib.TwitterCredentialsBearer
import com.twitter.clientlib.api.TwitterApi
import okhttp3.RequestBody
import org.apache.http.client.utils.URIBuilder
import org.apache.http.message.BasicNameValuePair
import org.junit.jupiter.api.Test
import org.springframework.web.reactive.function.client.WebClient
import org.springframework.http.MediaType
import org.springframework.web.reactive.function.client.body
import org.springframework.web.reactive.function.client.bodyToMono
import reactor.core.publisher.Mono
import java.net.URI

data class RequestBody(val numberOfQRCodes: Int, val url: String)
data class Result(val activityId: String)
data class ErrorData(val detail: String?)
data class Response(val code: String, val msg: String, val result: Result?, val data: ErrorData?)

data class Progress(val userId: String, val count: Int, val activityId: String, val totalCount: Int)
data class ResultDetails(val progress: List<Progress>, val activityId: String, val totalCount: Int, val zipUrl: String)
data class RequestDetails(val activityId: String, val userId: String)
data class ResponseDetails(val code: String, val msg: String, val result: ResultDetails?)

data class RequestCheckIn(val activityId: String, val checkInCode: String, val userId: String)
data class ResultCheckIn(
    val checkInCode: String?,
    val activityId: String,
    val userId: String?,
    val userCheckInCount: Int,
    val totalCheckInCount: Int
)

data class ResponseCheckIn(val code: String, val msg: String, val result: ResultCheckIn?)

class TwitterApiTest {

    @Test
    fun test() {
        val id = "NGNvM0Z1WlpmM0wyYkJoZ2gxQmo6MTpjaQ"
        val secret = "NGctQalrX3gPnkASa2P_xAT8rxxI7FZr8BRFSnYb5IejW_xjNa"
        val token = "dnQ2cU1YbnNVM0plTFVjRF9WM0NuYUVrcUdqd2tFckNoTEt3QTJQdllETFloOjE2OTIxMDAwMTk3ODI6MTowOmF0OjE"
        val refreshToken = "VExCN3BpZE1Ca0dwUHlVdGh5NUhqRjJJQnJLYmNnTVZKWlhLR0NHN1ppR0NGOjE2OTIxMDAwMTk3ODI6MToxOnJ0OjE"
        val credential =
            TwitterCredentialsBearer("AAAAAAAAAAAAAAAAAAAAAF3%2BcAEAAAAAe%2Fq0eaPSesDTscu6o5er2X16D6o%3Dkr0K0pnLpApRzApuq4r4bvC7NMg3YjeQn5Bv7sqr9uqdGHjpsl")
        // TwitterCredentialsOAuth2(id, secret, token, refreshToken)
        //credential.isOAUth2AutoRefreshToken = true
        val apiInstance = TwitterApi(credential)
//        val me = apiInstance.users().findMyUser(emptySet(), emptySet(), emptySet())
//        println(me)
//        val find = apiInstance.users().findUserByUsername("xinwendiaocha", null, null, null)
//        println(find)
//        val like = apiInstance.users().tweetsIdLikingUsers("1688096258777640960", 10, "")
//        println(like)
//
//        val follow = apiInstance.users().usersIdFollowing("1332712954047856641", 10, null)
//        println(follow)
//
//        val rt = apiInstance.users().tweetsIdRetweetingUsers("1688096258777640960", 10, null)
//        println(rt)

//        val like = apiInstance.users().tweetsIdLikingUsers("1688096258777640960", 10, null)
//        println(like)
//
//        val spaces = apiInstance.spaces().findSpacesByCreatorIds(listOf("28940967,1260553941714186241,31083308,1072109417200414720,1002945631407702017,1476739136023597060"))
//            .expansions(setOf("creator_id,host_ids,invited_user_ids,speaker_ids,topic_ids"))
//            .execute()
//        println(spaces)

//        val spaces = apiInstance.spaces().searchSpaces("crypto", "live", 10, emptySet(), emptySet())
//        println(spaces)

//        val space = apiInstance.spaces().findSpaceById("1djGXlrQnwzGZ")
//            .spaceFields(setOf("scheduled_start,started_at"))
//            .expansions(setOf("creator_id"))
//            .execute()
//        println(space)

        val s = URI("https://twitter.com/lidangzzz/status/1692445697226801505")
        println(s.path.split("/").size)

        val s2 = URI("https://twitter.com/i/spaces/1djGXlrQnwzGZ?s=20")
        println(s2.path.split("/").size)

//        val instance = TwitterApi(TwitterCredentialsOAuth2(id, secret, "dHlHdGhuTVRPdlgyZnhacmJZcm9hLWFrRjF0NGYwM1FvaEg0QkhTOWZSOWdmOjE2OTMxMDU1Nzc5NzU6MToxOmF0OjE", ""))
//        val response = instance.tweets().usersIdTimeline("11704522")
//            .expansions(setOf("referenced_tweets.id"))
//            .exclude(setOf("replies"))
//            .execute()
//        println(response)
//        val rts = response.data?.flatMap { it.referencedTweets?.filter { ref -> ref.type == TweetReferencedTweets.TypeEnum.RETWEETED }?.map { it.id }.orEmpty() }
//        println(rts?.contains("1695366849603514476")?:false)
//        val response = apiInstance.tweets().usersIdLikedTweets("11704522")
//            .execute()
//        println(response)
//        val liked = response.data?.map { it.id }?.contains("1691129151900749826")
//        println(liked)
    }

    @Test
    fun testUTApi() {
        val fromId = "845538829989654528"
        val mapper = jacksonObjectMapper()
        val webClient = WebClient.create()
        val uri = URIBuilder("https://twitter.good6.top/api/base/apitools/followingsIds")
            .setParameters(
                BasicNameValuePair(
                    "apiKey",
                    "OoIT9D9eobq65ygEMt4jBxhwXD52EZIcbx3G9XiWqLTnk|68958709-EurDcpKpEWJHgMD545OBIKSNo90kFtjkASEyhYTXA"
                ), BasicNameValuePair("userId", fromId)
            )
            .build()
        val response = webClient.get()
            .uri(uri)
            .retrieve()
            .bodyToMono(String::class.java)
            .block()
        val data = mapper.readTree(response)["data"]
        val ids = mapper.readTree(data.asText())["ids"].toList().map { it.asLong() }
        println(ids)
    }

    @Test
    fun testAPIs() {
        val webClient = WebClient.create("http://dev.iamswr.com:58054")
        val requestBody = RequestBody(100, "https://www.nftplay.fun/active")
        val response = webClient.post()
            .uri("/api/create_tasks")
            .header("x-api-key", "tUYNZ3aboVViF04xIjXD2WqcNFaoWxwdAMJA2XyhzgA")
            .contentType(MediaType.APPLICATION_JSON)
            .body(Mono.just(requestBody), RequestBody::class.java)
            .retrieve()
            .bodyToMono(Response::class.java)
            .block()!!

        when (response?.code) {
            "0" -> println("成功，活动ID：${response.result?.activityId}")
            else -> println("错误：${response.msg}，详情：${response.data?.detail}")
        }
    }

    @Test
    fun testDetailsAPI() {
        val activityId = "ACTIVITYID-9fd3d4cd-63a4-4b07-92f0-092a4a39424e-1707228017031"
        val userId = ""

        val webClient = WebClient.create("http://dev.iamswr.com:58054")
        val requestDetails = RequestDetails(activityId, userId)
        val response = webClient.post()
            .uri("/api/activity/details")
            .header("x-api-key", "tUYNZ3aboVViF04xIjXD2WqcNFaoWxwdAMJA2XyhzgA")
            .contentType(MediaType.APPLICATION_JSON)
            .body(Mono.just(requestDetails), RequestDetails::class.java)
            .retrieve()
            .bodyToMono(ResponseDetails::class.java)
            .block()

        if (response?.code == "0") {
            println("成功，活动ID：${response.result?.activityId}，二维码总数：${response.result?.totalCount}，压缩包下载链接：${response.result?.zipUrl}")
            response.result?.progress?.forEach {
                println("用户ID：${it.userId}，签到次数：${it.count}，活动ID：${it.activityId}，总二维码数：${it.totalCount}")
            }
        } else {
            println("错误：${response?.msg}")
        }
    }

    @Test
    fun testCheckInAPI() {
        // 使用特定的活动ID，签到码，用户ID进行测试
        val activityId = "ACTIVITYID-9fd3d4cd-63a4-4b07-92f0-092a4a39424e-1707228017031"
        val checkInCode = "CHECK_IN_CODE-24ab5368-caf3-45da-87df-b260150e29df-1707228017091"
        val userId = "0x1B670F2Cf48bb149C8E86ed246E8bD85FC58F028"

        val webClient = WebClient.create("http://dev.iamswr.com:58054")
        val requestCheckIn = RequestCheckIn(activityId, checkInCode, userId)
        val response = webClient.post()
            .uri("/api/checkin")
            .header("x-api-key", "tUYNZ3aboVViF04xIjXD2WqcNFaoWxwdAMJA2XyhzgA")
            .contentType(MediaType.APPLICATION_JSON)
            .body(Mono.just(requestCheckIn), RequestCheckIn::class.java)
            .retrieve()
            .bodyToMono(ResponseCheckIn::class.java)
            .block()
        println(response.toString())

        if (response?.code == "0") {
            println("签到成功，活动ID：${response.result?.activityId}，用户ID：${response.result?.userId}，签到码：${response.result?.checkInCode}，用户当前签到数量：${response.result?.userCheckInCount}，总签到数量：${response.result?.totalCheckInCount}")
        } else {
            println("错误：${response?.msg}")
        }
    }

    @Test
    fun testTwitterUtoolsApi() {
        val tweetId = "1871549538370228298"
        val mapper = jacksonObjectMapper()
        val webClient = WebClient.create()
        val uri = URIBuilder("https://twitter.good6.top/api/base/apitools/tweetSimple")
            .setParameters(
                BasicNameValuePair(
                    "apiKey",
                    "OoIT9D9eobq65ygEMt4jBxhwXD52EZIcbx3G9XiWqLTnk|68958709-EurDcpKpEWJHgMD545OBIKSNo90kFtjkASEyhYTXA"
                ), BasicNameValuePair("id", tweetId)
            )
            .build()
        println(uri.toString())
        val response = webClient.get()
            .uri(uri)
            .retrieve()
            .bodyToMono(String::class.java)
            .block()
        println(response.toString())
        val data = mapper.readTree(response)["data"]
        println(data.toString())
        val tweetResult = mapper.readTree(data.toString())["tweetResult"]
        println(tweetResult)
    }
}