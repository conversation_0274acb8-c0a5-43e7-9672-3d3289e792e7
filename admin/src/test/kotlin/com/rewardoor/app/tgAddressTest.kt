package com.rewardoor.app

import com.fasterxml.jackson.databind.node.ArrayNode
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.google.gson.Gson
import com.rewardoor.model.TokenHolder
import com.rewardoor.model.TokenResponse
import org.apache.http.client.utils.URIBuilder
import org.apache.http.message.BasicNameValuePair
import org.json.JSONArray
import org.json.JSONObject
import org.junit.jupiter.api.Test
import org.springframework.web.reactive.function.BodyInserters
import org.springframework.web.reactive.function.client.ExchangeStrategies
import org.springframework.web.reactive.function.client.WebClient
import java.net.URI
import java.text.DecimalFormat

class TgAddressTest(
) {
    private final val strategies = ExchangeStrategies.builder()
        .codecs { codecs -> codecs.defaultCodecs().maxInMemorySize(20 * 1024 * 1024) }
        .build()
    private val webClient = WebClient.builder().exchangeStrategies(strategies).build()
    private val mapper = jacksonObjectMapper()

    private fun sendThirdAndCheck(uri: URI, target: String): Boolean {
        val response = webClient.get()
            .uri(uri)
            .retrieve()
            .bodyToMono(String::class.java)
            .block()
        val content = mapper.readTree(response)
        if (content["msg"].asText() != "SUCCESS") return false
        val data = content.get("data") ?: return false
        if (data.asText().equals("Not Found")) return false
        val ids = mapper.readTree(data.asText()).get("ids")?.toList()?.map { it.asLong() } ?: return false
        return ids.contains(target.toLong())
    }

    @Test
    fun getEvmBalanceResult() {
        val address = "******************************************"
        val response = WebClient.builder().build()
            .get()
            .uri(
                "https://api.etherscan.io/api?module=account&action=balance&address=${address}&tag=latest&apikey=B88663Z7CUY5JDUE12H7E8MWKP9WW5XFGI}"
            )
            .retrieve()
            .bodyToMono(String::class.java)
            .block();
        val jsonObject = JSONObject(response.toString())
        val result = jsonObject.getLong("result")
        val ethNum = result / 1_000_000_000_000_000_000.0
        val df = DecimalFormat("0.0000000")
        val ethStr = df.format(ethNum)
        println(ethStr)
    }

    @Test
    fun getCoinPrice() {
        val ethUri = URI("https://api.binance.com/api/v3/ticker/price?symbol=ETHUSDT")
        val futureUri = URI("https://fapi.binance.com/fapi/v1/ticker/price?symbol=TONUSDT")
        val response = WebClient.builder().build()
            .get()
            .uri(futureUri)
            .retrieve()
            .bodyToMono(String::class.java)
            .block();

        //  转化为 JSONObject
        val jsonObject = JSONObject(response.toString())
        println(jsonObject.toString())
        // 获取价格
        val price = jsonObject.getString("price")

        print(price.toDouble())
    }

    @Test
    fun getTwitterFollowingNum() {
        val uri = URIBuilder("https://twitter.good6.top/api/base/apitools/uerByIdOrNameLookUp")
            .setParameters(
                BasicNameValuePair(
                    "apiKey",
                    "o7ZFzpSQlSmRYHi612DGxgjsmhz263vDRbp35frwEcvyS|11704522-PShUS3dHZ8dhKds3z9mdzDNbQbSAvq08MRMbRtcHa"
                ), BasicNameValuePair("userId", "1478980168194555908")
            )
            .build()
        val response = webClient.get()
            .uri(uri)
            .retrieve()
            .bodyToMono(String::class.java)
            .block()
        val content = mapper.readTree(response)
        if (content["msg"].asText() != "SUCCESS") println("")
        val data = content.get("data")?.asText()
        if (data == "Not Found") println("")

        val dataArray = mapper.readValue(data, ArrayNode::class.java)
        val followerNum = dataArray.get(0).get("followers_count")?.asInt() ?: println("")
        println(followerNum)
    }


    @Test
    fun getBalanceResult() {
        val address = "Ef8zMzMzMzMzMzMzMzMzMzMzMzMzMzMzMzMzMzMzMzMzM0vF"
        val response = WebClient.builder().build()
            .get()
            .uri("https://toncenter.com/api/v2/getAddressBalance?address=${address}")
            .header("x-api-key", "63956d1b10345d7796c5526fffa73f486dd3547728bb6000ca630c6ed0dffb3b")
            .retrieve()
            .bodyToMono(String::class.java)
            .block()
        val jsonObject = JSONObject(response.toString())
        val result = jsonObject.getLong("result")
        val tons = result / 1_000_000_000.0
        val df = DecimalFormat("0.0000000")
        val tonsStr = df.format(tons)

        println(response.toString() + " " + tonsStr)
    }

    @Test
    fun getTokenPrice() {
        val response = WebClient.builder().build()
            .get()
            .uri("https://pro-api.coinmarketcap.com/v2/cryptocurrency/quotes/latest?id=11419")
            .header("X-CMC_PRO_API_KEY", "a12e9aab-3b62-45eb-80d1-94d58e7feaa0")
            .retrieve()
            .bodyToMono(String::class.java)
            .block()
        val jsonObject = JSONObject(response.toString())
        println(response.toString())
    }

    @Test
    fun getEthTransactions() {
        val apiKey = "B88663Z7CUY5JDUE12H7E8MWKP9WW5XFGI"
        val response = WebClient.builder().build()
            .get()
            .uri("https://api.etherscan.io/api?module=proxy&action=eth_getTransactionCount&address=******************************************&tag=latest&apikey=B88663Z7CUY5JDUE12H7E8MWKP9WW5XFGI")
            .retrieve()
            .bodyToMono(String::class.java)
            .block();
        val jsonObject = JSONObject(response.toString())
        var resultInHex = jsonObject.getString("result") // 获取"result"字段的值
        resultInHex = if (resultInHex.startsWith("0x")) resultInHex.substring(2) else resultInHex
        val resultAsDecimal = resultInHex.toLong(16) // 将十六进制的值转换为十进制
        println(resultAsDecimal)
    }

    @Test
    fun getTransactions() {
        val address = "Ef8zMzMzMzMzMzMzMzMzMzMzMzMzMzMzMzMzMzMzMzMzM0vF"
        val limit = 100
        val response = WebClient.builder().build()
            .get()
            .uri("https://toncenter.com/api/v2/getTransactions?address=${address}&limit=${100}")
            .header("x-api-key", "63956d1b10345d7796c5526fffa73f486dd3547728bb6000ca630c6ed0dffb3b")
            .retrieve()
            .bodyToMono(String::class.java)
            .block()

        val jsonObject = JSONObject(response.toString())
        val transactions = jsonObject.getJSONArray("result") // 获取 'result' 数组
        val transactionCount = transactions.length()

//        println(response.toString())

        println("Total number of transactions: $transactionCount")

    }

    @Test
    fun getTokenHolders() {
        val webClient =
            WebClient.builder().codecs { it.defaultCodecs().maxInMemorySize(30 * 1024 * 1024) }  // 提高到1MB build()
                .build()
        val currency = "0x115eC79F1de567eC68B7AE7eDA501b406626478e"
        val date = "2024-04-20"

        val response = webClient.post()
            .uri("https://streaming.bitquery.io/graphql")
            .header("Content-Type", "application/json")
            .header("X-API-KEY", "BQYcZ3KaDm7i54LWYWfUkyoMU1CvldGD")
            .header(
                "Authorization",
                "Bearer ory_at_6A5RfWk9cJxvcoACvZOHQny7iOZPadKWFkyUGsZ530Y.qhKEKCjvGAndYHiPWomvW0fUKWNmA772CQhZwLJiIx8"
            )
            .body(BodyInserters.fromValue("{\"query\":\"query(\$currency: String! \$date: String!) {\\n  EVM(dataset: archive) {\\n    TokenHolders(\\n      tokenSmartContract: \$currency\\n      date: \$date\\n  where: {Balance: {Amount: {gt: \\\"1\\\"}}}\\n limit: {count: 10}\\n orderBy: { descending: Balance_Amount }\\n ) {\\n      Balance {\\n        Amount\\n      }\\n      Holder {\\n        Address\\n      }\\n   }\\n  }\\n}\",\"variables\":\"{\\n  \\\"currency\\\": \\\"$currency\\\",\\n  \\\"date\\\": \\\"$date\\\"\\n}\"}"))
            .retrieve()
            .bodyToMono(String::class.java)
            .block()
        println(response.toString())
        val gson = Gson()
        val responseJson = gson.fromJson(response, TokenResponse::class.java)
        println(responseJson.data)
    }
}