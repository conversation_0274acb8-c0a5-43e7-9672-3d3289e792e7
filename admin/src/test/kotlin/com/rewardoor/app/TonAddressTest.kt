package com.rewardoor.app

import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test
import org.ton.java.address.Address

class TonAddressTest {
    @Test
    fun testTonAddress() {
        val addr = Address.of("EQDEd4jsQ0WJWnrPWIHxyiitVzjse5Nw3QJUiI8Bp_1Kjdgd")
        val hex = addr.toHex()
        val nonBounceable = addr.toNonBounceable()
        Assertions.assertEquals("0:c47788ec4345895a7acf5881f1ca28ad5738ec7b9370dd0254888f01a7fd4a8d", "0:$hex")
        Assertions.assertEquals("UQDEd4jsQ0WJWnrPWIHxyiitVzjse5Nw3QJUiI8Bp_1KjYXY", nonBounceable)
    }
}