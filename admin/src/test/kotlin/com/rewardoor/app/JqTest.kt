package com.rewardoor.app

import com.jayway.jsonpath.JsonPath
import org.json.JSONObject
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test

class JqTest {
    @Test
    fun testParseGemEvents() {
        val data = """
            {
                "events": [
                    {
                        "event_id": "39af9b2a99705d1c11042264347f5770a462ecc474e3cc0492d995e697639efe",
                        "account": {
                            "address": "0:7f9e8876d5034edbc696508a7026cfcd4168532765ef92877fea5fe2046bd591",
                            "is_scam": false,
                            "is_wallet": true
                        },
                        "timestamp": **********,
                        "actions": [
                            {
                                "type": "NftItemTransfer",
                                "status": "ok",
                                "NftItemTransfer": {
                                    "recipient": {
                                        "address": "0:7f9e8876d5034edbc696508a7026cfcd4168532765ef92877fea5fe2046bd591",
                                        "is_scam": false,
                                        "is_wallet": true
                                    },
                                    "nft": "0:96bcfaec757f8375626c9c197ce1ce5810658c68a15a71a38154f62d56a0343a",
                                    "comment": "🎁Get 70 TON @‌stonfi_kbot"
                                },
                                "simple_preview": {
                                    "name": "NFT Transfer",
                                    "description": "Transferring 1 NFT",
                                    "value": "1 NFT",
                                    "accounts": [
                                        {
                                            "address": "0:96bcfaec757f8375626c9c197ce1ce5810658c68a15a71a38154f62d56a0343a",
                                            "is_scam": false,
                                            "is_wallet": false
                                        }
                                    ]
                                },
                                "base_transactions": [
                                    "39af9b2a99705d1c11042264347f5770a462ecc474e3cc0492d995e697639efe",
                                    "73604804e73977ab163b6d5e7c19a5b2e32242bcdbadf45365484cde97d9acc8"
                                ]
                            }
                        ],
                        "is_scam": false,
                        "lt": **************,
                        "in_progress": false,
                        "extra": -14
                    },
                    {
                        "event_id": "d45be4559c4821dd28df8855f229198a7a139fd4b9857cd6ec8427b41bcc0b56",
                        "account": {
                            "address": "0:7f9e8876d5034edbc696508a7026cfcd4168532765ef92877fea5fe2046bd591",
                            "is_scam": false,
                            "is_wallet": true
                        },
                        "timestamp": **********,
                        "actions": [
                            {
                                "type": "SmartContractExec",
                                "status": "ok",
                                "SmartContractExec": {
                                    "executor": {
                                        "address": "0:7f9e8876d5034edbc696508a7026cfcd4168532765ef92877fea5fe2046bd591",
                                        "is_scam": false,
                                        "is_wallet": true
                                    },
                                    "contract": {
                                        "address": "0:1504bcec203373f025bdc119b5fb307ed64affc5c595a2e8d1a2ed2faeb88e99",
                                        "is_scam": false,
                                        "is_wallet": false
                                    },
                                    "ton_attached": *********,
                                    "operation": "0x00000002"
                                },
                                "simple_preview": {
                                    "name": "Smart Contract Execution",
                                    "description": "Execution of smart contract",
                                    "accounts": [
                                        {
                                            "address": "0:1504bcec203373f025bdc119b5fb307ed64affc5c595a2e8d1a2ed2faeb88e99",
                                            "is_scam": false,
                                            "is_wallet": false
                                        }
                                    ]
                                },
                                "base_transactions": [
                                    "50c05c1980559b5eac57daf85266a36cd957bd01cf009be0a0690b1ad82127f8"
                                ]
                            },
                            {
                                "type": "NftItemTransfer",
                                "status": "ok",
                                "NftItemTransfer": {
                                    "sender": {
                                        "address": "0:1504bcec203373f025bdc119b5fb307ed64affc5c595a2e8d1a2ed2faeb88e99",
                                        "is_scam": false,
                                        "is_wallet": false
                                    },
                                    "recipient": {
                                        "address": "0:7f9e8876d5034edbc696508a7026cfcd4168532765ef92877fea5fe2046bd591",
                                        "is_scam": false,
                                        "is_wallet": true
                                    },
                                    "nft": "0:1ec84fd7c01de86d19045179c420a956b4dbc0f9e51e504dfe20448255bdfb02"
                                },
                                "simple_preview": {
                                    "name": "NFT Transfer",
                                    "description": "Transferring 1 NFT",
                                    "value": "1 NFT",
                                    "accounts": [
                                        {
                                            "address": "0:1504bcec203373f025bdc119b5fb307ed64affc5c595a2e8d1a2ed2faeb88e99",
                                            "is_scam": false,
                                            "is_wallet": false
                                        },
                                        {
                                            "address": "0:1ec84fd7c01de86d19045179c420a956b4dbc0f9e51e504dfe20448255bdfb02",
                                            "is_scam": false,
                                            "is_wallet": false
                                        }
                                    ]
                                },
                                "base_transactions": [
                                    "90eb675114123a107c36841cdd9a15a80b2e925ad4af65f699e9d648ca0354b7",
                                    "52f4754b56daf67b0c25da5a6965f6bd9cc9b013130a4bb845c73a3f33d7d897",
                                    "c64d798c68d17bba8d25e6e0e400cb8b6d2a28df80776d771fc2aff4ac72e216"
                                ]
                            }
                        ],
                        "is_scam": false,
                        "lt": **************,
                        "in_progress": false,
                        "extra": *********
                    },
                    {
                        "event_id": "1a2fa5b6b203a718f2b21d98a480a1f58a887f600adcdd671487e0065f343c13",
                        "account": {
                            "address": "0:7f9e8876d5034edbc696508a7026cfcd4168532765ef92877fea5fe2046bd591",
                            "is_scam": false,
                            "is_wallet": true
                        },
                        "timestamp": **********,
                        "actions": [
                            {
                                "type": "TonTransfer",
                                "status": "ok",
                                "TonTransfer": {
                                    "sender": {
                                        "address": "0:97ad93444915089e812238ff10abe9066d0b03ea3dba2a8630fb9c9f88aa455c",
                                        "name": "Wallet in Telegram",
                                        "is_scam": false,
                                        "icon": "https://cache.tonapi.io/imgproxy/2_S8J7W2ZvbksgpKkzdenfkn5uqPoMTqsIxTwMUwkYc/rs:fill:200:200:1/g:no/aHR0cHM6Ly90b24uYW1zMy5kaWdpdGFsb2NlYW5zcGFjZXMuY29tL3RvbmFwaS1taXNjL2xvZ29zL3dhbGxldF9ib3RfMjg4LnN2Zw.webp",
                                        "is_wallet": true
                                    },
                                    "recipient": {
                                        "address": "0:7f9e8876d5034edbc696508a7026cfcd4168532765ef92877fea5fe2046bd591",
                                        "is_scam": false,
                                        "is_wallet": true
                                    },
                                    "amount": 2*********
                                },
                                "simple_preview": {
                                    "name": "Ton Transfer",
                                    "description": "Transferring 2.********* TON",
                                    "value": "2.********* TON",
                                    "accounts": [
                                        {
                                            "address": "0:97ad93444915089e812238ff10abe9066d0b03ea3dba2a8630fb9c9f88aa455c",
                                            "name": "Wallet in Telegram",
                                            "is_scam": false,
                                            "icon": "https://cache.tonapi.io/imgproxy/2_S8J7W2ZvbksgpKkzdenfkn5uqPoMTqsIxTwMUwkYc/rs:fill:200:200:1/g:no/aHR0cHM6Ly90b24uYW1zMy5kaWdpdGFsb2NlYW5zcGFjZXMuY29tL3RvbmFwaS1taXNjL2xvZ29zL3dhbGxldF9ib3RfMjg4LnN2Zw.webp",
                                            "is_wallet": true
                                        }
                                    ]
                                },
                                "base_transactions": [
                                    "0e47e0d072b654da31bfe4d05001bdcd661010875b69b52d7c186db4ef03c781"
                                ]
                            }
                        ],
                        "is_scam": false,
                        "lt": **************,
                        "in_progress": false,
                        "extra": -310058
                    },
                    {
                        "event_id": "a9689f8ddc4883e885e0b58fc0221e78166e246dfb86522803911c6b5cc144aa",
                        "account": {
                            "address": "0:7f9e8876d5034edbc696508a7026cfcd4168532765ef92877fea5fe2046bd591",
                            "is_scam": false,
                            "is_wallet": true
                        },
                        "timestamp": **********,
                        "actions": [
                            {
                                "type": "TonTransfer",
                                "status": "ok",
                                "TonTransfer": {
                                    "sender": {
                                        "address": "0:7f9e8876d5034edbc696508a7026cfcd4168532765ef92877fea5fe2046bd591",
                                        "is_scam": false,
                                        "is_wallet": true
                                    },
                                    "recipient": {
                                        "address": "0:293af16c881e0350f3194b6ab5a37a66cfd4dd100b318506764dba22e6f0a183",
                                        "is_scam": false,
                                        "is_wallet": true
                                    },
                                    "amount": ********,
                                    "comment": "1847982899703246848"
                                },
                                "simple_preview": {
                                    "name": "Ton Transfer",
                                    "description": "Transferring 0.028 TON",
                                    "value": "0.028 TON",
                                    "accounts": [
                                        {
                                            "address": "0:293af16c881e0350f3194b6ab5a37a66cfd4dd100b318506764dba22e6f0a183",
                                            "is_scam": false,
                                            "is_wallet": true
                                        }
                                    ]
                                },
                                "base_transactions": [
                                    "bb03a60f068cef72ca2d16826daadb1b787171ec84762289ae6b9c7941c222c2"
                                ]
                            }
                        ],
                        "is_scam": false,
                        "lt": **************,
                        "in_progress": false,
                        "extra": -3053629
                    },
                    {
                        "event_id": "b59de6b4c75b7e2dbde2f1bcf75a58ae58dc20b900e7a8869518368b9e708ffb",
                        "account": {
                            "address": "0:7f9e8876d5034edbc696508a7026cfcd4168532765ef92877fea5fe2046bd591",
                            "is_scam": false,
                            "is_wallet": true
                        },
                        "timestamp": **********,
                        "actions": [
                            {
                                "type": "TonTransfer",
                                "status": "ok",
                                "TonTransfer": {
                                    "sender": {
                                        "address": "0:7f9e8876d5034edbc696508a7026cfcd4168532765ef92877fea5fe2046bd591",
                                        "is_scam": false,
                                        "is_wallet": true
                                    },
                                    "recipient": {
                                        "address": "0:408da3b28b6c065a593e10391269baaa9c5f8caebc0c69d9f0aabbab2a99256b",
                                        "name": "Fragment",
                                        "is_scam": false,
                                        "icon": "https://cache.tonapi.io/imgproxy/0uK882EK9XAnVm4xooeVrk9QfvAgHdYdtXP0Wg9XlfY/rs:fill:200:200:1/g:no/aHR0cHM6Ly90b24uYW1zMy5kaWdpdGFsb2NlYW5zcGFjZXMuY29tL3RvbmFwaS1taXNjL2xvZ29zL2ZyYWdtZW50XzI4OC5zdmc.webp",
                                        "is_wallet": true
                                    },
                                    "amount": **********,
                                    "comment": "Telegram Premium for 3 months \n\nRef#PSwju1mpc"
                                },
                                "simple_preview": {
                                    "name": "Ton Transfer",
                                    "description": "Transferring 2.02 TON",
                                    "value": "2.02 TON",
                                    "accounts": [
                                        {
                                            "address": "0:408da3b28b6c065a593e10391269baaa9c5f8caebc0c69d9f0aabbab2a99256b",
                                            "name": "Fragment",
                                            "is_scam": false,
                                            "icon": "https://cache.tonapi.io/imgproxy/0uK882EK9XAnVm4xooeVrk9QfvAgHdYdtXP0Wg9XlfY/rs:fill:200:200:1/g:no/aHR0cHM6Ly90b24uYW1zMy5kaWdpdGFsb2NlYW5zcGFjZXMuY29tL3RvbmFwaS1taXNjL2xvZ29zL2ZyYWdtZW50XzI4OC5zdmc.webp",
                                            "is_wallet": true
                                        }
                                    ]
                                },
                                "base_transactions": [
                                    "449b2f0437a21e0e6a5f00dcfb852c62a35437da984438ce227ff0c53cbec90c"
                                ]
                            }
                        ],
                        "is_scam": false,
                        "lt": **************,
                        "in_progress": false,
                        "extra": -2622960
                    },
                    {
                        "event_id": "f920498519d0891bfb46acad0d8cf200cf4f3ff93f35e6f28fa97332f81656c7",
                        "account": {
                            "address": "0:7f9e8876d5034edbc696508a7026cfcd4168532765ef92877fea5fe2046bd591",
                            "is_scam": false,
                            "is_wallet": true
                        },
                        "timestamp": **********,
                        "actions": [
                            {
                                "type": "TonTransfer",
                                "status": "ok",
                                "TonTransfer": {
                                    "sender": {
                                        "address": "0:7f9e8876d5034edbc696508a7026cfcd4168532765ef92877fea5fe2046bd591",
                                        "is_scam": false,
                                        "is_wallet": true
                                    },
                                    "recipient": {
                                        "address": "0:408da3b28b6c065a593e10391269baaa9c5f8caebc0c69d9f0aabbab2a99256b",
                                        "name": "Fragment",
                                        "is_scam": false,
                                        "icon": "https://cache.tonapi.io/imgproxy/0uK882EK9XAnVm4xooeVrk9QfvAgHdYdtXP0Wg9XlfY/rs:fill:200:200:1/g:no/aHR0cHM6Ly90b24uYW1zMy5kaWdpdGFsb2NlYW5zcGFjZXMuY29tL3RvbmFwaS1taXNjL2xvZ29zL2ZyYWdtZW50XzI4OC5zdmc.webp",
                                        "is_wallet": true
                                    },
                                    "amount": **********,
                                    "comment": "Telegram Premium for 3 months \n\nRef#EvphRWp0x"
                                },
                                "simple_preview": {
                                    "name": "Ton Transfer",
                                    "description": "Transferring 2.07 TON",
                                    "value": "2.07 TON",
                                    "accounts": [
                                        {
                                            "address": "0:408da3b28b6c065a593e10391269baaa9c5f8caebc0c69d9f0aabbab2a99256b",
                                            "name": "Fragment",
                                            "is_scam": false,
                                            "icon": "https://cache.tonapi.io/imgproxy/0uK882EK9XAnVm4xooeVrk9QfvAgHdYdtXP0Wg9XlfY/rs:fill:200:200:1/g:no/aHR0cHM6Ly90b24uYW1zMy5kaWdpdGFsb2NlYW5zcGFjZXMuY29tL3RvbmFwaS1taXNjL2xvZ29zL2ZyYWdtZW50XzI4OC5zdmc.webp",
                                            "is_wallet": true
                                        }
                                    ]
                                },
                                "base_transactions": [
                                    "47abf32797d1c612a76a0bb257957dc78962cab543dc68399afa449873d60082"
                                ]
                            }
                        ],
                        "is_scam": false,
                        "lt": **************,
                        "in_progress": false,
                        "extra": -2658196
                    },
                    {
                        "event_id": "6319618e467e33bc66e24579ca12d71919545a7bb97e8c3fc732c5033492c40b",
                        "account": {
                            "address": "0:7f9e8876d5034edbc696508a7026cfcd4168532765ef92877fea5fe2046bd591",
                            "is_scam": false,
                            "is_wallet": true
                        },
                        "timestamp": **********,
                        "actions": [
                            {
                                "type": "NftItemTransfer",
                                "status": "ok",
                                "NftItemTransfer": {
                                    "recipient": {
                                        "address": "0:7f9e8876d5034edbc696508a7026cfcd4168532765ef92877fea5fe2046bd591",
                                        "is_scam": false,
                                        "is_wallet": true
                                    },
                                    "nft": "0:1b49e09011530a4b249b3d03c289af6e465bb2f1baae03bae969e60af9798e0d",
                                    "comment": "100,000 HMSTR VOUCHER"
                                },
                                "simple_preview": {
                                    "name": "NFT Transfer",
                                    "description": "Transferring 1 NFT",
                                    "value": "1 NFT",
                                    "accounts": [
                                        {
                                            "address": "0:1b49e09011530a4b249b3d03c289af6e465bb2f1baae03bae969e60af9798e0d",
                                            "is_scam": false,
                                            "is_wallet": false
                                        }
                                    ]
                                },
                                "base_transactions": [
                                    "ea581d9265a513a9fd05eb70d4442d0e05da34ab3edd8f053af9f3bf9b04a911",
                                    "b37ac8db4021f2df756a791605c147edaf72717baf0d08f7c70afb39b4d50a75"
                                ]
                            }
                        ],
                        "is_scam": false,
                        "lt": **************,
                        "in_progress": false,
                        "extra": -46
                    },
                    {
                        "event_id": "2684ef3db7ebfdd6b4bdd059cff8ac5d6c403f27031daf73e5765b9b49fde49b",
                        "account": {
                            "address": "0:7f9e8876d5034edbc696508a7026cfcd4168532765ef92877fea5fe2046bd591",
                            "is_scam": false,
                            "is_wallet": true
                        },
                        "timestamp": **********,
                        "actions": [
                            {
                                "type": "TonTransfer",
                                "status": "ok",
                                "TonTransfer": {
                                    "sender": {
                                        "address": "0:7f9e8876d5034edbc696508a7026cfcd4168532765ef92877fea5fe2046bd591",
                                        "is_scam": false,
                                        "is_wallet": true
                                    },
                                    "recipient": {
                                        "address": "0:408da3b28b6c065a593e10391269baaa9c5f8caebc0c69d9f0aabbab2a99256b",
                                        "name": "Fragment",
                                        "is_scam": false,
                                        "icon": "https://cache.tonapi.io/imgproxy/0uK882EK9XAnVm4xooeVrk9QfvAgHdYdtXP0Wg9XlfY/rs:fill:200:200:1/g:no/aHR0cHM6Ly90b24uYW1zMy5kaWdpdGFsb2NlYW5zcGFjZXMuY29tL3RvbmFwaS1taXNjL2xvZ29zL2ZyYWdtZW50XzI4OC5zdmc.webp",
                                        "is_wallet": true
                                    },
                                    "amount": **********,
                                    "comment": "Telegram Premium for 3 months \n\nRef#qgN1HjcYC"
                                },
                                "simple_preview": {
                                    "name": "Ton Transfer",
                                    "description": "Transferring 2.12 TON",
                                    "value": "2.12 TON",
                                    "accounts": [
                                        {
                                            "address": "0:408da3b28b6c065a593e10391269baaa9c5f8caebc0c69d9f0aabbab2a99256b",
                                            "name": "Fragment",
                                            "is_scam": false,
                                            "icon": "https://cache.tonapi.io/imgproxy/0uK882EK9XAnVm4xooeVrk9QfvAgHdYdtXP0Wg9XlfY/rs:fill:200:200:1/g:no/aHR0cHM6Ly90b24uYW1zMy5kaWdpdGFsb2NlYW5zcGFjZXMuY29tL3RvbmFwaS1taXNjL2xvZ29zL2ZyYWdtZW50XzI4OC5zdmc.webp",
                                            "is_wallet": true
                                        }
                                    ]
                                },
                                "base_transactions": [
                                    "69da6d0c62ab772bbb8309a0ea7f98142105d7649ec43fedd7d421c71adc9f88"
                                ]
                            }
                        ],
                        "is_scam": false,
                        "lt": **************,
                        "in_progress": false,
                        "extra": -2700242
                    },
                    {
                        "event_id": "568fead7321364a4873ea005d6f8a24c295d9c51f0e3cf1f0fe7f78e713533d5",
                        "account": {
                            "address": "0:7f9e8876d5034edbc696508a7026cfcd4168532765ef92877fea5fe2046bd591",
                            "is_scam": false,
                            "is_wallet": true
                        },
                        "timestamp": **********,
                        "actions": [
                            {
                                "type": "NftItemTransfer",
                                "status": "ok",
                                "NftItemTransfer": {
                                    "recipient": {
                                        "address": "0:7f9e8876d5034edbc696508a7026cfcd4168532765ef92877fea5fe2046bd591",
                                        "is_scam": false,
                                        "is_wallet": true
                                    },
                                    "nft": "0:6d4ca3a648bac489df91dc19d2bc48be5b46cd654f9e582fed9f4db5692e886e",
                                    "comment": "✅Verified!"
                                },
                                "simple_preview": {
                                    "name": "NFT Transfer",
                                    "description": "Transferring 1 NFT",
                                    "value": "1 NFT",
                                    "accounts": [
                                        {
                                            "address": "0:6d4ca3a648bac489df91dc19d2bc48be5b46cd654f9e582fed9f4db5692e886e",
                                            "is_scam": false,
                                            "is_wallet": false
                                        }
                                    ]
                                },
                                "base_transactions": [
                                    "eccdaf59c88059ab56a7fa2c5010d69f8dfdb00a97e29330df779c016c2829cf",
                                    "b34e9d639cfe8cc1e83ab4f58d984cdbdfe9ed96e7beeb67c38867e5e5b10d0c"
                                ]
                            }
                        ],
                        "is_scam": false,
                        "lt": **************,
                        "in_progress": false,
                        "extra": -25
                    },
                    {
                        "event_id": "2a181f9cadc0a0ebec93bc7a1a9ce993165b6578c0211367e17ea5ab45026610",
                        "account": {
                            "address": "0:7f9e8876d5034edbc696508a7026cfcd4168532765ef92877fea5fe2046bd591",
                            "is_scam": false,
                            "is_wallet": true
                        },
                        "timestamp": **********,
                        "actions": [
                            {
                                "type": "NftItemTransfer",
                                "status": "ok",
                                "NftItemTransfer": {
                                    "recipient": {
                                        "address": "0:7f9e8876d5034edbc696508a7026cfcd4168532765ef92877fea5fe2046bd591",
                                        "is_scam": false,
                                        "is_wallet": true
                                    },
                                    "nft": "0:73741cb8ba2f99cdc6a6f6f9a4de27e642900aac92f315fc50a82b1cf96c9406",
                                    "comment": "🎉Congrats!"
                                },
                                "simple_preview": {
                                    "name": "NFT Transfer",
                                    "description": "Transferring 1 NFT",
                                    "value": "1 NFT",
                                    "accounts": [
                                        {
                                            "address": "0:73741cb8ba2f99cdc6a6f6f9a4de27e642900aac92f315fc50a82b1cf96c9406",
                                            "is_scam": false,
                                            "is_wallet": false
                                        }
                                    ]
                                },
                                "base_transactions": [
                                    "7c2590cb1974ab7da99bc405bcdebb27490495934eab39f07c5ebd30737ec7d3",
                                    "9719b60f3d47cdf4b2d69a0563e3b2e97b53a664703a2a09e7a85eb713018e99"
                                ]
                            }
                        ],
                        "is_scam": false,
                        "lt": **************,
                        "in_progress": false,
                        "extra": -22
                    }
                ],
                "next_from": **************
            }
        """.trimIndent()

        val json = JsonPath.parse(data)
        val evt = json.read<List<String>>("$.events[*].actions[?(@.type == 'NftItemTransfer')].NftItemTransfer.sender.address")
        Assertions.assertEquals(evt[0], "0:1504bcec203373f025bdc119b5fb307ed64affc5c595a2e8d1a2ed2faeb88e99")
    }

    @Test
    fun testParseInterface() {
        val json = JsonPath.parse("""{"accounts": [{
    "address": "0:1504bcec203373f025bdc119b5fb307ed64affc5c595a2e8d1a2ed2faeb88e99",
    "balance": 0,
    "last_activity": **********,
    "status": "active",
    "interfaces": [
        "nft_sale_getgems_v3"
    ],
    "get_methods": [
        "get_sale_data"
    ],
    "is_wallet": false
}]}""")
        val infs = json.read<List<String>>("$.accounts[*].interfaces[*]")
        println(infs)
        Assertions.assertEquals(infs[0], "nft_sale_getgems_v3")
    }

    @Test
    fun testParseGamee() {
        val json = JsonPath.parse("""{"events": [
            {
                "event_id": "23c83cf46bcfde61d7c5590399ce4ffad341e553cc6ed855d00cfad7318696a9",
                "account": {
                    "address": "0:d7a54160ee7205e19363ab38bd5c0acb75b19e82e6323634d75ebc65ad06a446",
                    "is_scam": false,
                    "is_wallet": true
                },
                "timestamp": **********,
                "actions": [
                    {
                        "type": "JettonTransfer",
                        "status": "ok",
                        "JettonTransfer": {
                            "sender": {
                                "address": "0:d7a54160ee7205e19363ab38bd5c0acb75b19e82e6323634d75ebc65ad06a446",
                                "is_scam": false,
                                "is_wallet": true
                            },
                            "recipient": {
                                "address": "0:779dcc815138d9500e449c5291e7f12738c23d575b5310000f6a253bd607384e",
                                "name": "STON.fi Dex",
                                "is_scam": false,
                                "is_wallet": false
                            },
                            "senders_wallet": "0:1150b518b2626ad51899f98887f8824b70065456455f7fe2813f012699a4061f",
                            "recipients_wallet": "0:0000000000000000000000000000000000000000000000000000000000000000",
                            "amount": "867210",
                            "comment": "Call: StonfiProvideLiquidity",
                            "jetton": {
                                "address": "0:8cdc1d7640ad5ee326527fc1ad0514f468b30dc84b0173f0e155f451b4e11f7c",
                                "name": "Proxy TON",
                                "symbol": "pTON",
                                "decimals": 9,
                                "image": "https://cache.tonapi.io/imgproxy/X7T-fLahBBVIxXacXAqrsCHIgFgTQE3Jt2HAdnc5_Mc/rs:fill:200:200:1/g:no/aHR0cHM6Ly9zdGF0aWMuc3Rvbi5maS9sb2dvL3Rvbl9zeW1ib2wucG5n.webp",
                                "verification": "whitelist"
                            }
                        },
                        "simple_preview": {
                            "name": "Jetton Transfer",
                            "description": "Transferring 0.******** Proxy TON",
                            "value": "0.******** Proxy TON",
                            "value_image": "https://cache.tonapi.io/imgproxy/X7T-fLahBBVIxXacXAqrsCHIgFgTQE3Jt2HAdnc5_Mc/rs:fill:200:200:1/g:no/aHR0cHM6Ly9zdGF0aWMuc3Rvbi5maS9sb2dvL3Rvbl9zeW1ib2wucG5n.webp",
                            "accounts": [
                                {
                                    "address": "0:779dcc815138d9500e449c5291e7f12738c23d575b5310000f6a253bd607384e",
                                    "name": "STON.fi Dex",
                                    "is_scam": false,
                                    "is_wallet": false
                                },
                                {
                                    "address": "0:8cdc1d7640ad5ee326527fc1ad0514f468b30dc84b0173f0e155f451b4e11f7c",
                                    "name": "pTON master",
                                    "is_scam": false,
                                    "is_wallet": false
                                }
                            ]
                        },
                        "base_transactions": [
                            "74e97d743c4f2d9a24912e63a915e180c74fe2b2d242d90750e32ca8af88ea26",
                            "768bfa5f727003aa9ea2fb7c21db9601ba4efb73d866a35a2aaa7c70b6f7a5c0"
                        ]
                    },
                    {
                        "type": "JettonTransfer",
                        "status": "ok",
                        "JettonTransfer": {
                            "sender": {
                                "address": "0:d7a54160ee7205e19363ab38bd5c0acb75b19e82e6323634d75ebc65ad06a446",
                                "is_scam": false,
                                "is_wallet": true
                            },
                            "recipient": {
                                "address": "0:779dcc815138d9500e449c5291e7f12738c23d575b5310000f6a253bd607384e",
                                "name": "STON.fi Dex",
                                "is_scam": false,
                                "is_wallet": false
                            },
                            "senders_wallet": "0:b45c20949db2ed2d83556275406775d4d809339f14bb33142e13883ce44c337f",
                            "recipients_wallet": "0:ff80ba5aa887940fa41f2a1bd82bbde2dafe86c8e4e5d9eb64dfd758c03cf864",
                            "amount": "9*********",
                            "comment": "Call: StonfiProvideLiquidity",
                            "jetton": {
                                "address": "0:84ab3db1dfe51bfc43b8639efdf0d368a8ac35b4ffed27a6fdcbe5f40b8bafb3",
                                "name": "WATCoin",
                                "symbol": "WAT",
                                "decimals": 9,
                                "image": "https://cache.tonapi.io/imgproxy/tVIkgQB8FTDxR9454VeGcfUP7J1oGerlbfLkXBoPD1Q/rs:fill:200:200:1/g:no/aHR0cHM6Ly9nYW1lcy5jZG4uZ2FtZWUuaW8vd2F0L1dBVGNvaW4ucG5n.webp",
                                "verification": "whitelist"
                            }
                        },
                        "simple_preview": {
                            "name": "Jetton Transfer",
                            "description": "Transferring 9.********* WATCoin",
                            "value": "9.********* WATCoin",
                            "value_image": "https://cache.tonapi.io/imgproxy/tVIkgQB8FTDxR9454VeGcfUP7J1oGerlbfLkXBoPD1Q/rs:fill:200:200:1/g:no/aHR0cHM6Ly9nYW1lcy5jZG4uZ2FtZWUuaW8vd2F0L1dBVGNvaW4ucG5n.webp",
                            "accounts": [
                                {
                                    "address": "0:779dcc815138d9500e449c5291e7f12738c23d575b5310000f6a253bd607384e",
                                    "name": "STON.fi Dex",
                                    "is_scam": false,
                                    "is_wallet": false
                                },
                                {
                                    "address": "0:84ab3db1dfe51bfc43b8639efdf0d368a8ac35b4ffed27a6fdcbe5f40b8bafb3",
                                    "name": "WAT master",
                                    "is_scam": false,
                                    "is_wallet": false
                                }
                            ]
                        },
                        "base_transactions": [
                            "229b29f936dce63f2ee799f31b36d1ed705c3db4109970be857d4891206b21f2",
                            "2d3eb4865c243557591141e3d2302983ae015eb2a8f6945e3e2ff0fb493daac0",
                            "38faf4932a83a5a7c31ab88048402be10be56b4aa12e5aca574c15b2a37863ed",
                            "6469d0678244b0ccd007ca08fd54b1e4bcf47133169653e228f4ac438f762e7f"
                        ]
                    },
                    {
                        "type": "TonTransfer",
                        "status": "ok",
                        "TonTransfer": {
                            "sender": {
                                "address": "0:ebe1057d45f50a2b84685e66068c388679a72d926ff7fae202b4a3c950d42a88",
                                "is_scam": false,
                                "is_wallet": false
                            },
                            "recipient": {
                                "address": "0:d7a54160ee7205e19363ab38bd5c0acb75b19e82e6323634d75ebc65ad06a446",
                                "is_scam": false,
                                "is_wallet": true
                            },
                            "amount": *********
                        },
                        "simple_preview": {
                            "name": "Ton Transfer",
                            "description": "Transferring 0.********* TON",
                            "value": "0.********* TON",
                            "accounts": [
                                {
                                    "address": "0:ebe1057d45f50a2b84685e66068c388679a72d926ff7fae202b4a3c950d42a88",
                                    "is_scam": false,
                                    "is_wallet": false
                                }
                            ]
                        },
                        "base_transactions": [
                            "a82743205b543bb705d245eb99c19a188179020d2cfbade6734adc9c4aff9069"
                        ]
                    }
                ],
                "is_scam": false,
                "lt": **************,
                "in_progress": false,
                "extra": -*********
            }]}
        """.trimIndent())

        //val jettons = json.read<List<String>>("$.events[*].actions[?(@.JettonTransfer.comment == 'Call: StonfiProvideLiquidity')].JettonTransfer.jetton.address")
        val evts = json.read<List<Map<String, Any>>>("$.events[*]")
        val jettons = evts.map { event ->
            JsonPath.read<List<String>>(
                event,
                "$.actions[?(@.JettonTransfer.comment == 'Call: StonfiProvideLiquidity')].JettonTransfer.jetton.address"
            )
        }.filter { it.isNotEmpty() }
        println(jettons)
        val hasLP = jettons.map { lp ->
            lp.contains("0:8cdc1d7640ad5ee326527fc1ad0514f468b30dc84b0173f0e155f451b4e11f7c") && lp.contains("0:84ab3db1dfe51bfc43b8639efdf0d368a8ac35b4ffed27a6fdcbe5f40b8bafb3")
        }
        Assertions.assertTrue(hasLP.contains(true))
    }
}