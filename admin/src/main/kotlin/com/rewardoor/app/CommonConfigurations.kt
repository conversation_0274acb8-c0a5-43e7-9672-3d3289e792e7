package com.rewardoor.app

import com.amazonaws.auth.AWSCredentials
import com.amazonaws.auth.AWSStaticCredentialsProvider
import com.amazonaws.auth.BasicAWSCredentials
import com.amazonaws.client.builder.AwsClientBuilder.EndpointConfiguration
import com.amazonaws.services.s3.AmazonS3
import com.amazonaws.services.s3.AmazonS3ClientBuilder
import com.github.scribejava.apis.DiscordApi
import com.github.scribejava.core.builder.ServiceBuilder
import com.github.scribejava.core.oauth.OAuth10aService
import com.github.scribejava.core.oauth.OAuth20Service
import com.twitter.clientlib.auth.TwitterOAuth20Service
import discord4j.common.ReactorResources
import discord4j.core.DiscordClient
import discord4j.core.GatewayDiscordClient
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.context.annotation.Primary
import org.springframework.scheduling.annotation.EnableAsync
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor
import org.springframework.web.client.RestTemplate
import reactor.netty.http.client.HttpClient
import java.util.concurrent.Executor
import java.util.concurrent.ThreadPoolExecutor.CallerRunsPolicy


@Configuration
@EnableAsync
class CommonConfigurations {
    @Bean
    @Primary
    fun s3client(
        @Value("\${r2.endpoint}") r2ServiceEndpoint: String,
        @Value("\${r2.accountId}") accountIdValue: String,
        @Value("\${r2.accessKey}") accessKeyValue: String,
        @Value("\${r2.secretKey}") secretKeyValue: String
    ): AmazonS3? {
        val accountR2Url = String.format(r2ServiceEndpoint, accountIdValue)
        val credentials: AWSCredentials = BasicAWSCredentials(
            accessKeyValue,
            secretKeyValue
        )
        val endpointConfiguration = EndpointConfiguration(accountR2Url, null)
        return AmazonS3ClientBuilder
            .standard()
            .withEndpointConfiguration(endpointConfiguration)
            .withPathStyleAccessEnabled(true)
            .withCredentials(AWSStaticCredentialsProvider(credentials))
            .build()
    }

    @Bean("s3clientBasic")
    fun s3clientBasic(
        @Value("\${r2.endpoint}") r2ServiceEndpoint: String,
        @Value("\${r2.accountId}") accountIdValue: String,
        @Value("\${r2.accessKey}") accessKeyValue: String,
        @Value("\${r2.secretKey}") secretKeyValue: String
    ): AmazonS3? {
        val accountR2Url = r2ServiceEndpoint.removeSuffix("/rd-nft")
        val credentials: AWSCredentials = BasicAWSCredentials(
            accessKeyValue,
            secretKeyValue
        )
        val endpointConfiguration = EndpointConfiguration(accountR2Url, null)
        return AmazonS3ClientBuilder
            .standard()
            .withEndpointConfiguration(endpointConfiguration)
            .withPathStyleAccessEnabled(true)
            .withCredentials(AWSStaticCredentialsProvider(credentials))
            .build()
    }

    @Bean
    fun restTemplate(): RestTemplate {
        return RestTemplate()
    }

    @Bean
    fun oAuth10Service(@Value("\${twitter.api.consumer_key}") consumerKey: String,
                       @Value("\${twitter.api.consumer_secret}") consumerSecret: String): OAuth10aService {
        return ServiceBuilder(consumerKey)
            .apiSecret(consumerSecret)
            .callback("https://rewardoor-staging.tbook.com/twittter/callback")
            .build(com.github.scribejava.apis.TwitterApi.instance())
    }

    @Bean
    fun twitterService(@Value("\${twitter.api.id}") clientId: String,
                       @Value("\${twitter.api.secret}") clientSecret: String): TwitterOAuth20Service {
        return TwitterOAuth20Service(clientId, clientSecret,
            "https://rewardoor-staging.tbook.com/twitter/callback", "tweet.read users.read follows.read space.read like.read list.read")
    }

    @Bean("dc20Service")
    fun dc20Service(@Value("\${dc.app_id}") appId: String,
                    @Value("\${dc.app_secret}") appSecret: String,
                    @Value("\${dc.callback}") callback: String): OAuth20Service {
        return ServiceBuilder(appId)
            .apiSecret(appSecret)
            .defaultScope("identify guilds guilds.members.read")
            .userAgent("ScribeJava")
            .callback(callback)
            .responseType("code")
            .build(DiscordApi.instance())
    }

    @Bean("discordClient")
    fun dcClient(@Value("\${dc.bot_token}") botToken: String, resource: ReactorResources): DiscordClient {
        return DiscordClient.builder(botToken).setReactorResources(resource).build()
    }

    @Bean("nettySystemProxyClient")
    fun systemProxyClient(): HttpClient {
        return HttpClient.create().proxyWithSystemProperties().responseTimeout(java.time.Duration.ofSeconds(5))
    }

    @Bean
    fun resources(): ReactorResources {
        return ReactorResources.builder().httpClient(systemProxyClient()).build()
    }

    @Bean("callbackServiceExecutor")
    fun asyncServiceExecutor(): Executor {
        val executor = ThreadPoolTaskExecutor()
        executor.corePoolSize = Runtime.getRuntime().availableProcessors()
        executor.maxPoolSize = Runtime.getRuntime().availableProcessors()
        executor.queueCapacity = 100
        executor.setThreadNamePrefix("callback-async-service-")

        // rejection-policy：当pool已经达到max size的时候，如何处理新任务
        // CALLER_RUNS：不在新线程中执行任务，而是有调用者所在的线程来执行
        executor.setRejectedExecutionHandler(CallerRunsPolicy())
        executor.initialize()
        return executor
    }

    @Bean
    fun addVoiceStateListener(dcClient: DiscordClient): GatewayDiscordClient {
        return dcClient.gateway().login().block()!!
    }
}
