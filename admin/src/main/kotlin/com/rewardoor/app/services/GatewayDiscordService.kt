package com.rewardoor.app.services

import discord4j.core.GatewayDiscordClient
import discord4j.core.event.domain.VoiceStateUpdateEvent
import org.springframework.stereotype.Component
import reactor.core.publisher.Mono
import reactor.core.scheduler.Schedulers
import org.springframework.core.env.Environment

@Component
class GatewayDiscordService(
    private val discordGateway: GatewayDiscordClient,
    private val discordService: DiscordService,
    private val env: Environment
) {
    private val log = mu.KotlinLogging.logger {}

    private val voiceStateScheduler = Schedulers.newBoundedElastic(20, 10000, "discord-voice-state")

    init {
        if (env.matchesProfiles("prod")) {
            discordGateway.on(VoiceStateUpdateEvent::class.java,
                this::handleEvent).subscribe()
        }
    }

    fun handleEvent(event: VoiceStateUpdateEvent): Mono<Void> {
        return Mono.just(event)
            .publishOn(voiceStateScheduler)
            .flatMap { voiceEvent ->
                // use asynchronous call
                Mono.fromCallable {
                    try {
                        discordService.handleVoiceStateUpdate(voiceEvent)
                    } catch (e: Exception) {
                        log.error("Error handling voice state update", e)
                        throw e
                    }
                }
                .subscribeOn(Schedulers.boundedElastic())
                .onErrorResume { error ->
                    log.error("Failed to handle voice state update for user ${event.current.userId.asString()}", error)
                    Mono.empty() // continue on error
                }
            }
            .then()
    }
}