package com.rewardoor.app

import com.zaxxer.hikari.HikariDataSource
import org.jetbrains.exposed.spring.SpringTransactionManager
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.boot.autoconfigure.jdbc.DataSourceProperties
import org.springframework.boot.context.properties.ConfigurationProperties
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.core.env.Environment
import org.springframework.transaction.annotation.EnableTransactionManagement
import javax.sql.DataSource

@Configuration
@EnableTransactionManagement
class DataSourceConfiguration(private val env: Environment) {
    @Bean(name = ["masterProperties"])
    @ConfigurationProperties("datasource.master")
    fun dataSourceProperties(): DataSourceProperties {
        return DataSourceProperties()
    }

    @Bean
    @ConfigurationProperties("datasource.master.hikari")
    fun dataSource(@Qualifier("masterProperties") properties: DataSourceProperties): HikariDataSource {
        return properties.initializeDataSourceBuilder().type(HikariDataSource::class.java).build()
    }

    @Bean
    fun springTransactionManager(dataSource: DataSource): SpringTransactionManager {
        val showSql = !env.matchesProfiles("prod")
        return SpringTransactionManager(dataSource, showSql = showSql)
    }
}
