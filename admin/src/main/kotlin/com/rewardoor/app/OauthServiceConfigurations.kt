package com.rewardoor.app

import com.github.scribejava.apis.GoogleApi20
import com.github.scribejava.core.builder.ServiceBuilder
import com.github.scribejava.core.oauth.OAuth20Service
import com.google.api.client.googleapis.auth.oauth2.GoogleIdTokenVerifier
import com.google.api.client.http.javanet.NetHttpTransport
import com.google.api.client.json.gson.GsonFactory
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration


@Configuration
class OauthServiceConfigurations {
    @Bean("googleOauthService")
    fun googleOauthService(@Value("\${zklogin.google.client_id}") appId: String,
                           @Value("\${zklogin.google.client_secret}") appSecret: String,
                           @Value("\${zklogin.google.redirect_uri}") callback: String): OAuth20Service {
        return ServiceBuilder(appId)
            .apiSecret(appSecret)
            .callback(appSecret)
            .build(GoogleApi20.instance())
    }

    @Bean("googleVerifier")
    fun googleVerifier(@Value("\${zklogin.google.client_id}") clientId: String,
                       @Value("\${zklogin.google.client_secret}") appSecret: String,
                       @Value("\${zklogin.google.redirect_uri}") callback: String): GoogleIdTokenVerifier {
        val verifier = GoogleIdTokenVerifier
                .Builder(NetHttpTransport(), GsonFactory()) // Specify the CLIENT_ID of the app that accesses the backend:
                .setAudience(listOf(clientId)) // Or, if multiple clients access the backend:
                .build()
        return verifier
    }
}