package com.rewardoor.app.controller

import com.rewardoor.app.dao.CallbackHistoryRepository
import com.rewardoor.app.services.*
import com.rewardoor.model.Credential
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestHeader
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController

@RestController
class BKController(private val callbackService: CallbackService,
                   private val userService: UserService,
                   private val campaignGroupService: CredentialGroupService,
                   private val callbackHistoryRepository: CallbackHistoryRepository,
                   private val discordService: DiscordService,
                   private val telegramService: TelegramService,
                   private val userTwitterService: UserTwitterService,
                   private val credentialService: CredentialService
    ) {
    private val log = mu.KotlinLogging.logger {  }

    @PostMapping("/bk/callback")
    fun callback(@RequestHeader("x-auth") auth: String): String {
        if (!auth.equals("evt_3OrcaaFcx8G3bhf31Q0yP9K7")) {
            log.error("Unauthorized request")
            return "Unauthorized request"
        }
        val history = callbackHistoryRepository.getFailed(5425097)
        for (h in history) {
            if (h.times > 100) continue
            try {
                val user = userService.getUserById(h.userId)!!
                if (user.evm.binded) {
                    val uc = campaignGroupService.getUserCredential(h.userId, h.credentialId)!!
                    if (uc.status != 1) {
                        log.info { "status error:$uc" }
                    } else {
                        when {
                            Credential.isDiscordTask(uc.labelType) -> {
                                callbackService.callback(user, h.projectId, h.credentialId, uc.participantDate!!.toEpochMilli(), userDiscord = discordService.getUserDcInfo(user.userId))
                            }
                            Credential.isTwitterTask(uc.labelType) -> {
                                callbackService.callback(user, h.projectId, h.credentialId, uc.participantDate!!.toEpochMilli(), userTwitterInfo = userTwitterService.getUserInfo(user.userId))
                            }
                            Credential.isTelegramTask(uc.labelType) -> {
                                callbackService.callback(user, h.projectId, h.credentialId, uc.participantDate!!.toEpochMilli(), userTelegramInfo = telegramService.getUserInfo(user.userId))
                            }
                            else -> {
                                callbackService.callback(user, h.projectId, h.credentialId, uc.participantDate!!.toEpochMilli())
                            }
                        }
                        log.info { "OK:$h" }
                    }
                } else {
                    log.info { "user not evm binded:$user" }
                }
            } catch (e: Exception) {
                log.error("Callback failed", e)
                log.info { "failed"}
            }
        }
        return "OK"
    }

    @PostMapping("/cb/compensate")
    fun compensate(@RequestHeader("x-auth") auth: String,
                   @RequestParam("address") address: String,
                   @RequestParam("credentialId") credentialId: Long): String {
        if (!auth.equals("evt_3OrcaaFcx8G3bhf31Q0yP9K7")) {
            log.error("Unauthorized request")
            return "Unauthorized request"
        }
        val user = userService.getUserByAddress(address)?: return "User not found by address $address"
        val credential = credentialService.getCredentialById(credentialId)?: return "Credential not found by credentialId $credentialId"
        val userId = user.userId
        val uc = campaignGroupService.getUserCredential(userId, credentialId)?: return "Credential not complete by userId $userId and credentialId $credentialId"
        if (uc.status != 1) {
            return "Credential not complete by userId $userId and credentialId $credentialId"
        }
        callbackService.callback(user, credential.projectId, credentialId, uc.participantDate!!.toEpochMilli())
        return "OK"
    }
}