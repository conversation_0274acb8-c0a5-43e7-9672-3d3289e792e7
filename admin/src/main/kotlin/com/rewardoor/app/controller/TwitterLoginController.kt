package com.rewardoor.app.controller

import com.fasterxml.jackson.databind.ObjectMapper
import com.github.scribejava.core.oauth.AccessTokenRequestParams
import com.rewardoor.app.auth.Jwt
import com.rewardoor.app.dto.SocialAccountBound
import com.rewardoor.app.services.UserService
import com.rewardoor.app.services.UserTwitterService
import com.rewardoor.app.utils.isDuplicate
import com.rewardoor.model.PassportAccounts
import com.rewardoor.model.UserTwitterInfo
import com.twitter.clientlib.TwitterCredentialsOAuth2
import com.twitter.clientlib.api.TwitterApi
import com.twitter.clientlib.auth.TwitterOAuth20Service
import jakarta.servlet.http.HttpServletRequest
import org.apache.commons.lang3.RandomStringUtils
import org.apache.http.client.utils.URIBuilder
import org.springframework.beans.factory.annotation.Value
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.web.bind.annotation.*
import org.springframework.web.server.ResponseStatusException
import java.net.URI

@RestController()
@RequestMapping("/twitter/login")
class TwitterLoginController(
    @Value("\${twitter.api.id}") val clientId: String,
    @Value("\${twitter.api.secret}") val clientSecret: String,
    @Value("\${twitter.api.bearer}") val bearerToken: String,
    val userTwitterService: UserTwitterService,
    val userService: UserService
) {
    private val mapper = ObjectMapper()
    private val logger = mu.KotlinLogging.logger {}
    private val defaultScope = "tweet.read users.read follows.read space.read like.read list.read"

    @GetMapping("auth")
    fun twitterAuth(request: HttpServletRequest): Map<String, String> {
        val redirectUri = getCallbackUrl(request.getHeader("referer"))
        val challenge = RandomStringUtils.random(10, true, true)
        val state = RandomStringUtils.random(10, true, true)
        val uri = URIBuilder().setScheme("https").setHost("twitter.com").setPath("/i/oauth2/authorize")
            .addParameter("response_type", "code")
            .addParameter("client_id", clientId)
            .addParameter("redirect_uri", redirectUri)
            .addParameter("scope", defaultScope)
            .addParameter("state", state)
            .addParameter("code_challenge", challenge)
            .addParameter("code_challenge_method", "plain")
            .build()
        userTwitterService.addStateChallengeCache(state, challenge)
        return mapOf("url" to uri.toString())
    }

    @PostMapping("callback")
    fun callback(
        @RequestParam("state") state: String,
        @RequestParam("code") code: String,
        request: HttpServletRequest
    ): ResponseEntity<Any> {
        logger.info { "Twitter callback, state: $state, code: $code" }
        val challenge = userTwitterService.getChallengeByState(state)
        if (challenge == null) {
            logger.info { "Invalid state: $state" }
            throw ResponseStatusException(HttpStatus.BAD_REQUEST, "Invalid state")
        }
        val (user, token) = try {
            val redirectUri = getCallbackUrl(request.getHeader("referer"))
            val param = AccessTokenRequestParams.create(code).pkceCodeVerifier(challenge)
                .scope(defaultScope)
            val twitter20Service = TwitterOAuth20Service(clientId, clientSecret, redirectUri, defaultScope)
            val token = twitter20Service.getAccessToken(param)

            val credential = TwitterCredentialsOAuth2(clientId, clientSecret, token.accessToken, token.refreshToken)
            val apiInstance = TwitterApi(credential)
            val user = apiInstance.users().findMyUser().userFields(setOf("profile_image_url")).execute()
            user to token
        } catch (e: Exception) {
            logger.error(e) { "fetch Twitter account info fail" }
            return ResponseEntity.ok(mapOf("code" to 401, "message" to "Get Twitter account info fail"))
        }
        val utInfo = UserTwitterInfo().apply {
            accessToken = token.accessToken
            refreshToken = token.refreshToken ?: ""
            twitterScreenName = user.data?.name.toString()
            twitterName = user.data?.username.toString()
            twitterId = user.data?.id.toString()
            twitterProfileImage = user.data?.profileImageUrl.toString()
            connected = true
        }
        return try {
            val (newInfo, newUser) = userTwitterService.updateOrCreateUserInfo(utInfo)
            val cookie = Jwt.buildCookie(newInfo.userId)
            val setCk = cookie.toString().split(";")[0] + ";"
            val user = userService.getUserById(newInfo.userId)!!
            val twitterInfo = userTwitterService.getUserInfo(newInfo.userId)!!
            val passport = PassportAccounts(
                userId = newInfo.userId,
                evmAddress = user.evm.evmWallet ?: "",
                tonAddress = user.ton.tonWallet ?: "",
                twitterName = twitterInfo.twitterName,
                dcName = user.dcName,
                tgName = user.tgName
            )
            ResponseEntity.ok().header("Set-Cookie", cookie.toString())
                .body(
                    mapOf(
                        "code" to 200, "socialName" to utInfo.twitterName,
                        "info" to twitterInfo,
                        "passport" to passport,
                        "newUser" to newUser
                    )
                )
        } catch (e: Exception) {
            if (e.isDuplicate()) {
                logger.error(e) { "Twitter account already connected" }
                val principal = SecurityContextHolder.getContext().authentication.principal.toString()
                val addressUser = userService.getUserByPrincipal(principal)!!
                val passportA = PassportAccounts(
                    userId = addressUser.userId,
                    evmAddress = addressUser.evm.evmWallet ?: "",
                    tonAddress = addressUser.ton.tonWallet ?: "",
                    twitterName = addressUser.twitterName,
                    dcName = addressUser.dcName,
                    tgName = addressUser.tgName
                )
                val curUser = userTwitterService.getUserInfoByTwitterId(utInfo.twitterId)!!
                val twitterUser = userService.getUserById(curUser.userId)!!
                val address = twitterUser.wallet
                val passportB = PassportAccounts(
                    userId = twitterUser.userId,
                    evmAddress = twitterUser.evm.evmWallet ?: "",
                    tonAddress = twitterUser.ton.tonWallet ?: "",
                    twitterName = utInfo.twitterName,
                    dcName = twitterUser.dcName,
                    tgName = twitterUser.tgName
                )
                ResponseEntity.ok(
                    SocialAccountBound(
                        "Twitter account already connected",
                        utInfo.twitterName, address, passportA = passportA, passportB = passportB
                    )
                )
            } else {
                logger.error(e) { "Database error" }
                ResponseEntity.ok(mapOf("code" to 500, "message" to "Internal error"))
            }
        }
    }

    private fun getCallbackUrl(referer: String): String {
        val requestURI = URI(referer)
        return URIBuilder().setScheme("https")
            .setHost(requestURI.host)
            .setPath("twitter/login/callback").build().toString()
    }
}