package com.rewardoor.app.controller

import com.jayway.jsonpath.JsonPath
import com.rewardoor.app.auth.AddressAuthentication
import com.rewardoor.app.dao.LateNightAirdropRepository
import com.rewardoor.app.dao.UserTonRepository
import com.rewardoor.app.services.TelegramService
import com.rewardoor.app.services.WiseInviteService
import com.rewardoor.app.services.WiseScoreService
import com.rewardoor.enums.SocialType
import com.rewardoor.enums.WiseScoreLevel
import com.rewardoor.model.LateNightAirdropUser
import com.rewardoor.model.UserDcTgShareLink
import com.rewardoor.model.UserWiseScore
import org.json.JSONObject
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import org.springframework.web.reactive.function.client.ExchangeStrategies
import org.springframework.web.reactive.function.client.WebClient
import kotlin.math.round

@RestController
@RequestMapping("wiseScore")
class WiseScoreController(
    val wiseScoreService: WiseScoreService,
    val wiseInviteService: WiseInviteService,
    val lateNightAirdropRepository: LateNightAirdropRepository,
    val tonRepository: UserTonRepository
) {

    private final val strategies = ExchangeStrategies.builder()
        .codecs { codecs -> codecs.defaultCodecs().maxInMemorySize(20 * 1024 * 1024) }
        .build()
    private val webClient = WebClient.builder().exchangeStrategies(strategies).build()
    private val tonConsoleBearer = "AFXEZ3ACHWCQPGIAAAACDBX3ACNADH4DWOIU3DOWOQMPYF7M5QIP5ZZFE5WI227KRIKIQ2Y"


    @GetMapping("/{userId}")
    fun getWiseScore(@PathVariable("userId") userId: Long): Any {
        val current = wiseScoreService.getScoreById(userId)
            ?: return mapOf(
                "code" to 400,
                "message" to "user has not yet generated WiseScore."
            )

        val userWiseScore = wiseScoreService.addScoreResult(userId)
        val shareLinks = wiseScoreService.getUserShareLinks(userId)
        for (link in shareLinks) {
            if (link.socialType == SocialType.DISCORD.code) {
                wiseScoreService.appendDiscordInfo(userId, link)
            } else if (link.socialType == SocialType.TELEGRAM.code) {
                wiseScoreService.appendTelegramInfo(link)
            }
        }
        userWiseScore.userDcTgShareLink = shareLinks
        return mapOf(
            "code" to 200,
            "message" to "user has already generated  WiseScore",
            "userWiseScore" to userWiseScore
        )
    }

    @GetMapping("initSBTLink")
    fun initSBTLink(): Map<Long, String> {
        return wiseScoreService.updateSBTLink()
    }

    @GetMapping("/topWealth")
    fun getTopWealth(): List<UserWiseScore> {
        return wiseScoreService.getTop500WealthWiseScore()
    }

    @GetMapping("/tonWiseScore")
    fun addTonAndEvmWiseScore() {
        return wiseScoreService.addAllTmpToScore()
    }

    private fun replaceMiddleWithStar(input: String): String {
        return if (input.length <= 2) {
            input
        } else {
            val firstCharacter = input.first()
            val lastCharacter = input.last()
            val middle = "*".repeat(input.length - 2)
            firstCharacter + middle + lastCharacter
        }
    }

    @PostMapping("/addLink")
    fun submitLink(@RequestBody dcTgLinks: UserDcTgShareLink): SimpleCodeResponseEntity<List<UserDcTgShareLink>> {
        val userId = SecurityContextHolder.getContext().authentication.principal.toString().toLong()
        val hash = TelegramService.extractPrivateLink(dcTgLinks.shareLink)
        if (hash != null) {
            return SimpleCodeResponseEntity.failed(
                410, "The private channel and group can't be attested so far.\n" +
                        "Maybe you could set it to public and try again."
            )
        }
        dcTgLinks.userId = userId
        val (result, message) = wiseScoreService.addUserShareLink(dcTgLinks)
        return if (result != null) {
            SimpleCodeResponseEntity.success("", result)
        } else {
            SimpleCodeResponseEntity.failed(410, message)
        }
    }

    @GetMapping("/shareLinks/{userId}")
    fun getLinks(@PathVariable("userId") userId: Long): List<UserDcTgShareLink> {
        return wiseScoreService.getUserShareLinks(userId)
    }

    @GetMapping("/airdrop/info")
    fun getAirdropInfo() {
        val userAddress = lateNightAirdropRepository.getUsersAddress()
        for (address in userAddress) {
            val tonUser = tonRepository.findTonWallet(address.trim())
            val userId = tonUser?.userId ?: 0
            println(tonUser?.tonWallet + "  " + userId)
            if (userId != 0L) {
                val wiseScore = wiseScoreService.getScoreById(userId)
                val lateNightAirdropUser = LateNightAirdropUser(
                    userId = userId,
                    tonAddress = address,
                    wiseScore = wiseScore?.totalScore ?: 0,
                    walletAge = getWalletAge(address),
                    tonBalance = getTonBalance(address),
                    transactionNum = getTransactionNum(address)
                )
                println("update:" + lateNightAirdropRepository.updateAirdropUser(lateNightAirdropUser))
            }
        }
    }

    private fun getWalletAge(address: String): Int {
        val response = webClient
            .get()
            .uri(
                "https://tonapi.io/v2/blockchain/accounts/${address}/transactions?limit=1&sort_order=asc"
            )
            .header("Authorization", "Bearer $tonConsoleBearer")
            .retrieve()
            .bodyToMono(String::class.java)
            .block();
        val json = JSONObject(response)
        try {
            val transactions = json.getJSONArray("transactions")
            if (transactions.length() == 0) {
                return 0
            }
            val theFirstTransaction = transactions[0] as JSONObject
            val theFirstTransactionTime = theFirstTransaction.getLong("utime")
            // 2024.11.21 00:00:00
            val currentTimestamp = **********
            val age = currentTimestamp - theFirstTransactionTime
            return round(age.toDouble() / (24 * 3600)).toInt()
        } catch (err: Exception) {
            return 0
        }
    }

    private fun getTonBalance(address: String): Long {
        val response = webClient
            .get()
            .uri(
                "https://tonapi.io/v2/accounts/${address}"
            )
            .header("Authorization", "Bearer $tonConsoleBearer")
            .retrieve()
            .bodyToMono(String::class.java)
            .block();
        val json = JSONObject(response)
        val balance = json.optLong("balance")
        return balance
    }

    private fun getTransactionNum(address: String):Int {
        val response = webClient
            .get()
            .uri(
                "https://tonapi.io/v2/blockchain/accounts/${address}/transactions?limit=100&sort_order=desc"
            )
            .header("Authorization", "Bearer $tonConsoleBearer")
            .retrieve()
            .bodyToMono(String::class.java)
            .block();
        val json = JSONObject(response)
        val transactions = json.getJSONArray("transactions")
        // 0: 0、1: 1-99、2: > 100
        if (transactions.length() == 0) {
            return 0
        }
        if (transactions.length() in 1..99) {
            return 1
        } else {
            return 2
        }
    }
}