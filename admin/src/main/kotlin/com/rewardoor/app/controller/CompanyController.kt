package com.rewardoor.app.controller

import com.rewardoor.app.dto.*
import com.rewardoor.app.services.*
import com.rewardoor.model.*
import org.springframework.beans.factory.annotation.Value
import org.springframework.data.redis.core.StringRedisTemplate
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.web.bind.annotation.*

@RestController
@RequestMapping("company")
class CompanyController(
    val companyService: CompanyService,
    val projectService: ProjectService,
    val campaignService: CampaignService,
    val userService: UserService,
) {

}