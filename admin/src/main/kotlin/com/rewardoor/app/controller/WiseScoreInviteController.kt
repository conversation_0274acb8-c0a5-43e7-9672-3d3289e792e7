package com.rewardoor.app.controller

import com.rewardoor.app.dto.UserInfoDto
import com.rewardoor.app.services.TelegramService
import com.rewardoor.app.services.UserService
import com.rewardoor.app.services.WiseInviteService
import com.rewardoor.app.services.WiseScoreService
import com.rewardoor.enums.InviteCodeCheckedType
import com.rewardoor.enums.WiseScoreLevel
import com.rewardoor.model.User
import com.rewardoor.model.WiseInviteCode
import org.jetbrains.exposed.sql.javatime.timestampWithTimeZoneParam
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import java.time.Instant

@RestController
@RequestMapping("/wise-score-invite")
class WiseScoreInviteController(
    private val wiseInviteService: WiseInviteService,
    private val wiseScoreService: WiseScoreService,
    private val userService: UserService,
    private val telegramService: TelegramService
) {
}