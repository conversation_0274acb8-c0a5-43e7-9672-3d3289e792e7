package com.rewardoor.app.controller

import com.rewardoor.app.services.AiAgentService
import com.rewardoor.model.AiAgent
import org.springframework.web.bind.annotation.*

@RestController
@RequestMapping("/ai-agent")
class AiAgentController(val aiAgentService: AiAgentService) {

    @PostMapping("/create")
    fun createAgent(@RequestBody aiAgent: AiAgent): Any? {
        val result = aiAgentService.createAiAgent(aiAgent)
        return result
    }

    @GetMapping("/{agentId}")
    fun getAgent(@PathVariable("agentId") agentId: Long): AiAgent? {
        return aiAgentService.getAiAgent(agentId)
    }

    @PostMapping("/update")
    fun updateAgent(@RequestBody aiAgent: AiAgent): AiAgent? {
        return aiAgentService.updateAiAgent(aiAgent)
    }

    @GetMapping("/agentList")
    fun getAgentList(): List<AiAgent>? {
        return aiAgentService.getAiAgentList()
    }

}