package com.rewardoor.app.controller

import com.github.scribejava.apis.DiscordApi
import com.github.scribejava.core.builder.ServiceBuilder
import com.github.scribejava.core.httpclient.jdk.JDKHttpClient
import com.github.scribejava.core.httpclient.jdk.JDKHttpClientConfig
import com.github.scribejava.core.oauth.OAuth20Service
import com.rewardoor.app.auth.AddressAuthentication
import com.rewardoor.app.dto.GuildRoleDto
import com.rewardoor.app.dto.SocialAccountBound
import com.rewardoor.app.dto.StringPayloadRequest
import com.rewardoor.app.services.DiscordService
import com.rewardoor.app.utils.DcUtils
import com.rewardoor.app.utils.isDuplicate
import com.rewardoor.model.PassportAccounts
import discord4j.common.ReactorResources
import discord4j.rest.service.UserService
import jakarta.servlet.http.HttpServletRequest
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.beans.factory.annotation.Value
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController
import reactor.netty.http.client.HttpClient
import java.net.URI

@RestController
@RequestMapping("dc")
class DcController(
    val dcService: DiscordService,
    val userService: com.rewardoor.app.services.UserService,
    @Value("\${dc.app_id}") val appId: String,
    @Value("\${dc.app_secret}") val appSecret: String,
    private val resources: ReactorResources
) {
    private val log = mu.KotlinLogging.logger {}
    private val httpClient = JDKHttpClient(JDKHttpClientConfig.defaultConfig());

    @PostMapping("callback")
    fun dcCallback(@RequestParam("code") code: String, req: HttpServletRequest): Any {
        val dc20Service = getDc20Service(req)
        val principal = SecurityContextHolder.getContext().authentication as AddressAuthentication
        try {
            val tokens = dc20Service.getAccessToken(code)
            val dcUserService = UserService(DcUtils.createDefaultRouter(tokens.accessToken, resources = resources))
            val du = dcUserService.currentUser.block()
            return try {
                dcService.addUserDcInfo(principal.userId, du!!, tokens)
                mapOf("code" to 200, "socialName" to du!!.username(), "data" to du)
            } catch (e: Exception) {
                if (e.isDuplicate()) {
                    val nowUser = userService.getUserById(principal.userId)!!
                    val passportA = PassportAccounts(
                        userId = nowUser.userId,
                        evmAddress = nowUser.evm.evmWallet ?: "",
                        tonAddress = nowUser.ton.tonWallet ?: "",
                        twitterName = nowUser.twitterName,
                        dcName = nowUser.dcName,
                        tgName = nowUser.tgName
                    )
                    log.error(e) { "DC account already connected" }
                    val curUser = dcService.getUserInfoByDcId(du!!.id().asString())!!
                    val boundUser = userService.getUserById(curUser.userId)!!
                    val passportB = PassportAccounts(
                        userId = boundUser.userId,
                        evmAddress = boundUser.evm.evmWallet ?: "",
                        tonAddress = boundUser.ton.tonWallet ?: "",
                        twitterName = boundUser.twitterName,
                        dcName = boundUser.dcName,
                        tgName = boundUser.tgName
                    )
                    val address = boundUser.wallet.ifEmpty {
                        boundUser.twitterName
                    }
                    val isAbleToMerge = isPassportsAbleToMerge(passportA, passportB)
                    if (!isAbleToMerge) {
                        SocialAccountBound(
                            "Discord account already connected",
                            du!!.username(), address, passportA, passportB
                        )
                    } else {
                        return mapOf(
                            "code" to 400,
                            "message" to "The address has been taken, you need to merge the accounts.",
                            "passportA" to passportA,
                            "passportB" to passportB
                        )
                    }
                } else {
                    log.error(e) { "Database error" }
                    mapOf("code" to 500, "message" to "Internal error")
                }
            }
        } catch (e: Exception) {
            log.error("get access token error", e)
            return mapOf("code" to 500, "msg" to "get access token error")
        }
    }

    private fun isPassportsAbleToMerge(passportA: PassportAccounts, passportB: PassportAccounts): Boolean {
        return when {
            (passportA.evmAddress != "" && passportB.evmAddress != "") || (passportA.tonAddress != "" && passportB.tonAddress != "")
                    || (passportA.suiAddress != "" && passportB.suiAddress != "")
                    || (passportA.twitterName != "" && passportB.twitterName != "") || (passportA.dcName != "" && passportB.dcName != "")
                    || (passportA.tgName != "" && passportB.tgName != "") -> false

            else -> true
        }
    }

    @PostMapping("roles")
    fun getDcServerRoles(@RequestBody payload: StringPayloadRequest): Any {
        val roles = dcService.getRolesByInviteLink(payload.payload)
        val roleDto = roles.map {
            GuildRoleDto(it.id().asString(), it.name())
        }
        return mapOf("code" to 200, "data" to roleDto)
    }

    private fun getDc20Service(req: HttpServletRequest): OAuth20Service {
        val refer = URI(req.getHeader("referer"))
        val callback = "${refer.scheme}://${refer.host}/dc_callback"
        return ServiceBuilder(appId)
            .httpClient(httpClient)
            .apiSecret(appSecret)
            .defaultScope("identify guilds guilds.members.read")
            .userAgent("ScribeJava")
            .callback(callback)
            .responseType("code")
            .build(DiscordApi.instance())
    }
}