package com.rewardoor.app.controller

import com.rewardoor.app.services.CampaignService
import com.rewardoor.app.services.UserService
import com.rewardoor.model.Asset
import com.rewardoor.model.NFT
import com.rewardoor.model.UserAsset
import com.rewardoor.model.UserCampaigns
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

@RestController
@RequestMapping("")
class AssetController(
    val campaignService: CampaignService, val userService: UserService
) {
    @GetMapping("/project/{projectId}/assets")
    fun getAsset(@PathVariable("projectId") projectId: Long): Asset? {
        val assets = campaignService.getAsset(projectId)
        return assets
    }
}