package com.rewardoor.app.controller

import com.amazonaws.services.s3.AmazonS3
import com.amazonaws.services.s3.model.ObjectMetadata
import com.amazonaws.services.s3.model.PutObjectRequest
import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.databind.ObjectMapper
import com.rewardoor.app.dao.ParticipantRepository
import com.rewardoor.app.dto.ContractDto
import com.rewardoor.app.dto.NFTClaimDto
import com.rewardoor.app.dto.NFTGiveawayDto
import com.rewardoor.app.services.*
import com.rewardoor.app.utils.Signs
import com.rewardoor.model.NFT
import com.rewardoor.model.NFTGiveaway
import com.rewardoor.model.NFTMetaData
import mu.KotlinLogging
import org.springframework.beans.factory.annotation.Value
import org.springframework.http.HttpStatus
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.web.bind.annotation.*
import org.springframework.web.client.HttpClientErrorException
import org.springframework.web.server.ResponseStatusException
import java.io.ByteArrayInputStream

@RestController
@RequestMapping("nft")
class NFTController(
    val nftService: NFTService,
    val userService: UserService,
    val idGenerator: GeneratorService,
    val campaignService: CampaignService,
    val participantRepo: ParticipantRepository,
    val credentialGroupService: CredentialGroupService,
    val s3: AmazonS3,
    val userNFTService: UserNFTService,
    val contractService: ContractService,
    @Value("\${manager.private_key}") val privateKey: String,
    private val privilegeCheck: PrivilegeCheck
) {

    private val logger = KotlinLogging.logger {}
    val mapper = ObjectMapper()

    @PostMapping("create")
    fun createNFT(@RequestBody nft: NFT): NFT {
        logger.info { "createNFT : $nft" }
        val principal = SecurityContextHolder.getContext().authentication.principal.toString()
        val nftId = idGenerator.getNewId()
        privilegeCheck.checkUserId(nft.creatorId, principal)

        if (nft.contract.isEmpty()) throw HttpClientErrorException(HttpStatus.BAD_REQUEST, "Contract address is empty")
        if (nft.picUrl.isEmpty()) {
            // TODO use uploaded image
            nft.picUrl = "https://pub-871184d7338746be92cfba5f3c362feb.r2.dev/Media.png"
        }
        putMeta(nft.contract, "0", NFTMetaData(nft.name, "Rewardoor", "Rewardoor Badger", nft.coverUrl))
        val user = userService.getUserByPrincipal(principal)!!
        nft.nftId = nftId
        nft.creatorId = user.userId
        return nftService.createNFT(nft)
    }

    @PostMapping("preCreate")
    fun preCreateNFT(@RequestBody nft: NFT): NFT {
        logger.info { "createNFT : $nft" }
        val principal = SecurityContextHolder.getContext().authentication.principal.toString()
        val nftId = idGenerator.getNewId()
        privilegeCheck.checkUserId(nft.creatorId, principal)

        putMeta("${nftId}/meta", "0", NFTMetaData(nft.name, "Rewardoor", "Rewardoor Badger NFT", nft.coverUrl))
        val user = userService.getUserByPrincipal(principal)!!
        nft.nftId = nftId
        nft.creatorId = user.userId
        nft.chainId = 10
        nft.contract = ""
        return nftService.createNFT(nft)
    }

    @PostMapping("finishCreate")
    fun finishCreateNFT(
        @RequestParam("nftId") nftId: Long,
        @RequestParam("contractAddress") contractAddress: String
    ): NFT {
        val principal = SecurityContextHolder.getContext().authentication.principal.toString()
        val user = userService.getUserByPrincipal(principal)!!
        val nft = nftService.getNFTById(nftId) ?: throw HttpClientErrorException(HttpStatus.NOT_FOUND, "NFT not found")
        if (nft.creatorId != user.userId) {
            throw HttpClientErrorException(HttpStatus.FORBIDDEN, "You are not the creator of this NFT")
        }
        if (nft.contract.isNotEmpty()) {
            throw HttpClientErrorException(HttpStatus.BAD_REQUEST, "NFT is already created with address")
        }
        return nftService.createNFT(nft)
    }

    @GetMapping("/{nftId}")
    fun getNFTById(@PathVariable("nftId") nftId: Long): NFT? {
        return nftService.getNFTById(nftId)
    }

    @GetMapping("/project/{projectId}")
    fun getNFTByProjectId(@PathVariable("projectId") projectId: Long): List<NFT> {
        return nftService.getNFTByProjectId(projectId)
    }

    @GetMapping("/creator/{creatorId}")
    fun getNFTByCreatorId(@PathVariable("creatorId") creatorId: Long): List<NFT> {
        return nftService.getNFTByCreatorId(creatorId)
    }

    @GetMapping("/giveaways")
    fun getNFTGives(
        @RequestParam("nftId") nftId: Long,
        @RequestParam("groupId") groupId: Long
    ): NFTGiveawayDto {
        val nft = nftService.getNFTByPairId(nftId, groupId) ?: throw ResponseStatusException(
            HttpStatus.NOT_FOUND,
            "NFT not found"
        )
        val usersIdAndDate = campaignService.getWinnersByRewardAndGroupId(nftId, nft.groupId)
            .filter { it.claimType == 4 }.associate { Pair(it.userId, it.participantDate) }
        val giveaways = usersIdAndDate.keys.map { userId ->
            val user = userService.getUserById(userId)
            NFTGiveaway(
                nftId = nftId,
                address = user?.wallet ?: "",
                nftNo = 0,
                mintDate = usersIdAndDate[userId]?.let {
                    it.toString().replace("T", " ").replace("Z","")
                } ?: ""
            )
        }
        return NFTGiveawayDto(nft, giveaways)
    }

    fun putMeta(bucketName: String, nftId: String, meta: NFTMetaData): String {
        val name = "${nftId}.json"

        val objectMeta = ObjectMetadata()
        objectMeta.contentType = MediaType.APPLICATION_JSON_VALUE
        val request = PutObjectRequest(
            bucketName, name,
            ByteArrayInputStream(mapper.valueToTree<JsonNode>(meta).toString().toByteArray()),
            objectMeta
        )
        val response = s3.putObject(request)
        logger.info("put meta response: {}", response)
        return name
    }

    fun createBucket(bucketName: String) {
        if (!s3.doesBucketExistV2(bucketName)) {
            s3.createBucket(bucketName)
        }
    }

    @GetMapping("/supportedChains")
    fun chains(): List<ContractDto> {
        return contractService.getAll().values.map {
            ContractDto(it.stationContractAddress, it.factoryAddress, it.chainId, it.chainName, it.icon)
        }
    }
}
