package com.rewardoor.app.controller

import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.rewardoor.app.services.CredentialService
import com.rewardoor.app.services.UserService
import com.rewardoor.model.Credential
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.web.bind.annotation.*

@RestController
@RequestMapping("credentials")
class CredentialController(
    val credentialService: CredentialService,
    private val userService: UserService,
    private val privilegeCheck: PrivilegeCheck
) {
    @PostMapping("create")
    fun createCredential(@RequestBody credential: Credential): Credential {
        val address = SecurityContextHolder.getContext().authentication.principal.toString()
        privilegeCheck.checkUserId(credential.creatorId, address)
        val user = userService.getUserByPrincipal(address)!!
        credential.creatorId = user.userId
        credential.ofMultiRoles()
        return credentialService.createCredential(credential)
    }

    @GetMapping("/{id}")
    fun getCredentialById(@PathVariable("id") id: Long): Credential? {
        return credentialService.getCredentialById(id)
    }

    @GetMapping("/all")
    fun getAllCredential(): List<Credential> {
        val crs = credentialService.getAllCredentials()
        val eligibles = credentialService.getCredentialEligibleCount(crs.map { it.credentialId })
        crs.forEach { it.eligibleCount = eligibles[it.credentialId]?:0 }
        return crs
    }

    @GetMapping("/project/{projectId}")
    fun getCredentialByProjectId(@PathVariable("projectId") projectId: Long): List<Credential> {
        val crs = credentialService.getCredentialByProjectId(projectId)
        val eligibles = credentialService.getCredentialEligibleCount(crs.map { it.credentialId })
        crs.forEach { it.eligibleCount = eligibles[it.credentialId]?:0 }
        return crs
    }

    @GetMapping("/creator/{creatorId}")
    fun getCredentialByCreatorId(@PathVariable("creatorId") creatorId: Long): List<Credential> {
        val crs = credentialService.getCredentialByCreatorId(creatorId)
        val eligibles = credentialService.getCredentialEligibleCount(crs.map { it.credentialId })
        crs.forEach { it.eligibleCount = eligibles[it.credentialId]?:0 }
        return crs
    }
}