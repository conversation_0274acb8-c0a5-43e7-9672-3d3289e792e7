package com.rewardoor.app.controller

import com.rewardoor.app.services.AdminService
import com.rewardoor.app.services.UserService
import com.rewardoor.model.Campaign
import com.rewardoor.model.Project
import org.springframework.http.HttpStatus
import org.springframework.stereotype.Component
import org.springframework.web.server.ResponseStatusException

@Component
class PrivilegeCheck(
    private val userService: UserService,
    private val adminService: AdminService
) {
    fun checkProject(userId: Long, projectCreatorId: Long) {
        if (userId != projectCreatorId) throw ResponseStatusException(
            HttpStatus.FORBIDDEN,
            "Project creator is not the same as the user"
        )
    }

    fun checkCampaign(campaign: Campaign, userId: Long) {
        if (campaign.creatorId != userId) throw ResponseStatusException(
            HttpStatus.FORBIDDEN,
            "Campaign creator is not the same as the user"
        )
    }

    fun checkUserId(userId: Long, principal: String) {
        userService.getUserByPrincipal(principal)?.let {
            if (it.userId != userId) throw ResponseStatusException(
                HttpStatus.FORBIDDEN,
                "User id is not the same as the principal"
            )
        }
    }

    fun checkUserId(fromUserId: Long, targetUserId: Long) {
        if (fromUserId != targetUserId) throw ResponseStatusException(
            HttpStatus.FORBIDDEN,
            "User id is not the same as the principal"
        )
    }

    fun checkProjectAdmin(userId: Long, project: Project, address: String) {
        if (userId == project.creatorId) return
        val adminList = adminService.getAdminList(project.projectId).orEmpty()
        val admins = adminList.map { it.userId }.orEmpty()
        val adminAddresses = adminList.map { it.wallet }
        println("address is $address and adminAddresses is $adminAddresses")
        if (!admins.contains(userId) && !adminAddresses.contains(address)) {
            throw ResponseStatusException(HttpStatus.BAD_REQUEST, "User is not admin of this project")
        }
    }
}