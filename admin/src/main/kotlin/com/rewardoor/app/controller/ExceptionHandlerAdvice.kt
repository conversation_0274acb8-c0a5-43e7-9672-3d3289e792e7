package com.rewardoor.app.controller

import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.ControllerAdvice
import org.springframework.web.bind.annotation.ExceptionHandler
import org.springframework.web.server.ResponseStatusException


@ControllerAdvice
class ExceptionHandlerAdvice {
    private val log = mu.KotlinLogging.logger {}
    @ExceptionHandler(ResponseStatusException::class)
    fun handleException(e: ResponseStatusException): ResponseEntity<*> {
        log.info { "handle exception: ${e.reason}-${e.message}" }
        val response = ResponseEntity
            .status(e.statusCode)
            .body<String>(e.reason)
        log.info { "response: $response" }
        return response
    }
}

