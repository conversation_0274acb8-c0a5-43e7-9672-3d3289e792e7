package com.rewardoor.app.controller

import com.rewardoor.app.services.CredentialGroupService
import com.rewardoor.app.services.CredentialService
import com.rewardoor.app.services.ProjectService
import com.rewardoor.app.services.UserService
import org.springframework.beans.factory.annotation.Value
import org.springframework.http.HttpStatus
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestHeader
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import org.springframework.web.server.ResponseStatusException

@RequestMapping("/partner")
@RestController
class PartnerController(
    private val credentialService: CredentialService,
    private val userService: UserService,
    private val projectService: ProjectService,
    private val campaignGroupService: CredentialGroupService,
    @Value("\${tenant.go-plus.id}") private val goPlusId: Long,
    @Value("\${tenant.go-plus.token}") private val gpToken: String,
    ) {

    @PostMapping("/check-credential")
    fun checkCredential(@RequestHeader("x-app-key") appKey: String,
                        @RequestBody request: CredentialCheckRequest): CredentialCheckResponse {
        val projectId: Long
        if (appKey == gpToken) {
            projectId = goPlusId
        } else {
            val config = projectService.getProjectExternalConfigByKey(appKey)
                ?: throw ResponseStatusException(HttpStatus.UNAUTHORIZED, "project not found")
            projectId = config.projectId
        }
        val credential = credentialService.getCredentialById(request.credentialId)
            ?: throw ResponseStatusException(HttpStatus.NOT_FOUND, "credential not found")
        if (credential.projectId != projectId) {
            throw ResponseStatusException(HttpStatus.FORBIDDEN, "credential is not for this project")
        }
        val user = userService.getUserByAddress(request.evmAddress)
            ?: throw ResponseStatusException(HttpStatus.NOT_FOUND, "user not found")
        val userCredential = campaignGroupService.getUserCredential(user.userId, credential.credentialId)
        if (userCredential == null || userCredential.status != 1) {
            return CredentialCheckResponse(false, "credential not finished")
        }
        return CredentialCheckResponse(true, "credential finished", userCredential.participantDate?.toEpochMilli())
    }
}

data class CredentialCheckRequest(
    val evmAddress: String,
    val credentialId: Long
)

data class CredentialCheckResponse(
    val finished: Boolean,
    val message: String,
    val finishedAt: Long? = null
)