package com.rewardoor.app.controller

import com.amazonaws.HttpMethod
import com.amazonaws.services.s3.AmazonS3
import com.rewardoor.model.SignedUploadResponse
import io.swagger.v3.oas.annotations.Operation
import org.springframework.beans.factory.annotation.Value
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RestController
import java.time.LocalDateTime
import java.time.ZoneId
import java.util.*

@RestController
class MediaController(val s3: AmazonS3,
                      @Value("\${r2.publicUrl}") val publicUrl: String) {
    @GetMapping("/signedUploadUrl")
    @Operation(summary = "获取上传文件地址", description = "获取之后对signedUrl使用put请求上传文件，注意设置一下正确的Content-Type；上传完成之后可以通过accessUrl来访问")
    fun getSignedUploadUrl(): SignedUploadResponse {
        val name = UUID.randomUUID().toString().replace("-", "")
        val expire = Date.from(LocalDateTime.now().plusHours(1).atZone(ZoneId.systemDefault()).toInstant())
        return SignedUploadResponse(
            s3.generatePresignedUrl("img", name, expire, HttpMethod.PUT).toString(),
            name, "${publicUrl}img/${name}"
        )
    }

    @PostMapping("/signedUploadUrlWithSuffix")
    @Operation(summary = "获取上传文件地址", description = "获取之后对signedUrl使用put请求上传文件，注意设置一下正确的Content-Type；上传完成之后可以通过accessUrl来访问")
    fun getSignedUploadUrlWithSuffix(@RequestBody req: FileUploadReq): SignedUploadResponse {
        val suffix = if (req.suffix.isBlank()) "" else ".${req.suffix}"
        val name = UUID.randomUUID().toString().replace("-", "") + suffix
        val expire = Date.from(LocalDateTime.now().plusHours(1).atZone(ZoneId.systemDefault()).toInstant())
        return SignedUploadResponse(
            s3.generatePresignedUrl("img", name, expire, HttpMethod.PUT).toString(),
            name, "${publicUrl}img/${name}"
        )
    }
}

class FileUploadReq(
    var suffix: String = ""
)