package com.rewardoor.app.controller

import com.amazonaws.services.s3.AmazonS3
import com.amazonaws.services.s3.model.ObjectMetadata
import com.rewardoor.IdGenerator
import com.rewardoor.app.dao.SBTRewardRepository
import com.rewardoor.app.services.CampaignService
import com.rewardoor.app.services.ProjectService
import com.rewardoor.app.services.TonSyncService
import com.rewardoor.app.dao.TonSocietySyncRepository
import com.rewardoor.app.services.UserService
import com.rewardoor.model.SBTReward
import com.rewardoor.model.TonSyncHistory
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.beans.factory.annotation.Value
import org.springframework.http.HttpStatus
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.transaction.annotation.Transactional
import org.springframework.web.bind.annotation.*
import org.springframework.web.server.ResponseStatusException
import java.time.Instant
import java.util.*

@RestController
@RequestMapping("ton-sync")
class TonSyncController(
    private val tonSyncService: TonSyncService,
    private val TonSocietySyncRepo: TonSocietySyncRepository,
    private val campaignService: CampaignService,
    private val userService: UserService,
    private val projectService: ProjectService,
    private val idGenerator: IdGenerator,
    private val sbtRepo: SBTRewardRepository,
    @Qualifier("s3clientBasic") private val s3: AmazonS3
) {
    private val log = mu.KotlinLogging.logger { }

    @GetMapping("privilege/{campaignId}")
    fun getSyncPrivilege(@PathVariable("campaignId") campaignId: Long): Any {
        val hasPrivilege = tonSyncService.hasPrivilege(campaignId)
        if (!hasPrivilege) {
            return mapOf("hasPrivilege" to false)
        }
        val synced = tonSyncService.getSyncHistoryByCampaignId(campaignId)
        return if (synced != null) {
            mapOf("hasPrivilege" to true, "synced" to true, "syncHistory" to synced)
        } else {
            mapOf("hasPrivilege" to true, "synced" to false)
        }
    }

    @PostMapping("sync")
    fun syncCampaign(@RequestBody req: TonSyncRequest): SimpleResponseEntity<TonSyncHistory> {
        log.info { "sync campaign: $req" }
        val principal = SecurityContextHolder.getContext().authentication.principal.toString()
        val user = userService.getUserByPrincipal(principal)!!
        val synced = tonSyncService.getSyncHistoryByGroupId(req.groupId)
        if (synced != null) {
            return SimpleResponseEntity.success("OK", synced)
        }
        val campaign = campaignService.getCampaignById(req.campaignId)
        if (campaign == null || campaign.projectId != req.projectId) {
            return SimpleResponseEntity.failed("Campaign not found")
        }

        if (!req.sbtImage.endsWith("jpg") && !req.sbtImage.endsWith("png")) {
            val imgName = "img/" + req.sbtImage.split("/").last()
            val nk = "$imgName.jpg"
            val obj = s3.getObject("rd-nft", imgName)
            val objMeta = ObjectMetadata()
            objMeta.contentType = obj.objectMetadata.contentType
            s3.putObject("rd-nft", nk, obj.objectContent, objMeta)
            req.sbtImage = "${req.sbtImage}.jpg"
        }

        // 去掉权限控制
//        val hasPrivilege = tonSyncService.hasPrivilege(req.campaignId)
//        if (!hasPrivilege) {
//            return SimpleResponseEntity.failed("No privilege")
//        }

        val h = tonSyncService.syncCampaign(req.toHistory())
        return SimpleResponseEntity.success("OK", h)
    }

    @Transactional
    @GetMapping("sbtId")
    fun getPreviewSbtById(@RequestParam sbtId: Long): SimpleResponseEntity<TonSyncHistory>? {
        val principal = SecurityContextHolder.getContext().authentication.principal.toString()
        val user = userService.getUserByPrincipal(principal)!!
        val sbtReward = sbtRepo.getSBTById(sbtId)!!
        val entity = tonSyncService.getSyncHistoryByActivityId(sbtReward.activityId.toLong())
        if (entity != null) {
            entity.category = sbtReward.category
            return SimpleResponseEntity.success("OK", entity)
        }
        return SimpleResponseEntity.failed("Failed", null)
    }

    @Transactional
    @PostMapping("preCreate")
    fun preCreateSbt(@RequestBody req: TonSyncRequest): SimpleResponseEntity<TonSyncHistory> {
        log.info { "pre create sbt: ${req.toString()}" }
        val principal = SecurityContextHolder.getContext().authentication.principal.toString()
        val user = userService.getUserByPrincipal(principal)!!

        val auth = SecurityContextHolder.getContext().authentication as? com.rewardoor.app.auth.AddressAuthentication
        val chain = auth?.chain
        if (chain == null || chain.lowercase() != "ton") {
            return SimpleResponseEntity.failed("Only TON wallet can create TON SBT")
        }

//        val synced = tonSyncService.getSyncHistoryByGroupId(req.groupId)
//        if (synced != null) {
//            return SimpleResponseEntity.success("OK", synced)
//        }
        // 提前创建sbt reward，获取sbt id
        val sbtId = idGenerator.getNewId()
        val sbt = SBTReward(
            sbtId = sbtId,
            name = req.sbtCollectionTitle,
            category = req.category,
            picUrl = req.sbtImage,
            methodType = 1,
            projectId = req.projectId
        )
        sbtRepo.createSBT(sbt)
        req.sbtId = sbtId
        if (!req.sbtImage.endsWith("jpg") && !req.sbtImage.endsWith("png")) {
            val imgName = "img/" + req.sbtImage.split("/").last()
            val nk = "$imgName.jpg"
            val obj = s3.getObject("rd-nft", imgName)
            val objMeta = ObjectMetadata()
            objMeta.contentType = obj.objectMetadata.contentType
            s3.putObject("rd-nft", nk, obj.objectContent, objMeta)
            req.sbtImage = "${req.sbtImage}.jpg"
        }

        // 去掉权限控制
//        val hasPrivilege = tonSyncService.hasPrivilege(req.campaignId)
//        if (!hasPrivilege) {
//            return SimpleResponseEntity.failed("No privilege")
//        }

        // 提交审核
        val h = tonSyncService.createSBTCheck(req.toHistory())
//        val h = tonSyncService.syncCampaign(req.toHistory())
        return SimpleResponseEntity.success("OK", h)
    }

    @GetMapping("check-list")
    fun getSbtCheckList(): List<TonSyncHistory>? {
        val principal = SecurityContextHolder.getContext().authentication.principal.toString()
        val user = userService.getUserByPrincipal(principal)!!
        println("${user.wallet} is setting project home")
        val adminAddressA = "******************************************" // Keyla
        val adminAddressB = "******************************************" // LakeHu
        val adminAddressC = "******************************************" // Kelly
        val adminAddressD = "******************************************" // zhanbin
        if (user.wallet.lowercase(Locale.getDefault()) != adminAddressA.lowercase(Locale.getDefault())
            && user.wallet.lowercase(Locale.getDefault()) != adminAddressB.lowercase(Locale.getDefault())
            && user.wallet.lowercase(Locale.getDefault()) != adminAddressC.lowercase(Locale.getDefault())
            && user.wallet.lowercase(Locale.getDefault()) != adminAddressD.lowercase(Locale.getDefault())
        ) {
            return emptyList()
        }
        return tonSyncService.getCheckList()
    }

    @GetMapping("check")
    fun checkSBTStatus(@RequestParam sbtId: Long, @RequestParam checkStatus: Int): Any {
        val principal = SecurityContextHolder.getContext().authentication.principal.toString()
        val user = userService.getUserByPrincipal(principal)!!
        println("${user.wallet} is setting project home")
        val adminAddressA = "******************************************" // Keyla
        val adminAddressB = "******************************************" // LakeHu
        val adminAddressC = "******************************************" // Kelly
        val adminAddressD = "******************************************" // zhanbin
        if (user.wallet.lowercase(Locale.getDefault()) != adminAddressA.lowercase(Locale.getDefault())
            && user.wallet.lowercase(Locale.getDefault()) != adminAddressB.lowercase(Locale.getDefault())
            && user.wallet.lowercase(Locale.getDefault()) != adminAddressC.lowercase(Locale.getDefault())
            && user.wallet.lowercase(Locale.getDefault()) != adminAddressD.lowercase(Locale.getDefault())
        ) {
            return mapOf(
                "code" to 400,
                "message" to "你没有权限审批"
            )
        }
        val updateCnt = tonSyncService.checkSBTStatus(sbtId, checkStatus)
        return mapOf("code" to 200, "message" to "check success", "updateCnt" to updateCnt)
    }

    @PostMapping("updateSync")
    fun updateSyncActivity(@RequestBody req: TonSyncHistory): SimpleResponseEntity<TonSyncHistory> {
        val principal = SecurityContextHolder.getContext().authentication.principal.toString()
        val user = userService.getUserByPrincipal(principal)!!
        val creatorId = projectService.getProjectCreator(req.projectId) ?: throw ResponseStatusException(
            HttpStatus.NOT_FOUND,
            "Project not found"
        )
        if (user.userId != creatorId) {
            return SimpleResponseEntity.failed(
                "You are not the creator of this project, so you don't have permission to update"
            )
        }

        val synced = tonSyncService.getSyncHistoryByGroupId(req.groupId)
        if (synced != null) {
            val updateActivity = tonSyncService.updateSyncActivity(req)
            return SimpleResponseEntity.success("OK", updateActivity)
        }
        return SimpleResponseEntity.failed("error")
    }


    @PostMapping("updateSbtCategory")
    fun updateSBTCategory(@RequestBody req: UpdateSBTCategoryRequest): SimpleResponseEntity<TonSyncHistory> {
        val principal = SecurityContextHolder.getContext().authentication.principal.toString()
        val user = userService.getUserByPrincipal(principal)!!
        val sbtCheckDetail = TonSocietySyncRepo.getTonSyncCheckBySBTId(req.sbtId)
        if (sbtCheckDetail != null) {
            val sbtSyncHistory = TonSocietySyncRepo.getTonSyncHistoryBySBTId(req.sbtId)
            if (sbtSyncHistory != null) {
                val activityId = sbtSyncHistory.activityId
                val sbtList = sbtRepo.getSBTByActivityId(activityId.toInt())
                for (sbt in sbtList) {
                    tonSyncService.updateSbtCategory(sbt.sbtId, req.category)
                }
                return SimpleResponseEntity.success("OK", sbtSyncHistory)
            } else {
                tonSyncService.updateSbtCategory(req.sbtId, req.category)
                return SimpleResponseEntity.success("OK", sbtCheckDetail)
            }
        }
        return SimpleResponseEntity.failed("error")
    }

    class TonSyncRequest(
        val projectId: Long = 0L,
        val campaignId: Long = 0L,
        val groupId: Long = 0L,
        val category: Int = 0, // 0 - default , 1 - TBook OG SBTs, 2 - DeFi SBTs, 3 - Meme SBTs, 4 - Game SBTs, 5-premium SBTs, 6 - ton pioneer SBTs
        val taskCategory: List<Int> = emptyList(), // 0 - default , 1- social contributions, 2 - defi tasks, 3 - In-game levels, 4 - credit score greater than a certain level, 5 - other tasks
        val title: String = "",
        val subtitle: String,
        val description: String = "",
        val startDate: Long,
        val endDate: Long,
        val buttonLabel: String,
        val buttonLink: String,
        val sbtCollectionTitle: String,
        val sbtCollectionDesc: String,
        val sbtItemTitle: String,
        var sbtImage: String,
        var sbtVideo: String,
        val sbtDesc: String,
        var sbtId: Long = 0L
    ) {
        fun toHistory(): TonSyncHistory {
            return TonSyncHistory(
                projectId,
                campaignId,
                groupId,
                title,
                subtitle,
                if (description == "") sbtDesc else description,
                Instant.ofEpochMilli(startDate),
                Instant.ofEpochMilli(endDate),
                buttonLabel,
                if (buttonLink == "") "https://t.me/tbook_incentive_bot/tbook" else buttonLink,
                sbtCollectionTitle,
                sbtCollectionDesc,
                sbtItemTitle,
                sbtImage,
                sbtVideo,
                sbtDesc,
                Instant.now(),
                0L,
                "",
                sbtId,
                category,
                taskCategory
            )
        }
    }


    data class UpdateSBTCategoryRequest(
        val sbtId: Long,
        val category: Int
    )
}