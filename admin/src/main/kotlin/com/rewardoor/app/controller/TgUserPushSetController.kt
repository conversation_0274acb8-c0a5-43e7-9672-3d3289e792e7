package com.rewardoor.app.controller

import com.rewardoor.app.services.TgUserPushService
import com.rewardoor.app.services.UserService
import com.rewardoor.model.TgUserPushSetting
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.web.bind.annotation.*

@RestController
@RequestMapping("user-push")
class TgUserPushSetController(val userService: UserService, val tgUserPushService: TgUserPushService) {
    @PostMapping("create")
    fun createSetting(@RequestBody tgUserPushSetting: TgUserPushSetting): TgUserPushSetting? {
        val userId = SecurityContextHolder.getContext().authentication.principal.toString()
        val user = userService.getUserByPrincipal(userId)!!
        println("user address is ${user.evm.evmWallet}")
        if (user.evm.evmWallet!!.lowercase() != "******************************************".lowercase()
            && user.evm.evmWallet!!.lowercase() != "******************************************".lowercase()
        ) {
            return null
        }
        return tgUserPushService.createPush(tgUserPushSetting)
    }

    @GetMapping("{id}")
    fun getSetting(@PathVariable id: Long): TgUserPushSetting? {
        val userId = SecurityContextHolder.getContext().authentication.principal.toString()
        val user = userService.getUserByPrincipal(userId)!!
        if (user.evm.evmWallet!!.lowercase() != "******************************************".lowercase()
            && user.evm.evmWallet!!.lowercase() != "******************************************".lowercase()
        ) {
            return null
        }
        return tgUserPushService.getPushSettingById(id)
    }

    @GetMapping("list")
    fun getList(): List<TgUserPushSetting>? {
        val userId = SecurityContextHolder.getContext().authentication.principal.toString()
        val user = userService.getUserByPrincipal(userId)!!
        if (user.evm.evmWallet!!.lowercase() != "******************************************".lowercase()
            && user.evm.evmWallet!!.lowercase() != "******************************************".lowercase()
        ) {
            return emptyList()
        }
        return tgUserPushService.getPushSettingList()
    }

    // 初始化推送user
    @GetMapping("init/{id}")
    fun init(@PathVariable id: Long): Any {
        val userId = SecurityContextHolder.getContext().authentication.principal.toString()
        val user = userService.getUserByPrincipal(userId)!!
        if (user.evm.evmWallet!!.lowercase() != "******************************************".lowercase()
            && user.evm.evmWallet!!.lowercase() != "******************************************".lowercase()
        ) {
            return mapOf(
                "code" to 404,
                "message" to "Permission is denied"
            )
        }
        val initCnt = tgUserPushService.initPushUsers(id)
        if (initCnt == -1) {
            return mapOf(
                "code" to 400,
                "initCnt" to 0,
                "message" to "It's already initialized"
            )
        }
        return mapOf(
            "code" to 200,
            "initCnt" to initCnt
        )
    }

    //获取推送结果
    @GetMapping("pushInfo/{id}")
    fun getPushInfo(@PathVariable id: Long): Any {
        val userId = SecurityContextHolder.getContext().authentication.principal.toString()
        val user = userService.getUserByPrincipal(userId)!!
        if (user.evm.evmWallet!!.lowercase() != "******************************************".lowercase()
            && user.evm.evmWallet!!.lowercase() != "******************************************".lowercase()
        ) {
            return mapOf(
                "code" to 404,
                "message" to "Permission is denied"
            )
        }
        val pushInfo = tgUserPushService.getPushResultInfo(id)
        return mapOf(
            "code" to 200,
            "pushResultInfo" to pushInfo
        )
    }

}