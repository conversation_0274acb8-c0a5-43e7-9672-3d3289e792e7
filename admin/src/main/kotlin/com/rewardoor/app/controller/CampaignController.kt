package com.rewardoor.app.controller

import com.rewardoor.app.services.CampaignService
import com.rewardoor.app.services.ProjectService
import com.rewardoor.app.services.UserService
import com.rewardoor.model.Campaign
import org.springframework.http.HttpStatus
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.web.bind.annotation.*
import org.springframework.web.server.ResponseStatusException

@RestController
@RequestMapping("campaign")
class CampaignController(
    val campaignService: CampaignService,
    val userService: UserService,
    private val projectService: ProjectService
) {

    @PostMapping("/create")
    fun createCampaign(@RequestBody campaign: Campaign): Campaign {
        val userId = SecurityContextHolder.getContext().authentication.principal.toString().toLong()
        val project = projectService.getProject(campaign.projectId)
        if (project?.creatorId != userId) {
            throw ResponseStatusException(HttpStatus.BAD_REQUEST, "Project creator is not the same as the user")
        }
        campaign.creatorId = userId
        return campaignService.createCampaign(campaign)
    }

}