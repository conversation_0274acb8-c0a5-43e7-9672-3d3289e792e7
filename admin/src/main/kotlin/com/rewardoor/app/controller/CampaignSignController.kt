package com.rewardoor.app.controller

import com.rewardoor.app.services.CredentialService
import com.rewardoor.app.services.CredentialSignService
import com.rewardoor.app.services.UserService
import com.rewardoor.app.utils.Signs
import org.springframework.http.HttpStatus
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.web.bind.annotation.*
import org.springframework.web.server.ResponseStatusException

@RestController
@RequestMapping("campaignSign")
class CampaignSignController(
    val userService: UserService,
    val credentialService: CredentialService,
    val credentialSignService: CredentialSignService
) {
}