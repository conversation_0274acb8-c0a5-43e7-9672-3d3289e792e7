package com.rewardoor.app.controller

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.databind.node.ObjectNode
import com.fasterxml.jackson.module.kotlin.readValue
import com.fasterxml.jackson.module.kotlin.treeToValue
import com.rewardoor.app.auth.AddressAuthentication
import com.rewardoor.app.auth.Jwt
import com.rewardoor.app.dto.SocialAccountBound
import com.rewardoor.app.dto.StringPayloadRequest
import com.rewardoor.app.dto.TgAuthCallbackReq
import com.rewardoor.app.services.TelegramService
import com.rewardoor.app.services.UserService
import com.rewardoor.app.utils.isDuplicate
import com.rewardoor.model.PassportAccounts
import jakarta.servlet.http.HttpServletRequest
import org.apache.http.client.utils.URLEncodedUtils
import org.springframework.beans.factory.annotation.Value
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.stereotype.Controller
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestHeader
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.ResponseBody
import org.springframework.web.server.ResponseStatusException
import java.util.*


@Controller
@RequestMapping("tg")
class TelegramController(
    @Value("\${tg.bot_token}") val tgToken: String,
    @Value("\${tg.mini_app.token}") val miniAppToken: String,
    @Value("\${tg.mini_app.game_build_token}") val gameBuildToken: String,
    @Value("\${tg.mini_app.ai_agent_token}") val aiAgentToken: String,
    @Value("\${tg.mini_app.bot_name}") val miniAppBotName: String,
    val telegramService: TelegramService,
    val userService: UserService
) {
    private val mapper = ObjectMapper()
    private val logger = mu.KotlinLogging.logger {}

    @PostMapping("callback", produces = ["application/json"])
    @ResponseBody
    fun tgCallback(@RequestParam("originAuthResult") authResult: String): Any {
        val principal = SecurityContextHolder.getContext().authentication as AddressAuthentication

        logger.info { "tg callback data: $authResult" }
        val auth = String(Base64.getUrlDecoder().decode(authResult))
        if (!telegramService.verifyHash(auth)) {
            logger.warn { "invalidate hash: $authResult" }
            throw ResponseStatusException(HttpStatus.BAD_REQUEST, "Invalid hash")
        }
        val tgInfo = mapper.readValue<TgAuthCallbackReq>(auth)
        return try {
            telegramService.addUserTgInfo(principal.userId, tgInfo)
            mapOf(
                "code" to 200,
                "socialName" to tgInfo.username,
                "data" to telegramService.getUserInfo(principal.userId)!!
            )
        } catch (e: Exception) {
            if (e.isDuplicate()) {
                logger.error(e) { "TG account already connected, ${tgInfo.username}" }
                val curUser = telegramService.getUserInfoByTgId(tgInfo.id)!!
                val address = userService.getUserById(curUser.userId)!!.wallet
                SocialAccountBound(
                    "Telegram account already connected",
                    tgInfo.username.orEmpty(), address
                )
            } else {
                logger.error(e) { "error for tg bind: ${tgInfo.username}" }
                mapOf("code" to 500, "message" to "Internal error")
            }
        }
    }

    @PostMapping("callback/v2", produces = ["application/json"])
    @ResponseBody
    fun tgCallbackV2(@RequestBody req: ObjectNode): Any {
        val principal = SecurityContextHolder.getContext().authentication as AddressAuthentication

        logger.info { "tg callback data: $req" }
        if (!telegramService.verifyHash(req)) {
            logger.warn { "invalidate hash: $req" }
            return mapOf("code" to 500, "message" to "invalid hash")
        }
        val tgInfo = mapper.treeToValue<TgAuthCallbackReq>(req)
        var tgUserName = tgInfo.username
        if (tgUserName.isNullOrEmpty()) {
            tgUserName = "${tgInfo.firstName} ${tgInfo.lastName}"
        }
        return try {
            telegramService.addUserTgInfo(principal.userId, tgInfo)
            mapOf(
                "code" to 200,
                "socialName" to tgUserName,
                "data" to telegramService.getUserInfo(principal.userId)!!
            )
        } catch (e: Exception) {
            if (e.isDuplicate()) {
                logger.error(e) { "TG account already connected, $tgUserName" }
                val curUser = telegramService.getUserInfoByTgId(tgInfo.id)!!
                val address = userService.getUserById(curUser.userId)!!.evm.evmWallet
                val userA = userService.getUserById(principal.userId)!!
                val passportA = PassportAccounts(
                    userId = userA.userId,
                    evmAddress = userA.evm.evmWallet ?: "",
                    tonAddress = userA.ton.tonWallet ?: "",
                    twitterName = userA.twitterName,
                    dcName = userA.dcName,
                    tgName = userA.tgName
                )
                val userB = userService.getUserById(curUser.userId)!!
                val passportB = PassportAccounts(
                    userId = userB.userId,
                    evmAddress = userB.evm.evmWallet ?: "",
                    tonAddress = userB.ton.tonWallet ?: "",
                    twitterName = userB.twitterName,
                    dcName = userB.dcName,
                    tgName = userB.tgName
                )
                if (address != "" && address != null) {
                    SocialAccountBound(
                        "Telegram account already connected",
                        tgUserName, address, passportA, passportB
                    )
                } else {
                    mapOf(
                        "code" to 400,
                        "passportA" to passportA,
                        "passportB" to passportB
                    )
                }
            } else {
                logger.error(e) { "error for tg bind: $tgUserName" }
                mapOf("code" to 500, "message" to "Internal error")
            }
        }
    }

    @PostMapping("tma/ot/generate", produces = ["application/json"])
    @ResponseBody
    fun oneTimeToken(): Any {
        val principal = SecurityContextHolder.getContext().authentication as AddressAuthentication
        val token = telegramService.generateOneTimeToken(principal.userId)
        return mapOf("code" to 200, "token" to token)
    }

    @PostMapping("tma/ot/verify", produces = ["application/json"])
    @ResponseBody
    fun verifyOneTimeToken(@RequestBody req: StringPayloadRequest): Any {
        val userId = telegramService.verifyOneTimeToken(req.payload)
        return if (userId != null) {
            val cookie = Jwt.buildCookie(userId)
            val setCk = cookie.toString().split(";")[0] + ";"
            ResponseEntity.ok().header("Set-Cookie", cookie.toString())
                .body(mapOf("code" to 200, "data" to userService.getUserById(userId)))
        } else {
            mapOf("code" to 401, "message" to "Invalid token")
        }
    }

    @PostMapping("tma/auth", produces = ["application/json"])
    @ResponseBody
    fun miniAppAuth(@RequestBody req: StringPayloadRequest, request: HttpServletRequest): Any {
        val referer = request.getHeader("Referer")
        logger.info { "tg callback data: $req" }
//        val appToken = if (referer?.contains("https://gamebuild.tbook.com") == true) gameBuildToken else miniAppToken
        val appToken = when {
            referer?.contains("https://gamebuild.tbook.com") == true -> {
                gameBuildToken
            }

            referer?.contains("amplifyapp.com") == true -> {
                aiAgentToken
            }

            else -> miniAppToken
        }

        val (verifyResult, user) = TelegramService.verifyMiniApp(req.payload, appToken)
        if (!verifyResult) {
            logger.warn { "invalidate hash: $req" }
            return mapOf("code" to 500, "message" to "invalid hash")
        }
        val tgInfo = mapper.readValue<TgAuthCallbackReq>(user)
        var tgUserName = tgInfo.username
        if (tgUserName.isNullOrEmpty()) {
            tgUserName = "${tgInfo.firstName} ${tgInfo.lastName}"
        }
        return try {
            val userId = telegramService.registerTgUser(tgInfo)
            val cookie = Jwt.buildCookie(userId)
            val setCk = cookie.toString().split(";")[0] + ";"
            ResponseEntity.ok().header("Set-Cookie", cookie.toString())
                .body(
                    mapOf(
                        "code" to 200,
                        "socialName" to tgUserName,
                        "data" to userService.getUserById(userId)
                    )
                )
        } catch (e: Exception) {
            if (e.isDuplicate()) {
                val curUser = telegramService.getUserInfoByTgId(tgInfo.id)!!
                val cookie = Jwt.buildCookie(curUser.userId)
                val setCk = cookie.toString().split(";")[0] + ";"
                ResponseEntity.ok().header("Set-Cookie", cookie.toString())
                    .body(
                        mapOf(
                            "code" to 200,
                            "socialName" to tgUserName,
                            "data" to userService.getUserById(curUser.userId)
                        )
                    )
            } else {
                logger.error(e) { "error for tg bind: $tgUserName" }
                mapOf("code" to 500, "message" to "Internal error")
            }
        }
    }

    @GetMapping("callback/{domain}")
    fun callbackPage(): String {
        return "tg_callback.html"
    }

    @GetMapping("bindToken")
    @ResponseBody
    fun getBindTokenAddress(): Any {
        val principal = SecurityContextHolder.getContext().authentication as AddressAuthentication

        // Check if the user is already bound to a Telegram account
        val existingBinding = telegramService.getUserInfo(principal.userId)
        if (existingBinding != null) {
            return mapOf(
                "code" to 400,
                "message" to "Your account is already bound to a Telegram account",
                "tgInfo" to existingBinding
            )
        }

        val token = telegramService.generateBindToken(principal.userId)
        val botName = miniAppBotName
        val bindUrl = "https://t.me/$botName?start=bind_$token"
        return mapOf("code" to 200, "bindUrl" to bindUrl)
    }
}