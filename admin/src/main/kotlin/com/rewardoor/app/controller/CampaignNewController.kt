package com.rewardoor.app.controller

import com.rewardoor.app.dao.CampaignRepository
import com.rewardoor.app.services.*
import com.rewardoor.model.*
import org.springframework.http.HttpStatus
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.web.bind.annotation.*
import org.springframework.web.server.ResponseStatusException
import java.util.*
import kotlin.random.Random

@RestController
@RequestMapping("campaignNew")
class CampaignNewController(
    val credentialGroupService: CredentialGroupService,
    val userService: UserService,
    val campaignService: CampaignService,
    val nftService: NFTService,
    val projectService: ProjectService,
    private val privilegeCheck: PrivilegeCheck,
    val idGenerator: GeneratorService,
    private val credentialService: CredentialService,
    private val campaignRepository: CampaignRepository
) {
    @PostMapping("createCampaign")
    fun createCam(@RequestBody campaignTotal: CampaignTotal): CampaignTotal {
        val address = SecurityContextHolder.getContext().authentication.principal.toString()
        val user = userService.getUserByPrincipal(address)!!
        val userId = SecurityContextHolder.getContext().authentication.principal.toString().toLong()
        val project = projectService.getProject(campaignTotal.campaign.projectId)!!
        val checkAddress = when (project.chain) {
            "ton" -> user.ton.tonWallet ?: ""
            "sui" -> user.suiAddress
            "evm" -> user.evm.evmWallet ?: ""
            else -> ""
        }
        privilegeCheck.checkProjectAdmin(userId, project, checkAddress)
        campaignTotal.campaign.creatorId = user.userId
        return campaignService.createCampaignNew(campaignTotal, userId)
    }

    @GetMapping("check")
    fun checkCampaign(@RequestParam campaignId: Long, @RequestParam checkStatus: Int): Any {
        val principal = SecurityContextHolder.getContext().authentication.principal.toString()
        val user = userService.getUserByPrincipal(principal)!!
        println("${user.wallet} is checking campaign")
        val adminAddressA = "******************************************" // Keyla
        val adminAddressB = "******************************************" // LakeHu
        val adminAddressC = "******************************************" // Kelly
        val adminAddressD = "******************************************" // zhanbin
        if (user.wallet.lowercase(Locale.getDefault()) != adminAddressA.lowercase(Locale.getDefault())
            && user.wallet.lowercase(Locale.getDefault()) != adminAddressB.lowercase(Locale.getDefault())
            && user.wallet.lowercase(Locale.getDefault()) != adminAddressC.lowercase(Locale.getDefault())
            && user.wallet.lowercase(Locale.getDefault()) != adminAddressD.lowercase(Locale.getDefault())
        ) {
            return mapOf(
                "code" to 400,
                "message" to "你没有权限审批"
            )
        }
        val updateCnt = campaignService.checkCampaignStatus(campaignId, checkStatus)
        return mapOf("code" to 200, "message" to "check success", "updateCnt" to updateCnt)
    }

    @GetMapping("check-list")
    fun getCampaignCheckList(): Any {
        val principal = SecurityContextHolder.getContext().authentication.principal.toString()
        val user = userService.getUserByPrincipal(principal)!!
        val adminAddressA = "******************************************" // Keyla
        val adminAddressB = "******************************************" // LakeHu
        val adminAddressC = "******************************************" // Kelly
        val adminAddressD = "******************************************" // zhanbin
        if (user.wallet.lowercase(Locale.getDefault()) != adminAddressA.lowercase(Locale.getDefault())
            && user.wallet.lowercase(Locale.getDefault()) != adminAddressB.lowercase(Locale.getDefault())
            && user.wallet.lowercase(Locale.getDefault()) != adminAddressC.lowercase(Locale.getDefault())
            && user.wallet.lowercase(Locale.getDefault()) != adminAddressD.lowercase(Locale.getDefault())
        ) {
            return mapOf(
                "code" to 400,
                "message" to "你没有权限审批",
                "campaignList" to emptyList<CampaignTotal>()
            )
        }
        val checkCampaigns = campaignRepository.getCheckCampaigns()
        val campaignIds = checkCampaigns.map { it.campaignId }
        val campaignList = campaignService.getCampaignTotalsByIds(campaignIds, checkCampaigns)
        return mapOf(
            "code" to 200,
            "message" to "success",
            "campaignList" to campaignList
        )
    }

    @GetMapping("/{id}")
    fun getCam(@PathVariable("id") id: Long): CampaignTotal? {
        val principal = SecurityContextHolder.getContext().authentication.principal.toString()
        if (principal == "anonymousUser") {
            val campaignTotal = campaignService.getVerifiedCamByCredential(id, 0, "") ?: return null
            campaignTotal.participation = campaignService.getParticipationById(id)
            return campaignTotal
        }
        val user = userService.getUserByPrincipal(principal)!!
        val userId = user.userId

        println("userId is " + userId + " wallet is " + user.wallet)
        val campaignTotal = campaignService.getVerifiedCamByCredential(id, userId, user.wallet) ?: return null
        campaignTotal.participantNum = campaignService.getParticipantNumsByCampaignId(id)
//        campaignTotal.participation = campaignService.getParticipationById(id)
        return campaignTotal
    }

    @GetMapping("/consumer/{id}/user/{userId}")
    fun getUserCampaign(@PathVariable("id") id: Long, @PathVariable("userId") userId: Long): Any? {
        if (campaignService.getCampaignById(id) == null) {
            return mapOf("code" to 404, "message" to "The campaign is not exist")
        }
        val user = userService.getUserById(userId)!!
        val userId = user.userId
        val campaignTotal = campaignService.getVerifiedCamByCredential(id, userId, user.wallet) ?: return null
        campaignTotal.participantNum = campaignService.getParticipantNumsByCampaignId(id)
//        campaignTotal.participation = campaignService.getParticipationById(id)
        if (campaignTotal.campaign.status == CampaignStatus.DELETED) {
            return mapOf("code" to 204, "campaignTotal" to campaignTotal)
        }
        return mapOf("code" to 200, "campaignTotal" to campaignTotal)
    }

    @GetMapping("claimNft/{nftId}")
    fun claimNft(@PathVariable("nftId") nftId: Long): CredentialGroup? {
        val principal = SecurityContextHolder.getContext().authentication.principal.toString()
        val nft = nftService.getNFTById(nftId)!!
        val group = credentialGroupService.getCredentialGroupById(nft.groupId)!!
        if (principal == "anonymousUser") {
            println(" not login " + group.id)
            return group
        }
        val user = userService.getUserByPrincipal(principal)!!
        val userId = user.userId
        return campaignService.updateClaimType(1, nftId, userId, group.id, 4)
    }

//    @GetMapping("/user/{campaignId}")
//    fun getUserCamInfo(
//        @PathVariable("campaignId") campaignId: Long,
//        @PathVariable("spaceId") spaceId: String
//    ): CampaignTotalWithUser? {
//        val campaignTotal = campaignService.getCampaignTotalById(campaignId)!!
//        val address = SecurityContextHolder.getContext().authentication.principal.toString()
//        val user = userService.getUserByAddress(address)!!
//        val userId = user.userId
//        val participant = campaignService.getParticipantByUserId(userId, campaignId)!!
//        //更新C端登录participant状态
//        campaignService.updateParticipantBySpace(userId, campaignId, spaceId)
//        return CampaignTotalWithUser(
//            campaignTotal, participant
//        )
//    }

    @GetMapping("/project/{projectId}")
    fun getCredentialGroupByProjectId(@PathVariable("projectId") projectId: Long): List<CampaignTotal>? {
        val address = SecurityContextHolder.getContext().authentication.principal.toString()
        val user = userService.getUserByPrincipal(address)!!
        val creatorId = user.userId
        val project = projectService.getProject(projectId)!!
        val checkAddress = when (project.chain) {
            "ton" -> user.ton.tonWallet ?: ""
            "sui" -> user.suiAddress
            "evm" -> user.evm.evmWallet ?: ""
            else -> ""
        }
        privilegeCheck.checkProjectAdmin(user.userId, project, checkAddress)
        val campaignTotals = campaignService.getCampaignTotalsByProjectId(projectId)!!
            .filter { it.campaign.status != CampaignStatus.DELETED }
//                    && it.campaign.status != CampaignStatus.UNDER_REVIEWED }
        for (campaignTotal in campaignTotals) {
            campaignTotal.participantNum =
                campaignService.getParticipantNumsByCampaignId(campaignTotal.campaign.campaignId)
        }
//        val eligibles = credentialService.getCredentialEligibleCount(crs.map { it.credentialId })
//        crs.forEach { it.eligibleCount = eligibles[it.credentialId]?:0 }
        return campaignTotals
    }

    @GetMapping("/groups/{projectId}")
    fun getGroupsByProjectId(@PathVariable("projectId") projectId: Long): List<CredentialGroup>? {
        val address = SecurityContextHolder.getContext().authentication.principal.toString()
        val user = userService.getUserByPrincipal(address)!!
        val creatorId = user.userId
        //        val groups = credentialGroupService.getCredentialGroupByProjectId(projectId,creatorId)
//        val eligibles = credentialService.getCredentialEligibleCount(crs.map { it.credentialId })
//        crs.forEach { it.eligibleCount = eligibles[it.credentialId]?:0 }
        return campaignService.getGroupsByProjectId(projectId, creatorId)
    }

    @GetMapping("/participation")
    fun getParticipation(
        @RequestParam("campaignId") campaignId: Long,
        @RequestParam("page") page: Int,
        @RequestParam("limitNum") limitNum: Int
    ): Participation {
        val address = SecurityContextHolder.getContext().authentication.principal.toString()
        val user = userService.getUserByPrincipal(address)!!
        val curCampaign = campaignService.getCampaignById(campaignId)
            ?: throw ResponseStatusException(HttpStatus.NOT_FOUND, "campaign not found")
        val projectId = curCampaign.projectId
        val project = projectService.getProject(projectId)!!
        val checkAddress = when (project.chain) {
            "ton" -> user.ton.tonWallet ?: ""
            "sui" -> user.suiAddress
            "evm" -> user.evm.evmWallet ?: ""
            else -> ""
        }
        privilegeCheck.checkProjectAdmin(user.userId, project, checkAddress)
//        if (curCampaign.creatorId != user.userId) {
//            throw ResponseStatusException(HttpStatus.FORBIDDEN, "user is not the creator of the campaign")
//        }
        val participant = campaignService.getParticipationByIdV2(campaignId, page, limitNum)
        return participant
    }

    @GetMapping("/participationv2")
    fun getParticipationv2(
        @RequestParam("campaignId") campaignId: Long,
        @RequestParam("page") page: Int,
        @RequestParam("limitNum") limitNum: Int
    ): Participation {
        val participant = campaignService.getParticipationByIdV2(campaignId, page, limitNum)
        return participant
    }

    @GetMapping("/participants/{campaignId}")
    fun getParticipants(@PathVariable("campaignId") campaignId: Long): List<Participant> {
        val address = SecurityContextHolder.getContext().authentication.principal.toString()
        val user = userService.getUserByPrincipal(address)!!
        val curCampaign = campaignService.getCampaignById(campaignId)
            ?: throw ResponseStatusException(HttpStatus.NOT_FOUND, "campaign not found")
        val project = projectService.getProject(curCampaign.projectId)!!
        val checkAddress = when (project.chain) {
            "ton" -> user.ton.tonWallet ?: ""
            "sui" -> user.suiAddress
            "evm" -> user.evm.evmWallet ?: ""
            else -> ""
        }
        privilegeCheck.checkProjectAdmin(user.userId, project, checkAddress)
        val participants = campaignService.getParticipationById(campaignId).participantList
        for (participant in participants) {
            participant.credentials = emptyList()
            participant.verifiedCredentials = emptyList()
        }
        return participants
    }

    @GetMapping("/reward/{campaignId}")
    fun getRewardTab(@PathVariable("campaignId") campaignId: Long): CampaignRewardWithWinner {
        val address = SecurityContextHolder.getContext().authentication.principal.toString()
        val user = userService.getUserByPrincipal(address)!!
        val curCampaign = campaignService.getCampaignById(campaignId)
            ?: throw ResponseStatusException(HttpStatus.NOT_FOUND, "campaign not found")
        val project = projectService.getProject(curCampaign.projectId)!!
        val checkAddress = when (project.chain) {
            "ton" -> user.ton.tonWallet ?: ""
            "sui" -> user.suiAddress
            "evm" -> user.evm.evmWallet ?: ""
            else -> ""
        }
        privilegeCheck.checkProjectAdmin(user.userId, project, checkAddress)
        return campaignService.getCampaignRewardById(campaignId)
    }

    @GetMapping("/suspend/{campaignId}")
    fun suspendCampaign(@PathVariable("campaignId") campaignId: Long): CampaignTotal? {
        val address = SecurityContextHolder.getContext().authentication.principal.toString()
        val user = userService.getUserByPrincipal(address)!!
        val curCampaign = campaignService.getCampaignById(campaignId)
            ?: throw ResponseStatusException(HttpStatus.NOT_FOUND, "campaign not found")
        val userId = SecurityContextHolder.getContext().authentication.principal.toString().toLong()
        if (curCampaign.creatorId != user.userId) {
            throw ResponseStatusException(HttpStatus.FORBIDDEN, "user is not the creator of the campaign")
        }
        return campaignService.suspendCampaign(campaignId)
    }


    @PostMapping("update")
    fun updateCam(@RequestBody campaignTotal: CampaignTotal): CampaignTotal? {
        val address = SecurityContextHolder.getContext().authentication.principal.toString()
        val user = userService.getUserByPrincipal(address)!!
        val curCampaign = campaignService.getCampaignById(campaignTotal.campaign.campaignId)
            ?: throw ResponseStatusException(HttpStatus.NOT_FOUND, "campaign not found")
//        if (curCampaign.creatorId != user.userId) {
//            throw ResponseStatusException(HttpStatus.FORBIDDEN, "user is not the creator of the campaign")
//        }
        if (curCampaign.status == CampaignStatus.UNDER_REVIEWED || curCampaign.status == CampaignStatus.DELETED) {
            throw ResponseStatusException(HttpStatus.FORBIDDEN, "The current campaign status cannot be modified")
        }
        val project = projectService.getProject(curCampaign.projectId)!!
        val checkAddress = when (project.chain) {
            "ton" -> user.ton.tonWallet ?: ""
            "sui" -> user.suiAddress
            "evm" -> user.evm.evmWallet ?: ""
            else -> ""
        }
        privilegeCheck.checkProjectAdmin(user.userId, project, checkAddress)
        return campaignService.updateCampaignNew(campaignTotal, user.userId)
    }

    @GetMapping("/deleteCampaign/{campaignId}")
    fun deleteCam(@PathVariable("campaignId") campaignId: Long): Any {
        val principal = SecurityContextHolder.getContext().authentication.principal.toString()
        val user = userService.getUserByPrincipal(principal)!!

        val campaign = campaignService.getCampaignById(campaignId)
        if (campaign != null) {
            if (campaign.creatorId !== user.userId) {
                return "You are not allowed to delete a campaign"
            }
        }
        return SimpleResponseEntity.success("ok", campaignService.deleteCampaign(campaignId))
    }

    @GetMapping("/privateCredential/generateId")
    fun generatePrivateTmpCredentialId(): Long {
        val tmpCredentialId = idGenerator.getNewId()
        if (credentialService.getCredentialById(tmpCredentialId) == null) {
            return tmpCredentialId
        } else {
            return idGenerator.getNewId()
        }
    }

    // mock for test
    @GetMapping("/privateCredential/verify")
    fun privateTaskVerifyTest(
        @RequestParam("tonAddress") tonAddress: String
    ): Any {
        val isVerified = Random.nextBoolean()
        return mapOf(
            "code" to 200,
            "status" to "success",
            "message" to "msg",
            "isVerified" to isVerified
        )
    }

    // mock for test
    @GetMapping("/privateCredential/link")
    fun privateTaskLinkTest(
    ): Any {
        return mapOf(
            "code" to 200,
            "status" to "success",
            "message" to "msg",
            "link" to "https://t.me/premium"
        )
    }

    //init user sbt claimedType
    @GetMapping("/privateCredential/initSBT/lateNightDefi")
    fun initUserSBTClaimedType() {
        campaignService.initSbtClaimedType()
    }

    @GetMapping("/tvl/{credentialId}")
    fun countCampaignTVL(@PathVariable("credentialId") credentialId: Long): Any {
        // 56120827779162 - tonstaker, 56120827779166 - bemo, 56120827779170 - evaa , 56120827779175 - ston.fi, 56120827779179 - dedust, 56120827779183 - stormTrade
        val lateNightDefiCredentialId = 56120827779162
        val resultList = campaignService.getCampaignTVL(credentialId)
        var totalAmount = 0L
        for (result in resultList) {
            totalAmount += result.amount
        }
        return mapOf(
            "code" to 200,
            "status" to "success",
            "resultList" to resultList,
            "totalAmount" to totalAmount
        )
    }
}