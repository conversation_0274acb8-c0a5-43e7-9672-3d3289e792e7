package com.rewardoor.app.controller

import com.rewardoor.app.auth.AddressAuthentication
import com.rewardoor.app.auth.Jwt
import com.rewardoor.app.dto.SocialAccountBound
import com.rewardoor.app.dto.TonProofRequest
import com.rewardoor.app.dto.UserTonDto
import com.rewardoor.app.services.CredentialGroupService
import com.rewardoor.app.services.TonProofService
import com.rewardoor.app.services.UserService
import com.rewardoor.app.services.UserTwitterService
import com.rewardoor.enums.SocialType
import com.rewardoor.model.PassportAccounts
import com.rewardoor.model.UnbindInfo
import com.rewardoor.model.UserTon
import jakarta.servlet.http.HttpServletRequest
import org.springframework.http.ResponseEntity
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController

@RestController
@RequestMapping("ton-proof")
class TonProofController(
    private val tonProofService: TonProofService,
    private val userService: UserService,
    private val userCredentialGroupService: CredentialGroupService,
    private val userTwitterService: UserTwitterService
) {

    @GetMapping("generate-payload")
    fun generatePayload(): Any {
        val payload = tonProofService.generatePayload()
        return mapOf("tonProof" to payload)
    }

    @PostMapping("unbindCheck")
    fun unbindTonCheck(
        @RequestParam("address") tonAddress: String
    ): Any {
        val tonUser = userService.getTonWalletByAddress(tonAddress)
        if (tonUser != null) {
            val curUserId = tonUser.userId
            val isTonTheOnlyPassportAccount = isTonTheOnlyPassportAccount(curUserId)
            if (isTonTheOnlyPassportAccount) {
                return mapOf(
                    "code" to 400,
                    "message" to "The TON address is the last identity that can log in to this incentive passport. After disconnecting, your incentive passport will be disabled.\n" +
                            "\n" +
                            "We recommend binding a social account before disconnecting.\n" +
                            "\n" +
                            "Do you confirm to disconnect the TON address and disable the incentive passport?"
                )
            } else {
                return mapOf(
                    "code" to 200,
                    "message" to "OK"
                )
            }
        }
        return mapOf(
            "code" to 401,
            "message" to "The ton address is not exist."
        )
    }

    @PostMapping("unbind")
    fun unbindTon(
        @RequestParam("address") tonAddress: String
    ): Any {
        val principal = SecurityContextHolder.getContext().authentication as AddressAuthentication
        val addressUserId = principal.userId
        try {
            val tonUser = userService.getTonWalletByAddress(tonAddress)
            val curUserId = tonUser?.userId
            if (curUserId != addressUserId) {
                return mapOf(
                    "code" to 400,
                    "message" to "You cannot unbind the ton wallet that is not associated with the current passport."
                )
            }
            if (tonUser != null) {
                val userCredentials = userCredentialGroupService.getByUserId(curUserId)
                    .filter { it.socialType == SocialType.TON.code }
                if (userCredentials.isEmpty()) {
                    val unbindInfo = UnbindInfo(
                        userId = curUserId,
                        address = principal.address,
                        socialType = SocialType.DEFAULT.code,
                        socialId = "",
                        credentialId = 0
                    )
                    userService.addUnbindInfo(unbindInfo)
                }
                userService.unbindTonUser(curUserId)
                for (userCredential in userCredentials) {
                    val unbindInfo = UnbindInfo(
                        userId = curUserId,
                        address = principal.address,
                        socialType = SocialType.TON.code,
                        socialId = "",
                        credentialId = 0
                    )
                    userService.addUnbindInfo(unbindInfo)
                }
            }
            return mapOf("code" to 200, "message" to "OK")
        } catch (e: Exception) {
            println("bind exception : $e")
            return mapOf("code" to 401, "message" to "FAILED")
        }

    }

    private fun isTonTheOnlyPassportAccount(userId: Long): Boolean {
        val user = userService.getUserById(userId)!!
        val twitterUser = userTwitterService.getUserInfo(userId)
        if (user.ton.tonWallet.isNullOrEmpty()) {
            return false
        }
        return user.evm.evmWallet.isNullOrEmpty() && user.dcName.isNullOrEmpty() && user.tgName.isNullOrEmpty() && twitterUser == null
    }

    @PostMapping("verify")
    fun verifyProof(@RequestBody request: TonProofRequest): Any {
        val proof = request.tonProofItem
        val result = tonProofService.verifyProof(request.address, request.publicKey, proof, request.network)
        if (!result) {
            return mapOf("message" to "Invalid proof", "code" to 4001)
        }

        var isLogin = false
        val idPrincipal = SecurityContextHolder.getContext().authentication.principal.toString()
        if (idPrincipal == "anonymousUser") isLogin = true

        val userTon = userService.getTonWalletByAddress(request.frAddress)
        if (isLogin) {
            return withLogin(userTon, request.frAddress, request.publicKey)
        }

        val principal = SecurityContextHolder.getContext().authentication as AddressAuthentication
        val userInfo = userService.getUserByPrincipal(principal.principal)!!
        if (userTon != null && userTon.userId != userInfo.userId) {
            val passportA = PassportAccounts(
                userId = userInfo.userId,
                evmAddress = userInfo.evm.evmWallet ?: "",
                tonAddress = userInfo.ton.tonWallet ?: "",
                suiAddress = userInfo.suiAddress,
                twitterName = userInfo.twitterName,
                dcName = userInfo.dcName,
                tgName = userInfo.tgName
            )
            val tonUser = userService.getUserById(userTon.userId)!!
            val passportB = PassportAccounts(
                userId = tonUser.userId,
                evmAddress = tonUser.evm.evmWallet ?: "",
                tonAddress = tonUser.ton.tonWallet ?: "",
                suiAddress = tonUser.suiAddress,
                twitterName = tonUser.twitterName,
                dcName = tonUser.dcName,
                tgName = tonUser.tgName
            )
            val isAbleToMerge = isPassportsAbleToMerge(passportA, passportB)
            if (!isAbleToMerge) {
                return SocialAccountBound(
                    "This wallet is already bound to another user",
                    "TON",
                    request.frAddress,
                    passportA,
                    passportB
                )
            } else {
                val tonAddress = request.frAddress
                return mapOf(
                    "code" to 400,
                    "message" to "The address $tonAddress has been taken, you need to merge the accounts.",
                    "passportA" to passportA,
                    "passportB" to passportB
                )
            }
        }
        val bindResult = userService.bindTonWallet(userInfo.userId, request.frAddress, request.publicKey)
        if (!bindResult) {
            val passportA = PassportAccounts(
                userId = userInfo.userId,
                evmAddress = userInfo.evm.evmWallet ?: "",
                tonAddress = userInfo.ton.tonWallet ?: "",
                suiAddress = userInfo.suiAddress,
                twitterName = userInfo.twitterName,
                dcName = userInfo.dcName,
                tgName = userInfo.tgName
            )
            val tonUser = userService.getUserById(userTon!!.userId)!!
            val passportB = PassportAccounts(
                userId = tonUser.userId,
                evmAddress = tonUser.evm.evmWallet ?: "",
                tonAddress = tonUser.ton.tonWallet ?: "",
                suiAddress = tonUser.suiAddress,
                twitterName = tonUser.twitterName,
                dcName = tonUser.dcName,
                tgName = tonUser.tgName
            )

            return SocialAccountBound(
                "This wallet is already bound to another user",
                "TON",
                request.frAddress,
                passportA,
                passportB
            )
        }
        return mapOf("code" to 200, "user" to UserTonDto(request.frAddress, true))
    }

    private fun isPassportsAbleToMerge(passportA: PassportAccounts, passportB: PassportAccounts): Boolean {
        return when {
            (passportA.evmAddress != "" && passportB.evmAddress != "") || (passportA.tonAddress != "" && passportB.tonAddress != "")
                    || (passportA.suiAddress != "" && passportB.suiAddress != "")
                    || (passportA.twitterName != "" && passportB.twitterName != "") || (passportA.dcName != "" && passportB.dcName != "")
                    || (passportA.tgName != "" && passportB.tgName != "") -> false

            else -> true
        }
    }

    private fun withLogin(current: UserTon?, wallet: String, publicKey: String): ResponseEntity<Any> {
        if (current != null) {
            val cookie = Jwt.buildCookie(current.userId, "ton")
            val setCk = cookie.toString().split(";")[0] + ";"
            return ResponseEntity.ok().header("Set-Cookie", cookie.toString()).body(
                mapOf("code" to 200, "user" to UserTonDto(current.tonWallet, true))
            )
        } else {
            val userId = tonProofService.registerTgUser(wallet, publicKey)
            val cookie = Jwt.buildCookie(userId, "ton")
            val setCk = cookie.toString().split(";")[0] + ";"
            return ResponseEntity.ok().header("Set-Cookie", cookie.toString()).body(
                mapOf("code" to 200, "user" to UserTonDto(wallet, true))
            )
        }
    }

    @GetMapping("manifest.json")
    fun manifest(request: HttpServletRequest): Any {
        return mapOf(
            "url" to "https://tbook.com/",
            "name" to "TBOOK Incentive Passport",
            "iconUrl" to "https://tbook.com/logo.svg",
            "termsOfUseUrl" to "https://ton-connect.github.io/demo-dapp-with-react-ui/terms-of-use.txt",
            "privacyPolicyUrl" to "https://ton-connect.github.io/demo-dapp-with-react-ui/privacy-policy.txt"
        )
    }
}
