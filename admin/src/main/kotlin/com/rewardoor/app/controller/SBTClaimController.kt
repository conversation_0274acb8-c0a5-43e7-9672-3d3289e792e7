package com.rewardoor.app.controller

import org.json.JSONObject
import org.springframework.beans.factory.annotation.Value
import org.springframework.web.bind.annotation.*
import org.springframework.web.reactive.function.client.ExchangeStrategies
import org.springframework.web.reactive.function.client.WebClient
import reactor.core.publisher.Mono

@RestController
@RequestMapping("sbt")
class SBTClaimController(
    @Value("\${tg.ton-x-api-key}") val tonXApiKey: String,
    @Value("\${tg.ton-x-partner-id}") val tonXPartnerId: String
) {
    data class UserTonAddress(
        val wallet_address: String
    )

    private final val strategies = ExchangeStrategies.builder()
        .codecs { codecs -> codecs.defaultCodecs().maxInMemorySize(20 * 1024 * 1024) }
        .build()
    private val webClient = WebClient.builder().exchangeStrategies(strategies).build()

    private val partnerApiKeyList = listOf(
        mapOf("partnerId" to "sign", "api-key" to "ff0e6a6affecsdsd"),
        mapOf("partnerId" to "otherPartner", "api-key" to "another-api-key"),
        mapOf("partnerId" to "testPartner", "api-key" to "test-api-key")
    )

    private fun isValidPartnerAndApiKey(partnerId: String, apiKey: String): Boolean {
        return partnerApiKeyList.any { it["partnerId"] == partnerId && it["api-key"] == apiKey }
    }

    private fun isUserInWhiteList(tonAddress: String): Boolean {
        // todo
        return true
    }

    private fun getUserMintedSBTUrl(tonAddress: String, activityId: Int): String {
        val response = webClient
            .get()
            .uri("https://id.ton.org/v1/activities/$activityId/rewards/$tonAddress")
            .header("x-api-key", tonXApiKey)
            .header("x-partner-id", tonXPartnerId)
            .header("Content-Type", "application/json")
            .retrieve()
            .onStatus({ status -> status.is4xxClientError }, { clientResponse ->
                clientResponse.bodyToMono(String::class.java).flatMap { errorBody ->
                    println("Error response from server: $errorBody")
                    Mono.error(RuntimeException(errorBody))
                }
            })
            .bodyToMono(String::class.java)
            .block()
        val json = JSONObject(response)
        val rewardLink = if (json.getString("status") == "success") {
            json.getJSONObject("data").getString("reward_link")
        } else {
            ""
        }
        return rewardLink
    }

    @PostMapping("/activities/{activityId}/rewards")
    fun get(
        @PathVariable activityId: Int,
        @RequestBody userTonAddress: UserTonAddress,
        @RequestHeader("api-key") apiKey: String,
        @RequestHeader("partner-id") partnerId: String
    ): Any {
        if (!isValidPartnerAndApiKey(partnerId, apiKey)) {
            return mapOf(
                "code" to 403,
                "message" to "error",
                "link" to "Invalid API Key or Partner ID"
            )
        }
        if (!isUserInWhiteList(userTonAddress.wallet_address)) {
            return mapOf(
                "code" to 404,
                "message" to "error",
                "link" to "User is not in white list"
            )
        }
        val tonAddress = userTonAddress.wallet_address
        try {
            val response = webClient
                .post()
                .uri("https://id.ton.org/v1/activities/$activityId/rewards")
                .header("x-api-key", tonXApiKey)
                .header("x-partner-id", tonXPartnerId)
                .header("Content-Type", "application/json")
                .body(Mono.just(mapOf("wallet_address" to tonAddress)), Map::class.java)
                .retrieve()
                .onStatus({ status -> status.is4xxClientError }, { clientResponse ->
                    clientResponse.bodyToMono(String::class.java).flatMap { errorBody ->
                        println("Error response from server: $errorBody")
                        Mono.error(RuntimeException(errorBody))
                    }
                })
                .bodyToMono(String::class.java)
                .block()
            val json = JSONObject(response)
            val rewardLink = if (json.getString("status") == "success") {
                json.getJSONObject("data").getString("reward_link")
            } else {
                ""
            }
            if (rewardLink != null) {
                return mapOf(
                    "code" to 200,
                    "message" to "success",
                    "link" to rewardLink
                )
            }
        } catch (e: Exception) {
            val errorMessage = e.message ?: "Unknown error"
            val json = JSONObject(errorMessage)
            return if (json.getString("status") == "error") {
                if (json.getString("message") == "reward link with such activity id and wallet address already created") {
                    val mintedUrl = getUserMintedSBTUrl(tonAddress, activityId)
                    if (mintedUrl.isNotEmpty()) {
                        mapOf(
                            "code" to 200,
                            "message" to "success",
                            "link" to mintedUrl
                        )
                    } else {
                        mapOf("code" to 401, "message" to "error", "link" to json.getString("message"))
                    }
                }
                mapOf("code" to 401, "message" to "error", "link" to json.getString("message"))
            } else {
                mapOf("code" to 401, "message" to "error", "link" to json.getString("message"))
            }
        }
        return mapOf("code" to 401, "message" to "error", "link" to "")
    }

}