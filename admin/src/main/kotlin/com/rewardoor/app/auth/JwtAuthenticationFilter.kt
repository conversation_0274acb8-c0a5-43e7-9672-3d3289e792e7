package com.rewardoor.app.auth

import com.rewardoor.app.services.UserService
import jakarta.servlet.FilterChain
import jakarta.servlet.http.HttpServletRequest
import jakarta.servlet.http.HttpServletResponse
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.security.web.authentication.www.BasicAuthenticationFilter
import org.springframework.stereotype.Component


@Component
class JwtAuthenticationFilter(
    authManager: AddressAuthManager,
    private val userService: UserService
) : BasicAuthenticationFilter(authManager) {
    private val log = mu.KotlinLogging.logger {}

    override fun doFilterInternal(
        request: HttpServletRequest,
        response: HttpServletResponse,
        chain: FilterChain
    ) {
        val tokenCookie = request.cookies?.find {
            it.name.equals(Jwt.TOKEN_NAME) || it.name.equals(
                "Authorization",
                ignoreCase = true
            )
        }
        if (tokenCookie == null) {
            log.info("no cookie token found, path: {}", request.servletPath)
            chain.doFilter(request, response)
            return
        }

        val auth = Jwt.validateToken(tokenCookie.value) as AddressAuthentication
        if (!auth.isAuthenticated) {
            log.info("invalid token, path: {}, auth result: {}", request.servletPath, auth)
            chain.doFilter(request, response)
            return
        }

        val path = request.servletPath

        SecurityContextHolder.getContext().authentication = auth
        chain.doFilter(request, response)
    }
}
