package com.rewardoor.app.auth

import org.springframework.security.authentication.AbstractAuthenticationToken
import org.springframework.security.core.GrantedAuthority

class AddressAuthentication(
    val address: String,
    val sign: String = "",
    var userId: Long = 0,
    val nonce: String = "",
    var chain: String? = null,
    authorities: List<GrantedAuthority> = emptyList()): AbstractAuthenticationToken(authorities) {
    override fun getCredentials(): List<String> {
        return listOf(address, userId.toString())
    }

    override fun getPrincipal(): String {
        return userId.toString()
    }

    override fun toString(): String {
        return "AddressAuthentication(address='$address', sign='$sign', userId=$userId, chain=$chain)"
    }
}
