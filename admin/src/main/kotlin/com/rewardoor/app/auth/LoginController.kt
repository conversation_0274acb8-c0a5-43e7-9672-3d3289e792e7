package com.rewardoor.app.auth

import com.rewardoor.app.services.NonceService
import com.rewardoor.app.services.UserService
import com.rewardoor.app.services.UserTwitterService
import com.rewardoor.model.PassportAccounts
import io.swagger.v3.oas.annotations.Operation
import jakarta.servlet.http.HttpServletResponse
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseCookie
import org.springframework.http.ResponseEntity
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController
import java.util.*
import java.util.concurrent.ConcurrentHashMap

@RestController
class LoginController(
    private val manager: AddressAuthManager,
    private val userService: UserService,
    private val nonceService: NonceService,
    private val userTwitterService: UserTwitterService
) {
    @PostMapping("/authenticate")
    @Operation(summary = "login using wallet", description = "login with address value and sign message")
    fun login(
        @RequestParam("address") address: String,
        @RequestParam("sign") sign: String,
        response: HttpServletResponse
    ): ResponseEntity<Any> {
        val (r, newUser) = tryAuthenticate(address, sign)
        return if (r.isSuccess) {
            val cookie = Jwt.buildCookie(r.getOrNull()!!, "eth")
            ResponseEntity.ok().header(HttpHeaders.SET_COOKIE, cookie.toString())
                .body(mapOf("code" to 200, "newUser" to newUser))
        } else {
            ResponseEntity.status(HttpStatus.FORBIDDEN).body("login failed")
        }
    }

    private fun tryAuthenticate(address: String, sign: String): Pair<Result<AddressAuthentication>, Boolean> {
        val nonce = nonceService.getAndInvalidateNonce(address.lowercase())
        val authentication = AddressAuthentication(address, sign, 0, nonce.orEmpty())
        val authResult = manager.authenticate(authentication) as AddressAuthentication
        return if (authResult.isAuthenticated) {
            var newUser = false
            var user = userService.getUserByAddress(address)
            if (user == null) {
                user = userService.registerUserByWallet(address)
                newUser = true
            }
            authResult.userId = user.userId
            Result.success(authResult) to newUser
        } else {
            Result.failure<AddressAuthentication>(Exception()) to false
        }
    }

    @GetMapping(path = ["signout", "logout"])
    @Operation(summary = "退出登录", description = "")
    fun logout(response: HttpServletResponse): ResponseEntity<Any> {
        val cookie = ResponseCookie.from(Jwt.TOKEN_NAME, "").secure(true).sameSite("none")
            .httpOnly(true).maxAge(0).build()
        return ResponseEntity.ok().header(HttpHeaders.SET_COOKIE, cookie.toString())
            .body("logout success")
    }

    @GetMapping("nonce")
    fun getNonce(@RequestParam("address") address: String): String {
        val message = "sign in to rewardoor, nonce: ${UUID.randomUUID()}, timestamp: ${System.currentTimeMillis()}"
        nonceService.addNonce(address.lowercase(), message)
        return message
    }
}
