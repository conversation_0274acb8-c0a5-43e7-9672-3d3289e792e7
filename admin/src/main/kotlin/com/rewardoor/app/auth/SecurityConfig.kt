package com.rewardoor.app.auth

import jakarta.servlet.DispatcherType
import org.springframework.beans.factory.annotation.Value
import org.springframework.boot.web.servlet.FilterRegistrationBean
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.core.Ordered
import org.springframework.http.HttpStatus
import org.springframework.security.config.Customizer
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity
import org.springframework.security.config.annotation.web.builders.HttpSecurity
import org.springframework.security.config.http.SessionCreationPolicy
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder
import org.springframework.security.web.SecurityFilterChain
import org.springframework.security.web.authentication.AuthenticationFailureHandler
import org.springframework.security.web.authentication.HttpStatusEntryPoint
import org.springframework.security.web.util.matcher.RegexRequestMatcher
import org.springframework.web.cors.CorsConfiguration
import org.springframework.web.cors.UrlBasedCorsConfigurationSource
import org.springframework.web.filter.CorsFilter
import java.time.Duration


@Configuration
@EnableMethodSecurity
class SecurityConfig {
    @Bean
    fun filterChain(http: HttpSecurity, jwtTokenFilter: JwtAuthenticationFilter): SecurityFilterChain {
        http.sessionManagement { s -> s.sessionCreationPolicy(SessionCreationPolicy.STATELESS) }

            .exceptionHandling{ s -> s.authenticationEntryPoint(HttpStatusEntryPoint(HttpStatus.UNAUTHORIZED)) }

                .csrf{c -> c.disable() }
                .authorizeHttpRequests{ a -> a
                    .requestMatchers("/authenticate", "/nonce", "/logout", "/signout", "/zkproxy/**",
                        "/twitter/login/**","/project/byUrl/**", "/tg/callback/**", "/zk/salt", "/bk/**",
                        "/tg/tma/**", "/tg/webhook", "/ton-proof/**", "/game_airdrop/check", "/partner/check-credential",
                        "/cb/compensate", "tg_sign/webhook", "/sui/nonce", "/sui/login",
                        "/swagger-ui/**", "/v3/api-docs/**", "/actuator/**").permitAll()
                    .requestMatchers(
                        RegexRequestMatcher("^/campaignNew/[\\d]+", null),
                        RegexRequestMatcher("^/project/[\\d]+", null),
                        RegexRequestMatcher("^/company/[\\d]+", null),
                        RegexRequestMatcher("^/project/explore", null),
                        RegexRequestMatcher("^/project/home/<USER>", null),
                        RegexRequestMatcher("^/project/setHome/.*", null),
                        RegexRequestMatcher("^/project/coreData", null),
                        RegexRequestMatcher("^/campaignNew/project/[\\d]+", null),
                        RegexRequestMatcher("^/campaignNew/consumer/[\\d]+", null),
                        RegexRequestMatcher("^/campaignNew/consumer/.*", null),
                        RegexRequestMatcher("^/campaignNew/defi", null),
                        RegexRequestMatcher("^/campaignNew/tvl/[\\d]+", null),
                        RegexRequestMatcher("^/campaignNew/privateCredential/.*", null),
                        RegexRequestMatcher("^/campaignNew/verify/.*", null),
                        RegexRequestMatcher("^/wiseScore/.*", null),
                        RegexRequestMatcher("^/airDrop/.*", null),
                        RegexRequestMatcher("^/luckyDraw/.*", null),
                        RegexRequestMatcher("^/tPoints/.*", null),
                        RegexRequestMatcher("^/vanguard/.*", null),
                        RegexRequestMatcher("^/ai-agent/.*", null),
                        RegexRequestMatcher("^/user-push/.*", null),
                        RegexRequestMatcher("^/ton-sync/.*", null)
                        ).permitAll()
                    .dispatcherTypeMatchers(DispatcherType.ERROR).permitAll()
                    .anyRequest().authenticated()
                }

            .httpBasic(Customizer.withDefaults())
                .addFilter(jwtTokenFilter)

        return http.build()
    }

    @Bean
    fun passWordEncoder(): BCryptPasswordEncoder {
        return BCryptPasswordEncoder()
    }

    @Bean
    fun corsFilter(@Value("\${cors.origins}") origins: List<String>): FilterRegistrationBean<*>? {
        val source = UrlBasedCorsConfigurationSource()
        val config = CorsConfiguration()
        config.allowCredentials = true
        config.allowedOrigins = origins
        config.allowedHeaders = arrayListOf("*", "X-Requested-With", "Content-Type", "Authorization")
        config.allowedMethods = arrayListOf("GET", "POST", "HEAD", "PUT", "DELETE", "PATCH", "OPTIONS")
        config.setMaxAge(Duration.ofDays(1))
        source.registerCorsConfiguration("/**", config)
        val bean = FilterRegistrationBean(CorsFilter(source))
        bean.order = Ordered.HIGHEST_PRECEDENCE
        return bean
    }

    @Bean
    fun authFailureHandler(): AuthenticationFailureHandler {
        return CustomAuthenticationFailureHandler()
    }

}
