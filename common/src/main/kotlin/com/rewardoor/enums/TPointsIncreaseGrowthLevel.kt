package com.rewardoor.enums

import com.fasterxml.jackson.annotation.JsonCreator
import com.fasterxml.jackson.annotation.JsonValue

enum class TPointsIncreaseGrowthLevel(@JsonValue val code: Int, val pointsNum: Int) {
    LEVEL_1(1, 2000),
    LEVEL_2(2, 4000),
    LEVEL_3(3, 8000),
    LEVEL_4(4, 16000),
    LEVEL_5(5, 32000),
    LEVEL_6(6, 64000),
    LEVEL_7(7, 128000),
    LEVEL_8(8, 256000);

    companion object {
        @JvmStatic
        @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
        fun getByCode(code: Int) = values().first { it.code == code }
    }
}