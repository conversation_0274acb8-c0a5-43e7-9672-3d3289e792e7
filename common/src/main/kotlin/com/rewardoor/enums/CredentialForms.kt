package com.rewardoor.enums

enum class CredentialForms(val json: String) {
    TELEGRAM_GROUP(
        """
        {
            "list": [
                {
                    "component": "HTML",
                    "html": "<p>Add TBOOK support bot as an admin to your group or channel</p> <a href='https://t.me/tbook_sign_bot' style='color:#1D9BF0' class='underline' target='_blank'> Invite bot </a >"
                },
                {
                    "name": "link",
                    "label": "Group Invite Link",
                    "component": "Input",
                    "componentProps": {
                        "placeholder": "Please paste the invite link to your telegram group"
                    },
                    "rules": [
                        {
                            "required": true,
                            "message": "Please input the invite link"
                        },
                        {
                            "type": "url",
                            "message": "Please input a valid url"
                        }
                    ]
                }
            ]
        }
    """
    ),
    TELEGRAM_CHANNEL(
        """
        {
            "list": [
                {
                    "component": "HTML",
                    "html": "<p>Add TBOOK support bot as an admin to your group or channel</p> <a href='https://t.me/tbook_sign_bot' style='color:#1D9BF0' class='underline' target='_blank'> Invite bot </a >"
                },
                {
                    "name": "link",
                    "label": "Channel Invite Link",
                    "component": "Input",
                    "componentProps": {
                        "placeholder": "Please paste the invite link to your telegram channel"
                    },
                    "rules": [
                        {
                            "required": true,
                            "message": "Please input the invite link"
                        },
                        {
                            "type": "url",
                            "message": "Please input a valid url"
                        }
                    ]
                }
            ]
        }
    """
    ),
    TWITTER(
        """
        {
            "list": [
                {
                    "name": "link",
                    "label": "Tweet Link",
                    "component": "Input",
                    "componentProps": {
                        "placeholder": "Paste tweet link here"
                    },
                    "rules": [
                        {
                            "required": true,
                            "message": "Please input your tweet link"
                        },
                        {
                            "type": "url",
                            "message": "Please input a valid url"
                        }
                    ]
                }
            ]
        }
    """
    ),
    SPACE(
        """
        {
            "list": [
                {
                    "name": "link",
                    "label": "Tweet Space Link",
                    "component": "Input",
                    "componentProps": {
                        "placeholder": "Paste twitter space link here"
                    },
                    "rules": [
                        {
                            "required": true,
                            "message": "Please input your tweet link"
                        },
                        {
                            "type": "url",
                            "message": "Please input a valid url"
                        }
                    ]
                }
            ]
        }
    """
    ),
    DISCORD_SERVICE(
        """
        {
            "list": [
                {
                    "name": "link",
                    "label": "Discord Server URL",
                    "component": "Input",
                    "componentProps": {
                        "placeholder": "https://discord.gg/xxxx"
                    },
                    "rules": [
                        {
                            "required": true,
                            "message": "Please input the Discord Server URL"
                        },
                        {
                            "type": "url",
                            "message": "Please input a valid url"
                        }
                    ]
                }
            ]
        }
    """
    ),
    DISCORD_ROLE(
        """
        {
            "list": [
                {
                    "component": "HTML",
                    "html": "<a style='color:#1D9BF0' class='underline' href='https://app.gitbook.com/o/XmLEuzCUK0IIbhY5X44k/s/xLOTfURQ4EC9jmYQjFob/how-to-get-role-id-in-discord' target='_blank'>How to get Role ID in Discord</a>"
                },
                {
                    "name": "link",
                    "label": "Discord Server URL",
                    "component": "Input",
                    "componentProps": {
                        "placeholder": "https://discord.gg/xxxx"
                    },
                    "rules": [
                        {
                            "required": true,
                            "message": "Please input the Verify Discord Role"
                        },
                        {
                            "type": "url",
                            "message": "Please input a valid url"
                        }
                    ]
                },
                {
                    "name": "roleId",
                    "label": "Role ID",
                    "component": "Input",
                    "componentProps": {
                        "placeholder": "Enter Role ID"
                    },
                    "rules": [
                        {
                            "required": true,
                            "message": "Please input the Role ID"
                        }
                    ]
                },
                {
                    "name": "roleName",
                    "label": "Role Name",
                    "component": "Input",
                    "componentProps": {
                        "placeholder": "Enter Role Name"
                    },
                    "rules": [
                        {
                            "required": true,
                            "message": "Please input the Role Name"
                        }
                    ]
                }
            ]
        }
    """
    ),
    VISIT_PAGE_OR_SITE(
        """
    {
          "list": [
            {
              "name": "visitPageName",
              "label": "Name",
              "component": "Input",
              "componentProps": {
                "placeholder": "Please enter the page name or site name"
              },
              "rules": [
                {
                  "required": true,
                  "message": "Please input the name",
                  "type": null
                }
              ]
            },
            {
              "name": "link",
              "label": "Link",
              "component": "Input",
              "componentProps": {
                "placeholder": "Please paste the link the users need to visit"
              },
              "rules": [
                {
                  "required": true,
                  "message": "Please input the link",
                  "type": null
                },
                {
                  "required": false,
                  "message": "Please input a valid url",
                  "type": "url"
                }
              ]
            }
          ]
    }
    """
    ),
    REGISTER_BY_TWITTER(
        """
    {
        "list": [
            {
                "name": "name",
                "label": "Name",
                "component": "Input",
                "componentProps": {
                    "placeholder": "Please paste the name"
                },
                "rules": [
                    {
                        "required": true,
                        "message": "Please input the name",
                        "type": null
                    }
                ]
            },
            {
                "name": "link",
                "label": "Link",
                "component": "Input",
                "componentProps": {
                    "placeholder": "Please paste the link the users need to visit"
                },
                "rules": [
                    {
                        "required": true,
                        "message": "Please input the link",
                        "type": null
                    },
                    {
                        "required": false,
                        "message": "Please input a valid url",
                        "type": "url"
                    }
                ]
            }
        ]
    }
    """
    ),
    SIGN_MESSAGE(
        """
    {
        "list": [
            {
                "name": "name",
                "label": "Event Name",
                "component": "Input",
                "componentProps": {
                    "placeholder": "Please input the name"
                },
                "rules": [
                    {
                        "required": true,
                        "message": "Please input the name"
                    }
                ]
            }
        ]
    }
    """
    ),
    SNAPSHOT(
        """
        {
            "list": [
                {
                    "name": "link",
                    "label": "Snapshot Proposal URL",
                    "component": "Input",
                    "componentProps": {
                        "placeholder": "Please enter a specific Snapshot Proposal"
                    },
                    "rules": [
                        {
                            "required": true,
                            "message": "Please input your Snapshot link"
                        },
                        {
                            "type": "url",
                            "message": "Please input a valid url"
                        }
                    ]
                }
            ]
        }
    """
    ),AIRDROP_ADDRESS_AGGREGATION(
        """
    {
        "list": [
            {
                "name": "title",
                "label": "Title",
                "component": "Input",
                "componentProps": {
                    "placeholder": "Enter the aggregation Title, like Submit Exchange Address"
                },
                "rules": [
                    {
                        "required": true,
                        "message": "Please input the link",
                        "type": null
                    }
                ]
            },
            {
                "name": "description",
                "label": "Description",
                "component": "Input",
                "componentProps": {
                    "placeholder": "Enter the description..."
                },
                "rules": [
                    {
                        "type": null
                    }
                ]
            },
            {
                "name": null,
                "label": null,
                "component": "HTML",
                "componentProps": null,
                "rules":  [
                    {
                        "type": null
                    }
                ],
                "html": "<p>After users fill in their addresses, you can view the address list in the participant list.</p >"
          }
        ]
    }
    """
    )
}