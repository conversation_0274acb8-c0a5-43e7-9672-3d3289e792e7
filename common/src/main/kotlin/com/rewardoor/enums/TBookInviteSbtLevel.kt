package com.rewardoor.enums

import com.rewardoor.model.SBTReward
import org.springframework.core.env.Environment

class InviteSbtInfo(
    val level: Int,
    val taskName: String,
    val invitesRequired: Int,
    val title: String
)

class CombinedSbtInfo(
    val sbtId: Long,
    val claimedType: Int,
    val activityUrl: String,
    val activityId: Int,
    val picUrl: String,
    val level: Int,
    val taskName: String,
    val invitesRequired: Int,
    val title: String
)

class ActIdInfo(
    val level: Int,
    val id: Int,
    val sbtId: Long,
)

data class LevelCheckResult(
    val currentLevel: Int,          // 当前等级
    val nextLevel: Int?,            // 下一等级（如果存在）
    val canLevelUp: Boolean,        // 是否可以升级
)

enum class TBookInviteSbtLevel(val level: Int, val taskName: String, val invitesRequired: Int, val title: String) {
    CONNECTOR(1, "Invite 1 friend to claim", 1, "TBook Connector"),
    BUILDER(2, "Invite 5 friends to claim", 5, "TBook Builder"),
    INFLUENCER(3, "Invite 10 friends to claim", 10, "TBook Influencer"),
    TRAILBLAZER(4, "Invite 20 friends to claim", 20, "TBook Trailblazer"),
    VISIONARY(5, "Invite 50 friends to claim", 50, "TBook Visionary"),
    LEGEND(6, "Invite 100 friends to claim", 100, "TBook Legend");

//    CONNECTOR(1, "Invite 1 friend to claim",1, "TBook Connector"),
//    BUILDER(2, "Invite 3 friends to claim",3, "TBook Builder"),
//    INFLUENCER(3, "Invite 5 friends to claim",5, "TBook Influencer"),
//    TRAILBLAZER(4, "Invite 7 friends to claim",7, "TBook Trailblazer"),
//    VISIONARY(5, "Invite 8 friends to claim",8, "TBook Visionary"),
//    LEGEND(6, "Invite 10 friends to claim",10, "TBook Legend");

    companion object {
        private val STAG_ACTIVITY_IDS = listOf(
            ActIdInfo(1, 601, 642362644707),
            ActIdInfo(2, 602, 642365264712),
            ActIdInfo(3, 603, 642366454717),
            ActIdInfo(4, 604, 642475544722),
            ActIdInfo(5, 605, 642476674727),
            ActIdInfo(6, 606, 642477224732),
        )
        private val PROD_ACTIVITY_IDS = listOf(
            ActIdInfo(1, 1469, 646591101739993),
            ActIdInfo(2, 1470, 646593591740045),
            ActIdInfo(3, 1471, 646595861740086),
            ActIdInfo(4, 1472, 646597741740130),
            ActIdInfo(5, 1473, 646600411740190),
            ActIdInfo(6, 1474, 646602201740230)
        )

        fun getActivityIdsByEnv(env: Environment): Pair<List<Int>, Map<Int, ActIdInfo>> {
            val activityInfos = if (env.activeProfiles.contains("prod")) {
                PROD_ACTIVITY_IDS
            } else {
                STAG_ACTIVITY_IDS
            }
            return Pair(
                activityInfos.map { it.id },
                activityInfos.associateBy { it.id }
            )
        }

        fun combineSbtInfo(sbts: List<SBTReward>, activityIdMap: Map<Int, ActIdInfo>): List<CombinedSbtInfo> {
            val levelMap = values().associateBy { it.level }

            return sbts.mapNotNull { sbt ->
                val activityInfo = sbt.activityId.let { activityIdMap[it] } ?: return@mapNotNull null
                val level = levelMap[activityInfo.level] ?: return@mapNotNull null

                CombinedSbtInfo(
                    sbtId = sbt.sbtId,
                    claimedType = sbt.claimedType,
                    activityUrl = sbt.activityUrl,
                    activityId = sbt.activityId,
                    picUrl = sbt.picUrl,
                    level = level.level,
                    taskName = level.taskName,
                    invitesRequired = level.invitesRequired,
                    title = level.title
                )
            }
        }

        fun getLevelByInvites(invites: Int): Int {
            return values().findLast { invites >= it.invitesRequired }?.level ?: 1
        }

        fun getActivityIdByInvites(inviteeCount: Int, env: Environment): ActIdInfo? {
            val level = values().findLast { inviteeCount >= it.invitesRequired }?.level ?: 1
            println("new level - ${level}")
            val activityInfos = if (env.activeProfiles.contains("prod")) {
                PROD_ACTIVITY_IDS
            } else {
                STAG_ACTIVITY_IDS
            }
            println("current level's activityId - ${activityInfos.find { it.level == level }?.id ?: 0}")
            return activityInfos.find { it.level == level }
        }

        fun checkIsIntoNextLevel(userId: Long, inviteeCount: Int): LevelCheckResult {
            // 获取当前等级
            val currentLevel = values().findLast { (inviteeCount - 1) >= it.invitesRequired }?.level ?: 0

            // 获取下一等级（如果存在）
            val nextLevel = values().find { it.level == currentLevel + 1 }

            // 计算是否可以升级和距离下一等级还需要的邀请数
            val canLevelUp = nextLevel?.let { inviteeCount >= it.invitesRequired } ?: false
            println("can level up : user Id $userId inviteeCount is $inviteeCount currentLevel is $currentLevel and nextLevel is $nextLevel")
//            val remainingInvites = nextLevel?.let { it.invitesRequired - inviteeCount }

            return LevelCheckResult(
                currentLevel = currentLevel,
                nextLevel = nextLevel?.level,
                canLevelUp = canLevelUp
            )
        }

        fun getHistorySbtListByStage1(inviteeCount: Int): List<TBookInviteSbtLevel> {
            return values().filter { it.invitesRequired <= inviteeCount }
        }
    }
}