package com.rewardoor.enums

import com.fasterxml.jackson.annotation.JsonCreator
import com.fasterxml.jackson.annotation.JsonValue

enum class TPointsIncreaseLimitLevel(@JsonValue val code: Int, val pointsNum: Int) {
    LEVEL_1(1, 400),
    LEVEL_2(2, 800),
    LEVEL_3(3, 1600),
    LEVEL_4(4, 3200),
    LEVEL_5(5, 6400),
    LEVEL_6(6, 12800),
    LEVEL_7(7, 25600),
    LEVEL_8(8, 51200),
    LEVEL_9(9, 102400),
    LEVEL_10(10, 204800);

    companion object {
        @JvmStatic
        @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
        fun getByCode(code: Int) = values().first { it.code == code }
    }
}