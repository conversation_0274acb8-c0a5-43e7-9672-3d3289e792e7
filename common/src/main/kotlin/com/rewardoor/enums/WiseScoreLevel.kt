package com.rewardoor.enums

import com.fasterxml.jackson.annotation.JsonCreator
import com.fasterxml.jackson.annotation.JsonValue

enum class WiseScoreLevel(
    @JsonValue val code: Int,
    val totalScore: Int,
    val wiseCreditLevelName: String,
    val inviteTotalTimes: Int
) {
    LEVEL_0(0, 20000, "Novice", 3),
    LEVEL_1(1, 50000, "Adept", 3),
    LEVEL_2(2, 200000, "Pathfinder", 20),
    LEVEL_3(3, 500000, "Strategist", 50),
    LEVEL_4(4, 1000000, "Mentor", 100),
    LEVEL_5(5, 2147483647, "Apex", 200);

    companion object {
        @JvmStatic
        @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
        fun getByCode(code: Int) = values().first { it.code == code }
    }
}