package com.rewardoor.enums

import com.fasterxml.jackson.annotation.JsonCreator
import com.fasterxml.jackson.annotation.JsonValue

enum class Actions(@JsonValue val code: Int, val desc: String) {
    DEFAULT(0, "unknown"),
    LIKE(1, "like"),
    <PERSON><PERSON><PERSON><PERSON>(2, "follow"),
    RET<PERSON>EE<PERSON>(3, "retweet"),
    <PERSON>AC<PERSON>(4, "space");

    companion object {
        @JvmStatic
        @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
        fun getByCode(code: Int) = values().first { it.code == code }
    }

}