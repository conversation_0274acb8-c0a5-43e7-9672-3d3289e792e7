package com.rewardoor.enums

import com.fasterxml.jackson.annotation.JsonCreator
import com.fasterxml.jackson.annotation.JsonValue

enum class Tags(@JsonValue val code: Int, val desc: String) {
    Governance(0, "Governance"),
    Community(1, "Community"),
    Trade(2, "Trade"),
    ProductTest(3, "ProductTest"),
    My(4, "My");

    companion object {
        @JvmStatic
        @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
        fun getByCode(code: Int) = values().first { it.code == code }
    }
}