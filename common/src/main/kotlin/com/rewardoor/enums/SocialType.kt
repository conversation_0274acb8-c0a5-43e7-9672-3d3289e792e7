package com.rewardoor.enums

import com.fasterxml.jackson.annotation.JsonCreator
import com.fasterxml.jackson.annotation.JsonValue

enum class SocialType(@JsonValue val code: Int, val desc: String) {
    DEFAULT(0, "unknown"),
    TWITTER(1, "twitter/X"),
    TELEGRAM(2, "telegram"),
    DISCORD(3, "discord"),
    TON(4, "ton");

    companion object {
        @JvmStatic
        @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
        fun getByCode(code: Int) = SocialType.values().first { it.code == code }
    }

}