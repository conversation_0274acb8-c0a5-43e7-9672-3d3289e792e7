package com.rewardoor.enums

import com.fasterxml.jackson.annotation.JsonCreator
import com.fasterxml.jackson.annotation.JsonValue
import com.rewardoor.model.VanguardPrivilege

enum class TPointsVanguardLevel(
    @JsonValue val code: Int,
    val pointsNum: Int,
    val nextLevelPointsNum: Int,
    val vanguardPrivilege: VanguardPrivilege? = null
) {
    LEVEL_0(0, 0, 500000, VanguardPrivilege(200, 1, 0, 0, 0)),
    LEVEL_1(1, 500000, 1000000, VanguardPrivilege(500, 1, 0, 0, 0)),
    LEVEL_2(2, 1000000, 2500000, VanguardPrivilege(1000, 1, 0, 1, 0)),
    LEVEL_3(3, 2500000, 5000000, VanguardPrivilege(3000, 1, 1, 2, 0)),
    LEVEL_4(4, 5000000, 12500000, VanguardPrivilege(10000, 1, 1, 4, 1)),
    LEVEL_5(5, 12500000, 25000000, VanguardPrivilege(30000, 1, 1, 8, 1)),
    LEVEL_6(6, 25000000, 50000000, VanguardPrivilege(100000, 1, 1, 16, 1)),
    LEVEL_7(7, 50000000, 100000000, VanguardPrivilege(500000, 1, 1, 32, 1)),
    LEVEL_8(8, 100000000, 0, VanguardPrivilege(2147483647, 1, 1, 32, 1));

    companion object {
        @JvmStatic
        @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
        fun getByCode(code: Int) = values().first { it.code == code }
    }
}