package com.rewardoor.enums

import com.fasterxml.jackson.annotation.JsonCreator
import com.fasterxml.jackson.annotation.JsonValue

enum class TPointsBuyCardsLevel(@JsonValue val cardCnt: Int, val pointsNum: Int) {
    LEVEL_1(1, 150),
    LEVEL_2(3, 300),
    LEVEL_3(10, 800);

    companion object {
        @JvmStatic
        @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
        fun getByCardCnt(cardCnt: Int) = values().first { it.cardCnt == cardCnt }
    }
}