package com.rewardoor.enums

import com.rewardoor.model.SbtSetting

enum class WiseScoreUserStreakSbt(
    val streakDays: Int,
    val level: String,
    val sbtName: String,
    val sbtId: Long,
    val credentialId: Long
) {
    LEVEL1(3, "level1", "WISE Beginner", 676179382440783, 676179382440781),
    <PERSON>E<PERSON>L2(5, "level2", "WISE Seeker", 676181422440788, 676181422440786),
    <PERSON>E<PERSON>L3(8, "level3", "WISE Rover", 676183642440793, 676183642440791),
    <PERSON><PERSON><PERSON>L4(12, "level4", "WISE Trekker", 676186482440798, 676186482440796),
    <PERSON>E<PERSON>L5(17, "level5", "WISE Explorer", 676189042440803, 676189042440801),
    <PERSON><PERSON><PERSON><PERSON>6(23, "level6", "WISE Journeyer", 676191272440808, 676191272440806),
    <PERSON>E<PERSON>L7(30, "level7", "WISE Champion", 676193162440813, 676193162440811),
    LEVEL8(38, "level8", "WISE Pursuer", 676195422440820, 676195422440818),
    LEVEL9(46, "level9", "WISE Conqueror", 676196932440826, 676196932440824),
    LEVEL10(55, "level10", "WISE Hero", 676198432440833, 676198432440831),
    LEVEL11(60, "level11", "WISE Mastermind", 676200192440838, 676200192440836),

    // update
    LEVEL12(66, "level12", "WISE Virtuoso", 730344682551911, 730344682551909),
    LEVEL13(72, "level13", "WISE Ascendant", 730346732551917, 730346732551915),
    LEVEL14(80, "level14", "WISE Luminary", 730354162551925, 730354162551923),
    LEVEL15(88, "level15", "WISE Visionary", 730357172551934, 730357172551932),
    LEVEL16(92, "level16", "WISE Guardian", 730359562551942, 730359562551940),
    LEVEL17(100, "level17", "WISE Paramount", 730360812551949, 730360812551947),

    // update
    LEVEL18(105, "level18", "WISE Streak Apex", 763168992581385, 763168992581383),
    LEVEL19(112, "level19", "WISE Streak Radiant", 763168472581380, 763168472581378),
    LEVEL20(118, "level20", "WISE Streak Sentinel", 763167972581371, 763167972581369),
    LEVEL21(125, "level21", "WISE Streak Envoy", 763167362581366, 763167362581364),
    LEVEL22(130, "level22", "WISE Streak Eternan", 763166812581361, 763166812581359),
    LEVEL23(136, "level23", "WISE Streak Nova", 763166302581356, 763166302581354),
    LEVEL24(142, "level24", "WISE Streak Ascella", 763165682581351, 763165682581349),
    LEVEL25(150, "level25", "WISE Streak Infinity", 763165162581346, 763165152581344),

    // update
    LEVEL26(158, "level26", "WISE Ember", 806489752605184, 806489752605182),
    LEVEL27(166, "level27", "WISE Crest", 806498342605205, 806498342605203),
    LEVEL28(172, "level28", "WISE Rhythm", 806544862605240, 806544862605238),
    LEVEL29(180, "level29", "WISE Titan", 806545972605246, 806545972605244),
    LEVEL30(188, "level30", "WISE Thread", 806547872605251, 806547872605249),
    LEVEL31(195, "level31", "WISE Pulse", 806548512605262, 806548512605260),
    LEVEL32(200, "level32", "WISE Eternal", 806549182605267, 806549182605265);

    companion object {
        // 根据 streakDays 返回对应的 SbtLevel
        fun getByStreakDays(streakDays: Int): WiseScoreUserStreakSbt? {
            return values().lastOrNull { streakDays >= it.streakDays }
        }
    }
}