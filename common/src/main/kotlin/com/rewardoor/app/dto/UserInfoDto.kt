package com.rewardoor.app.dto

import com.fasterxml.jackson.annotation.JsonIgnore
import com.rewardoor.model.Project
import com.rewardoor.model.User
import com.rewardoor.model.UserSui
import com.rewardoor.model.UserZK

class UserInfoDto(val user: User, var projects: List<Project>,
                  val userTwitter: UserTwitterDto,
                  val userTg: UserTgDto,
                  val userDc: UserDcDto,
                  val userZk: UserZKDto,
                  val userTon: UserTonDto,
                  @JsonIgnore
                  val sui: UserSuiDto,
    val deployer: String = "0x3BEfF95bBB844015372075AaE6fE8Ff1E0DE5d27") {
    init {
        if (userTwitter.twitterProfileImage.isNotEmpty()) {
            user.avatar = userTwitter.twitterProfileImage
        } else {
            user.avatar = User.AVATAR_BASE + user.userId
        }
    }
    val newUser: Boolean get() = user.newUser

    var canCreateProject: Boolean = false

    val userSui: UserSuiDto get() {
        if (userZk.address.isNotEmpty()) {
            return UserSuiDto(userZk.address, true, true)
        }
        if (sui.address?.isNotEmpty() == true) {
            return UserSuiDto(sui.address, true, false)
        }
        return UserSuiDto("", false, false)
    }
}

class UserZKDto(
    var userId: Long = 0,
    var address: String = "",
    var issuer: String = "",
    @JsonIgnore
    var email: String = "",
) {
    companion object {
        fun fromModel(zk: UserZK): UserZKDto {
            return UserZKDto(zk.userId, zk.address ?: "", zk.displayIssuer(), zk.identity.orEmpty())
        }
    }
}
class UserTonDto(
        var address: String? = "",
        var binded: Boolean = false
)

class UserSuiDto(
    var address: String? = "",
    var binded: Boolean = false,
    var zkLogin: Boolean = false,
)