package com.rewardoor.app.dto

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonProperty

@JsonIgnoreProperties(ignoreUnknown = true)
class TgAuthCallbackReq(
    @JsonProperty("id")
    var id: Long,

    @JsonProperty("first_name")
    var firstName: String? = "",

    @JsonProperty("last_name")
    var lastName: String? = "",

    @JsonProperty("username")
    var username: String? = "",

    @JsonProperty("photo_url")
    var photoUrl: String? = "",

    @JsonProperty("auth_date")
    var authDate: Long = 0,

    @JsonProperty("hash")
    var hash: String? = "",

    @JsonProperty("language_code")
    var langCode: String? = "",

    @JsonProperty("allows_write_to_pm")
    var writeToPM: Boolean? = true
) {
}