package com.rewardoor.app.dto

import com.rewardoor.model.PassportAccounts

class SocialAccountBound(
    var message: String = "",
    var socialName: String = "",
    private var privateAddress: String = "",
    var passportA: PassportAccounts? = null,
    var passportB: PassportAccounts? = null
) {
    val code: Int = 4004

    val address: String
        get() {
            if (privateAddress.isEmpty()) return "Other"
            if (privateAddress.length < 32) return privateAddress
            return privateAddress.substring(0, 6) + "..." + privateAddress.substring(privateAddress.length - 4)
        }
}