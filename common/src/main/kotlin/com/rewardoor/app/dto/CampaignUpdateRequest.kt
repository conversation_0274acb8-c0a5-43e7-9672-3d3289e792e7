package com.rewardoor.app.dto

import com.fasterxml.jackson.annotation.JsonFormat
import com.rewardoor.model.CampaignStatus
import com.rewardoor.model.Credential
import java.time.Instant

class CampaignUpdateRequest(
    var campaignId: Long,
    var title: String? = null,
    var name: String? = null,
    var picUrl: String? = null,
    var description: String? = null,
    @JsonFormat(shape = JsonFormat.Shape.NUMBER, without = [JsonFormat.Feature.WRITE_DATE_TIMESTAMPS_AS_NANOSECONDS])
    var startAt: Instant? = null,
    @JsonFormat(shape = JsonFormat.Shape.NUMBER, without = [JsonFormat.Feature.WRITE_DATE_TIMESTAMPS_AS_NANOSECONDS])
    var endAt: Instant? = null,
    var status: CampaignStatus? = null,
    var reward: String? = null,
    var rewardAction: String? = null,
    var projectId: Long? = null,
    var creatorId: Long? = null,
    var points: Int? = null,
    var credentialId: Long? = null,
    var nft: Long? = null,
    var credentials: List<Credential>? = null
)