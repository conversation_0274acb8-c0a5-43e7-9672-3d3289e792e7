package com.rewardoor.app.dto

class TonProofRequest(
    val address: String,
    val tonProofItem: TonProofItem,
    val publicKey: String,
    val frAddress: String,
    val login: Boolean = false,
    val network: Int = -239,
)

class TonProofItem(
    val name: String = "ton_proof",
    val proof: Proof,
    val walletInit: String
)

class Proof(
    val timestamp: Long,
    val domain: Domain,
    val signature: String,
    val payload: String
)

class Domain(
    val lengthBytes: Int,
    val value: String
)