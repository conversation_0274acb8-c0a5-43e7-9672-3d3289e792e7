package com.rewardoor.app.dto

import com.rewardoor.model.ProjectExternalConfig

class ProjectExternalConfigDto(
    var appId: String = "",
    var appKey: String = "",
    var callbackUrl: String = "",
    var enable: Boolean = false
) {
    companion object{
        fun fromModel(model: ProjectExternalConfig): ProjectExternalConfigDto {
            return ProjectExternalConfigDto(model.projectId.toString(), model.appKey, model.callbackUrl, model.status == 1)
        }
    }
}