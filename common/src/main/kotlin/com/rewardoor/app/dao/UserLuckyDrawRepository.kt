package com.rewardoor.app.dao

import com.rewardoor.app.dao.tables.TBLuckyDrawResult
import com.rewardoor.model.LuckyDrawResult
import org.jetbrains.exposed.sql.*
import org.springframework.stereotype.Repository
import org.springframework.transaction.annotation.Transactional

@Repository
@Transactional
class UserLuckyDrawRepository {
    fun createUserLuckyDraw(luckyDrawResult: LuckyDrawResult): Int {
        return TBLuckyDrawResult.insertIgnore {
            it[userId] = luckyDrawResult.userId
            it[fissionLevel] = luckyDrawResult.fissionLevel
            it[tPointsNum] = luckyDrawResult.tPointsNum
            it[isEligibleToGenerateWiseScore] = luckyDrawResult.isEligibleToGenerateWiseScore
            it[isEligibleToGenerateSBT] = luckyDrawResult.isEligibleToGenerateSBT
        }.insertedCount
    }

    fun getUserLuckyDrawById(userId: Long): LuckyDrawResult? {
        return TBLuckyDrawResult.select { TBLuckyDrawResult.userId eq userId }.limit(1).map(::mapUserLuckyDraw)
            .firstOrNull()
    }

    fun getAllTPointsUserId(): List<Long> {
        return TBLuckyDrawResult
            .slice(TBLuckyDrawResult.userId, TBLuckyDrawResult.tPointsNum.sum())
            .selectAll()
            .groupBy(TBLuckyDrawResult.userId)
            .orderBy(TBLuckyDrawResult.tPointsNum.sum(), SortOrder.DESC)
            .map { it[TBLuckyDrawResult.userId] }
    }

    fun getTotalPointsForAllUsers(): Map<Long, Int> {
        return TBLuckyDrawResult
            .slice(TBLuckyDrawResult.userId, TBLuckyDrawResult.tPointsNum.sum())
            .selectAll()
            .groupBy(TBLuckyDrawResult.userId).associate {
                val id = it[TBLuckyDrawResult.userId]
                val totalPoints = it[TBLuckyDrawResult.tPointsNum.sum()] ?: 0
                id to totalPoints
            }
    }

    fun getUserTPoints(userId: Long): Int? {
        return TBLuckyDrawResult.slice(TBLuckyDrawResult.tPointsNum).select { TBLuckyDrawResult.userId eq userId }
            .sumOf { it[TBLuckyDrawResult.tPointsNum] }
    }

    fun getUserCheckInTPointsRecord(userId: Long): List<LuckyDrawResult>? {
        return TBLuckyDrawResult.select {
            (TBLuckyDrawResult.userId eq userId) and (TBLuckyDrawResult.fissionLevel inList listOf(
                -3,
                -4
            ))
        }
            .orderBy(TBLuckyDrawResult.createTime, SortOrder.DESC)
            .map(::mapUserLuckyDraw)
    }

    fun mapUserLuckyDraw(row: ResultRow): LuckyDrawResult {
        return LuckyDrawResult(
            userId = row[TBLuckyDrawResult.userId],
            fissionLevel = row[TBLuckyDrawResult.fissionLevel],
            tPointsNum = row[TBLuckyDrawResult.tPointsNum],
            isEligibleToGenerateWiseScore = row[TBLuckyDrawResult.isEligibleToGenerateWiseScore],
            isEligibleToGenerateSBT = row[TBLuckyDrawResult.isEligibleToGenerateSBT],
            createTime = row[TBLuckyDrawResult.createTime],
            updateTime = row[TBLuckyDrawResult.updateTime]
        )
    }
}