package com.rewardoor.app.dao

import com.rewardoor.app.dao.tables.TBProjectToken
import org.jetbrains.exposed.sql.insert
import org.jetbrains.exposed.sql.select
import org.springframework.stereotype.Repository

@Repository
class ProjectTokenRepository {
    fun addProjectToken(tokenId: Long, projectId: Long, userId: Long, token: String) {
        TBProjectToken.insert { row ->
            row[TBProjectToken.tokenId] = tokenId
            row[TBProjectToken.projectId] = projectId
            row[TBProjectToken.token] = token
            row[creatorId] = userId
        }
    }

    fun getProjectTokens(projectId: Long): List<String> {
        return TBProjectToken.select { TBProjectToken.projectId eq projectId }
            .map { it[TBProjectToken.token] }
    }

    fun getProjectToken(projectId: Long): List<String> {
        return TBProjectToken.select { TBProjectToken.projectId eq projectId}
            .map { it[TBProjectToken.token] }
    }
}