package com.rewardoor.app.dao

import com.fasterxml.jackson.databind.ObjectMapper
import org.jetbrains.exposed.sql.*
import com.rewardoor.app.dao.tables.TBLayerOne
import com.rewardoor.model.*
import org.springframework.stereotype.Repository

@Repository
class LayerOneRepository(val om: ObjectMapper) {

    fun getLayerOneInfoById(layerOneId: Long): LayerOne {
        return TBLayerOne.select { TBLayerOne.layerOneId eq layerOneId }.limit(1).map(::mapLayerOne).first()
    }

    fun mapLayerOne(r: ResultRow): LayerOne {
        return LayerOne(
            r[TBLayerOne.layerOneId],
            r[TBLayerOne.name],
            r[TBLayerOne.icon],
            r[TBLayerOne.url],
        )
    }
}