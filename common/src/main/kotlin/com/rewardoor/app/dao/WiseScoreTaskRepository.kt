package com.rewardoor.app.dao

import com.rewardoor.app.dao.tables.TBWiseScoreOneTimeTask
import org.jetbrains.exposed.sql.and
import org.jetbrains.exposed.sql.insertIgnore
import org.jetbrains.exposed.sql.select
import org.springframework.stereotype.Repository
import java.time.Instant

@Repository
public class WiseScoreTaskRepository {
    fun addWiseScoreTask(userId: Long, taskName: String): Int {
        return TBWiseScoreOneTimeTask.insertIgnore {
            it[TBWiseScoreOneTimeTask.userId] = userId
            it[TBWiseScoreOneTimeTask.taskName] = taskName
            it[taskTime] = Instant.now()
        }.insertedCount
    }

    fun getUserTasks(userId: Long): List<String> {
        return TBWiseScoreOneTimeTask.select { TBWiseScoreOneTimeTask.userId eq userId }
            .map { it[TBWiseScoreOneTimeTask.taskName] }
    }

    fun getUserTask(userId: Long, taskName: String): String? {
        return TBWiseScoreOneTimeTask.select {
            (TBWiseScoreOneTimeTask.userId eq userId) and (TBWiseScoreOneTimeTask.taskName eq taskName) }
            .map { it[TBWiseScoreOneTimeTask.taskName] }.firstOrNull()
    }
}
