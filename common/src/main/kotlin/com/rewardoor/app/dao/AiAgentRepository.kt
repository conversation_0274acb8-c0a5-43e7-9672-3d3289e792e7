package com.rewardoor.app.dao

import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.rewardoor.app.dao.tables.TBAiAgent
import com.rewardoor.app.dao.tables.TBTgClientConfig
import com.rewardoor.model.AiAgent
import com.rewardoor.model.Conversation
import com.rewardoor.model.TgClientConfig
import org.jetbrains.exposed.sql.ResultRow
import org.jetbrains.exposed.sql.insert
import org.jetbrains.exposed.sql.*
import org.springframework.stereotype.Repository

@Repository
class AiAgentRepository {

    fun createAiAgent(aiAgent: AiAgent) {
        TBAiAgent.insert {
            it[agentId] = aiAgent.agentId
            it[agentName] = aiAgent.agentName
            it[agentImage] = aiAgent.agentImage
            it[bio] = aiAgent.bio
            it[systemPrompt] = aiAgent.systemPrompt
            it[lore] = aiAgent.lore
            it[status] = aiAgent.status
            it[knowledgeList] = aiAgent.knowledgeList.joinToString(",") // 将列表转换为字符串存储
            it[topicsList] = aiAgent.topicsList.joinToString(",") // 将列表转换为字符串存储
            it[adjectives] = aiAgent.adjectives.joinToString(",") // 将列表转换为字符串存储
            it[generalStyleRules] = aiAgent.generalStyleRules
            it[modelProvider] = aiAgent.modelProvider
            it[modelName] = aiAgent.modelName
            it[voiceModel] = aiAgent.voiceModel
        }
    }

    fun updateAiAgent(aiAgent: AiAgent) {
        TBAiAgent.update({ TBAiAgent.agentId eq aiAgent.agentId }) { // 条件：根据 agentId 更新
            it[agentName] = aiAgent.agentName
            it[agentImage] = aiAgent.agentImage
            it[bio] = aiAgent.bio
            it[systemPrompt] = aiAgent.systemPrompt
            it[lore] = aiAgent.lore
            it[status] = aiAgent.status
            it[knowledgeList] = aiAgent.knowledgeList.joinToString(",") // 将列表转换为字符串存储
            it[topicsList] = aiAgent.topicsList.joinToString(",") // 将列表转换为字符串存储
            it[adjectives] = aiAgent.adjectives.joinToString(",") // 将列表转换为字符串存储
            it[generalStyleRules] = aiAgent.generalStyleRules
            it[modelProvider] = aiAgent.modelProvider
            it[modelName] = aiAgent.modelName
            it[voiceModel] = aiAgent.voiceModel
        }
    }

    fun createTgClientConfig(tgClientConfig: TgClientConfig) {
        TBTgClientConfig.insert {
            it[tgClientConfigId] = tgClientConfig.tgClientConfigId
            it[agentId] = tgClientConfig.agentId
            it[chatStyles] = tgClientConfig.chatStyles
            it[tgBotToken] = tgClientConfig.tgBotToken
            it[conversationExamples] = tgClientConfig.conversationExamples.joinToString(",") { conversation ->
                """{"roleType":"${conversation.roleType}","content":"${conversation.content}"}"""
            } // 将会话示例序列化为 JSON 格式
        }
    }

    fun updateTgClientConfig(tgClientConfig: TgClientConfig) {
        TBTgClientConfig.update({ TBTgClientConfig.tgClientConfigId eq tgClientConfig.tgClientConfigId }) { // 根据 tgClientConfigId 更新
            it[agentId] = tgClientConfig.agentId
            it[chatStyles] = tgClientConfig.chatStyles
            it[tgBotToken] = tgClientConfig.tgBotToken
            it[conversationExamples] = tgClientConfig.conversationExamples.joinToString(",") { conversation ->
                """{"roleType":"${conversation.roleType}","content":"${conversation.content}"}"""
            } // 将会话示例序列化为 JSON 格式
        }
    }

    fun getAiAgentByAgentId(agentId: Long): AiAgent? {
        return TBAiAgent
            .select { TBAiAgent.agentId eq agentId }
            .map(::mapAiAgent)
            .firstOrNull()
    }

    fun getAiAgentList(): List<AiAgent>? {
        return TBAiAgent
            .selectAll()
            .orderBy(TBAiAgent.createTime, SortOrder.DESC)
            .map(::mapAiAgent)
    }

    fun getTgClientConfigByAgentId(agentId: Long): TgClientConfig? {
        return TBTgClientConfig
            .select { TBTgClientConfig.agentId eq agentId }
            .map(::mapTgClientConfig)
            .firstOrNull()
    }

    fun mapAiAgent(r: ResultRow): AiAgent {
        return AiAgent(
            agentId = r[TBAiAgent.agentId],
            agentName = r[TBAiAgent.agentName],
            agentImage = r[TBAiAgent.agentImage],
            bio = r[TBAiAgent.bio],
            systemPrompt = r[TBAiAgent.systemPrompt],
            lore = r[TBAiAgent.lore],
            status = r[TBAiAgent.status],
            knowledgeList = r[TBAiAgent.knowledgeList].split(",").map { it.trim() }, // 将字符串转换为列表
            topicsList = r[TBAiAgent.topicsList].split(",").map { it.trim() }, // 将字符串转换为列表
            adjectives = r[TBAiAgent.adjectives].split(",").map { it.trim() }, // 将字符串转换为列表
            generalStyleRules = r[TBAiAgent.generalStyleRules],
            modelProvider = r[TBAiAgent.modelProvider],
            modelName = r[TBAiAgent.modelName],
            voiceModel = r[TBAiAgent.voiceModel],
            createTime = r[TBAiAgent.createTime],
            updateTime = r[TBAiAgent.updateTime]
        )
    }

    fun mapTgClientConfig(r: ResultRow): TgClientConfig {
        return TgClientConfig(
            tgClientConfigId = r[TBTgClientConfig.tgClientConfigId],
            agentId = r[TBTgClientConfig.agentId],
            chatStyles = r[TBTgClientConfig.chatStyles],
            tgBotToken = r[TBTgClientConfig.tgBotToken],
            conversationExamples = parseConversationExamples(r[TBTgClientConfig.conversationExamples])
        )
    }

    // Helper function to parse conversationExamples
    fun parseConversationExamples(conversationExamplesString: String): List<Conversation> {
        if (conversationExamplesString.isBlank()) return emptyList()
        val gson = Gson()
        val json = "[$conversationExamplesString]" // 将 JSON 数据包裹成数组格式
        val type = object : TypeToken<List<Conversation>>() {}.type
        return try {
            gson.fromJson(json, type)
        } catch (e: Exception) {
            emptyList()
        }
    }

}