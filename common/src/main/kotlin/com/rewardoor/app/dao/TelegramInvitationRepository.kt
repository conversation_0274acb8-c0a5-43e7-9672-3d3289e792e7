package com.rewardoor.app.dao

import com.rewardoor.app.dao.tables.TBTelegramInvitation
import com.rewardoor.model.TelegramInvitee
import org.jetbrains.exposed.sql.*
import org.springframework.stereotype.Repository
import java.time.LocalDateTime

@Repository
class TelegramInvitationRepository {
    fun addInvitation(userId: Long, inviterTgId: Long, inviteeTgId: Long,
                      inviteeFirstName: String, inviteeLastName: String,
                      inviteeUsername: String, premium: Boolean): Int {
        return TBTelegramInvitation.insertIgnore {
            it[TBTelegramInvitation.userId] = userId
            it[TBTelegramInvitation.inviterTgId] = inviterTgId
            it[TBTelegramInvitation.inviteeTgId] = inviteeTgId
            it[TBTelegramInvitation.inviteeFirstName] = inviteeFirstName
            it[TBTelegramInvitation.inviteeLastName] = inviteeLastName
            it[TBTelegramInvitation.inviteeUsername] = inviteeUsername
            it[TBTelegramInvitation.premium] = if (premium) 1 else 0
            it[TBTelegramInvitation.inviteAt] = LocalDateTime.now()
        }.insertedCount
    }

    fun getByInvitee(inviteeTgId: Long): Long? {
        return TBTelegramInvitation.select { TBTelegramInvitation.inviteeTgId eq inviteeTgId }
            .map { it[TBTelegramInvitation.inviterTgId] }.firstOrNull()
    }

    fun getInvitee(userId: Long, limit: Int): List<TelegramInvitee> {
        return TBTelegramInvitation.select { TBTelegramInvitation.userId eq userId }
            .orderBy(TBTelegramInvitation.inviteAt, SortOrder.DESC)
            .limit(limit).map {
                TelegramInvitee(it[TBTelegramInvitation.inviteeTgId],
                    it[TBTelegramInvitation.inviteeFirstName],
                    it[TBTelegramInvitation.inviteeLastName],
                    it[TBTelegramInvitation.inviteeUsername],
                    it[TBTelegramInvitation.premium] == 1
                    )
            }
    }

    fun getInviteCnt(userId: Long): Long {
        return TBTelegramInvitation.slice(TBTelegramInvitation.inviteeTgId.count())
            .select { TBTelegramInvitation.userId eq userId }
            .map { it[TBTelegramInvitation.inviteeTgId.count()] }.first()
    }

    fun getInviteCntWithPremium(userId: Long, premium: Boolean): Long {
        return TBTelegramInvitation.slice(TBTelegramInvitation.inviteeTgId.count())
            .select { (TBTelegramInvitation.userId eq userId).and(TBTelegramInvitation.premium eq if (premium) 1 else 0) }
            .map { it[TBTelegramInvitation.inviteeTgId.count()] }.first()
    }

    fun getInviteeWithPremium(userId: Long, limit: Int, premium: Boolean): List<TelegramInvitee> {
        return TBTelegramInvitation.select {
            TBTelegramInvitation.userId eq userId and (TBTelegramInvitation.premium eq if (premium) 1 else 0)
        }.orderBy(TBTelegramInvitation.inviteAt, SortOrder.DESC)
            .limit(limit).map {
                TelegramInvitee(it[TBTelegramInvitation.inviteeTgId],
                    it[TBTelegramInvitation.inviteeFirstName],
                    it[TBTelegramInvitation.inviteeLastName],
                    it[TBTelegramInvitation.inviteeUsername],
                    it[TBTelegramInvitation.premium] == 1
                )
            }
    }
}