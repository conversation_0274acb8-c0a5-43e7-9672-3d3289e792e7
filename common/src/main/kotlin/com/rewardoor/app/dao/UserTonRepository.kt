package com.rewardoor.app.dao

import com.rewardoor.app.dao.tables.TBUserTon
import com.rewardoor.app.utils.TonAddressUtils
import com.rewardoor.model.UserTon
import org.jetbrains.exposed.sql.*
import org.jetbrains.exposed.sql.SqlExpressionBuilder.eq
import org.springframework.stereotype.Repository
import org.springframework.transaction.annotation.Transactional

@Repository
@Transactional
class UserTonRepository {
    fun bindTonWallet(userId: Long, wallet: String, pk: String): Int {
        val hexAddress = TonAddressUtils.toHexAddress(wallet)
        return TBUserTon.insertIgnore {
            it[TBUserTon.userId] = userId
            it[address] = wallet
            it[TBUserTon.hexAddress] = hexAddress
            it[publicKey] = pk
        }.insertedCount
    }

    fun findTonWallet(wallet: String): UserTon? {
        val hexAddress = try {
            TonAddressUtils.toHexAddress(wallet)
        } catch (e: Exception) {
            null
        }

        return if (hexAddress != null) {
            TBUserTon.select { TBUserTon.hexAddress eq hexAddress }.limit(1).map(::mapUserTon).firstOrNull()
        } else {
            TBUserTon.select { TBUserTon.address eq wallet }.limit(1).map(::mapUserTon).firstOrNull()
        }
    }

    fun getTonUserIds(): List<Long> {
        return TBUserTon.select { TBUserTon.address neq "" }.map { it[TBUserTon.userId] }.distinct()
    }

    fun getTonUserWalletByUserId(userId: Long): UserTon? {
        return TBUserTon.select { TBUserTon.userId eq userId }.limit(1).map(::mapUserTon).firstOrNull()
    }

    fun updateTonUserId(uid: Long, newUserId: Long): Int {
        return TBUserTon.update({ (TBUserTon.userId eq uid) }) {
            it[userId] = newUserId
        }
    }

    fun deleteTonUserById(userId: Long): Int {
        return TBUserTon.deleteWhere { TBUserTon.userId eq userId }
    }

    fun getTonUserCnt(): Long {
        return TBUserTon.slice(TBUserTon.address.count()).selectAll()
            .single()[TBUserTon.address.count()]
    }

    fun findTonWalletByHexAddress(hexAddress: String): UserTon? {
        return TBUserTon.select { TBUserTon.hexAddress eq hexAddress }.limit(1).map(::mapUserTon).firstOrNull()
    }

    fun migrateHexAddresses(): Int {
        var updatedCount = 0
        TBUserTon.selectAll().forEach { row ->
            val address = row[TBUserTon.address]
            val currentHexAddress = row.getOrNull(TBUserTon.hexAddress)

            if (currentHexAddress.isNullOrEmpty() && address.isNotEmpty()) {
                try {
                    val hexAddress = TonAddressUtils.toHexAddress(address)
                    TBUserTon.update({ TBUserTon.userId eq row[TBUserTon.userId] }) {
                        it[TBUserTon.hexAddress] = hexAddress
                    }
                    updatedCount++
                } catch (e: Exception) {
                    println("Failed to convert address: $address, error: ${e.message}")
                }
            }
        }
        return updatedCount
    }

    fun getMigrationStatus(): MigrationStatus {
        val totalRecords = TBUserTon.selectAll().count().toInt()
        val recordsWithHexAddress = TBUserTon.select {
            TBUserTon.hexAddress.isNotNull() and (TBUserTon.hexAddress neq "")
        }.count().toInt()
        val recordsMissingHexAddress = totalRecords - recordsWithHexAddress

        return MigrationStatus(
            totalRecords = totalRecords,
            recordsWithHexAddress = recordsWithHexAddress,
            recordsMissingHexAddress = recordsMissingHexAddress
        )
    }

    data class MigrationStatus(
        val totalRecords: Int,
        val recordsWithHexAddress: Int,
        val recordsMissingHexAddress: Int
    )

    fun mapUserTon(row: ResultRow): UserTon {
        return UserTon(
            userId = row[TBUserTon.userId],
            tonWallet = row[TBUserTon.address],
            publicKey = row[TBUserTon.publicKey],
            hexAddress = row[TBUserTon.hexAddress]
        )
    }
}