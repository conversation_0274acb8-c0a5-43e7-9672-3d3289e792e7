package com.rewardoor.app.dao

import com.rewardoor.app.dao.tables.TBProjectExternalConfig
import com.rewardoor.model.ProjectExternalConfig
import org.jetbrains.exposed.sql.ResultRow
import org.jetbrains.exposed.sql.insert
import org.jetbrains.exposed.sql.select
import org.jetbrains.exposed.sql.update
import org.springframework.stereotype.Repository

@Repository
class ProjectExternalRepository {
    fun addProjectExternalConfig(projectId: Long, appKey: String, callbackUrl: String, status: Int, extra: String) {
        TBProjectExternalConfig.insert {
            it[TBProjectExternalConfig.projectId] = projectId
            it[TBProjectExternalConfig.appKey] = appKey
            it[TBProjectExternalConfig.callbackUrl] = callbackUrl
            it[TBProjectExternalConfig.status] = status
            it[TBProjectExternalConfig.extra] = extra
        }
    }

    fun updateAppKey(projectId: Long, appKey: String) {
        TBProjectExternalConfig.update({ TBProjectExternalConfig.projectId eq projectId }) {
            it[TBProjectExternalConfig.appKey] = appKey
        }
    }

    fun updateCallbackUrl(projectId: Long, callbackUrl: String) {
        TBProjectExternalConfig.update({ TBProjectExternalConfig.projectId eq projectId }) {
            it[TBProjectExternalConfig.callbackUrl] = callbackUrl
        }
    }

    fun updateCallbackStatus(projectId: Long, enable: Boolean) {
        TBProjectExternalConfig.update({ TBProjectExternalConfig.projectId eq projectId }) {
            it[status] = if (enable) 1 else 0
        }
    }

    fun updateCallback(projectId: Long, enable: Boolean, callbackUrl: String) {
        TBProjectExternalConfig.update({ TBProjectExternalConfig.projectId eq projectId }) {
            it[status] = if (enable) 1 else 0
            it[TBProjectExternalConfig.callbackUrl] = callbackUrl
        }
    }

    fun getProjectExternalConfig(projectId: Long): ProjectExternalConfig? {
        return TBProjectExternalConfig.select { TBProjectExternalConfig.projectId eq projectId }
            .map(::mapper).firstOrNull()
    }

    fun mapper(r: ResultRow): ProjectExternalConfig {
        return ProjectExternalConfig(
            r[TBProjectExternalConfig.projectId],
            r[TBProjectExternalConfig.appKey],
            r[TBProjectExternalConfig.callbackUrl],
            r[TBProjectExternalConfig.status],
            r[TBProjectExternalConfig.extra]
        )
    }

    fun getProjectExternalConfigByAppKey(appKey: String): ProjectExternalConfig? {
        return TBProjectExternalConfig.select { TBProjectExternalConfig.appKey eq appKey }
            .map(::mapper).firstOrNull()
    }
}