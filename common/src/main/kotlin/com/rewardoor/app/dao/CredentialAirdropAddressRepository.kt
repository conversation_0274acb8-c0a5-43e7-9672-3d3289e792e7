package com.rewardoor.app.dao

import com.rewardoor.app.dao.tables.TBCredentialAirdropAddress
import com.rewardoor.model.CredentialAirdropAddress
import org.jetbrains.exposed.sql.*
import org.jetbrains.exposed.sql.SqlExpressionBuilder.eq
import org.springframework.stereotype.Repository

@Repository
class CredentialAirdropAddressRepository {
    fun addCredentialAddressData(credentialAirdropAddress: CredentialAirdropAddress) {
        TBCredentialAirdropAddress.insert {
            it[TBCredentialAirdropAddress.userId] = credentialAirdropAddress.userId
            it[TBCredentialAirdropAddress.credentialId] = credentialAirdropAddress.credentialId
            it[TBCredentialAirdropAddress.address] = credentialAirdropAddress.address
            it[TBCredentialAirdropAddress.verified] = if (credentialAirdropAddress.verified) 1 else 0
        }
    }

//    fun updateCredentialRawData(userId: Long, credentialId: Long, rawData: String) {
//        TBCredentialSign.update({ TBCredentialSign.userId.eq(userId) and TBCredentialSign.credentialId.eq(credentialId)}) {
//            it[TBCredentialSign.rawData] = rawData
//            it[TBCredentialSign.verified] = 0
//        }
//    }
//
//    fun updateCredentialVerified(userId: Long, credentialId: Long, sign: String, verified: Int) {
//        TBCredentialSign.update({ TBCredentialSign.userId.eq(userId) and TBCredentialSign.credentialId.eq(credentialId)}) {
//            it[TBCredentialSign.sign] = sign
//            it[TBCredentialSign.verified] = verified
//            it[TBCredentialSign.signTime] = LocalDateTime.now()
//        }
//    }

    fun getCredentialAirDropAddress(userId: Long, credentialId: Long): CredentialAirdropAddress? {
        return TBCredentialAirdropAddress.select {
            TBCredentialAirdropAddress.userId.eq(userId) and TBCredentialAirdropAddress.credentialId.eq(
                credentialId
            )
        }
            .map(::mapper).firstOrNull()
    }

    private fun mapper(resultRow: ResultRow): CredentialAirdropAddress {
        return CredentialAirdropAddress(
            userId = resultRow[TBCredentialAirdropAddress.userId],
            credentialId = resultRow[TBCredentialAirdropAddress.credentialId],
            address = resultRow[TBCredentialAirdropAddress.address],
            verified = resultRow[TBCredentialAirdropAddress.verified] == 1,
        )
    }
}