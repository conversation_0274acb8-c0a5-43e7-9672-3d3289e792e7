package com.rewardoor.app.dao

import com.rewardoor.app.dao.tables.TBTgUserPush
import com.rewardoor.app.dao.tables.TBUserTelegram
import com.rewardoor.app.dto.TgAuthCallbackReq
import com.rewardoor.model.TelegramUserPush
import com.rewardoor.model.UserTelegramInfo
import org.jetbrains.exposed.sql.*
import org.jetbrains.exposed.sql.SqlExpressionBuilder.eq
import org.springframework.stereotype.Repository
import org.springframework.transaction.annotation.Transactional
import java.time.Instant
import java.time.LocalDateTime
import java.time.ZoneOffset

@Repository
@Transactional
class TelegramUserPushRepository {

    fun addTgUserPush(info: TelegramUserPush) {
        TBTgUserPush.insert {
            it[tgPushSettingId] = info.tgPushSettingId
            it[tgId] = info.tgId
            it[userId] = info.userId
            it[pushType] = info.pushType
            it[participateType] = info.participateType
            it[groupId] = info.groupId
        }
    }

    fun addTgUserPushBatch(infos: List<TelegramUserPush>, batchSize: Int = 1000): Int {
        if (infos.isEmpty()) return 0

        var totalInserted = 0 // 记录总插入条数

        infos.chunked(batchSize).forEach { batch ->
            TBTgUserPush.batchInsert(batch) { info ->
                this[TBTgUserPush.tgPushSettingId] = info.tgPushSettingId
                this[TBTgUserPush.tgId] = info.tgId
                this[TBTgUserPush.userId] = info.userId
                this[TBTgUserPush.pushType] = info.pushType
                this[TBTgUserPush.participateType] = info.participateType
                this[TBTgUserPush.groupId] = info.groupId
                this[TBTgUserPush.createdAt] = Instant.now()
                this[TBTgUserPush.updatedAt] = Instant.now()
            }
            totalInserted += batch.size // 累加本批次的插入条数
        }

        return totalInserted // 返回总插入条数
    }

    fun updateTgUserPushParticipateType(uid: Long, pushType: String, newParticipateType: Int) {
        TBTgUserPush.update({ (TBTgUserPush.userId eq uid) and (TBTgUserPush.pushType eq pushType) }) {
            it[participateType] = newParticipateType
        }
    }

    fun getTgUserPush(userId: Long, pushType: String): TelegramUserPush? {
        return TBTgUserPush.select { (TBTgUserPush.userId eq userId) and (TBTgUserPush.pushType eq pushType) }.limit(1)
            .map(::mapTgUserPush).firstOrNull()
    }

    fun getTgUserPushByUidAndPushType(userId: Long, pushType: String): TelegramUserPush? {
        return TBTgUserPush.select { (TBTgUserPush.userId eq userId) and (TBTgUserPush.pushType eq pushType) }.limit(1)
            .map(::mapTgUserPush).firstOrNull()
    }

    fun getTgUserPushByPushId(pushId: Long): TelegramUserPush? {
        return TBTgUserPush.select { (TBTgUserPush.tgPushSettingId eq pushId) }
            .limit(1)
            .map(::mapTgUserPush).firstOrNull()
    }

    fun getTgUserPushCntByPushId(pushId: Long): Long {
        return TBTgUserPush.slice(TBTgUserPush.id.count())
            .select { (TBTgUserPush.tgPushSettingId eq pushId) }
            .map { it[TBTgUserPush.id.count()] }
            .firstOrNull() ?: 0
    }

    fun getTgUserPushCntByPushIdAndParticipateType(pushId: Long, participateType: Int): Long {
        return TBTgUserPush.slice(TBTgUserPush.id.count())
            .select { (TBTgUserPush.tgPushSettingId eq pushId) and (TBTgUserPush.participateType eq participateType) }
            .map { it[TBTgUserPush.id.count()] }
            .firstOrNull() ?: 0
    }


    private fun mapTgUserPush(resultRow: ResultRow): TelegramUserPush {
        return TelegramUserPush(
            resultRow[TBTgUserPush.tgPushSettingId],
            resultRow[TBTgUserPush.tgId],
            resultRow[TBTgUserPush.userId],
            resultRow[TBTgUserPush.pushType],
            resultRow[TBTgUserPush.participateType],
            resultRow[TBTgUserPush.groupId]
        )
    }
}