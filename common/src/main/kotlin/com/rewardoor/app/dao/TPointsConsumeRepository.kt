package com.rewardoor.app.dao

import com.rewardoor.app.dao.tables.TBTPointsConsume
import com.rewardoor.model.TPointsConsume
import org.jetbrains.exposed.sql.ResultRow
import org.jetbrains.exposed.sql.and
import org.jetbrains.exposed.sql.insertIgnore
import org.jetbrains.exposed.sql.select
import org.springframework.stereotype.Repository
import org.springframework.transaction.annotation.Transactional

@Transactional
@Repository
class TPointsConsumeRepository {
    fun addTPointsConsume(tPointsConsume: TPointsConsume): Int {
        return TBTPointsConsume.insertIgnore {
            it[userId] = tPointsConsume.userId
            it[consumeType] = tPointsConsume.consumeType
            it[level] = tPointsConsume.level
            it[tPointsNum] = tPointsConsume.tPointsNum
        }.insertedCount
    }

    fun getTPointsConsumeById(userId: Long): List<TPointsConsume>? {
        return TBTPointsConsume.select { TBTPointsConsume.userId eq userId }.map(::mapTPointsConsume)
    }

    fun getTPointsConsumeByIdAndTypeAndNum(userId: Long, consumeType: Int, tPointsNum: Int): List<TPointsConsume>? {
        return TBTPointsConsume.select { (TBTPointsConsume.userId eq userId) and (TBTPointsConsume.consumeType eq consumeType) and (TBTPointsConsume.tPointsNum eq tPointsNum) }
            .map(::mapTPointsConsume)
    }

    fun mapTPointsConsume(row: ResultRow): TPointsConsume {
        return TPointsConsume(
            userId = row[TBTPointsConsume.userId],
            consumeType = row[TBTPointsConsume.consumeType],
            level = row[TBTPointsConsume.level],
            tPointsNum = row[TBTPointsConsume.tPointsNum],
            createTime = row[TBTPointsConsume.createTime],
            updateTime = row[TBTPointsConsume.updateTime]
        )
    }

}