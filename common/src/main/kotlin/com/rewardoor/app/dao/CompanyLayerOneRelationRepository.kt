package com.rewardoor.app.dao

import com.fasterxml.jackson.databind.ObjectMapper
import org.jetbrains.exposed.sql.*
import com.rewardoor.app.dao.tables.TBCompanyLayerOneRelation
import com.rewardoor.model.*
import org.springframework.stereotype.Repository

@Repository
class CompanyLayerOneRelationRepository(val om: ObjectMapper) {

    fun getLayerOneListById(companyId: Long): List<CompanyLayerOneRelation> {
        return TBCompanyLayerOneRelation.select { TBCompanyLayerOneRelation.companyId eq companyId }
            .map { row ->
                CompanyLayerOneRelation(
                    layerOneId = row[TBCompanyLayerOneRelation.layerOneId],
                    status = row[TBCompanyLayerOneRelation.status]
                )
            }
    }
}