package com.rewardoor.app.dao

import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.rewardoor.app.dao.tables.TBPushBasicInfo
import com.rewardoor.app.dao.tables.TBPushResultInfo
import com.rewardoor.app.dao.tables.TBTgUserPushSetting
import com.rewardoor.model.PushBasicInfo
import com.rewardoor.model.PushResultInfo
import com.rewardoor.model.TgUserPushSetting
import org.jetbrains.exposed.sql.*
import org.springframework.stereotype.Repository
import org.springframework.transaction.annotation.Transactional
import java.time.Instant


@Repository
@Transactional
class TgUserPushSetRepository {

    fun insertTgUserPushSetting(pushSetting: TgUserPushSetting): Int {
        val gson = Gson()
        val whiteListString = gson.toJson(pushSetting.whiteList)
        return TBTgUserPushSetting.insertIgnore {
            it[TBTgUserPushSetting.tgPushId] = pushSetting.tgPushId
            it[TBTgUserPushSetting.pushType] = pushSetting.pushType
            it[TBTgUserPushSetting.userType] = pushSetting.userType
            it[TBTgUserPushSetting.activityId] = pushSetting.activityId
            it[TBTgUserPushSetting.topWiseScoreType] = pushSetting.topWiseScoreType
            it[TBTgUserPushSetting.wiseScoreType] = pushSetting.wiseScoreType
            it[TBTgUserPushSetting.wiseScoreLimit] = pushSetting.wiseScoreLimit
            it[TBTgUserPushSetting.whiteList] = whiteListString
            it[TBTgUserPushSetting.hasGroups] = pushSetting.hasGroups
            it[TBTgUserPushSetting.hasPushed] = pushSetting.hasPushed
            it[TBTgUserPushSetting.createTime] = Instant.now()
            it[TBTgUserPushSetting.updateTime] = Instant.now()
        }.insertedCount
    }

    fun insertPushBasicInfos(pushBasicInfos: List<PushBasicInfo>, tgPushSettingId: Long) {
        TBPushBasicInfo.batchInsert(pushBasicInfos) { pushBasicInfo ->
            this[TBPushBasicInfo.tgPushSettingId] = tgPushSettingId
            this[TBPushBasicInfo.imgUrl] = pushBasicInfo.imgUrl
            this[TBPushBasicInfo.content] = pushBasicInfo.content
            this[TBPushBasicInfo.buttonName] = pushBasicInfo.buttonName
            this[TBPushBasicInfo.buttonUrl] = pushBasicInfo.buttonUrl
            this[TBPushBasicInfo.pushDate] = pushBasicInfo.pushDate
            this[TBPushBasicInfo.createTime] = Instant.now() // 创建时间
            this[TBPushBasicInfo.updateTime] = Instant.now() // 更新时间
        }
    }

    fun getTgUserPushSettingById(tgPushId: Long): TgUserPushSetting? {
        return TBTgUserPushSetting
            .select { TBTgUserPushSetting.tgPushId eq tgPushId }
            .map(::mapTgUserPushSetting)
            .firstOrNull()
    }

    fun getPushBasicInfosByTgPushId(tgPushId: Long): List<PushBasicInfo>? {
        return TBPushBasicInfo
            .select { TBPushBasicInfo.tgPushSettingId eq tgPushId }
            .map(::mapPushBasicInfo) // 将查询结果映射为 PushBasicInfo 对象
    }

    fun getTgUserPushResultById(tgPushId: Long): List<PushResultInfo>? {
        return TBPushResultInfo
            .select { TBPushResultInfo.tgPushSettingId eq tgPushId }
            .map(::mapPushResult)
    }

    fun getTgUserPushSettingList(): List<TgUserPushSetting>? {
        return TBTgUserPushSetting
            .selectAll()
            .orderBy(TBTgUserPushSetting.createTime, SortOrder.DESC)
            .map(::mapTgUserPushSetting)
    }

    fun mapTgUserPushSetting(row: ResultRow): TgUserPushSetting {
        val gson = Gson()
        val whiteListString = row[TBTgUserPushSetting.whiteList]
        val whiteList: List<Long> = if (whiteListString.isNullOrEmpty()) {
            emptyList() // 如果字段为空，则返回空列表
        } else {
            gson.fromJson(whiteListString, object : TypeToken<List<Long>>() {}.type)
        }
        return TgUserPushSetting(
            tgPushId = row[TBTgUserPushSetting.tgPushId],
            pushType = row[TBTgUserPushSetting.pushType],
            userType = row[TBTgUserPushSetting.userType],
            activityId = row[TBTgUserPushSetting.activityId],
            topWiseScoreType = row[TBTgUserPushSetting.topWiseScoreType],
            wiseScoreType = row[TBTgUserPushSetting.wiseScoreType],
            wiseScoreLimit = row[TBTgUserPushSetting.wiseScoreLimit],
            whiteList = whiteList,
            hasGroups = row[TBTgUserPushSetting.hasGroups],
            hasPushed = row[TBTgUserPushSetting.hasPushed],
            pushBasicInfoList = emptyList(), // `pushBasicInfoList` 需要通过关联查询填充
            createdAt = row[TBTgUserPushSetting.createTime]
        )
    }

    fun mapPushBasicInfo(row: ResultRow): PushBasicInfo {
        return PushBasicInfo(
            imgUrl = row[TBPushBasicInfo.imgUrl],
            content = row[TBPushBasicInfo.content],
            buttonName = row[TBPushBasicInfo.buttonName],
            buttonUrl = row[TBPushBasicInfo.buttonUrl],
            pushDate = row[TBPushBasicInfo.pushDate]
        )
    }

    fun mapPushResult(row: ResultRow): PushResultInfo {
        return PushResultInfo(
            tgPushId = row[TBPushResultInfo.tgPushSettingId],
            groupId = row[TBPushResultInfo.groupId],
            totalPushCount = row[TBPushResultInfo.totalPushCnt].toLong(),
            succeedPushCount = row[TBPushResultInfo.successPushCnt].toLong(),
            failedPushCount = row[TBPushResultInfo.failedPushCnt].toLong()
        )
    }

}