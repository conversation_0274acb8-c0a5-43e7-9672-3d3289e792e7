package com.rewardoor.app.dao

import com.rewardoor.app.dao.tables.TBStarInvoices
import com.rewardoor.app.dao.tables.TBStarPayments
import com.rewardoor.model.StarInvoice
import com.rewardoor.model.StarPayment
import org.jetbrains.exposed.sql.*
import org.jetbrains.exposed.sql.transactions.transaction
import org.springframework.stereotype.Repository
import java.time.Instant

@Repository
class StarPaymentRepository {
    fun create(starPayment: StarPayment): StarPayment = transaction {
        TBStarPayments.insert {
            it[paymentId] = starPayment.paymentId
            it[telegramChargeId] = starPayment.telegramChargeId
            it[userId] = starPayment.userId
            it[starAmount] = starPayment.starAmount
            it[productAmount] = starPayment.productAmount
            it[createdAt] = Instant.now()
            it[invoiceId] = starPayment.invoiceId
        }.let { starPayment }
    }

    fun findUserPayments(userId: Long): List<StarPayment> {
        return TBStarPayments
            .select { TBStarPayments.userId eq userId }
            .orderBy(TBStarPayments.createdAt, SortOrder.DESC)
            .map { StarPayment(it) }
    }

    fun findByPaymentId(paymentId: Long): StarPayment? = transaction {
        TBStarPayments
            .select { TBStarPayments.paymentId eq paymentId }
            .singleOrNull()
            ?.let { StarPayment(it) }
    }

    fun createInvoice(starInvoice: StarInvoice) {
        TBStarInvoices.insert {
            it[invoiceId] = starInvoice.invoiceId
            it[userId] = starInvoice.userId
            it[amount] = starInvoice.amount
            it[product] = starInvoice.product
        }
    }

    fun findInvoiceById(invoiceId: Long): StarInvoice? {
        return TBStarInvoices
            .select { TBStarInvoices.invoiceId eq invoiceId }
            .singleOrNull()
            ?.let { StarInvoice(it) }
    }
}
