package com.rewardoor.app.dao

import com.rewardoor.app.dao.tables.TBAdminNonceSign
import com.rewardoor.app.dao.tables.TBWiseInviteRecord
import com.rewardoor.app.dao.tables.TBWiseScoreInvite
import com.rewardoor.model.WiseInviteCode
import org.jetbrains.exposed.sql.*
import org.jetbrains.exposed.sql.SqlExpressionBuilder.eq
import org.springframework.stereotype.Repository
import java.time.Instant

@Repository
class WiseScoreInviteRepository {
    fun addInviteCode(userId: Long, eventStage: Int, inviteCode: String, times: Int = 3): Int {
        return TBWiseScoreInvite.insertIgnore {
            it[TBWiseScoreInvite.userId] = userId
            it[TBWiseScoreInvite.eventStage] = eventStage
            it[TBWiseScoreInvite.inviteCode] = inviteCode
            it[TBWiseScoreInvite.totalTimes] = times
        }.insertedCount
    }

    fun getInviteCode(userId: Long, eventStage: Int): WiseInviteCode? {
        return TBWiseScoreInvite.select { (TBWiseScoreInvite.userId eq userId) and (TBWiseScoreInvite.eventStage eq eventStage) }
            .map(::mapper).firstOrNull()
    }

    fun getInviteByCode(code: String): WiseInviteCode? {
        return TBWiseScoreInvite.select { TBWiseScoreInvite.inviteCode eq code }
            .map(::mapper).firstOrNull()
    }

    fun addInvitee(userId: Long, inviteTgName: String, code: String, eventStage: Int, inviteeId: Long) {
        TBWiseInviteRecord.insert {
            it[TBWiseInviteRecord.userId] = userId
            it[TBWiseInviteRecord.inviteTgName] = inviteTgName
            it[TBWiseInviteRecord.inviteCode] = code
            it[TBWiseInviteRecord.eventStage] = eventStage
            it[TBWiseInviteRecord.inviteeId] = inviteeId
            it[inviteAt] = Instant.now()
        }
    }

    fun updateInviteTimes(userId: Long, code: String, times: Int) {
        TBWiseScoreInvite.update({ (TBWiseScoreInvite.userId eq userId) and (TBWiseScoreInvite.inviteCode eq code) }) {
            it[usedTimes] = times
        }
    }

    fun updateInviteTotalTimes(userId: Long, code: String, times: Int) {
        TBWiseScoreInvite.update({ (TBWiseScoreInvite.userId eq userId) and (TBWiseScoreInvite.inviteCode eq code) }) {
            it[totalTimes] = times
        }
    }

    fun mapper(r: ResultRow): WiseInviteCode {
        return WiseInviteCode(
            userId = r[TBWiseScoreInvite.userId],
            eventStage = r[TBWiseScoreInvite.eventStage],
            inviteCode = r[TBWiseScoreInvite.inviteCode],
            totalTimes = r[TBWiseScoreInvite.totalTimes],
            usedTimes = r[TBWiseScoreInvite.usedTimes]
        )
    }

    fun getInvitees(userId: Long): List<Long> {
        return TBWiseInviteRecord.select { TBWiseInviteRecord.userId eq userId }
            .orderBy(TBWiseInviteRecord.createdAt, SortOrder.ASC)
            .map { it[TBWiseInviteRecord.inviteeId] }
    }

    fun getInviteesByState(userId: Long, eventStage: Int): List<Long> {
        return TBWiseInviteRecord.select {
            TBWiseInviteRecord.userId eq userId
            TBWiseInviteRecord.eventStage eq eventStage
        }
            .orderBy(TBWiseInviteRecord.createdAt, SortOrder.ASC)
            .map { it[TBWiseInviteRecord.inviteeId] }
    }

    fun getInviteesCount(userId: Long, eventStage: Int): Int {
        return TBWiseInviteRecord.select {
            (TBWiseInviteRecord.userId eq userId) and (TBWiseInviteRecord.eventStage eq eventStage)
        }.count().toInt()
    }

    fun getInviteesCountAll(userId: Long): Int {
        return TBWiseInviteRecord.select {
            TBWiseInviteRecord.userId eq userId
        }.count().toInt()
    }


    data class InviteeWithDate(val inviteeId: Long, val createdAt: Instant)

    fun getInviteesWithDate(userId: Long, eventStage: Int = 1): List<InviteeWithDate> {
        return TBWiseInviteRecord.select {
            (TBWiseInviteRecord.userId eq userId) and (TBWiseInviteRecord.eventStage eq eventStage)
        }
            .orderBy(TBWiseInviteRecord.createdAt, SortOrder.ASC)
            .map { InviteeWithDate(it[TBWiseInviteRecord.inviteeId], it[TBWiseInviteRecord.createdAt]) }
    }

    fun getInviteesWithDateAll(userId: Long): List<InviteeWithDate> {
        return TBWiseInviteRecord.select {
            TBWiseInviteRecord.userId eq userId
        }
            .orderBy(TBWiseInviteRecord.createdAt, SortOrder.ASC)
            .map { InviteeWithDate(it[TBWiseInviteRecord.inviteeId], it[TBWiseInviteRecord.createdAt]) }
    }

    //当前邀请用户的tgName
    fun getInviteTgName(userId: Long): String? {
        return TBWiseInviteRecord.select { TBWiseInviteRecord.userId eq userId }
            .map { it[TBWiseInviteRecord.inviteTgName] }.firstOrNull()
    }

    //当前用户被邀请人的uid
    fun getInviterUid(userId: Long): Long? {
        return TBWiseInviteRecord.select { TBWiseInviteRecord.inviteeId eq userId }
            .map { it[TBWiseInviteRecord.userId] }.firstOrNull()
    }

    //当前用户被邀请人的code
    fun getInviterCode(userId: Long): String? {
        return TBWiseInviteRecord.select { TBWiseInviteRecord.inviteeId eq userId }
            .map { it[TBWiseInviteRecord.inviteCode] }.firstOrNull()
    }

    //当前用户被邀请人的tgName
    fun getInviterTgName(userId: Long): String? {
        return TBWiseInviteRecord.select { TBWiseInviteRecord.inviteeId eq userId }
            .map { it[TBWiseInviteRecord.inviteTgName] }.firstOrNull()
    }

    fun getInvitedCount(userId: Long): Int {
        return TBWiseInviteRecord.select { TBWiseInviteRecord.userId eq userId }
            .count().toInt()
    }
}