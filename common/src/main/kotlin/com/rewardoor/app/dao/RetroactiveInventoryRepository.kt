package com.rewardoor.app.dao

import com.rewardoor.app.dao.tables.TBRetroactiveCardInventory
import com.rewardoor.app.dao.tables.TBRetroactiveCardLedger
import com.rewardoor.app.dao.tables.TBRetroactiveCardLedger.toAmount
import com.rewardoor.model.RetroactiveCardInventory
import com.rewardoor.model.RetroactiveCardLedger
import org.jetbrains.exposed.sql.insertIgnore
import org.jetbrains.exposed.sql.select
import org.jetbrains.exposed.sql.update
import org.springframework.stereotype.Repository
import org.springframework.transaction.annotation.Transactional

@Repository
@Transactional
class RetroactiveInventoryRepository {
    fun getUserCardInventoryWithLock(userId: Long): RetroactiveCardInventory? {
        return TBRetroactiveCardInventory
            .select { TBRetroactiveCardInventory.userId eq userId }
            .forUpdate()
            .map { RetroactiveCardInventory(it) }.firstOrNull()
    }

    fun addUserCardInventory(inventory: RetroactiveCardInventory): Int {
        return TBRetroactiveCardInventory.insertIgnore {
            it[inventoryId] = inventory.inventoryId
            it[userId] = inventory.userId
            it[totalCards] = inventory.totalCards
            it[balanceCards] = inventory.balanceCards
            it[usedCards] = inventory.usedCards
        }.insertedCount
    }

    fun updateCardInventory(inventory: RetroactiveCardInventory): Int {
        return TBRetroactiveCardInventory.update({ TBRetroactiveCardInventory.inventoryId eq inventory.inventoryId }) {
            it[totalCards] = inventory.totalCards
            it[balanceCards] = inventory.balanceCards
            it[usedCards] = inventory.usedCards
        }
    }

    fun addCardLedger(ledger: RetroactiveCardLedger): Int {
        return TBRetroactiveCardLedger.insertIgnore {
            it[ledgerId] = ledger.ledgerId
            it[userId] = ledger.userId
            it[outId] = ledger.outId
            it[usedAmount] = ledger.usedAmount
            it[addedAmount] = ledger.addedAmount
            it[fromAmount] = ledger.fromAmount
            it[toAmount] = ledger.toAmount
        }.insertedCount
    }
}