package com.rewardoor.app.dao

import com.rewardoor.app.dao.tables.TBNft
import com.rewardoor.app.dao.tables.TBNftGroup
import com.rewardoor.model.NFT
import org.jetbrains.exposed.sql.*
import org.jetbrains.exposed.sql.SqlExpressionBuilder.eq
import org.springframework.stereotype.Repository

@Repository
class NFTRepository {
    fun createNFT(nft: NFT) {
        TBNft.insert {
            it[nftId] = nft.nftId
            it[creatorId] = nft.creatorId
            it[projectId] = nft.projectId
            it[name] = nft.name
            it[symbol] = nft.symbol
            it[contract] = nft.contract
            it[chainId] = nft.chainId
            it[coverUrl] = nft.coverUrl
            it[picUrl] = nft.picUrl
            it[mintCap] = nft.mintCap
            it[unlimited] = nft.unlimited.toString()
            it[groupId] = nft.groupId
            it[methodType] = nft.methodType
        }
    }

    fun createNFTGroup(nft: NFT) {
        TBNftGroup.insert {
            it[nftId] = nft.nftId
            it[creatorId] = nft.creatorId
            it[projectId] = nft.projectId
            it[name] = nft.name
            it[symbol] = nft.symbol
            it[contract] = nft.contract
            it[chainId] = nft.chainId
            it[coverUrl] = nft.coverUrl
            it[picUrl] = nft.picUrl
            it[mintCap] = nft.mintCap
            it[unlimited] = nft.unlimited.toString()
            it[groupId] = nft.groupId
            it[methodType] = nft.methodType
        }
    }

    fun getNFTById(nftId: Long): NFT? {
        return TBNft.select { TBNft.nftId eq nftId }.map(::mapper).firstOrNull()
    }

    fun getNFTByProjectId(projectId: Long): List<NFT> {
        return TBNft.select { TBNft.projectId eq projectId }.map(::mapper)
    }

    fun getNFTByCreatorId(creatorId: Long): List<NFT> {
        return TBNft.select { TBNft.creatorId eq creatorId }.map(::mapper)
    }

    fun getNFTByGroupId(groupId: Long): List<NFT> {
        return TBNft.select { TBNft.groupId eq groupId }.map(::mapper)
    }

    fun getNFTByPairId(nftId: Long, groupId: Long): NFT? {
        return TBNftGroup.select { (TBNftGroup.nftId eq nftId) and (TBNftGroup.groupId eq groupId) }
            .map(::nftGroupMapper)
            .firstOrNull()
    }

    fun deleteNFTByPairIds(nftGroupMap: Map<Long, Long>) {
        nftGroupMap.forEach { (nftId, groupId) ->
            TBNftGroup.deleteWhere { (TBNftGroup.nftId eq nftId) and (TBNftGroup.groupId eq groupId) }
        }
    }

    fun getNFTGroupByGroupId(groupId: Long): List<NFT> {
        return TBNftGroup.select { TBNftGroup.groupId eq groupId }.map(::nftGroupMapper)
    }

    fun getNFTGroupCntByGroupId(groupId: Long): Long {
        return TBNftGroup.select { TBNftGroup.groupId eq groupId }.count()
    }

    fun updateNFT(nft: NFT): Int {
        return TBNft.update({ TBNft.nftId eq nft.nftId }) {
            it[creatorId] = nft.creatorId
            it[projectId] = nft.projectId
            it[name] = nft.name
            it[symbol] = nft.symbol
            it[contract] = nft.contract
            it[chainId] = nft.chainId
            it[coverUrl] = nft.coverUrl
            it[picUrl] = nft.picUrl
            it[mintCap] = nft.mintCap
            it[unlimited] = nft.unlimited.toString()
            it[groupId] = nft.groupId
            it[methodType] = nft.methodType
        }
    }


    private fun mapper(r: ResultRow): NFT {
        return NFT(
            nftId = r[TBNft.nftId],
            creatorId = r[TBNft.creatorId],
            projectId = r[TBNft.projectId],
            name = r[TBNft.name],
            symbol = r[TBNft.symbol],
            contract = r[TBNft.contract],
            chainId = r[TBNft.chainId],
            groupId = r[TBNft.groupId],
            coverUrl = r[TBNft.coverUrl],
            picUrl = r[TBNft.picUrl],
            mintCap = r[TBNft.mintCap],
            unlimited = r[TBNft.unlimited].toBoolean(),
            methodType = r[TBNft.methodType]
        )
    }

    private fun nftGroupMapper(r: ResultRow): NFT {
        return NFT(
            nftId = r[TBNftGroup.nftId],
            creatorId = r[TBNftGroup.creatorId],
            projectId = r[TBNftGroup.projectId],
            name = r[TBNftGroup.name],
            symbol = r[TBNftGroup.symbol],
            contract = r[TBNftGroup.contract],
            chainId = r[TBNftGroup.chainId],
            groupId = r[TBNftGroup.groupId],
            coverUrl = r[TBNftGroup.coverUrl],
            picUrl = r[TBNftGroup.picUrl],
            mintCap = r[TBNftGroup.mintCap],
            unlimited = r[TBNftGroup.unlimited].toBoolean(),
            methodType = r[TBNftGroup.methodType]
        )
    }

    fun updateContract(nftId: Long, contract: String): Int {
        return TBNft.update({ TBNft.nftId eq nftId }) {
            it[TBNft.contract] = contract
        }
    }
}