package com.rewardoor.app.dao

import com.rewardoor.app.dao.tables.TBTwitterNameIdMapping
import org.jetbrains.exposed.sql.insertIgnore
import org.jetbrains.exposed.sql.select
import org.springframework.stereotype.Repository
import org.springframework.transaction.annotation.Transactional

@Repository
@Transactional
class TwitterMappingRepo {
    fun addMapping(name: String, id: String) {
        TBTwitterNameIdMapping.insertIgnore {
            it[twitterName] = name
            it[twitterId] = id
        }
    }

    fun getByName(name: String): String? {
        return TBTwitterNameIdMapping.select { TBTwitterNameIdMapping.twitterName eq name }.firstOrNull()?.get(TBTwitterNameIdMapping.twitterId)
    }
}