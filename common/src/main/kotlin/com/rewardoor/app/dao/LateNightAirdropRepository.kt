package com.rewardoor.app.dao

import com.rewardoor.app.dao.tables.TBLateNightAirdrop
import com.rewardoor.app.dao.tables.TBUserWiseScore
import com.rewardoor.model.LateNightAirdropUser
import com.rewardoor.model.UserWiseScore
import org.jetbrains.exposed.sql.SqlExpressionBuilder.eq
import org.jetbrains.exposed.sql.select
import org.jetbrains.exposed.sql.selectAll
import org.jetbrains.exposed.sql.trim
import org.jetbrains.exposed.sql.update
import org.springframework.stereotype.Repository
import org.springframework.transaction.annotation.Transactional

@Repository
@Transactional
class LateNightAirdropRepository {

    fun getUsersAddress(): List<String> {
        return TBLateNightAirdrop.select { TBLateNightAirdrop.transactionNum eq 0 }
            .map { it[TBLateNightAirdrop.tonAddress] }
    }

    fun updateAirdropUser(lateNightAirdropUser: LateNightAirdropUser): Int {
        return TBLateNightAirdrop.update({ TBLateNightAirdrop.tonAddress.trim() eq lateNightAirdropUser.tonAddress.trim() }) {
            it[userId] = lateNightAirdropUser.userId
            it[wiseScore] = lateNightAirdropUser.wiseScore
            it[walletAge] = lateNightAirdropUser.walletAge
            it[tonBalance] = lateNightAirdropUser.tonBalance
            it[transactionNum] = lateNightAirdropUser.transactionNum
        }
    }

}