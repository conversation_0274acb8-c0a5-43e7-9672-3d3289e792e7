package com.rewardoor.app.dao

import com.rewardoor.app.dao.tables.TBDailyLuckyDrawTimes
import com.rewardoor.app.dao.tables.TBTgLuckyDrawTimes
import com.rewardoor.model.DailyLuckyDraw
import com.rewardoor.model.TelegramLuckyDrawTimes
import org.jetbrains.exposed.sql.*
import org.springframework.stereotype.Repository
import java.time.*
import java.time.temporal.ChronoUnit

@Repository
class TgLuckyDrawTimesRepository {
    fun getLuckyDrawCnt(userId: Long): Int? {
        return TBTgLuckyDrawTimes.select { TBTgLuckyDrawTimes.userId eq userId }
            .map { it[TBTgLuckyDrawTimes.times] }.firstOrNull()
    }

    fun getLuckyDrawTimesModel(userId: Long): TelegramLuckyDrawTimes? {
        return TBTgLuckyDrawTimes.select { TBTgLuckyDrawTimes.userId eq userId }
            .map {
                TelegramLuckyDrawTimes(
                    it[TBTgLuckyDrawTimes.userId],
                    it[TBTgLuckyDrawTimes.tgUserId],
                    it[TBTgLuckyDrawTimes.times],
                    it[TBTgLuckyDrawTimes.lastDailyAddAt]
                )
            }.firstOrNull()
    }

    fun getByTgId(tgId: Long): Int? {
        return TBTgLuckyDrawTimes.select { TBTgLuckyDrawTimes.tgUserId eq tgId }
            .map { it[TBTgLuckyDrawTimes.times] }.firstOrNull()
    }

    fun addInitialLuckyDrawCnt(userId: Long, tgUserId: Long, count: Int): Int {
        return TBTgLuckyDrawTimes.insertIgnore {
            it[TBTgLuckyDrawTimes.userId] = userId
            it[TBTgLuckyDrawTimes.tgUserId] = tgUserId
            it[times] = count
        }.insertedCount
    }

    fun addLuckDrawCnt(userId: Long, count: Int): Int {
        return TBTgLuckyDrawTimes.update({ TBTgLuckyDrawTimes.userId eq userId }) {
            with(SqlExpressionBuilder) {
                it.update(times, times + count)
            }
        }
    }

    fun addTodayCnt(userId: Long, count: Int): Int {
        return TBTgLuckyDrawTimes.update({ TBTgLuckyDrawTimes.userId eq userId }) {
            it[lastDailyAddAt] = java.time.LocalDateTime.now()
            with(SqlExpressionBuilder) {
                it.update(times, times + count)
            }
        }
    }

    fun addDailyInitialCount(userId: Long, date: String, count: Int): Int {
        return TBDailyLuckyDrawTimes.insertIgnore {
            it[TBDailyLuckyDrawTimes.userId] = userId
            it[TBDailyLuckyDrawTimes.date] = date
            it[times] = count
            it[nextAddAt] = Instant.now().plus(Duration.ofDays(1)).truncatedTo(ChronoUnit.DAYS)
        }.insertedCount
    }

    fun getDailyTimeItem(userId: Long, date: String): DailyLuckyDraw? {
        return TBDailyLuckyDrawTimes.select { (TBDailyLuckyDrawTimes.userId eq userId) and (TBDailyLuckyDrawTimes.date eq date) }
            .map(::mapDailyLuckyDraw).firstOrNull()
    }

    fun getDailyTimeWithLock(userId: Long, date: String): DailyLuckyDraw? {
        return TBDailyLuckyDrawTimes.select { (TBDailyLuckyDrawTimes.userId eq userId) and
                (TBDailyLuckyDrawTimes.date eq date)
        }.forUpdate().map(::mapDailyLuckyDraw).firstOrNull()
    }

    fun updateDailyItem(userId: Long, date: String, remainTimes: Int, usedTimes: Int): Int {
        return TBDailyLuckyDrawTimes.update({ (TBDailyLuckyDrawTimes.userId eq userId) and (TBDailyLuckyDrawTimes.date eq date) }) {
            it[times] = remainTimes
            it[TBDailyLuckyDrawTimes.usedTimes] = usedTimes
        }
    }

    fun updateDailyItemWithNext(userId: Long, date: String, remainTimes: Int, usedTimes: Int, next: Instant): Int {
        return TBDailyLuckyDrawTimes.update({ (TBDailyLuckyDrawTimes.userId eq userId) and (TBDailyLuckyDrawTimes.date eq date) }) {
            it[times] = remainTimes
            it[TBDailyLuckyDrawTimes.usedTimes] = usedTimes
            it[nextAddAt] = next
        }
    }

    fun mapDailyLuckyDraw(r: ResultRow): DailyLuckyDraw {
        return DailyLuckyDraw(r[TBDailyLuckyDrawTimes.userId],
            r[TBDailyLuckyDrawTimes.times], r[TBDailyLuckyDrawTimes.usedTimes],
            r[TBDailyLuckyDrawTimes.date],
            r[TBDailyLuckyDrawTimes.nextAddAt],
            r[TBDailyLuckyDrawTimes.lastUseAt])
    }
}