package com.rewardoor.app.dao

import com.rewardoor.app.dao.tables.*
import com.rewardoor.model.*
import org.jetbrains.exposed.sql.*
import org.springframework.stereotype.Repository
import org.jetbrains.exposed.sql.transactions.transaction
import java.math.BigDecimal
import java.time.Instant

@Repository
class OntonFairLaunchRepository {

    /**
     * 查找小于指定轮次的所有记录
     */
    fun findByRoundNumLessThan(roundNum: Long): List<OntonRoundRecordData> {
        return transaction {
            TBOnTonRoundRecord
                .select { TBOnTonRoundRecord.roundNumber less roundNum }
                .orderBy(TBOnTonRoundRecord.roundNumber, SortOrder.ASC)
                .map { mapperRoundRecord(it) }
        }
    }

    /**
     * 根据轮次编号查找历史单条记录
     */
    fun findByRoundNum(roundNum: Long): OntonRoundRecordData? {
        return transaction {
            TBOnTonRoundRecord
                .select { TBOnTonRoundRecord.roundNumber eq roundNum }
                .singleOrNull()
                ?.let { mapperRoundRecord(it) }
        }
    }

    /**
     * 初始化下一轮数据 - 只写入基本字段
     */
    fun initRoundData(record: OntonRoundRecordData) {
        transaction {
            // 查询是否存在该 round_number
            val exists = TBOnTonRoundRecord.select {
                TBOnTonRoundRecord.roundNumber eq record.roundNumber
            }.count() > 0

            if (!exists) {
                // 不存在则插入基本数据
                TBOnTonRoundRecord.insert {
                    it[roundNumber] = record.roundNumber
                    it[startTime] = record.startTime
                    it[endTime] = record.endTime
                    it[price] = record.price
                    it[tonPrice] = record.tonPrice

                    // 其他字段使用默认值
                    it[totalRaisedTon] = 0L
                    it[totalRaisedUsdt] = 0L
                    it[tokensSold] = 0L
                    it[purchaseCount] = 0L
                    it[uniqueUsers] = 0L
                    it[refundCount] = 0L
                    it[refundedAmountTon] = 0L
                    it[refundedAmountUsdt] = 0L
                    it[totalRaised] = BigDecimal.ZERO
                }
            }
        }
    }

    /**
     * 更新指定轮次的总计数据 - 当total_raised不存在时写入
     */
    fun updateRoundTotal(roundNumber: Long, record: OntonRoundContractUpdateData) {
        transaction {
            // 查找指定轮次的记录
            val existingRecord = TBOnTonRoundRecord.select {
                TBOnTonRoundRecord.roundNumber eq roundNumber
            }.singleOrNull()

            if (existingRecord != null) {
                // 检查tokens_sold是否为0（表示未设置）
                val currentTokensSold = existingRecord[TBOnTonRoundRecord.tokensSold]

                if (currentTokensSold == 0L) {

                    // 更新总计数据
                    TBOnTonRoundRecord.update({ TBOnTonRoundRecord.roundNumber eq roundNumber }) {
                        it[totalRaisedTon] = record.totalRaisedTon
                        it[totalRaisedUsdt] = record.totalRaisedUsdt
                        it[tokensSold] = record.tokensSold
                        it[purchaseCount] = record.purchaseCount
                        it[uniqueUsers] = record.uniqueUsers
                        it[refundCount] = record.refundCount
                        it[refundedAmountTon] = record.refundedAmountTon
                        it[refundedAmountUsdt] = record.refundedAmountUsdt
                        it[totalRaised] = record.totalRaised
                        it[isRoundEnded] = 1 // 标记轮次已结束
                        // 根据需要设置 isHardCapReached
                    }
                }
            }
        }
    }

//    /**
//     * 计算总筹集金额（USDT等值）
//     * @param totalRaisedTon TON金额（精度9）
//     * @param totalRaisedUsdt USDT金额（精度6）
//     * @param tonPrice TON价格（USDT）
//     */
//    private fun calculateTotalRaised(
//        totalRaisedTon: Long,
//        totalRaisedUsdt: Long,
//        tonPrice: BigDecimal
//    ): BigDecimal {
//        // 将TON转换为USDT精度（除以10^3，因为TON是10^9精度，USDT是10^6精度）
//        val tonAmountInUsdtPrecision = BigDecimal(totalRaisedTon).divide(BigDecimal("1000"))
//
//        val tonInUsdt = tonAmountInUsdtPrecision.multiply(tonPrice)
//
//        val total = tonInUsdt.plus(totalRaisedUsdt.toBigDecimal())
//
//        // 计算TON的USDT等值 + 直接的USDT金额
//        return total
//    }

    fun updateRoundEnded(roundNumber: Long): Boolean {
        println("updateRoundEnded: roundNumber=$roundNumber")
        return transaction {
            val updatedRows = TBOnTonRoundRecord.update({
                TBOnTonRoundRecord.roundNumber eq roundNumber
            }) {
                it[isRoundEnded] = 1
            }
            updatedRows > 0 // 返回是否有行被更新
        }
    }

    /**
     * 获取最近的已结束轮次（按roundNumber排序，is_round_ended=1）
     */
    fun getLastEndedRound(): OntonRoundRecordData? {
        println("getLastEndedRound")
        return transaction {
            TBOnTonRoundRecord
                .select { TBOnTonRoundRecord.isRoundEnded eq 1 }
                .orderBy(TBOnTonRoundRecord.roundNumber, SortOrder.DESC)
                .limit(1)
                .singleOrNull()
                ?.let { mapperRoundRecord(it) }
        }
    }
}


private fun mapperRoundRecord(row: ResultRow): OntonRoundRecordData {
    return OntonRoundRecordData(
        roundNumber = row[TBOnTonRoundRecord.roundNumber],
        startTime = row[TBOnTonRoundRecord.startTime],
        endTime = row[TBOnTonRoundRecord.endTime],
        price = row[TBOnTonRoundRecord.price],
        totalRaisedTon = row[TBOnTonRoundRecord.totalRaisedTon],
        totalRaisedUsdt = row[TBOnTonRoundRecord.totalRaisedUsdt],
        tokensSold = row[TBOnTonRoundRecord.tokensSold],
        purchaseCount = row[TBOnTonRoundRecord.purchaseCount],
        uniqueUsers = row[TBOnTonRoundRecord.uniqueUsers],
        refundCount = row[TBOnTonRoundRecord.refundCount],
        refundedAmountTon = row[TBOnTonRoundRecord.refundedAmountTon],
        refundedAmountUsdt = row[TBOnTonRoundRecord.refundedAmountUsdt],
        tonPrice = row[TBOnTonRoundRecord.tonPrice],
        totalRaised = row[TBOnTonRoundRecord.totalRaised],
        isRoundEnded = row[TBOnTonRoundRecord.isRoundEnded],
        isHardCapReached = row[TBOnTonRoundRecord.isHardCapReached],
    )
}