package com.rewardoor.app.dao

import com.rewardoor.app.dao.tables.TBTwitterStateChallenge
import org.jetbrains.exposed.sql.insert
import org.jetbrains.exposed.sql.select
import org.springframework.stereotype.Repository

@Repository
class TwitterStateChallengeRepository {
    fun addStateChallengeCache(state: String, challenge: String) {
        TBTwitterStateChallenge.insert {
            it[TBTwitterStateChallenge.state] = state
            it[TBTwitterStateChallenge.challenge] = challenge
        }
    }

    fun getChallengeByState(state: String): String? {
        return TBTwitterStateChallenge.select { TBTwitterStateChallenge.state eq state }.limit(1).map { it[TBTwitterStateChallenge.challenge] }.firstOrNull()
    }
}