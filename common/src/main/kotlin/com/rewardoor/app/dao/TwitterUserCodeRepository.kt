package com.rewardoor.app.dao

import com.rewardoor.app.dao.tables.TBTwitterUserCode
import com.rewardoor.app.dao.tables.TBUserTwitter
import com.rewardoor.model.UserTwitterCode
import org.jetbrains.exposed.sql.ResultRow
import org.jetbrains.exposed.sql.insert
import org.jetbrains.exposed.sql.select
import org.jetbrains.exposed.sql.update
import org.springframework.stereotype.Repository
import org.springframework.transaction.annotation.Transactional

@Repository
@Transactional
class TwitterUserCodeRepository {
    fun addUserCode(info: UserTwitterCode) {
        TBTwitterUserCode.insert {
            it[userId] = info.userId
            it[userCode] = info.userCode
            it[status] = info.status
            it[tweetId] = info.tweetId
            it[twitterId] = info.twitterId
            it[twitterName] = info.twitterName
        }
    }

    fun updateUserCode(info: UserTwitterCode) {
        TBTwitterUserCode.update({ TBTwitterUserCode.userId eq info.userId }) {
            it[status] = info.status
            it[tweetId] = info.tweetId
            it[twitterId] = info.twitterId
            it[twitterName] = info.twitterName
        }
    }

    fun getUserCodeByUserId(userId: Long): UserTwitterCode? {
        return TBTwitterUserCode.select { TBTwitterUserCode.userId eq userId }.limit(1).map(::mapTwitterUserCode)
            .firstOrNull()
    }

    fun getUserCodeByCode(code: String): UserTwitterCode? {
        return TBTwitterUserCode.select { TBTwitterUserCode.userCode eq code }.limit(1).map(::mapTwitterUserCode)
            .firstOrNull()
    }

    fun mapTwitterUserCode(r: ResultRow): UserTwitterCode {
        return UserTwitterCode(
            r[TBTwitterUserCode.userId],
            r[TBTwitterUserCode.userCode],
            r[TBTwitterUserCode.status],
            r[TBTwitterUserCode.tweetId],
            r[TBTwitterUserCode.twitterId],
            r[TBTwitterUserCode.twitterName]
        )
    }
}