package com.rewardoor.app.dao

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import com.rewardoor.app.dao.tables.TBProject
import com.rewardoor.app.dao.tables.TBCompany
import com.rewardoor.app.dao.tables.TBCompanyConfig
import com.rewardoor.model.Project
import org.jetbrains.exposed.sql.*
import com.rewardoor.model.*
import org.springframework.stereotype.Repository

@Repository
class CompanyRepository(val om: ObjectMapper) {

    fun getInfoById(companyId: Long): Company? {
        return TBCompany.select { TBCompany.companyId eq companyId }.limit(1).map(::mapCompany).first()
    }

    fun getCompanyConfig(companyId: Long):  Map<String, String> {
        return TBCompanyConfig
            .select { TBCompanyConfig.companyId eq companyId }
            .associate { row ->
            row[TBCompanyConfig.configKey] to row[TBCompanyConfig.configValue]
        }
    }

    fun mapCompany(r: ResultRow): Company {
        return Company(
            r[TBCompany.companyId],
            r[TBCompany.companyName],
            r[TBCompany.creatorId],
            r[TBCompany.companyDescription].orEmpty(),
            r[TBCompany.logo],
            r[TBCompany.channelName],
            r[TBCompany.channelLink],
            r[TBCompany.twitterLink],
            r[TBCompany.webUrl],
            r[TBCompany.aboutBgImage]
        )
    }

}