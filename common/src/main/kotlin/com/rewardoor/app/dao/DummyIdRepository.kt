package com.rewardoor.app.dao

import com.rewardoor.app.dao.tables.TBDummyId
import org.jetbrains.exposed.sql.and
import org.jetbrains.exposed.sql.insertIgnoreAndGetId
import org.jetbrains.exposed.sql.select
import org.springframework.stereotype.Repository

@Repository
class DummyIdRepository {
    fun getDummyId(userId: Long, groupId: Long): Long {
        val id = TBDummyId.insertIgnoreAndGetId {
            it[this.userId] = userId;
            it[this.groupId] = groupId
        }?.value
        if (id != null) return id
        return TBDummyId.select { TBDummyId.userId eq userId and (TBDummyId.groupId eq groupId) }.first()[TBDummyId.id].value
    }
}