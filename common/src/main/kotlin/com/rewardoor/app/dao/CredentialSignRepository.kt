package com.rewardoor.app.dao

import com.rewardoor.app.dao.tables.TBCredentialSign
import com.rewardoor.model.CredentialSign
import org.jetbrains.exposed.sql.*
import org.springframework.stereotype.Repository
import java.time.LocalDateTime

@Repository
class CredentialSignRepository {
    fun addCredentialRawData(userId: Long, credentialId: Long, rawData: String) {
        TBCredentialSign.insert {
            it[TBCredentialSign.userId] = userId
            it[TBCredentialSign.credentialId] = credentialId
            it[TBCredentialSign.rawData] = rawData
        }
    }

    fun updateCredentialRawData(userId: Long, credentialId: Long, rawData: String) {
        TBCredentialSign.update({TBCredentialSign.userId.eq(userId) and TBCredentialSign.credentialId.eq(credentialId)}) {
            it[TBCredentialSign.rawData] = rawData
            it[TBCredentialSign.verified] = 0
        }
    }

    fun updateCredentialVerified(userId: Long, credentialId: Long, sign: String, verified: Int) {
        TBCredentialSign.update({TBCredentialSign.userId.eq(userId) and TBCredentialSign.credentialId.eq(credentialId)}) {
            it[TBCredentialSign.sign] = sign
            it[TBCredentialSign.verified] = verified
            it[TBCredentialSign.signTime] = LocalDateTime.now()
        }
    }

    fun getCredentialSign(userId: Long, credentialId: Long): CredentialSign? {
        return TBCredentialSign.select { TBCredentialSign.userId.eq(userId) and TBCredentialSign.credentialId.eq(credentialId) }
            .map(::mapper).firstOrNull()
    }

    private fun mapper(resultRow: ResultRow): CredentialSign {
        return CredentialSign(
            userId = resultRow[TBCredentialSign.userId],
            credentialId = resultRow[TBCredentialSign.credentialId],
            rawData = resultRow[TBCredentialSign.rawData],
            sign = resultRow[TBCredentialSign.sign],
            verified = resultRow[TBCredentialSign.verified] == 1,
            signTime = resultRow[TBCredentialSign.signTime]
        )
    }
}