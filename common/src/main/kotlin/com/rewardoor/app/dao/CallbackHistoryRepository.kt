package com.rewardoor.app.dao

import com.rewardoor.app.dao.tables.TBCallbackHistory
import com.rewardoor.model.CallbackHistory
import org.jetbrains.exposed.sql.and
import org.jetbrains.exposed.sql.insertIgnore
import org.jetbrains.exposed.sql.select
import org.jetbrains.exposed.sql.update
import org.springframework.stereotype.Repository
import org.springframework.transaction.annotation.Transactional

@Repository
@Transactional
class CallbackHistoryRepository {

    fun getCallbackHistory(credentialId: Long, userId: Long): CallbackHistory? {
        return TBCallbackHistory.select { TBCallbackHistory.credentialId eq credentialId and (TBCallbackHistory.userId eq userId) }.limit(1).map{
            CallbackHistory(
                it[TBCallbackHistory.projectId],
                it[TBCallbackHistory.credentialId],
                it[TBCallbackHistory.userId],
                it[TBCallbackHistory.callbackUrl],
                it[TBCallbackHistory.status],
                it[TBCallbackHistory.times],
                it[TBCallbackHistory.content]
            )
        }.firstOrNull()
    }

    fun addCallbackHistory(history: CallbackHistory) {
        TBCallbackHistory.insertIgnore {
            it[projectId] = history.projectId
            it[credentialId] = history.credentialId
            it[userId] = history.userId
            it[callbackUrl] = history.callbackUrl
            it[status] = history.status
            it[times] = history.times
            it[content] = history.content
        }
    }

    fun updateTimes(credentialId: Long, userId: Long, times: Int, status: String) {
        TBCallbackHistory.update({ TBCallbackHistory.credentialId eq credentialId and (TBCallbackHistory.userId eq userId) }) {
            it[TBCallbackHistory.times] = times
            it[TBCallbackHistory.status] = status
        }
    }

    fun getFailed(startId: Long): List<CallbackHistory> {
        return TBCallbackHistory.select { TBCallbackHistory.id.greater(startId) and
                (TBCallbackHistory.status eq "FAILED")and
                (TBCallbackHistory.times.less(100)) }.limit(200).map{
            CallbackHistory(
                it[TBCallbackHistory.projectId],
                it[TBCallbackHistory.credentialId],
                it[TBCallbackHistory.userId],
                it[TBCallbackHistory.callbackUrl],
                it[TBCallbackHistory.status],
                it[TBCallbackHistory.times],
                it[TBCallbackHistory.content]
            )
        }
    }
}