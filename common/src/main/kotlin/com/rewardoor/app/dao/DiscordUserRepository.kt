package com.rewardoor.app.dao

import com.rewardoor.app.dao.tables.TBUserDiscord
import com.rewardoor.app.dao.tables.TBUserEvm
import com.rewardoor.model.UserDiscord
import org.jetbrains.exposed.sql.*
import org.jetbrains.exposed.sql.SqlExpressionBuilder.eq
import org.springframework.stereotype.Repository

@Repository
class DiscordUserRepository {
    fun addDcUser(ud: UserDiscord) {
        TBUserDiscord.insert {
            it[userId] = ud.userId
            it[dcId] = ud.dcId
            it[username] = ud.username
            it[avatar] = ud.avatar
            it[globalName] = ud.globalName
            it[accessToken] = ud.accessToken
            it[refreshToken] = ud.refreshToken
            it[tokenExpire] = ud.tokenExpire
            it[connected] = if (ud.connected) 1 else 0
        }
    }

    fun updateDcUser(ud: UserDiscord) {
        TBUserDiscord.update({ TBUserDiscord.userId eq ud.userId }) {
            it[dcId] = ud.dcId
            it[username] = ud.username
            it[avatar] = ud.avatar
            it[globalName] = ud.globalName
            it[accessToken] = ud.accessToken
            it[refreshToken] = ud.refreshToken
            it[tokenExpire] = ud.tokenExpire
            it[connected] = if (ud.connected) 1 else 0
        }
    }

    fun updateDcToken(ud: UserDiscord) {
        TBUserDiscord.update({ TBUserDiscord.userId eq ud.userId }) {
            it[accessToken] = ud.accessToken
            it[refreshToken] = ud.refreshToken
            it[tokenExpire] = ud.tokenExpire
        }
    }

    fun getUserDcInfo(userId: Long): UserDiscord? {
        return TBUserDiscord.select { TBUserDiscord.userId eq userId }.map(::mapper).firstOrNull()
    }

    fun deleteDcInfo(userId: Long): Int {
        return TBUserDiscord.deleteWhere { TBUserDiscord.userId eq userId }
    }

    fun updateDcUserId(uid: Long, newUserId: Long) {
        TBUserDiscord.update({ TBUserDiscord.userId eq uid }) {
            it[userId] = newUserId
        }
    }

    fun getDcUserCnt(): Long {
        return TBUserDiscord.slice(TBUserDiscord.dcId.count()).selectAll()
            .single()[TBUserDiscord.dcId.count()]
    }

    fun mapper(rs: ResultRow): UserDiscord {
        return UserDiscord(
            rs[TBUserDiscord.userId],
            rs[TBUserDiscord.dcId],
            rs[TBUserDiscord.username],
            rs[TBUserDiscord.avatar],
            rs[TBUserDiscord.globalName],
            rs[TBUserDiscord.accessToken],
            rs[TBUserDiscord.refreshToken],
            rs[TBUserDiscord.tokenExpire],
            rs[TBUserDiscord.connected] == 1
        )
    }

    fun getUserDcInfoByDcId(dcId: String): UserDiscord? {
        return TBUserDiscord.select { TBUserDiscord.dcId eq dcId }.map(::mapper).firstOrNull()
    }
}