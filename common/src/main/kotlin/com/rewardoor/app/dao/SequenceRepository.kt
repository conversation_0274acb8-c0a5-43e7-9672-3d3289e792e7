package com.rewardoor.app.dao

import org.jetbrains.exposed.sql.transactions.TransactionManager
import org.springframework.stereotype.Repository
import java.sql.ResultSet

@Repository
class SequenceRepository {
    fun getNewId(): Long {
        "replace into tb_sequence(place_holder) value ('a'); ".execAndMap {}
        return ("select last_insert_id() as id").execAndMap {
            it.getLong("id")
        }.first()
    }

    fun <T : Any> String.execAndMap(transform: (ResultSet) -> T): List<T> {
        val result = arrayListOf<T>()
        TransactionManager.current().exec(this) { rs ->
            while (rs.next()) {
                result += transform(rs)
            }
        }
        return result
    }
}
