package com.rewardoor.app.dao

import com.rewardoor.app.dao.tables.TBUserCheckIn
import com.rewardoor.app.dao.tables.TBUserCheckIn.checkInDate
import com.rewardoor.app.dao.tables.TBUserSignCard
import com.rewardoor.app.dao.tables.TBUserStreaks
import com.rewardoor.model.WiseScoreUserCheckIn
import com.rewardoor.model.WiseScoreUserStreak
import org.jetbrains.exposed.sql.*
import org.springframework.stereotype.Repository
import org.springframework.transaction.annotation.Transactional
import java.time.Instant

@Repository
@Transactional
class WiseScoreUserStreakRepository {

    fun addUserCheckIn(uid: Long, date: Instant): Int {
        return TBUserCheckIn.insertIgnore {
            it[userId] = uid
            it[checkInDate] = date
        }.insertedCount
    }

    fun getUserCheckInByUidAndDate(uid: Long, date: Instant): WiseScoreUserCheckIn? {
        return TBUserCheckIn
            .select { (TBUserCheckIn.userId eq uid) and (TBUserCheckIn.checkInDate eq date) }
            .map {
                WiseScoreUserCheckIn(
                    it[TBUserCheckIn.userId],
                    it[checkInDate]
                )
            }
            .firstOrNull()
    }

    fun getUserCheckIns(uid: Long): List<WiseScoreUserCheckIn> {
        return TBUserCheckIn
            .select { TBUserCheckIn.userId eq uid }
            .orderBy(checkInDate, SortOrder.ASC) // 按打卡日期升序排序
            .map {
                WiseScoreUserCheckIn(
                    it[TBUserCheckIn.userId],
                    it[checkInDate]
                )
            }
    }


    fun addUserStreak(userStreak: WiseScoreUserStreak): Int {
        return TBUserStreaks.insertIgnore {
            it[userId] = userStreak.userId
            it[currentStreak] = userStreak.currentStreak
            it[maxStreak] = userStreak.maxStreak
            it[maxStreakBeginDate] = userStreak.maxStreakBeginDate
            it[maxStreakEndDate] = userStreak.maxStreakEndDate
            it[lastCheckInDate] = userStreak.lastCheckInDate
            it[totalSignCardsUsed] = userStreak.totalSignCardsUsed ?: 0
        }.insertedCount
    }

    fun getUserStreak(uid: Long): WiseScoreUserStreak? {
        return TBUserStreaks.select { TBUserStreaks.userId eq uid }.map(::mapWiseScoreUserStreak)
            .firstOrNull()
    }

    fun getUserBackCheckInRecord(uid: Long): List<Instant> {
        return TBUserSignCard.select { TBUserSignCard.userId eq uid }.orderBy(TBUserSignCard.createdAt, SortOrder.DESC)
            .map { it[TBUserSignCard.missedDate] }
    }

    fun updateUserStreak(userStreak: WiseScoreUserStreak): Int {
        return TBUserStreaks.update({ TBUserStreaks.userId eq userStreak.userId }) {
            it[currentStreak] = userStreak.currentStreak
            it[maxStreak] = userStreak.maxStreak
            it[maxStreakBeginDate] = userStreak.maxStreakBeginDate
            it[maxStreakEndDate] = userStreak.maxStreakEndDate
            it[lastCheckInDate] = userStreak.lastCheckInDate
            it[totalSignCardsUsed] = userStreak.totalSignCardsUsed ?: 0
        }
    }

    fun mapWiseScoreUserStreak(row: ResultRow): WiseScoreUserStreak {
        return WiseScoreUserStreak(
            userId = row[TBUserStreaks.userId],
            currentStreak = row[TBUserStreaks.currentStreak],
            maxStreak = row[TBUserStreaks.maxStreak],
            maxStreakBeginDate = row[TBUserStreaks.maxStreakBeginDate],
            maxStreakEndDate = row[TBUserStreaks.maxStreakEndDate],
            lastCheckInDate = row[TBUserStreaks.lastCheckInDate],
            totalSignCardsUsed = row[TBUserStreaks.totalSignCardsUsed]
        )
    }

    fun addUserSignCard(uid: Long, missDate: Instant, level: Int, price: Double): Int {
        return TBUserSignCard.insertIgnore {
            it[userId] = uid
            it[missedDate] = missDate
            it[cardLevel] = level
            it[cardPrice] = price
        }.insertedCount
    }

}