package com.rewardoor.app.dao

import com.rewardoor.app.dao.tables.TBUserDiscord
import com.rewardoor.app.dao.tables.TBUserEvm
import com.rewardoor.app.dao.tables.TBUserTelegram
import com.rewardoor.app.dto.TgAuthCallbackReq
import com.rewardoor.model.UserTelegramInfo
import org.jetbrains.exposed.sql.*
import org.jetbrains.exposed.sql.SqlExpressionBuilder.eq
import org.springframework.stereotype.Repository
import java.time.Instant
import java.time.LocalDateTime
import java.time.ZoneOffset

@Repository
class TelegramUserRepository {

    fun addTgUser(uid: Long, info: TgAuthCallbackReq) {
        TBUserTelegram.insert {
            it[userId] = uid
            it[tgId] = info.id
            it[firstName] = info.firstName.orEmpty()
            it[lastName] = info.lastName.orEmpty()
            it[username] = info.username.orEmpty()
            it[photoUrl] = info.photoUrl.orEmpty()
            it[authDate] = LocalDateTime.ofInstant(Instant.ofEpochMilli(info.authDate * 1000), ZoneOffset.UTC)
            it[connected] = 1
        }
    }

    fun updateTgUser(uid: Long, info: TgAuthCallbackReq) {
        TBUserTelegram.update({ TBUserTelegram.userId eq uid }) {
            it[tgId] = info.id
            it[firstName] = info.firstName.orEmpty()
            it[lastName] = info.lastName.orEmpty()
            it[username] = info.username.orEmpty()
            it[photoUrl] = info.photoUrl.orEmpty()
            it[authDate] = LocalDateTime.ofInstant(Instant.ofEpochMilli(info.authDate * 1000), ZoneOffset.UTC)
            it[connected] = 1
        }
    }

    fun getTgUser(userId: Long): UserTelegramInfo? {
        return TBUserTelegram.select { TBUserTelegram.userId eq userId }.limit(1).map(::mapUserTelegram).firstOrNull()
    }

    fun getTgUsers(userIds: List<Long>): List<UserTelegramInfo> {
        return TBUserTelegram.select { TBUserTelegram.userId inList userIds }.map(::mapUserTelegram)
    }

    fun getTgUserIdList(): List<Long> {
        return TBUserTelegram.select { TBUserTelegram.username neq "" }.map { it[TBUserTelegram.userId] }.distinct()
    }

    fun deleteTgUser(userId: Long): Int {
        return TBUserTelegram.deleteWhere { TBUserTelegram.userId eq userId }
    }

    fun updateTgUserId(uid: Long, newUserId: Long) {
        TBUserTelegram.update({ TBUserTelegram.userId eq uid }) {
            it[userId] = newUserId
        }
    }

    private fun mapUserTelegram(resultRow: ResultRow): UserTelegramInfo {
        return UserTelegramInfo(
            resultRow[TBUserTelegram.userId],
            resultRow[TBUserTelegram.tgId],
            resultRow[TBUserTelegram.firstName],
            resultRow[TBUserTelegram.lastName],
            resultRow[TBUserTelegram.username],
            resultRow[TBUserTelegram.photoUrl],
            resultRow[TBUserTelegram.connected] == 1
        )
    }

    fun getTgUserByTgId(tgId: Long): UserTelegramInfo? {
        return TBUserTelegram.select { TBUserTelegram.tgId eq tgId }.limit(1).map(::mapUserTelegram).firstOrNull()
    }

    fun getTgUserCnt(): Long {
        return TBUserTelegram.slice(TBUserTelegram.tgId.count()).selectAll()
            .single()[TBUserTelegram.tgId.count()]
    }
}