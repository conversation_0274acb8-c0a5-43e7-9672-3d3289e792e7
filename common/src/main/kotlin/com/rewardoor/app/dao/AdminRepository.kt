package com.rewardoor.app.dao

import com.rewardoor.app.dao.tables.TBAdmin
import com.rewardoor.model.Admin
import org.jetbrains.exposed.sql.*
import org.jetbrains.exposed.sql.SqlExpressionBuilder.eq
import org.jetbrains.exposed.sql.SqlExpressionBuilder.inList
import org.springframework.stereotype.Repository
import org.web3j.crypto.Wallet

@Repository
class AdminRepository {

    fun addAdmin(admin: Admin) {
        TBAdmin.insert {
            it[userId] = admin.userId
            it[wallet] = admin.wallet
            it[projectId] = admin.projectId
            it[creatorId] = admin.creatorId
            it[isOwner] = admin.isOwner
            it[status] = admin.status
        }
    }

    fun getAdminByWalletAndProjectId(wallet: String, projectId: Long): Admin? {
        return TBAdmin
            .select { (TBAdmin.wallet eq wallet) and (TBAdmin.projectId eq projectId) }
            .map(::mapAdmin).firstOrNull()
    }

    fun getAdminByWallet(wallet: String): Admin? {
        return TBAdmin
            .select { (TBAdmin.wallet eq wallet) }
            .map(::mapAdmin).firstOrNull()
    }

    fun getAdminByUserId(userId: Long): Admin? {
        return TBAdmin
            .select { (TBAdmin.userId eq userId) }
            .map(::mapAdmin).firstOrNull()
    }

    fun getAllAdmins(): List<Admin> {
        return TBAdmin.selectAll().map(::mapAdmin)
    }

    fun updateAdmin(admin: Admin): Int {
        return TBAdmin.update({ (TBAdmin.wallet eq admin.wallet) and (TBAdmin.projectId eq admin.projectId) }) {
            it[userId] = admin.userId
            it[wallet] = admin.wallet
            it[projectId] = admin.projectId
            it[creatorId] = admin.creatorId
            it[isOwner] = admin.isOwner
            it[status] = admin.status
        }
    }

    fun getAdmins(projectId: Long): List<Admin> {
        return TBAdmin
            .select { (TBAdmin.projectId eq projectId) and (TBAdmin.status eq 1) }
            .map(::mapAdmin)
    }

    fun mapAdmin(r: ResultRow): Admin {
        return Admin(
            r[TBAdmin.userId],
            r[TBAdmin.wallet],
            r[TBAdmin.projectId],
            r[TBAdmin.creatorId],
            r[TBAdmin.isOwner],
            r[TBAdmin.status]
        )
    }
}