package com.rewardoor.app.dao

import java.time.Instant
import java.time.Duration
import com.rewardoor.app.dao.tables.TBUserDiscordVoiceAttendance
import org.jetbrains.exposed.sql.*
import org.springframework.stereotype.Repository
import com.rewardoor.model.DiscordVoiceChannel

@Repository
class DiscordVoiceAttendanceRepository {
    fun getActiveAttendance(
        dcUserId: String,
        guildId: String,
        channelId: String
    ): DiscordVoiceChannel? {
        val resultRow = TBUserDiscordVoiceAttendance.select {
            (TBUserDiscordVoiceAttendance.dcUserId eq dcUserId) and
                    (TBUserDiscordVoiceAttendance.guildId eq guildId) and
                    (TBUserDiscordVoiceAttendance.channelId eq channelId) and
                    (TBUserDiscordVoiceAttendance.leaveTime.isNull())
        }.orderBy(TBUserDiscordVoiceAttendance.joinTime, SortOrder.ASC)
            .limit(1)
            .firstOrNull()

        return resultRow?.let {
            DiscordVoiceChannel(
                userId = it[TBUserDiscordVoiceAttendance.userId],
                dcUserId = it[TBUserDiscordVoiceAttendance.dcUserId],
                guildId = it[TBUserDiscordVoiceAttendance.guildId],
                channelId = it[TBUserDiscordVoiceAttendance.channelId],
                joinTime = it[TBUserDiscordVoiceAttendance.joinTime],
                leaveTime = it[TBUserDiscordVoiceAttendance.leaveTime],
                duration = it[TBUserDiscordVoiceAttendance.duration]
            )
        }
    }

    fun insertAttendance(
        userId: Long,
        dcUserId: String,
        guildId: String,
        channelId: String,
        joinTime: Instant,
        leaveTime: Instant ? = null,
        duration: Long
    ) {
        TBUserDiscordVoiceAttendance.insert {
            it[this.userId] = userId
            it[this.dcUserId] = dcUserId
            it[this.guildId] = guildId
            it[this.channelId] = channelId
            it[this.joinTime] = joinTime
            it[this.leaveTime] = leaveTime
            it[this.duration] = duration
        }
    }

    fun updateAttendance(
        dcUserId: String,
        guildId: String,
        channelId: String,
        leaveTime: Instant,
        duration: Long
    ) {
        TBUserDiscordVoiceAttendance.update({
            (TBUserDiscordVoiceAttendance.dcUserId eq dcUserId) and
                    (TBUserDiscordVoiceAttendance.guildId eq guildId) and
                    (TBUserDiscordVoiceAttendance.channelId eq channelId) and
                    (TBUserDiscordVoiceAttendance.leaveTime.isNull())
        }) {
            it[TBUserDiscordVoiceAttendance.leaveTime] = leaveTime
            it[TBUserDiscordVoiceAttendance.duration] = duration
        }
    }


    /**
     * 判断用户在 指定 channelId 语音频道出席时长，需要在单条数据内有满足 大于等于 targetDuration 的返回 true
     * 包括已完成的会话和当前活跃的会话
     */
    fun getUserAttendanceDuration(targetDuration: Long ,userId: Long, dcUserId: String, guildId: String, channelId: String, startTime: Long, endTime: Long): Boolean {

        val startTimeInstant = Instant.ofEpochMilli(startTime)
        val endTimeInstant = Instant.ofEpochMilli(endTime)

        val records = TBUserDiscordVoiceAttendance.select {
                    (TBUserDiscordVoiceAttendance.dcUserId eq dcUserId) and
                    (TBUserDiscordVoiceAttendance.guildId eq guildId) and
                    (TBUserDiscordVoiceAttendance.channelId eq channelId)
        }
        println("查询用户: $dcUserId")
        println("totalDuration - $targetDuration")
        println("活动时间: $startTimeInstant 到 $endTimeInstant")

        // 遍历记录
        records.forEach { row ->

            val joinTime = row[TBUserDiscordVoiceAttendance.joinTime]
            val leaveTime = row[TBUserDiscordVoiceAttendance.leaveTime] ?: Instant.now() // 如果未离开，使用当前时间

            println("会话记录: 加入时间=$joinTime, 离开时间=$leaveTime")

            // 检查会话是否与活动时间段有重叠
            if (joinTime.isBefore(endTimeInstant) && leaveTime.isAfter(startTimeInstant)) {
                // 计算有效开始时间（取会话开始和活动开始的较晚者）
                val effectiveStartTime = if (joinTime.isBefore(startTimeInstant))
                    startTimeInstant else joinTime

                // 计算有效结束时间（取会话结束和活动结束的较早者）
                val effectiveEndTime = if (leaveTime.isAfter(endTimeInstant))
                    endTimeInstant else leaveTime

                // 计算有效时长
                val effectiveDuration = Duration.between(effectiveStartTime, effectiveEndTime).seconds

                println("有效时间段: $effectiveStartTime 到 $effectiveEndTime")
                println("有效时长: $effectiveDuration 秒")

                // 检查有效时长是否达到目标
                if (effectiveDuration >= targetDuration) {
                    return true
                }
            } else {
                println("会话与活动时间无重叠")
                return false
            }

//            // 检查已完成的会话
//            if (row[TBUserDiscordVoiceAttendance.leaveTime] != null) {
//                val duration = row[TBUserDiscordVoiceAttendance.duration]
//                println("已完成会话 duration: $duration")
//                if (duration >= targetDuration) {
//                    return true
//                }
//            }
//            else {
//                val joinTime = row[TBUserDiscordVoiceAttendance.joinTime]
//                val currentDuration = Duration.between(joinTime, Instant.now()).seconds
//                println("活跃会话 currentDuration: $currentDuration")
//                if (currentDuration >= targetDuration) {
//                    return true
//                }
//            }
        }

        return false
    }
}
