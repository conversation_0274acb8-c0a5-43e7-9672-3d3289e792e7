package com.rewardoor.app.dao

import com.rewardoor.app.dao.tables.TBAirDrop
import com.rewardoor.model.AirDrop
import org.springframework.stereotype.Repository
import org.jetbrains.exposed.sql.*
import org.springframework.transaction.annotation.Transactional

@Repository
@Transactional
class AirDropRepository {
    fun createAirDrop(airDrop: AirDrop) {
        TBAirDrop.insert {
            it[airDropId] = airDrop.airDropId
            it[chainId] = airDrop.chainId
            it[chainName] = airDrop.chainName
            it[projectName] = airDrop.projectName
            it[projectDesc] = airDrop.projectDesc
            it[socialLink] = airDrop.socialLink
            it[airDropDesc] = airDrop.airDropDesc
            it[bannerUrl] = airDrop.bannerUrl
            it[title] = airDrop.title
            it[description] = airDrop.description
            it[details] = airDrop.details
            it[tokenName] = airDrop.tokenName
            it[tokenContractAddress] = airDrop.tokenContractAddress
            it[airdropContractAddress] = airDrop.airdropContractAddress
            it[gas] = airDrop.gas
            it[status] = airDrop.status
            it[checkStartTime] = airDrop.checkStartTime
            it[checkCntStartTime] = airDrop.checkCntStartTime
            it[claimStartTime] = airDrop.claimStartTime
            it[endTime] = airDrop.endTime
        }
    }

    fun updateAirDrop(airDrop: AirDrop, airDropId: Long) {
        TBAirDrop.update({ TBAirDrop.airDropId eq airDropId }) { // 条件：根据 `id` 更新
            it[projectName] = airDrop.projectName
            it[chainId] = airDrop.chainId
            it[chainName] = airDrop.chainName
            it[projectDesc] = airDrop.projectDesc
            it[socialLink] = airDrop.socialLink
            it[airDropDesc] = airDrop.airDropDesc
            it[bannerUrl] = airDrop.bannerUrl
            it[title] = airDrop.title
            it[description] = airDrop.description
            it[details] = airDrop.details
            it[tokenName] = airDrop.tokenName
            it[tokenContractAddress] = airDrop.tokenContractAddress
            it[airdropContractAddress] = airDrop.airdropContractAddress
            it[gas] = airDrop.gas
            it[status] = airDrop.status
            it[checkStartTime] = airDrop.checkStartTime
            it[checkCntStartTime] = airDrop.checkCntStartTime
            it[claimStartTime] = airDrop.claimStartTime
            it[endTime] = airDrop.endTime
        }
    }

    fun getAirDropById(airDropId: Long): AirDrop? {
        return TBAirDrop
            .select { TBAirDrop.airDropId eq airDropId }
            .map(::mapAirDrop)
            .firstOrNull()
    }

    fun mapAirDrop(row: ResultRow): AirDrop {
        return AirDrop(
            airDropId = row[TBAirDrop.airDropId],
            chainId = row[TBAirDrop.chainId],
            chainName = row[TBAirDrop.chainName],
            projectName = row[TBAirDrop.projectName],
            projectDesc = row[TBAirDrop.projectDesc],
            socialLink = row[TBAirDrop.socialLink],
            airDropDesc = row[TBAirDrop.airDropDesc],
            bannerUrl = row[TBAirDrop.bannerUrl],
            title = row[TBAirDrop.title],
            description = row[TBAirDrop.description],
            details = row[TBAirDrop.details],
            tokenName = row[TBAirDrop.tokenName],
            tokenContractAddress = row[TBAirDrop.tokenContractAddress],
            airdropContractAddress = row[TBAirDrop.airdropContractAddress],
            gas = row[TBAirDrop.gas],
            status = row[TBAirDrop.status],
            checkStartTime = row[TBAirDrop.checkStartTime],
            checkCntStartTime = row[TBAirDrop.checkCntStartTime],
            claimStartTime = row[TBAirDrop.claimStartTime],
            endTime = row[TBAirDrop.endTime]
        )
    }

}