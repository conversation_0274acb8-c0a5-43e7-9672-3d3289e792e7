package com.rewardoor.app.dao

import com.rewardoor.app.dao.tables.TBPoint
import com.rewardoor.app.dao.tables.TBToken
import com.rewardoor.model.Token
import org.jetbrains.exposed.sql.ResultRow
import org.jetbrains.exposed.sql.insert
import org.jetbrains.exposed.sql.select
import org.springframework.stereotype.Repository

@Repository
class TokenRepository {
    fun createToken(token: Token) {
        TBToken.insert {
            it[tokenId] = token.tokenId
            it[name] = token.name
            it[number] = token.number
            it[contract] = token.contract
            it[chainId] = token.chainId
            it[methodType] = token.methodType
            it[rewardNum] = token.rewardNum
            it[projectId] = token.projectId
            it[creatorId] = token.creatorId
            it[groupId] = token.groupId
        }
    }

    fun getTokenByGroupId(groupId: Long): List<Token> {
        return TBToken.select { TBToken.groupId eq groupId }.map(::mapper)
    }

    private fun mapper(r: ResultRow): Token {
        return Token(
            tokenId = r[TBToken.tokenId],
            name = r[TBToken.name],
            number = r[TBToken.number],
            contract = r[TBToken.contract],
            chainId = r[TBToken.chainId],
            methodType = r[TBToken.methodType],
            rewardNum = r[TBToken.rewardNum],
            projectId = r[TBToken.projectId],
            creatorId = r[TBToken.creatorId],
            groupId = r[TBToken.groupId]
        )
    }
}