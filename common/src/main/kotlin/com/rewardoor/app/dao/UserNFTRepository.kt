package com.rewardoor.app.dao

import com.rewardoor.app.dao.tables.TBUserNFT
import org.jetbrains.exposed.sql.insertIgnore
import org.jetbrains.exposed.sql.update
import org.springframework.stereotype.Repository

@Repository
class UserNFTRepository {
    fun addUserNFT(userId: Long,
                   nftId: Long,
                   dummyId: Long,
                   signature: String,
                   tx: String
                   ) {
        TBUserNFT.insertIgnore {
            it[TBUserNFT.userId] = userId
            it[TBUserNFT.nftId] = nftId
            it[TBUserNFT.signature] = signature
            it[TBUserNFT.dummyId] = dummyId.toString()
            it[TBUserNFT.tx] = tx
        }
    }

    fun updateUserNFTTx(dummyId: Long, tx: String) {
        TBUserNFT.update({ TBUserNFT.dummyId eq dummyId.toString() }) {
            it[TBUserNFT.tx] = tx
        }
    }
}