package com.rewardoor.app.dao

import com.rewardoor.app.dao.tables.TBZKLogin
import com.rewardoor.model.UserZK
import org.jetbrains.exposed.sql.*
import org.springframework.stereotype.Repository

@Repository
class ZKLoginRepository {
    fun addZKLogin(userId: Long, iss: String, sub: String, identity: String, salt: String, address: String = ""): <PERSON>olean {
        val r = TBZKLogin.insertIgnore {
            it[TBZKLogin.userId] = userId
            it[TBZKLogin.issuer] = iss
            it[TBZKLogin.sub] = sub
            it[TBZKLogin.identity] = identity
            it[TBZKLogin.salt] = salt
            it[TBZKLogin.address] = address
        }
        return r.isIgnore
    }

    fun getZKLogin(iss: String, identity: String): UserZK? {
        return TBZKLogin.select {
            TBZKLogin.issuer.eq(iss) and TBZKLogin.identity.eq(identity)
        }.map(::mapper).firstOrNull()
    }

    fun updateZKAddress(userId: Long, address: String) {
        TBZKLogin.update({ TBZKLogin.userId.eq(userId) and TBZKLogin.address.eq("") }) {
            it[TBZKLogin.address] = address
        }
    }

    fun getZKLoginById(userId: Long): UserZK? {
        return TBZKLogin.select {
            TBZKLogin.userId.eq(userId)
        }.map(::mapper).firstOrNull()
    }

    companion object {
        fun mapper(r: ResultRow): UserZK {
            return UserZK(
                    r[TBZKLogin.userId],
                    r[TBZKLogin.issuer],
                    r[TBZKLogin.sub],
                    r[TBZKLogin.identity],
                    r[TBZKLogin.address],
                    r[TBZKLogin.salt])
        }
    }
}