package com.rewardoor.app.dao

import com.rewardoor.app.dao.tables.TBTonSocietySyncPrivilege
import com.rewardoor.app.dao.tables.TBTonSyncCheck
import com.rewardoor.app.dao.tables.TBTonSyncHistory
import com.rewardoor.model.TonSyncHistory
import org.jetbrains.exposed.sql.*
import org.jetbrains.exposed.sql.SqlExpressionBuilder.eq
import org.springframework.stereotype.Repository
import org.springframework.transaction.annotation.Transactional

@Transactional
@Repository
class TonSocietySyncRepository {
    fun getTonSyncPrivilege(campaignId: Long): Boolean {
        return TBTonSocietySyncPrivilege.select {
            TBTonSocietySyncPrivilege.campaignId eq campaignId
        }.count() > 0
    }

    fun addTonSyncHistory(history: TonSyncHistory) {
        TBTonSyncHistory.insert {
            it[projectId] = history.projectId
            it[campaignId] = history.campaignId
            it[groupId] = history.groupId
            it[title] = history.title
            it[subtitle] = history.subtitle
            it[description] = history.description
            it[startDate] = history.startDate
            it[endDate] = history.endDate
            it[buttonLabel] = history.buttonLabel
            it[buttonLink] = history.buttonLink
            it[sbtCollectionTitle] = history.sbtCollectionTitle
            it[sbtCollectionDesc] = history.sbtCollectionDesc
            it[sbtItemTitle] = history.sbtItemTitle
            it[sbtImage] = history.sbtImage
            it[sbtVideo] = history.sbtVideo
            it[sbtDesc] = history.sbtDesc
            it[syncAt] = history.syncAt
            it[activityId] = history.activityId
            it[activityUrl] = history.activityUrl
            it[sbtId] = history.sbtId
        }
    }

    fun addTonSyncCheck(history: TonSyncHistory) {
        TBTonSyncCheck.insert {
            it[projectId] = history.projectId
            it[campaignId] = history.campaignId
            it[groupId] = history.groupId
            it[title] = history.title
            it[subtitle] = history.subtitle
            it[description] = history.description
            it[startDate] = history.startDate
            it[endDate] = history.endDate
            it[buttonLabel] = history.buttonLabel
            it[buttonLink] = history.buttonLink
            it[sbtCollectionTitle] = history.sbtCollectionTitle
            it[sbtCollectionDesc] = history.sbtCollectionDesc
            it[sbtItemTitle] = history.sbtItemTitle
            it[sbtImage] = history.sbtImage
            it[sbtVideo] = history.sbtVideo
            it[sbtDesc] = history.sbtDesc
            it[syncAt] = history.syncAt
            it[activityId] = history.activityId
            it[activityUrl] = history.activityUrl
            it[sbtId] = history.sbtId
            it[category] = history.category ?: 0
            it[taskCategory] = history.taskCategory?.joinToString(",") ?: ""
            it[networkId] = history.networkId ?: 0
            it[checkStatus] = history.checkStatus
        }
    }

    fun updateTonSyncCheck(sbtId: Long, newCheckStatus: Int): Int {
        return TBTonSyncCheck.update({ TBTonSyncCheck.sbtId eq sbtId }) {
            it[checkStatus] = newCheckStatus
        }
    }

    fun getTonSyncCheckList(): List<TonSyncHistory>? {
        return TBTonSyncCheck.selectAll().orderBy(TBTonSyncCheck.createdAt, SortOrder.DESC).map {
            TonSyncHistory(
                it[TBTonSyncCheck.projectId],
                it[TBTonSyncCheck.campaignId],
                it[TBTonSyncCheck.groupId],
                it[TBTonSyncCheck.title],
                it[TBTonSyncCheck.subtitle],
                it[TBTonSyncCheck.description],
                it[TBTonSyncCheck.startDate],
                it[TBTonSyncCheck.endDate],
                it[TBTonSyncCheck.buttonLabel],
                it[TBTonSyncCheck.buttonLink],
                it[TBTonSyncCheck.sbtCollectionTitle],
                it[TBTonSyncCheck.sbtCollectionDesc],
                it[TBTonSyncCheck.sbtItemTitle],
                it[TBTonSyncCheck.sbtImage],
                it[TBTonSyncCheck.sbtVideo],
                it[TBTonSyncCheck.sbtDesc],
                it[TBTonSyncCheck.syncAt],
                it[TBTonSyncCheck.activityId],
                it[TBTonSyncCheck.activityUrl],
                it[TBTonSyncCheck.sbtId],
                it[TBTonSyncCheck.category],
                it[TBTonSyncCheck.taskCategory].split(",").filter { it.isNotBlank() }.map { it.toInt() },
                it[TBTonSyncCheck.checkStatus],
                "",
                it[TBTonSyncCheck.networkId]
            )
        }
    }

    fun getTonSyncCheckListByProjectId(projectId: Long): List<TonSyncHistory>? {
        return TBTonSyncCheck.select {
            TBTonSyncCheck.projectId eq projectId
        }.orderBy(TBTonSyncCheck.createdAt, SortOrder.DESC).map {
            TonSyncHistory(
                it[TBTonSyncCheck.projectId],
                it[TBTonSyncCheck.campaignId],
                it[TBTonSyncCheck.groupId],
                it[TBTonSyncCheck.title],
                it[TBTonSyncCheck.subtitle],
                it[TBTonSyncCheck.description],
                it[TBTonSyncCheck.startDate],
                it[TBTonSyncCheck.endDate],
                it[TBTonSyncCheck.buttonLabel],
                it[TBTonSyncCheck.buttonLink],
                it[TBTonSyncCheck.sbtCollectionTitle],
                it[TBTonSyncCheck.sbtCollectionDesc],
                it[TBTonSyncCheck.sbtItemTitle],
                it[TBTonSyncCheck.sbtImage],
                it[TBTonSyncCheck.sbtVideo],
                it[TBTonSyncCheck.sbtDesc],
                it[TBTonSyncCheck.syncAt],
                it[TBTonSyncCheck.activityId],
                it[TBTonSyncCheck.activityUrl],
                it[TBTonSyncCheck.sbtId],
                it[TBTonSyncCheck.category],
                it[TBTonSyncCheck.taskCategory].split(",").filter { it.isNotBlank() }.map { it.toInt() },
                it[TBTonSyncCheck.checkStatus],
                "",
                it[TBTonSyncCheck.networkId]
            )
        }
    }


    fun updateCheckSBTCategory(sbtId: Long, categoryValue: Int): Int {
        return TBTonSyncCheck.update({
            TBTonSyncCheck.sbtId eq sbtId
        }) {
            it[TBTonSyncCheck.category] = categoryValue
        }
    }

    fun updateTonSyncHistory(history: TonSyncHistory): Int {
        return TBTonSyncHistory.update({ TBTonSyncHistory.activityId eq history.activityId }) {
            it[title] = history.title
            it[subtitle] = history.subtitle
            it[description] = history.description
            it[startDate] = history.startDate
            it[endDate] = history.endDate
            it[buttonLabel] = history.buttonLabel
            it[buttonLink] = history.buttonLink
        }
    }

    fun getTonSyncHistory(campaignId: Long): List<TonSyncHistory>? {
        return TBTonSyncHistory.select {
            TBTonSyncHistory.campaignId eq campaignId
        }.map {
            TonSyncHistory(
                it[TBTonSyncHistory.projectId],
                it[TBTonSyncHistory.campaignId],
                it[TBTonSyncHistory.groupId],
                it[TBTonSyncHistory.title],
                it[TBTonSyncHistory.subtitle],
                it[TBTonSyncHistory.description],
                it[TBTonSyncHistory.startDate],
                it[TBTonSyncHistory.endDate],
                it[TBTonSyncHistory.buttonLabel],
                it[TBTonSyncHistory.buttonLink],
                it[TBTonSyncHistory.sbtCollectionTitle],
                it[TBTonSyncHistory.sbtCollectionDesc],
                it[TBTonSyncHistory.sbtItemTitle],
                it[TBTonSyncHistory.sbtImage],
                it[TBTonSyncHistory.sbtVideo],
                it[TBTonSyncHistory.sbtDesc],
                it[TBTonSyncHistory.syncAt],
                it[TBTonSyncHistory.activityId],
                it[TBTonSyncHistory.activityUrl],
                it[TBTonSyncHistory.sbtId]
            )
        }
    }

    fun getTonSyncHistoryByGroupId(groupId: Long): TonSyncHistory? {
        return TBTonSyncHistory.select {
            TBTonSyncHistory.groupId eq groupId
        }.map {
            TonSyncHistory(
                it[TBTonSyncHistory.projectId],
                it[TBTonSyncHistory.campaignId],
                it[TBTonSyncHistory.groupId],
                it[TBTonSyncHistory.title],
                it[TBTonSyncHistory.subtitle],
                it[TBTonSyncHistory.description],
                it[TBTonSyncHistory.startDate],
                it[TBTonSyncHistory.endDate],
                it[TBTonSyncHistory.buttonLabel],
                it[TBTonSyncHistory.buttonLink],
                it[TBTonSyncHistory.sbtCollectionTitle],
                it[TBTonSyncHistory.sbtCollectionDesc],
                it[TBTonSyncHistory.sbtItemTitle],
                it[TBTonSyncHistory.sbtImage],
                it[TBTonSyncHistory.sbtVideo],
                it[TBTonSyncHistory.sbtDesc],
                it[TBTonSyncHistory.syncAt],
                it[TBTonSyncHistory.activityId],
                it[TBTonSyncHistory.activityUrl],
                it[TBTonSyncHistory.sbtId]
            )
        }.firstOrNull()
    }

    fun getTonSyncHistoryBySBTId(sbtId: Long): TonSyncHistory? {
        return TBTonSyncHistory.select {
            TBTonSyncHistory.sbtId eq sbtId
        }.map {
            TonSyncHistory(
                it[TBTonSyncHistory.projectId],
                it[TBTonSyncHistory.campaignId],
                it[TBTonSyncHistory.groupId],
                it[TBTonSyncHistory.title],
                it[TBTonSyncHistory.subtitle],
                it[TBTonSyncHistory.description],
                it[TBTonSyncHistory.startDate],
                it[TBTonSyncHistory.endDate],
                it[TBTonSyncHistory.buttonLabel],
                it[TBTonSyncHistory.buttonLink],
                it[TBTonSyncHistory.sbtCollectionTitle],
                it[TBTonSyncHistory.sbtCollectionDesc],
                it[TBTonSyncHistory.sbtItemTitle],
                it[TBTonSyncHistory.sbtImage],
                it[TBTonSyncHistory.sbtVideo],
                it[TBTonSyncHistory.sbtDesc],
                it[TBTonSyncHistory.syncAt],
                it[TBTonSyncHistory.activityId],
                it[TBTonSyncHistory.activityUrl],
                it[TBTonSyncHistory.sbtId]
            )
        }.firstOrNull()
    }

    fun getTonSyncCheckBySBTId(sbtId: Long): TonSyncHistory? {
        return TBTonSyncCheck.select {
            TBTonSyncCheck.sbtId eq sbtId
        }.map {
            TonSyncHistory(
                it[TBTonSyncCheck.projectId],
                it[TBTonSyncCheck.campaignId],
                it[TBTonSyncCheck.groupId],
                it[TBTonSyncCheck.title],
                it[TBTonSyncCheck.subtitle],
                it[TBTonSyncCheck.description],
                it[TBTonSyncCheck.startDate],
                it[TBTonSyncCheck.endDate],
                it[TBTonSyncCheck.buttonLabel],
                it[TBTonSyncCheck.buttonLink],
                it[TBTonSyncCheck.sbtCollectionTitle],
                it[TBTonSyncCheck.sbtCollectionDesc],
                it[TBTonSyncCheck.sbtItemTitle],
                it[TBTonSyncCheck.sbtImage],
                it[TBTonSyncCheck.sbtVideo],
                it[TBTonSyncCheck.sbtDesc],
                it[TBTonSyncCheck.syncAt],
                it[TBTonSyncCheck.activityId],
                it[TBTonSyncCheck.activityUrl],
                it[TBTonSyncCheck.sbtId],
                it[TBTonSyncCheck.category],
                it[TBTonSyncCheck.taskCategory].split(",").filter { it.isNotBlank() }.map { it.toInt() },
                it[TBTonSyncCheck.checkStatus],
                "",
                it[TBTonSyncCheck.networkId]
            )
        }.firstOrNull()
    }

    fun getTonSyncCheckByActivityId(activityId: Long): TonSyncHistory? {
        return TBTonSyncCheck.select {
            TBTonSyncCheck.activityId eq activityId
        }.map {
            TonSyncHistory(
                it[TBTonSyncCheck.projectId],
                it[TBTonSyncCheck.campaignId],
                it[TBTonSyncCheck.groupId],
                it[TBTonSyncCheck.title],
                it[TBTonSyncCheck.subtitle],
                it[TBTonSyncCheck.description],
                it[TBTonSyncCheck.startDate],
                it[TBTonSyncCheck.endDate],
                it[TBTonSyncCheck.buttonLabel],
                it[TBTonSyncCheck.buttonLink],
                it[TBTonSyncCheck.sbtCollectionTitle],
                it[TBTonSyncCheck.sbtCollectionDesc],
                it[TBTonSyncCheck.sbtItemTitle],
                it[TBTonSyncCheck.sbtImage],
                it[TBTonSyncCheck.sbtVideo],
                it[TBTonSyncCheck.sbtDesc],
                it[TBTonSyncCheck.syncAt],
                it[TBTonSyncCheck.activityId],
                it[TBTonSyncCheck.activityUrl],
                it[TBTonSyncCheck.sbtId],
                it[TBTonSyncCheck.category],
                it[TBTonSyncCheck.taskCategory].split(",").filter { it.isNotBlank() }.map { it.toInt() },
                it[TBTonSyncCheck.checkStatus],
                "",
                it[TBTonSyncCheck.networkId]
            )
        }.firstOrNull()
    }

    fun getTonSyncHistoryByActivityId(activityId: Long): TonSyncHistory? {
        return TBTonSyncHistory.select {
            TBTonSyncHistory.activityId eq activityId
        }.map {
            TonSyncHistory(
                it[TBTonSyncHistory.projectId],
                it[TBTonSyncHistory.campaignId],
                it[TBTonSyncHistory.groupId],
                it[TBTonSyncHistory.title],
                it[TBTonSyncHistory.subtitle],
                it[TBTonSyncHistory.description],
                it[TBTonSyncHistory.startDate],
                it[TBTonSyncHistory.endDate],
                it[TBTonSyncHistory.buttonLabel],
                it[TBTonSyncHistory.buttonLink],
                it[TBTonSyncHistory.sbtCollectionTitle],
                it[TBTonSyncHistory.sbtCollectionDesc],
                it[TBTonSyncHistory.sbtItemTitle],
                it[TBTonSyncHistory.sbtImage],
                it[TBTonSyncHistory.sbtVideo],
                it[TBTonSyncHistory.sbtDesc],
                it[TBTonSyncHistory.syncAt],
                it[TBTonSyncHistory.activityId],
                it[TBTonSyncHistory.activityUrl],
                it[TBTonSyncHistory.sbtId]
            )
        }.firstOrNull()
    }

    fun getTonSyncHistoryByProjectId(projectId: Long): List<TonSyncHistory>? {
        return TBTonSyncHistory.select {
            TBTonSyncHistory.projectId eq projectId
        }.map {
            TonSyncHistory(
                it[TBTonSyncHistory.projectId],
                it[TBTonSyncHistory.campaignId],
                it[TBTonSyncHistory.groupId],
                it[TBTonSyncHistory.title],
                it[TBTonSyncHistory.subtitle],
                it[TBTonSyncHistory.description],
                it[TBTonSyncHistory.startDate],
                it[TBTonSyncHistory.endDate],
                it[TBTonSyncHistory.buttonLabel],
                it[TBTonSyncHistory.buttonLink],
                it[TBTonSyncHistory.sbtCollectionTitle],
                it[TBTonSyncHistory.sbtCollectionDesc],
                it[TBTonSyncHistory.sbtItemTitle],
                it[TBTonSyncHistory.sbtImage],
                it[TBTonSyncHistory.sbtVideo],
                it[TBTonSyncHistory.sbtDesc],
                it[TBTonSyncHistory.syncAt],
                it[TBTonSyncHistory.activityId],
                it[TBTonSyncHistory.activityUrl],
                it[TBTonSyncHistory.sbtId]
            )
        }
    }
}