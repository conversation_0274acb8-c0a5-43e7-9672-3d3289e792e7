package com.rewardoor.app.dao

import com.rewardoor.app.dao.tables.TBSBTReward
import com.rewardoor.app.dao.tables.TBSuiSbtReward
import com.rewardoor.app.dao.tables.TBSuiSbtSync
import com.rewardoor.model.SBTReward
import com.rewardoor.model.SuiSbtReward
import com.rewardoor.model.SuiSbtSync
import org.jetbrains.exposed.sql.ResultRow
import org.jetbrains.exposed.sql.SqlExpressionBuilder.eq
import org.jetbrains.exposed.sql.insert
import org.jetbrains.exposed.sql.select
import org.jetbrains.exposed.sql.update
import org.springframework.stereotype.Repository
import org.springframework.transaction.annotation.Transactional

@Repository
@Transactional
class SuiSbtRepository {
    fun createSuiSbt(suiSbtReward: SuiSbtReward) {
        TBSuiSbtReward.insert {
            it[sbtId] = suiSbtReward.suiSbtId
            it[activityId] = suiSbtReward.suiSbtActivityId
            it[name] = suiSbtReward.sbtName
            it[desc] = suiSbtReward.sbtDesc
            it[url] = suiSbtReward.sbtUrl
            it[contractAddress] = suiSbtReward.contractAddress
            it[objectId] = suiSbtReward.objectId
            it[projectId] = suiSbtReward.projectId
            it[creatorId] = suiSbtReward.creatorId
            it[groupId] = suiSbtReward.groupId
            it[category] = suiSbtReward.category ?: 0
        }
    }

    fun updateSuiSbtCategory(suiSbtId: Long, categoryValue: Int): Int {
        return TBSuiSbtReward.update({
            TBSuiSbtReward.sbtId eq suiSbtId
        }) {
            it[category] = categoryValue
        }
    }

    fun createSuiSbtSync(suiSbtSync: SuiSbtSync) {
        TBSuiSbtSync.insert {
            it[activityId] = suiSbtSync.suiSbtActivityId
            it[sbtId] = suiSbtSync.sbtId
            it[projectId] = suiSbtSync.projectId
            it[name] = suiSbtSync.sbtName
            it[desc] = suiSbtSync.sbtDesc
            it[url] = suiSbtSync.sbtUrl
            it[contractAddress] = suiSbtSync.contractAddress
            it[objectId] = suiSbtSync.objectId
        }
    }

    fun getSuiSbtRewardById(sbtId: Long): SuiSbtReward? {
        return TBSuiSbtReward.select { TBSuiSbtReward.sbtId eq sbtId }
            .map { mapperReward(it) }
            .firstOrNull()
    }

    fun getSuiSbtRewardByIds(sbtIds: List<Long>): List<SuiSbtReward> {
        return TBSuiSbtReward.select { TBSuiSbtReward.sbtId inList sbtIds }
            .map { mapperReward(it) }
    }

    fun getSuiSbtRewardByActivityId(activityId: Long): List<SuiSbtReward> {
        return TBSuiSbtReward.select { TBSuiSbtReward.activityId eq activityId }
            .map { mapperReward(it) }
    }

    fun getSuiSbtRewardByGroupId(groupId: Long): List<SuiSbtReward> {
        return TBSuiSbtReward.select { TBSuiSbtReward.groupId eq groupId }
            .map { mapperReward(it) }
    }

    fun getSuiSbtSyncById(activityId: Long): SuiSbtSync? {
        return TBSuiSbtSync.select { TBSuiSbtSync.activityId eq activityId }
            .map { mapperSync(it) }
            .firstOrNull()
    }

    fun getSuiSbtSyncBySbtId(suiSbtId: Long): SuiSbtSync? {
        return TBSuiSbtSync.select { TBSuiSbtSync.sbtId eq suiSbtId }
            .map { mapperSync(it) }
            .firstOrNull()
    }

    fun getSuiSbtSyncByProjectId(projectId: Long): List<SuiSbtSync> {
        return TBSuiSbtSync.select { TBSuiSbtSync.projectId eq projectId }
            .map { mapperSync(it) }
    }

    fun getSuiSbtRewardByProjectId(projectId: Long): List<SuiSbtReward>? {
        return TBSuiSbtReward.select { TBSuiSbtReward.projectId eq projectId }
            .map { mapperReward(it) }
    }

    fun updateCollectionId(sbtId: Long, collectionId: String) {
        TBSuiSbtReward.update({ TBSuiSbtReward.sbtId eq sbtId }) {
            it[objectId] = collectionId
        }
    }

    fun updateSyncCollectionObjectId(activityId: Long, collectionId: String) {
        TBSuiSbtSync.update({ TBSuiSbtSync.activityId eq activityId }) {
            it[objectId] = collectionId
        }
    }

    private fun mapperReward(row: ResultRow): SuiSbtReward {
        return SuiSbtReward(
            suiSbtId = row[TBSuiSbtReward.sbtId],
            suiSbtActivityId = row[TBSuiSbtReward.activityId],
            sbtName = row[TBSuiSbtReward.name],
            sbtDesc = row[TBSuiSbtReward.desc],
            sbtUrl = row[TBSuiSbtReward.url],
            contractAddress = row[TBSuiSbtReward.contractAddress],
            objectId = row[TBSuiSbtReward.objectId],
            projectId = row[TBSuiSbtReward.projectId],
            creatorId = row[TBSuiSbtReward.creatorId],
            groupId = row[TBSuiSbtReward.groupId],
            category = row[TBSuiSbtReward.category]
        )
    }

    private fun mapperSync(row: ResultRow): SuiSbtSync {
        return SuiSbtSync(
            suiSbtActivityId = row[TBSuiSbtSync.activityId],
            sbtId = row[TBSuiSbtSync.sbtId],
            projectId = row[TBSuiSbtSync.projectId],
            sbtName = row[TBSuiSbtSync.name],
            sbtDesc = row[TBSuiSbtSync.desc],
            sbtUrl = row[TBSuiSbtSync.url],
            contractAddress = row[TBSuiSbtSync.contractAddress],
            objectId = row[TBSuiSbtSync.objectId]
        )
    }

    fun updateSbtActivityId(sbtId: Long, actId: Long) {
        TBSuiSbtReward.update({ TBSuiSbtReward.sbtId eq sbtId }) {
            it[activityId] = actId
        }
    }

}