package com.rewardoor.app.dao

import com.rewardoor.app.dao.tables.TBUnbind
import com.rewardoor.app.dao.tables.TBUserTwitter
import com.rewardoor.model.SimpleStatus
import com.rewardoor.model.UnbindInfo
import com.rewardoor.model.UserTwitterInfo
import org.jetbrains.exposed.sql.ResultRow
import org.jetbrains.exposed.sql.and
import org.jetbrains.exposed.sql.insert
import org.jetbrains.exposed.sql.select
import org.springframework.stereotype.Repository

@Repository
class UnbindInfoRepository {
    fun addUnbindInfo(info: UnbindInfo) {
        TBUnbind.insert {
            it[userId] = info.userId
            it[address] = info.address
            it[socialType] = info.socialType
            it[socialId] = info.socialId
            it[credentialId] = info.credentialId
        }
    }

    fun getUnbindInfo(userId: Long, socialId: String, credentialId: Long): UnbindInfo? {
        return TBUnbind.select {
            (TBUnbind.userId eq userId) and (TBUnbind.socialId eq socialId) and (TBUnbind.credentialId eq credentialId)
        }.map(::mapUnbindInfo).firstOrNull()
    }

    fun mapUnbindInfo(r: ResultRow): UnbindInfo {
        return UnbindInfo(
            r[TBUnbind.userId],
            r[TBUnbind.address],
            r[TBUnbind.socialType],
            r[TBUnbind.socialId],
            r[TBUnbind.credentialId]
        )
    }
}