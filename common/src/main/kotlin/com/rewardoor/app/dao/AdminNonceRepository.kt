package com.rewardoor.app.dao

import com.rewardoor.app.dao.tables.TBAdminNonceSign
import org.jetbrains.exposed.sql.*
import org.jetbrains.exposed.sql.SqlExpressionBuilder.eq
import org.springframework.stereotype.Repository

@Repository
class AdminNonceRepository {
    fun addAdminNonce(userId: Long,
                      projectId: Long,
                      address: String,
                      op: String,
                      nonce: String) {
        TBAdminNonceSign.insert {
            it[TBAdminNonceSign.userId] = userId
            it[TBAdminNonceSign.projectId] = projectId
            it[TBAdminNonceSign.address] = address
            it[TBAdminNonceSign.op] = op
            it[TBAdminNonceSign.nonce] = nonce
        }
    }

    fun getLatestNonce(userId: Long,
                       projectId: Long,
                       address: String,
                       op: String): Pair<Long, String>? {
        return TBAdminNonceSign.select {
                    TBAdminNonceSign.userId eq userId and
                            (TBAdminNonceSign.projectId eq projectId) and
                            (TBAdminNonceSign.address eq address) and
                            (TBAdminNonceSign.op eq op)
                }
                .orderBy(TBAdminNonceSign.id, SortOrder.DESC)
                .limit(1)
                .firstOrNull()?.let { it[TBAdminNonceSign.id].value to it[TBAdminNonceSign.nonce] }
    }

    fun updateSign(recordId: Long,
                   sign: String) {
        TBAdminNonceSign.update({TBAdminNonceSign.id eq recordId}) {
            it[TBAdminNonceSign.sign] = sign
        }
    }
}