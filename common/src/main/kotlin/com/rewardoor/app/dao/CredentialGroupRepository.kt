package com.rewardoor.app.dao

import com.rewardoor.app.dao.tables.TBCredential
import com.rewardoor.app.dao.tables.TBCredentialGroup
import com.rewardoor.model.Credential
import com.rewardoor.model.CredentialGroup
import org.jetbrains.exposed.sql.*
import org.jetbrains.exposed.sql.SqlExpressionBuilder.eq
import org.springframework.stereotype.Repository
import org.springframework.transaction.annotation.Transactional

@Repository
@Transactional
class CredentialGroupRepository {
    fun createCredentialGroup(credentialGroup: CredentialGroup) {
        TBCredentialGroup.insert { it ->
            it[groupId] = credentialGroup.id
            it[name] = credentialGroup.name
            it[groupType] = credentialGroup.groupType
//            it[credentialList] = credentialGroup.list
            it[projectId] = credentialGroup.projectId
            it[creatorId] = credentialGroup.creatorId
            it[campaignId] = credentialGroup.campaignId
            it[status] = 1 // status，0: 草稿, 1：进行中, 2：计划中，3: 已完成, 16: 已删除
            it[existActivityId] = credentialGroup.existActivityId
        }
    }

    fun getCredentialGroupById(groupId: Long): CredentialGroup? {
        return TBCredentialGroup.select { TBCredentialGroup.groupId eq groupId }.map(::mapper).firstOrNull()
    }

    fun getCredentialByGroupId(groupId: Long): List<CredentialGroup> {
        return TBCredential.select { TBCredentialGroup.groupId eq groupId }.map(::mapper)
    }

    fun getCredentialGroupByProjectId(projectId: Long): List<CredentialGroup> {
        return TBCredentialGroup.select { TBCredentialGroup.projectId eq projectId }.map(::mapper)
    }

    fun getCredentialGroupByCampaignId(campaignId: Long): List<CredentialGroup> {
        return TBCredentialGroup.select { TBCredentialGroup.campaignId eq campaignId }.map(::mapper)
    }

    fun getCredentialGroupsByCampaignIds(campaignIds: List<Long>): List<CredentialGroup> {
        return TBCredentialGroup.select { TBCredentialGroup.campaignId inList campaignIds }.map(::mapper)
    }

    fun updateCredentialGroup(credentialGroup: CredentialGroup): Int {
        return TBCredentialGroup.update({ TBCredentialGroup.groupId eq credentialGroup.id }) { it ->
            it[groupId] = credentialGroup.id
            it[name] = credentialGroup.name
//            it[credentialList] = credentialGroup.list
            it[projectId] = credentialGroup.projectId
            it[creatorId] = credentialGroup.creatorId
            it[status] = credentialGroup.status
        }
    }


    private fun mapper(r: ResultRow): CredentialGroup {
        return CredentialGroup(
            groupType = r[TBCredentialGroup.groupType],
            id = r[TBCredentialGroup.groupId],
            name = r[TBCredentialGroup.name],
//            list = r[TBCredentialGroup.credentialList],
            projectId = r[TBCredentialGroup.projectId],
            creatorId = r[TBCredentialGroup.creatorId],
            campaignId = r[TBCredentialGroup.campaignId],
            status = r[TBCredentialGroup.status],
            existActivityId = r[TBCredentialGroup.existActivityId]
        )
    }
}