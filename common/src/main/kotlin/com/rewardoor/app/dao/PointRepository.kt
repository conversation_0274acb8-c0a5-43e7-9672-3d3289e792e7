package com.rewardoor.app.dao

import com.rewardoor.app.dao.tables.TBPoint
import com.rewardoor.model.Point
import org.jetbrains.exposed.sql.*
import org.jetbrains.exposed.sql.SqlExpressionBuilder.eq
import org.springframework.stereotype.Repository

@Repository
class PointRepository {
    fun createPoint(point: Point) {
        TBPoint.insert {
            it[pointId] = point.pointId
            it[number] = point.number
            it[methodType] = point.methodType
            it[unlimited] = point.unlimited.toString()
            it[rewardNum] = point.rewardNum
            it[projectId] = point.projectId
            it[creatorId] = point.creatorId
            it[groupId] = point.groupId
        }
    }

    fun getPointById(pointId: Long): Point? {
        return TBPoint.select { TBPoint.pointId eq pointId }.map(::mapper).firstOrNull()
    }

    fun deletePointsByIds(pointIdList: List<Long>) {
        pointIdList.forEach { pointId ->
            TBPoint.deleteWhere { TBPoint.pointId eq pointId }
        }
    }

    fun getPointByProjectId(projectId: Long): List<Point> {
        return TBPoint
            .select { TBPoint.projectId eq projectId }
            .orderBy(TBPoint.createTime, SortOrder.DESC)
            .map(::mapper)
    }

    fun getPointByCreatorId(creatorId: Long): List<Point> {
        return TBPoint.select { TBPoint.creatorId eq creatorId }.map(::mapper)
    }

    fun getPointByGroupId(groupId: Long): List<Point> {
        return TBPoint.select { TBPoint.groupId eq groupId }.map(::mapper)
    }

    fun getPointByGroupIds(groupIds: List<Long>): List<Point> {
        return TBPoint.select { TBPoint.groupId.inList(groupIds) }.map(::mapper)
    }

    fun getPointCntByGroupId(groupId: Long): Long {
        return TBPoint.select { TBPoint.groupId eq groupId }.count()
    }

    fun updatePoint(point: Point): Int {
        return TBPoint.update({ TBPoint.pointId eq point.pointId }) {
            it[pointId] = point.pointId
            it[number] = point.number
            it[methodType] = point.methodType
            it[unlimited] = point.unlimited.toString()
            it[rewardNum] = point.rewardNum
            it[claimedNum] = point.claimedNum
        }
    }

    fun updatePointClaimedNum(pointId: Long, newClaimedNum: Long): Int {
        return TBPoint.update({ TBPoint.pointId eq pointId }) {
            it[claimedNum] = newClaimedNum
        }
    }

    private fun mapper(r: ResultRow): Point {
        return Point(
            pointId = r[TBPoint.pointId],
            number = r[TBPoint.number],
            methodType = r[TBPoint.methodType],
            unlimited = r[TBPoint.unlimited].toBoolean(),
            rewardNum = r[TBPoint.rewardNum],
            projectId = r[TBPoint.projectId],
            creatorId = r[TBPoint.creatorId],
            groupId = r[TBPoint.groupId],
            claimedNum = r[TBPoint.claimedNum]
        )
    }

}