package com.rewardoor.app.dao

import com.rewardoor.app.dao.tables.TBAddressNonce
import org.jetbrains.exposed.sql.*
import org.springframework.stereotype.Repository

@Repository
class NonceRepository {
    fun addNonce(address: String, nonce: String) {
        TBAddressNonce.upsert { row ->
            row[TBAddressNonce.address] = address
            row[TBAddressNonce.nonce] = nonce
            row[status] = 1
        }
    }

    fun getNonce(address: String): String? {
        return TBAddressNonce.select { TBAddressNonce.address eq address and TBAddressNonce.status.eq(1) }.firstOrNull()?.get(TBAddressNonce.nonce)
    }

    fun getAndInvalidateNonce(address: String): String? {
        val nonce = getNonce(address)
        if (nonce != null) {
            TBAddressNonce.update({ TBAddressNonce.address eq address }) {
                it[status] = 0
            }
        }
        return nonce
    }
}