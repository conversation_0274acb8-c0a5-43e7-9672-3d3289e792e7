package com.rewardoor.app.dao

import com.rewardoor.app.dao.tables.TBUserSui
import com.rewardoor.model.UserSui
import org.jetbrains.exposed.sql.*
import org.jetbrains.exposed.sql.SqlExpressionBuilder.eq
import org.springframework.stereotype.Repository
import org.springframework.transaction.annotation.Transactional

@Repository
@Transactional
class UserSuiRepository {
    fun bindSuiWallet(userId: Long, wallet: String, pk: String): Int {
        return TBUserSui.insertIgnore {
            it[TBUserSui.userId] = userId
            it[address] = wallet
            it[publicKey] = pk
        }.insertedCount
    }

    fun findSuiWallet(wallet: String): UserSui? {
        return TBUserSui.select { TBUserSui.address eq wallet }.limit(1).map(::mapUserSui).firstOrNull()
    }

    fun getSuiUserIds(): List<Long> {
        return TBUserSui.select { TBUserSui.address neq "" }.map { it[TBUserSui.userId] }.distinct()
    }

    fun getSuiUserWalletByUserId(userId: Long): UserSui? {
        return TBUserSui.select { TBUserSui.userId eq userId }.limit(1).map(::mapUserSui).firstOrNull()
    }

    fun updateSuiUserId(uid: Long, newUserId: Long): Int {
        return TBUserSui.update({ (TBUserSui.userId eq uid) }) {
            it[userId] = newUserId
        }
    }

    fun deleteSuiUserById(userId: Long): Int {
        return TBUserSui.deleteWhere { TBUserSui.userId eq userId }
    }

    fun getSuiUserCnt(): Long {
        return TBUserSui.slice(TBUserSui.address.count()).selectAll()
            .single()[TBUserSui.address.count()]
    }

    fun mapUserSui(row: ResultRow): UserSui {
        return UserSui(
            userId = row[TBUserSui.userId],
            suiWallet = row[TBUserSui.address],
            publicKey = row[TBUserSui.publicKey]
        )
    }
}
