package com.rewardoor.app.dao

import com.rewardoor.app.dao.tables.TBTokenAirdrop
import com.rewardoor.model.TokenAirDrop
import org.jetbrains.exposed.sql.batchInsert
import org.jetbrains.exposed.sql.select
import org.springframework.stereotype.Repository
import java.time.LocalDateTime

@Repository
class TokenAirDropRepository {

    fun insertTokenAirDrops(tokenAirDrops: List<TokenAirDrop>) {
        TBTokenAirdrop.batchInsert(tokenAirDrops) { tokenAirDrop ->
            this[TBTokenAirdrop.tokenName] = tokenAirDrop.tokenName
            this[TBTokenAirdrop.address] = tokenAirDrop.address
            this[TBTokenAirdrop.userId] = tokenAirDrop.userId
            this[TBTokenAirdrop.amount] = tokenAirDrop.amount
            this[TBTokenAirdrop.createTime] = LocalDateTime.now()
            this[TBTokenAirdrop.updateTime] = LocalDateTime.now()
        }
    }

    fun getByAddress(address: String): TokenAirDrop? {
        return TBTokenAirdrop.select { TBTokenAirdrop.address eq address }.map {
                TokenAirDrop(
                    tokenName = it[TBTokenAirdrop.tokenName],
                    address = it[TBTokenAirdrop.address],
                    userId = it[TBTokenAirdrop.userId],
                    amount = it[TBTokenAirdrop.amount]
                )
            }.firstOrNull()
    }

}