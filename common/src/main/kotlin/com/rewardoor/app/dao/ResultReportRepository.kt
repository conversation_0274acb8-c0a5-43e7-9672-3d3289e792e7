package com.rewardoor.app.dao

import com.rewardoor.app.dao.tables.TBUserCampaign
import com.rewardoor.app.dao.tables.TBUserCredential
import com.rewardoor.app.dto.CampaignReportDto
import com.rewardoor.model.Campaign
import org.jetbrains.exposed.sql.insert
import org.jetbrains.exposed.sql.select
import org.springframework.stereotype.Repository

@Repository
class ResultReportRepository {
    fun addResults(campaignReportDto: CampaignReportDto, token: String) {
        campaignReportDto.eligible.forEach { pair ->
            TBUserCredential.insert {
                it[address] = pair.address
                it[credentialId] = pair.credentialId
                it[campaignId] = campaignReportDto.campaignId
                it[opToken] = token
            }

            TBUserCampaign.insert {
                it[projectId] = campaignReportDto.projectId
                it[campaignId] = campaignReportDto.campaignId
                it[opToken] = token
                it[address] = pair.address
                it[points] = 1
            }
        }
    }

    fun getCampaignCredentialStats(id: Long): Map<String, List<Long>> {
        return TBUserCredential.select { TBUserCredential.campaignId eq id }
            .map { it[TBUserCredential.address] to it[TBUserCredential.credentialId] }
            .groupBy { it.first }.mapValues { it.value.map { it.second } }
    }

    fun getCampaignUserStats(id: Long): Map<String, List<Int>> {
        return TBUserCampaign.select{ TBUserCampaign.campaignId eq id}.map { it }
            .map { it[TBUserCampaign.address] to it[TBUserCampaign.points] }
            .groupBy { it.first }.mapValues { it.value.map { it.second } }
    }

    fun getCampaignPoints(id: Long): Map<String, Long> {
        return TBUserCampaign.select{ TBUserCampaign.campaignId eq id}
            .map { it[TBUserCampaign.address] to it[TBUserCampaign.points] }
            .associate { it.first to it.second.toLong() }
    }

    fun getProjectPoints(id: Long): Map<String, Long> {
        return TBUserCampaign.select{ TBUserCampaign.projectId eq id}
            .map { it[TBUserCampaign.address] to it[TBUserCampaign.points] }
            .associate { it.first to it.second.toLong() }
    }

    fun updateCampaign(campaign: Campaign) {

    }
}