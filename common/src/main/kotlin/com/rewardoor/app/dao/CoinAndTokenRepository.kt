package com.rewardoor.app.dao

import com.rewardoor.app.dao.tables.TBCoinAndTokenPrice
import org.jetbrains.exposed.sql.and
import org.jetbrains.exposed.sql.insertIgnore
import org.jetbrains.exposed.sql.select
import org.jetbrains.exposed.sql.update
import org.springframework.stereotype.Repository
import java.time.Instant

@Repository
class CoinAndTokenRepository {

    fun addTokenPrice(name: String, tokenPrice: Double): Int {
        return TBCoinAndTokenPrice.insertIgnore {
            it[tokenName] = name
            it[price] = tokenPrice
            it[createTime] = Instant.now()
            it[updateTime] = Instant.now()
        }.insertedCount
    }

    fun updateTokenPrice(name: String, newPrice: Double): Int {
        println("update token price $name + $newPrice")
        return TBCoinAndTokenPrice.update({ TBCoinAndTokenPrice.tokenName eq name }) {
            it[price] = newPrice
            it[updateTime] = Instant.now()
        }
    }

    fun getTokenPriceData(name: String): Pair<Double?, Instant?> {
        return TBCoinAndTokenPrice.select { TBCoinAndTokenPrice.tokenName eq name }
            .map {
                Pair(it[TBCoinAndTokenPrice.price], it[TBCoinAndTokenPrice.updateTime])
            }
            .singleOrNull() ?: Pair(null, null)
    }

    fun getJettonsPriceData(): Map<String, Double> {
        return TBCoinAndTokenPrice.select {
            (TBCoinAndTokenPrice.tokenName neq "ETH") and (TBCoinAndTokenPrice.tokenName neq "TON")
        }.associate {
            it[TBCoinAndTokenPrice.tokenName] to it[TBCoinAndTokenPrice.price]
        }
    }


}