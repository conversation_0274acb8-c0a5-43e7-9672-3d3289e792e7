package com.rewardoor.app.dao

import com.rewardoor.app.dao.tables.TBProjectApply
import com.rewardoor.app.dao.tables.TBUserProjectPrivilege
import com.rewardoor.model.ProjectApply
import org.jetbrains.exposed.sql.insert
import org.jetbrains.exposed.sql.select
import org.jetbrains.exposed.sql.upsert
import org.springframework.stereotype.Repository

@Repository
class ProjectApplyRepository {
    fun addProjectApply(apply: ProjectApply) {
        TBProjectApply.insert {
            it[userId] = apply.userId
            it[projectName] = apply.projectName
            it[category] = apply.category
            it[tmaLink] = apply.tmaLink
            it[email] = apply.email
            it[applyReason] = apply.applyReason
            it[estimatedParticipants] = apply.estimatedParticipants
            it[socialPlatforms] = apply.socialPlatforms
            it[additionalSocialInfo] = apply.additionalSocialInfo
        }
    }

    fun addUserPrivilege(userId: Long) {
        TBUserProjectPrivilege.upsert {
            it[TBUserProjectPrivilege.userId] = userId
            it[status] = 1
        }
    }

    fun getUserPrivilege(userId: Long): Bo<PERSON>an {
        return TBUserProjectPrivilege.select { TBUserProjectPrivilege.userId eq userId }
            .map { it[TBUserProjectPrivilege.status] }.firstOrNull() == 1
    }
}