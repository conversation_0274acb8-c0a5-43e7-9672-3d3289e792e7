package com.rewardoor.app.dao

import com.rewardoor.app.dao.tables.TBCredential
import com.rewardoor.app.dao.tables.TBUserEvm
import com.rewardoor.app.dao.tables.TBUserTwitter
import com.rewardoor.model.SimpleStatus
import com.rewardoor.model.UserTwitterInfo
import org.jetbrains.exposed.sql.*
import org.jetbrains.exposed.sql.SqlExpressionBuilder.eq
import org.springframework.stereotype.Repository

@Repository
class TwitterUserRepository {
    fun addUserRequestToken(userId: Long, state: String, codeChallenge: String) {
        val current = TBUserTwitter.select { TBUserTwitter.userId eq userId }.limit(1).firstOrNull()
        if (current != null) {
            TBUserTwitter.update({ TBUserTwitter.userId eq userId }) {
                it[this.state] = state
                it[this.codeChallenge] = codeChallenge
            }
        } else {
            TBUserTwitter.insert {
                it[TBUserTwitter.userId] = userId
                it[this.state] = state
                it[this.codeChallenge] = codeChallenge
                it[this.twitterId] = userId.toString()
            }
        }
    }

    fun addUserInfo(info: UserTwitterInfo) {
        TBUserTwitter.insert {
            it[userId] = info.userId
            it[state] = info.state
            it[codeChallenge] = info.codeChallenge
            it[accessToken] = info.accessToken
            it[refreshToken] = info.refreshToken
            it[twitterId] = info.twitterId
            it[twitterName] = info.twitterName
            it[twitterScreenName] = info.twitterScreenName
            it[twitterProfileImage] = info.twitterProfileImage
            it[connected] = if (info.connected) 1 else 0
            it[status] = info.status.value
        }
    }

    fun updateUserInfo(info: UserTwitterInfo) {
        TBUserTwitter.update({ TBUserTwitter.userId eq info.userId }) {
            it[accessToken] = info.accessToken
            it[refreshToken] = info.refreshToken
            it[twitterId] = info.twitterId
            it[twitterName] = info.twitterName
            it[twitterScreenName] = info.twitterScreenName
            it[twitterProfileImage] = info.twitterProfileImage
            it[connected] = if (info.connected) 1 else 0
        }
    }

    fun updateUserId(info: UserTwitterInfo, newUserId: Long) {
        TBUserTwitter.update({ TBUserTwitter.userId eq info.userId }) {
            it[userId] = newUserId
        }
    }

    fun updateTwitterUserId(uid: Long, newUserId: Long): Int {
        return TBUserTwitter.update({ TBUserTwitter.userId eq uid }) {
            it[userId] = newUserId
        }
    }

    fun deleteTwitterUser(twitterId: String): Int {
        return TBUserTwitter.deleteWhere { TBUserTwitter.twitterId eq twitterId }
    }

    fun updateUserAccessToken(userId: Long, token: String, refreshToken: String) {
        TBUserTwitter.update({ TBUserTwitter.userId eq userId }) {
            it[accessToken] = token
            it[this.refreshToken] = refreshToken
            it[connected] = 1
        }
    }

    fun getUserInfo(userId: Long): UserTwitterInfo? {
        return TBUserTwitter.select { TBUserTwitter.userId eq userId }.limit(1).map(::mapUserTwitter).firstOrNull()
    }

    fun getUsersInfo(userIds: List<Long>): List<UserTwitterInfo> {
        return TBUserTwitter.select { TBUserTwitter.userId inList userIds }
            .map(::mapUserTwitter)
    }

    fun getUserInfoByTwitterId(twitterId: String): UserTwitterInfo? {
        return TBUserTwitter.select { TBUserTwitter.twitterId eq twitterId }.limit(1).map(::mapUserTwitter)
            .firstOrNull()
    }

    fun getTwitterUserCnt(): Long {
        return TBUserTwitter.slice(TBUserTwitter.twitterId.count()).selectAll()
            .single()[TBUserTwitter.twitterId.count()]
    }

    fun mapUserTwitter(r: ResultRow): UserTwitterInfo {
        return UserTwitterInfo(
            r[TBUserTwitter.userId],
            r[TBUserTwitter.state],
            r[TBUserTwitter.codeChallenge],
            r[TBUserTwitter.accessToken],
            r[TBUserTwitter.refreshToken],
            r[TBUserTwitter.twitterId],
            r[TBUserTwitter.twitterName],
            r[TBUserTwitter.twitterScreenName],
            r[TBUserTwitter.twitterProfileImage],
            r[TBUserTwitter.connected] == 1,
            SimpleStatus.fromValue(r[TBUserTwitter.status]),
        )
    }
}