package com.rewardoor.app.dao

import com.rewardoor.app.dao.tables.TBTPointsVanguard
import com.rewardoor.model.TPointsVanguard
import org.jetbrains.exposed.sql.ResultRow
import org.jetbrains.exposed.sql.SqlExpressionBuilder.eq
import org.jetbrains.exposed.sql.insertIgnore
import org.jetbrains.exposed.sql.select
import org.jetbrains.exposed.sql.update
import org.springframework.stereotype.Repository

@Repository
class TPointsVanguardRepository {

    fun addTPointsVanguard(tPointsVanguard: TPointsVanguard): Int {
        return TBTPointsVanguard.insertIgnore {
            it[userId] = tPointsVanguard.userId
            it[level] = tPointsVanguard.level
            it[tPointsNum] = tPointsVanguard.tPointsNum
        }.insertedCount
    }

    fun getTPointsVanguardById(userId: Long): TPointsVanguard? {
        return TBTPointsVanguard.select { TBTPointsVanguard.userId eq userId }.map(::mapTPointsVanguard).firstOrNull()
    }

    fun updateTPointsVanguard(tPointsVanguard: TPointsVanguard): Int {
        return TBTPointsVanguard.update({ TBTPointsVanguard.userId eq tPointsVanguard.userId }) {
            it[level] = tPointsVanguard.level
            it[tPointsNum] = tPointsVanguard.tPointsNum
        }
    }

    fun mapTPointsVanguard(row: ResultRow): TPointsVanguard {
        return TPointsVanguard(
            userId = row[TBTPointsVanguard.userId],
            level = row[TBTPointsVanguard.level],
            tPointsNum = row[TBTPointsVanguard.tPointsNum],
            createTime = row[TBTPointsVanguard.createTime],
            updateTime = row[TBTPointsVanguard.updateTime]
        )
    }
}