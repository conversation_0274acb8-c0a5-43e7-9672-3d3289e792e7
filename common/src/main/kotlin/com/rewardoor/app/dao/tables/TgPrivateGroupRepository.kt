package com.rewardoor.app.dao.tables

import com.rewardoor.model.TgPrivateGroupInfo
import org.jetbrains.exposed.sql.and
import org.jetbrains.exposed.sql.insert
import org.jetbrains.exposed.sql.select
import org.jetbrains.exposed.sql.update
import org.springframework.stereotype.Repository

@Repository
class TgPrivateGroupRepository {
    fun addTgPrivateGroup(info: TgPrivateGroupInfo) {
        TBTgPrivateGroupInfo.insert {
            it[tgId] = info.tgId
            it[privateHash] = info.privateHash
            it[status] = info.status
            it[operationAt] = info.operationAt
        }
    }

    fun updateTgPrivateGroup(info: TgPrivateGroupInfo) {
        TBTgPrivateGroupInfo.update({
            (TBTgPrivateGroupInfo.tgId eq info.tgId).and(
                TBTgPrivateGroupInfo.operationAt.less(info.operationAt)) }) {
            it[privateHash] = info.privateHash
            it[status] = info.status
            it[operationAt] = info.operationAt
        }
    }

    fun getTgPrivateGroupInfoByHash(hash: String): TgPrivateGroupInfo? {
        return TBTgPrivateGroupInfo.select { TBTgPrivateGroupInfo.privateHash eq hash }
            .map { TgPrivateGroupInfo(it[TBTgPrivateGroupInfo.tgId],
                it[TBTgPrivateGroupInfo.privateHash],
                it[TBTgPrivateGroupInfo.status],
                it[TBTgPrivateGroupInfo.operationAt]) }
            .firstOrNull()
    }

    fun getTgPrivateGroupInfoByTgId(tgId: Long): TgPrivateGroupInfo? {
        return TBTgPrivateGroupInfo.select { TBTgPrivateGroupInfo.tgId eq tgId }
            .map { TgPrivateGroupInfo(it[TBTgPrivateGroupInfo.tgId],
                it[TBTgPrivateGroupInfo.privateHash],
                it[TBTgPrivateGroupInfo.status],
                it[TBTgPrivateGroupInfo.operationAt]) }
            .firstOrNull()
    }
}