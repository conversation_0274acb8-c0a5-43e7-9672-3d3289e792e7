package com.rewardoor.app.dao

import com.rewardoor.app.dao.tables.TBCampaign
import com.rewardoor.app.dao.tables.TBCampaignCredential
import com.rewardoor.model.Campaign
import com.rewardoor.model.CampaignStatus
import com.rewardoor.model.CampaignTotal
import com.rewardoor.model.SimpleStatus
import org.jetbrains.exposed.sql.*
import org.jetbrains.exposed.sql.SqlExpressionBuilder.eq
import org.springframework.stereotype.Repository
import org.springframework.transaction.annotation.Transactional
import java.time.LocalDateTime
import java.time.ZoneOffset

@Repository
@Transactional
class CampaignRepository {

    fun createCampaign(campaign: Campaign) {
        TBCampaign.insert {
            it[campaignId] = campaign.campaignId
            it[name] = campaign.getUniName()
            it[projectId] = campaign.projectId
            it[creatorId] = campaign.creatorId
            it[picUrl] = campaign.picUrl
            it[description] = campaign.description
            it[shareText] = campaign.shareText
            it[startAt] = LocalDateTime.ofInstant(campaign.startAt, ZoneOffset.UTC)
            it[endAt] = LocalDateTime.ofInstant(campaign.endAt, ZoneOffset.UTC)
            it[status] = campaign.status.value
            it[reward] = campaign.reward
            it[rewardAction] = campaign.rewardAction
            it[points] = campaign.points ?: 0
            it[credential] = campaign.credentialId ?: 0
            it[nft] = campaign.nft ?: 0
        }
    }

    fun getCampaignById(campaignId: Long): Campaign? {
        return TBCampaign.select { TBCampaign.campaignId eq campaignId }.map(::mapper).firstOrNull()
    }

    fun getCampaignsByIds(campaignIds: List<Long>): List<Campaign> {
        return TBCampaign.select { TBCampaign.campaignId inList campaignIds }.map(::mapper)
    }

    fun getAllScheduledAndOnGoingCampaigns(): List<Campaign>? {
        return TBCampaign.select { (TBCampaign.status eq CampaignStatus.SCHEDULED.value) or (TBCampaign.status eq CampaignStatus.ON_GOING.value) or (TBCampaign.status eq CampaignStatus.COMPLETED.value) }
            .orderBy(TBCampaign.createTime to SortOrder.DESC)
            .map(::mapper)
    }

    fun getCampaignByProjectId(projectId: Long): List<Campaign> {
        return TBCampaign.select { TBCampaign.projectId eq projectId }.map(::mapper)
    }

    @Transactional
    fun getCheckCampaigns(): List<Campaign> {
        return TBCampaign.select { TBCampaign.status eq CampaignStatus.UNDER_REVIEWED.value }.map(::mapper)
    }

    fun getCampaignByCreatorId(creatorId: Long): List<Campaign> {
        return TBCampaign.select { TBCampaign.creatorId eq creatorId }.map(::mapper)
    }

    fun mapper(r: ResultRow): Campaign {
        return Campaign(
            r[TBCampaign.campaignId],
            r[TBCampaign.name],
            r[TBCampaign.name],
            r[TBCampaign.picUrl],
            r[TBCampaign.description],
            r[TBCampaign.shareText],
            r[TBCampaign.startAt].toInstant(ZoneOffset.UTC),
            r[TBCampaign.endAt].toInstant(ZoneOffset.UTC),
            CampaignStatus.fromValue(r[TBCampaign.status]),
            r[TBCampaign.reward],
            r[TBCampaign.rewardAction],
            r[TBCampaign.projectId],
            r[TBCampaign.creatorId],
            r[TBCampaign.points],
            r[TBCampaign.credential],
            r[TBCampaign.nft],
            r[TBCampaign.createTime]
        )

    }

    fun updateCampaign(campaign: Campaign): Campaign? {
        TBCampaign.update({ TBCampaign.campaignId eq campaign.campaignId }) {
            it[name] = campaign.getUniName()
            it[picUrl] = campaign.picUrl
            it[description] = campaign.description
            it[startAt] = LocalDateTime.ofInstant(campaign.startAt, ZoneOffset.UTC)
            it[endAt] = LocalDateTime.ofInstant(campaign.endAt, ZoneOffset.UTC)
            it[status] = campaign.status.value
            it[reward] = campaign.reward
            it[rewardAction] = campaign.rewardAction
            it[points] = campaign.points ?: 0
            it[credential] = campaign.credentialId ?: 0
            it[nft] = campaign.nft ?: 0
        }
        return getCampaignById(campaign.campaignId)
    }

    fun updateCampaignStatus(campaign: Campaign, newStatus: Int) {
        val campaignStatus = getCampaignById(campaign.campaignId)?.status
        if (campaignStatus != CampaignStatus.DELETED) {
            TBCampaign.update({ TBCampaign.campaignId eq campaign.campaignId }) {
                it[status] = newStatus
            }
        }
    }

    fun addCampaignCredential(campaignId: Long, credentialIds: List<Pair<Long, Long>>) {
        TBCampaignCredential.batchInsert(credentialIds, true) {
            this[TBCampaignCredential.campaignCredentialId] = it.second
            this[TBCampaignCredential.campaignId] = campaignId
            this[TBCampaignCredential.credentialId] = it.first
            this[TBCampaignCredential.status] = SimpleStatus.NORMAL.value
            this[TBCampaignCredential.createTime] = LocalDateTime.now()
            this[TBCampaignCredential.updateTime] = LocalDateTime.now()
        }
    }

    fun deleteCampaignCredentials(campaignId: Long) {
        TBCampaignCredential.deleteWhere { TBCampaignCredential.campaignId eq campaignId }
    }
}