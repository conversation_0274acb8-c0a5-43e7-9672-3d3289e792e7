package com.rewardoor.app.dao

import com.rewardoor.app.dao.tables.*
import com.rewardoor.model.*
import org.jetbrains.exposed.sql.*
import org.springframework.stereotype.Repository
import org.jetbrains.exposed.exceptions.ExposedSQLException
import org.jetbrains.exposed.sql.SqlExpressionBuilder.eq
import org.jetbrains.exposed.sql.SqlExpressionBuilder.greater
import org.jetbrains.exposed.sql.SqlExpressionBuilder.greaterEq
import org.jetbrains.exposed.sql.SqlExpressionBuilder.less
import org.jetbrains.exposed.sql.SqlExpressionBuilder.lessEq
import java.time.ZoneOffset

@Repository
class UserWiseScoreRepository {

    fun addUserWiseScore(userWiseScore: UserWiseScore): Int {
        return TBUserWiseScore.insertIgnore {
            it[userId] = userWiseScore.userId
            it[address] = userWiseScore.address.orEmpty()
            it[addressType] = userWiseScore.addressType
            it[avatar] = userWiseScore.avatar
            it[totalScore] = userWiseScore.totalScore
            it[wealthScore] = userWiseScore.wealthScore?.score ?: 0
            it[identityScore] = userWiseScore.identityScore?.score ?: 0
            it[socialScore] = userWiseScore.socialScore?.score ?: 0
            it[engagementScore] = userWiseScore.engagementScore?.score ?: 0
            it[isNotCoinHolder] = userWiseScore.isNotCoinHolder
            it[hasNotCoinTransaction] = userWiseScore.hasNotCoinTransaction
            it[hasTonStake] = userWiseScore.hasTonStake
            it[hasTonLiquidityProvide] = userWiseScore.hasTonLiquidityProvide
            it[rank] = userWiseScore.rank
        }.insertedCount
    }

    fun addUserSuiWiseScore(userWiseScore: UserWiseScore): Int {
        return TBUserSuiWiseScore.insertIgnore {
            it[userId] = userWiseScore.userId
            it[address] = userWiseScore.address.orEmpty()
            it[addressType] = userWiseScore.addressType
            it[avatar] = userWiseScore.avatar
            it[totalScore] = userWiseScore.totalScore
            it[wealthScore] = userWiseScore.wealthScore?.score ?: 0
            it[identityScore] = userWiseScore.identityScore?.score ?: 0
            it[socialScore] = userWiseScore.socialScore?.score ?: 0
            it[engagementScore] = userWiseScore.engagementScore?.score ?: 0
            it[isNotCoinHolder] = userWiseScore.isNotCoinHolder
            it[hasNotCoinTransaction] = userWiseScore.hasNotCoinTransaction
            it[hasTonStake] = userWiseScore.hasTonStake
            it[hasTonLiquidityProvide] = userWiseScore.hasTonLiquidityProvide
            it[rank] = userWiseScore.rank
        }.insertedCount
    }

    fun addUserWiseScoreTmp(userWiseScore: UserWiseScore): Int {
        return TBUserWiseScoreTmp.insertIgnore {
            it[userId] = userWiseScore.userId
            it[address] = userWiseScore.address.orEmpty()
            it[addressType] = userWiseScore.addressType
            it[avatar] = userWiseScore.avatar
            it[totalScore] = userWiseScore.totalScore
            it[wealthScore] = userWiseScore.wealthScore?.score ?: 0
            it[identityScore] = userWiseScore.identityScore?.score ?: 0
            it[socialScore] = userWiseScore.socialScore?.score ?: 0
            it[engagementScore] = userWiseScore.engagementScore?.score ?: 0
            it[TBUserWiseScore.isNotCoinHolder] = userWiseScore.isNotCoinHolder
            it[TBUserWiseScore.hasNotCoinTransaction] = userWiseScore.hasNotCoinTransaction
            it[TBUserWiseScore.hasTonStake] = userWiseScore.hasTonStake
            it[TBUserWiseScore.hasTonLiquidityProvide] = userWiseScore.hasTonLiquidityProvide
            it[rank] = userWiseScore.rank
        }.insertedCount
    }

    fun getAllScoreTmp(): List<UserWiseScore> {
        return TBUserWiseScoreTmp.selectAll().map(::mapUserWiseScoreTmp)
    }

    fun updateUserWiseScore(userWiseScore: UserWiseScore): Int {
        return TBUserWiseScore.update({ TBUserWiseScore.userId eq userWiseScore.userId }) {
            it[address] = userWiseScore.address.orEmpty()
            it[addressType] = userWiseScore.addressType
            it[totalScore] = userWiseScore.totalScore
            it[wealthScore] = userWiseScore.wealthScore?.score ?: 0
            it[identityScore] = userWiseScore.identityScore?.score ?: 0
            it[socialScore] = userWiseScore.socialScore?.score ?: 0
            it[engagementScore] = userWiseScore.engagementScore?.score ?: 0
            it[isNotCoinHolder] = userWiseScore.isNotCoinHolder
            it[hasNotCoinTransaction] = userWiseScore.hasNotCoinTransaction
            it[hasTonStake] = userWiseScore.hasTonStake
            it[hasTonLiquidityProvide] = userWiseScore.hasTonLiquidityProvide
            it[rank] = userWiseScore.rank
        }
    }

    fun updateUserSuiWiseScore(userWiseScore: UserWiseScore): Int {
        return TBUserSuiWiseScore.update({ TBUserSuiWiseScore.userId eq userWiseScore.userId }) {
            it[address] = userWiseScore.address.orEmpty()
            it[addressType] = userWiseScore.addressType
            it[totalScore] = userWiseScore.totalScore
            it[wealthScore] = userWiseScore.wealthScore?.score ?: 0
            it[identityScore] = userWiseScore.identityScore?.score ?: 0
            it[socialScore] = userWiseScore.socialScore?.score ?: 0
            it[engagementScore] = userWiseScore.engagementScore?.score ?: 0
            it[isNotCoinHolder] = userWiseScore.isNotCoinHolder
            it[hasNotCoinTransaction] = userWiseScore.hasNotCoinTransaction
            it[hasTonStake] = userWiseScore.hasTonStake
            it[hasTonLiquidityProvide] = userWiseScore.hasTonLiquidityProvide
            it[rank] = userWiseScore.rank
        }
    }

    fun getScoreByUserId(userId: Long): UserWiseScore? {
        return TBUserWiseScore.select { TBUserWiseScore.userId eq userId }.limit(1).map(::mapUserWiseScore)
            .firstOrNull()
    }

    fun getSuiScoreByUserId(userId: Long): UserWiseScore? {
        return TBUserSuiWiseScore.select { TBUserSuiWiseScore.userId eq userId }.limit(1).map(::mapUserSuiWiseScore)
            .firstOrNull()
    }

    fun getTop100ScoreUser(): List<UserWiseScore> {
        return TBUserWiseScore.selectAll()
            .orderBy(TBUserWiseScore.totalScore, SortOrder.DESC)
            .limit(500)
            .map(::mapUserWiseScore)
    }

    fun getTop100SocialScoreUser(): List<UserWiseScore> {
        return TBUserWiseScore.selectAll()
            .orderBy(TBUserWiseScore.socialScore, SortOrder.DESC)
            .limit(100)
            .map(::mapUserWiseScore)
    }

    fun getTop100EngageScoreUser(): List<UserWiseScore> {
        return TBUserWiseScore.selectAll()
            .orderBy(TBUserWiseScore.engagementScore, SortOrder.DESC)
            .limit(100)
            .map(::mapUserWiseScore)
    }

    fun getTopScoreUser(limitNum: Int): List<Long> {
        return TBUserWiseScore
            .slice(TBUserWiseScore.userId) // 只查询 userId 列，避免查询其他无关列
            .selectAll()
            .orderBy(TBUserWiseScore.totalScore, SortOrder.DESC)
            .limit(limitNum)
            .map { it[TBUserWiseScore.userId] }
    }

    fun getUserIdsByScoreCondition(wiseScoreType: Int, wiseScoreLimit: Int): List<Long> {
        val condition = when (wiseScoreType) {
            1 -> TBUserWiseScore.totalScore greater wiseScoreLimit // 大于
            2 -> TBUserWiseScore.totalScore greaterEq wiseScoreLimit // 大于等于
            3 -> TBUserWiseScore.totalScore less wiseScoreLimit // 小于
            4 -> TBUserWiseScore.totalScore lessEq wiseScoreLimit // 小于等于
            else -> throw IllegalArgumentException("Invalid wiseScoreType: $wiseScoreType") // 非法类型处理
        }

        return TBUserWiseScore
            .slice(TBUserWiseScore.userId)
            .select { condition }
            .orderBy(TBUserWiseScore.totalScore, SortOrder.DESC) // 按 totalScore 降序排序
            .map { it[TBUserWiseScore.userId] } // 提取 userId 列值
    }


    fun getAllScoreUser(): List<UserWiseScore> {
        return TBUserWiseScore.selectAll()
            .orderBy(TBUserWiseScore.totalScore, SortOrder.DESC)
            .map(::mapUserWiseScore)
    }

    fun addUserShareLink(uid: Long, soType: Int, link: String): Int {
        return TBUserShareLink.insertIgnore {
            it[userId] = uid
            it[socialType] = soType
            it[shareLink] = link
        }.insertedCount
    }

    fun getUserShareLink(uid: Long, soType: Int): List<UserDcTgShareLink> {
        return TBUserShareLink.select { (TBUserShareLink.userId eq uid) and (TBUserShareLink.socialType eq soType) }
            .map {
                UserDcTgShareLink(
                    userId = uid,
                    socialType = soType,
                    it[TBUserShareLink.shareLink]
                )
            }
    }

    fun getAllUserShareLink(uid: Long): List<UserDcTgShareLink> {
        return TBUserShareLink.select { TBUserShareLink.userId eq uid }
            .map {
                UserDcTgShareLink(
                    userId = uid,
                    socialType = it[TBUserShareLink.socialType],
                    it[TBUserShareLink.shareLink]
                )
            }
    }

    fun mapUserWiseScore(row: ResultRow): UserWiseScore {
        return UserWiseScore(
            userId = row[TBUserWiseScore.userId],
            address = row[TBUserWiseScore.address],
            addressType = row[TBUserWiseScore.addressType],
            avatar = row[TBUserWiseScore.avatar],
            totalScore = row[TBUserWiseScore.totalScore],
            wealthScore = WealthScore(score = row[TBUserWiseScore.wealthScore]),
            identityScore = IdentityScore(score = row[TBUserWiseScore.identityScore]),
            socialScore = SocialScore(score = row[TBUserWiseScore.socialScore]),
            engagementScore = EngagementScore(score = row[TBUserWiseScore.engagementScore]),
            isNotCoinHolder = row[TBUserWiseScore.isNotCoinHolder],
            hasNotCoinTransaction = row[TBUserWiseScore.hasNotCoinTransaction],
            hasTonStake = row[TBUserWiseScore.hasTonStake],
            hasTonLiquidityProvide = row[TBUserWiseScore.hasTonLiquidityProvide],
            rank = row[TBUserWiseScore.rank],
            createTime = row[TBUserWiseScore.createTime],
            updateTime = row[TBUserWiseScore.updateTime]
        )
    }

    fun mapUserSuiWiseScore(row: ResultRow): UserWiseScore {
        return UserWiseScore(
            userId = row[TBUserSuiWiseScore.userId],
            address = row[TBUserSuiWiseScore.address],
            addressType = row[TBUserSuiWiseScore.addressType],
            avatar = row[TBUserSuiWiseScore.avatar],
            totalScore = row[TBUserSuiWiseScore.totalScore],
            wealthScore = WealthScore(score = row[TBUserSuiWiseScore.wealthScore]),
            identityScore = IdentityScore(score = row[TBUserSuiWiseScore.identityScore]),
            socialScore = SocialScore(score = row[TBUserSuiWiseScore.socialScore]),
            engagementScore = EngagementScore(score = row[TBUserSuiWiseScore.engagementScore]),
            isNotCoinHolder = row[TBUserSuiWiseScore.isNotCoinHolder],
            hasNotCoinTransaction = row[TBUserSuiWiseScore.hasNotCoinTransaction],
            hasTonStake = row[TBUserSuiWiseScore.hasTonStake],
            hasTonLiquidityProvide = row[TBUserSuiWiseScore.hasTonLiquidityProvide],
            rank = row[TBUserSuiWiseScore.rank],
            createTime = row[TBUserSuiWiseScore.createTime],
            updateTime = row[TBUserSuiWiseScore.updateTime]
        )
    }

    fun mapUserWiseScoreTmp(row: ResultRow): UserWiseScore {
        return UserWiseScore(
            userId = row[TBUserWiseScoreTmp.userId],
            address = row[TBUserWiseScoreTmp.address],
            addressType = row[TBUserWiseScoreTmp.addressType],
            avatar = row[TBUserWiseScoreTmp.avatar],
            totalScore = row[TBUserWiseScoreTmp.totalScore],
            wealthScore = WealthScore(score = row[TBUserWiseScore.wealthScore]),
            identityScore = IdentityScore(score = row[TBUserWiseScore.identityScore]),
            socialScore = SocialScore(score = row[TBUserWiseScore.socialScore]),
            engagementScore = EngagementScore(score = row[TBUserWiseScore.engagementScore]),
            isNotCoinHolder = row[TBUserWiseScore.isNotCoinHolder],
            hasNotCoinTransaction = row[TBUserWiseScore.hasNotCoinTransaction],
            hasTonStake = row[TBUserWiseScore.hasTonStake],
            hasTonLiquidityProvide = row[TBUserWiseScore.hasTonLiquidityProvide],
            rank = row[TBUserWiseScoreTmp.rank],
            createTime = row[TBUserWiseScoreTmp.createTime]
        )
    }
}