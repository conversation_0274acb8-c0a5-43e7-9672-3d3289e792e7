package com.rewardoor.app.dao


import com.rewardoor.app.dao.tables.TBUserSBTList
import com.rewardoor.model.User
import com.rewardoor.model.UserSBTList
import com.rewardoor.model.WiseInviteCode
import org.jetbrains.exposed.sql.*
import org.jetbrains.exposed.sql.SqlExpressionBuilder.eq
import org.springframework.stereotype.Repository
import org.springframework.transaction.annotation.Transactional

@Repository
@Transactional
class SBTWhiteListRepository {
    fun createUserSBT(userSBTList: UserSBTList): Int {
        return TBUserSBTList.insertIgnore {
            it[userId] = userSBTList.userId
            it[address] = userSBTList.address.orEmpty()
            it[addressType] = userSBTList.addressType
            it[avatar] = userSBTList.avatar
            it[activityId] = userSBTList.activityId ?: 371
            it[sbtLink] = userSBTList.sbtLink
            it[claimedType] = userSBTList.claimedType ?: 0
        }.insertedCount
    }

    fun updateUserSBT(userSBTList: UserSBTList): Int {
        return TBUserSBTList.update({ TBUserSBTList.userId eq userSBTList.userId }) {
            it[address] = userSBTList.address.orEmpty()
            it[addressType] = userSBTList.addressType
            it[avatar] = userSBTList.avatar
            it[activityId] = userSBTList.activityId ?: 371
            it[sbtLink] = userSBTList.sbtLink
        }
    }

    fun updateUserSBTLink(userId: Long, address: String, activityId: Int, newSbtLink: String): Int {
        return TBUserSBTList.update({
            (TBUserSBTList.userId eq userId) and
                    (TBUserSBTList.address eq address) and
                    (TBUserSBTList.activityId eq activityId)
        }) {
            it[sbtLink] = newSbtLink
        }
    }

    fun updateUserSBTClaimedType(userId: Long, address: String, activityId: Int, newClaimedType: Int): Int {
        return TBUserSBTList.update({
            (TBUserSBTList.userId eq userId) and
                    (TBUserSBTList.address eq address) and
                    (TBUserSBTList.activityId eq activityId)
        }) {
            it[claimedType] = newClaimedType
        }
    }

    fun getUserSbtById(userId: Long): UserSBTList? {
        return TBUserSBTList.select { TBUserSBTList.userId eq userId }.limit(1).map(::mapUserSBT)
            .firstOrNull()
    }

    fun getInitUserSbtList(): List<UserSBTList> {
        return TBUserSBTList.select {
            (TBUserSBTList.claimedType eq 0) and
                    (TBUserSBTList.sbtLink neq "") and
                    (TBUserSBTList.activityId neq 371)
        }
            .map(::mapUserSBT)
    }

    fun getUserSbtListByActivityId(activityId: Int): List<UserSBTList> {
        return TBUserSBTList.select {
            (TBUserSBTList.sbtLink neq "") and
                    (TBUserSBTList.activityId eq activityId)
        }
            .map(::mapUserSBT)
    }

    fun getUserIdListByActivityId(activityId: Int): List<Long> {
        return TBUserSBTList
            .slice(TBUserSBTList.userId).select {
                (TBUserSBTList.sbtLink neq "") and
                        (TBUserSBTList.activityId eq activityId)
            }
            .map { it[TBUserSBTList.userId] }
            .distinct()
    }

    fun getClaimedUserCntByActivityId(activityId: Int): Long {
        return TBUserSBTList
            .slice(TBUserSBTList.userId.count())
            .select {
                (TBUserSBTList.sbtLink neq "") and
                        (TBUserSBTList.activityId eq activityId)
            }
            .single()[TBUserSBTList.userId.count()]
    }

    fun getUserSbtByUidAddressActivityId(userId: Long, address: String, activityId: Int): UserSBTList? {
        return TBUserSBTList.select {
            (TBUserSBTList.userId eq userId) and
                    (TBUserSBTList.address eq address) and
                    (TBUserSBTList.activityId eq activityId)
        }
            .limit(1).map(::mapUserSBT).firstOrNull()
    }

    fun getUserSbtByAddress(address: String): UserSBTList? {
        return TBUserSBTList.select { TBUserSBTList.address eq address }.limit(1).map(::mapUserSBT)
            .firstOrNull()
    }

    fun getUserSbtListByUseId(userId: Long): List<UserSBTList>? {
        return TBUserSBTList.select { TBUserSBTList.userId eq userId }.map(::mapUserSBT)
    }

    fun insertUserSbtList(user: WiseInviteCode) {
        //
        TBUserSBTList.select { TBUserSBTList.userId eq user.userId }.map(::mapUserSBT)
    }


    fun mapUserSBT(row: ResultRow): UserSBTList {
        return UserSBTList(
            userId = row[TBUserSBTList.userId],
            address = row[TBUserSBTList.address],
            addressType = row[TBUserSBTList.addressType],
            avatar = row[TBUserSBTList.avatar],
            activityId = row[TBUserSBTList.activityId],
            sbtLink = row[TBUserSBTList.sbtLink],
            claimedType = row[TBUserSBTList.claimedType],
            createTime = row[TBUserSBTList.createTime],
            updateTime = row[TBUserSBTList.updateTime]
        )
    }
}