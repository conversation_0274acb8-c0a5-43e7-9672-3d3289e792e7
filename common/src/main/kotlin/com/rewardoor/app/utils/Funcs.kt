package com.rewardoor.app.utils

import kotlin.reflect.KMutableProperty
import kotlin.reflect.KProperty
import kotlin.reflect.full.memberProperties

object Funcs {
    fun <T : Any, R : Any> T.copyPropsFrom(fromObject: R, vararg props: KProperty<*>) {
        // only consider mutable properties
        val mutableProps = this::class.memberProperties.filterIsInstance<KMutableProperty<*>>()
        // if source list is provided use that otherwise use all available properties
        val sourceProps = if (props.isEmpty()) fromObject::class.memberProperties else props.toList()
        // copy all matching
        mutableProps.forEach { targetProp ->
            sourceProps.find {
                // make sure properties have same name and compatible types
                it.name == targetProp.name
            }?.let { matchingProp ->
                matchingProp.getter.call(fromObject)?.let {
                    targetProp.setter.call(this, it)
                }
            }
        }
    }

}