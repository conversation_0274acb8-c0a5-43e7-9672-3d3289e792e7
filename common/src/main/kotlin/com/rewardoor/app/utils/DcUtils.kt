package com.rewardoor.app.utils

import discord4j.common.JacksonResources
import discord4j.common.ReactorResources
import discord4j.rest.http.ExchangeStrategies.jackson
import discord4j.rest.http.client.AuthorizationScheme
import discord4j.rest.request.*
import discord4j.rest.response.ResponseFunction
import discord4j.rest.route.Routes.BASE_URL
import reactor.netty.http.client.HttpClient

object DcUtils {
    fun createDefaultRouter(token: String, authorizationScheme: AuthorizationScheme = AuthorizationScheme.BEARER, resources: ReactorResources): Router {
        return DefaultRouter(
            RouterOptions(
                authorizationScheme,
                token, resources,
                jackson(JacksonResources.create().objectMapper),
                emptyList<ResponseFunction>(),
                BucketGlobalRateLimiter.create(),
                RequestQueueFactory.buffering(),
                BASE_URL
            )
        )
    }
}