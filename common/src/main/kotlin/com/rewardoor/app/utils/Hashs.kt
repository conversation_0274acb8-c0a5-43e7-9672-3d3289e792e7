package com.rewardoor.app.utils

import com.auth0.jwt.interfaces.DecodedJWT
import com.google.crypto.tink.subtle.Hkdf
import java.math.BigInteger

object Hashs {
    private val seed = "c52bc469-cfc9-4924-98e1-6eb2bb885d08"

    fun hkdfJwt(token: DecodedJWT): String {
        var salt = token.issuer.toByteArray()
        token.audience.map { it.toByteArray() }.forEach { salt = salt.plus(it) }
        val hkdf = Hkdf.computeHkdf("HMACSHA256", seed.toByteArray(),
                salt, token.subject.toByteArray(), 16)
        return BigInteger(1, hkdf).toString(10)
    }
}