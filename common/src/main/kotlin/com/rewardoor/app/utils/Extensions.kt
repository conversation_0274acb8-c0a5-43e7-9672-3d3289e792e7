package com.rewardoor.app.utils

import com.rewardoor.enums.CredentialForms
import com.rewardoor.model.Credential
import org.springframework.dao.DataIntegrityViolationException
import org.springframework.data.redis.core.Cursor
import org.springframework.data.redis.core.ScanOptions
import org.springframework.data.redis.core.StringRedisTemplate
import java.sql.SQLIntegrityConstraintViolationException

fun Credential.getCredentialForm(): String {
    return when (labelType) {
        1, 2, 11 -> {
            CredentialForms.TWITTER.json
        }

        3 -> {
            CredentialForms.SPACE.json
        }

        4 -> {
            CredentialForms.DISCORD_SERVICE.json
        }

        5 -> {
            CredentialForms.DISCORD_ROLE.json
        }

        6 -> {
            CredentialForms.TELEGRAM_GROUP.json
        }

        7 -> {
            CredentialForms.TELEGRAM_CHANNEL.json
        }
        //twitter
        8 -> {
            CredentialForms.VISIT_PAGE_OR_SITE.json
        }

        9 -> {
            CredentialForms.REGISTER_BY_TWITTER.json
        }

        10 -> {
            CredentialForms.SIGN_MESSAGE.json
        }

        12 -> {
            CredentialForms.SNAPSHOT.json
        }
        13 -> {
            CredentialForms.AIRDROP_ADDRESS_AGGREGATION.json
        }

        else -> ""
    }
}

fun Exception.isDuplicate(): Boolean {
    if (this is DataIntegrityViolationException
            || this is SQLIntegrityConstraintViolationException
    ) {
        return true
    }
    if (this.cause is DataIntegrityViolationException
        || this.cause is SQLIntegrityConstraintViolationException) {
        return true
    }
    return false
}

fun StringRedisTemplate.scanKeys(pattern: String): Set<String> {
    val keys = mutableSetOf<String>()
    val scanOptions = ScanOptions.scanOptions().match(pattern).count(50).build()
    this.scan(scanOptions).use {
        it.forEachRemaining { k -> keys.add(k) }
    }
    return keys.toSet()
}