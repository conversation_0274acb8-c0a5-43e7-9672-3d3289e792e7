package com.rewardoor.app.utils

import org.web3j.abi.TypeEncoder
import org.web3j.abi.datatypes.Address
import org.web3j.abi.datatypes.DynamicStruct
import org.web3j.abi.datatypes.Utf8String
import org.web3j.abi.datatypes.generated.Bytes32
import org.web3j.abi.datatypes.generated.Uint256
import org.web3j.crypto.*
import org.web3j.utils.Numeric
import java.math.BigInteger
import kotlin.math.sqrt


object Signs {
    fun getAddressUsedToSignHashedMessage(signedHexMessage: String, originalMessage: String): String {
        var signedMessageInHex = signedHexMessage
        if (signedMessageInHex.startsWith("0x")) {
            signedMessageInHex = signedMessageInHex.substring(2)
        }

        // No need to prepend these strings with 0x because
        // Numeric.hexStringToByteArray() accepts both formats
        val r = signedMessageInHex.substring(0, 64)
        val s = signedMessageInHex.substring(64, 128)
        val v = signedMessageInHex.substring(128, 130)

        // Using Sign.signedPrefixedMessageToKey for EIP-712 compliant signatures.
        val pubkey = Sign.signedPrefixedMessageToKey(
            originalMessage.toByteArray(),
            Sign.SignatureData(
                Numeric.hexStringToByteArray(v)[0],
                Numeric.hexStringToByteArray(r),
                Numeric.hexStringToByteArray(s)
            )
        ).toString(16)
        return "0x${Keys.getAddress(pubkey)}"
    }

    fun keccak256(input: String): String {
        return Hash.sha3String(input)
    }

    fun hashNFTSpaceStation(privateKey: String, cid: Long, nftAddr: String, dummyId: Long,
                            powah: Long, accountAddr: String, chainId: Int, verifyingContract: String,
                            domainName: String): kotlin.Pair<String, Long> {
        val structJson = """
            {
              "types": {
                "EIP712Domain": [
                  {"name": "name", "type": "string"},
                  {"name": "version", "type": "string"},
                  {"name": "chainId", "type": "uint256"},
                  {"name": "verifyingContract", "type": "address"}
                ]
              },
              "domain": {
                "name": "$domainName",
                "version": "1.0.0",
                "chainId": ${chainId},
                "verifyingContract": "$verifyingContract"
              }
            }
        """.trimIndent()
        val fHash = keccak256("NFT(uint256 cid,address starNFT,uint256 dummyId,uint256 powah,address account)")
        val content = TypeEncoder.encode(DynamicStruct(
            Bytes32(Numeric.hexStringToByteArray(fHash)),
            Uint256(cid),
            Address(nftAddr),
            Uint256(dummyId),
            Uint256(powah),
            Address(accountAddr)))
        val fh = Hash.sha3(Numeric.hexStringToByteArray("0x$content"))
        val domain = StructuredDataEncoder(structJson).hashDomain()
        println("domain: " + Numeric.toHexString(domain))
        println("fh : " + Numeric.toHexString(fh))

        val typeHash = Hash.sha3(Numeric.hexStringToByteArray("0x1901") + domain + fh)
        println("typeHash: " + Numeric.toHexString(typeHash))

        val kp = Credentials.create(privateKey).ecKeyPair
        val sign = Sign.signMessage(typeHash, kp, false)

        val retval = ByteArray(65)
        System.arraycopy(sign.r, 0, retval, 0, 32)
        System.arraycopy(sign.s, 0, retval, 32, 32)
        System.arraycopy(sign.v, 0, retval, 64, 1)
        return kotlin.Pair(Numeric.toHexString(retval), dummyId)
    }

    fun szudzikPair(x: Long, y: Long): Long {
        return if (x >= y) x * x + x + y else x + y * y
    }

    fun unpair(z: Int): IntArray {
        val b = sqrt(z.toDouble()).toInt()
        val a = z - b * b
        return if (a < b) intArrayOf(a, b) else intArrayOf(b, a - b)
    }

    fun hashGameAirDrop(privateKey: String, accountAddr: String,
                        round: String, amount: BigInteger, salt: String,
                        chainId: Int, verifyingContract: String, domainName: String): String {
        val structJson = """
            {
              "types": {
                "EIP712Domain": [
                  {"name": "name", "type": "string"},
                  {"name": "version", "type": "string"},
                  {"name": "chainId", "type": "uint256"},
                  {"name": "verifyingContract", "type": "address"}
                ]
              },
              "domain": {
                "name": "$domainName",
                "version": "1.0.0",
                "chainId": ${chainId},
                "verifyingContract": "$verifyingContract"
              }
            }
        """.trimIndent()
        val fHash = keccak256("Claim(uint256 amount,string round,string salt,address account)")
        val content = TypeEncoder.encode(DynamicStruct(
            Bytes32(Numeric.hexStringToByteArray(fHash)),
            Uint256(amount),
            Utf8String(round),
            Utf8String(salt),
            Address(accountAddr)))
        val fh = Hash.sha3(Numeric.hexStringToByteArray("0x$content"))
        val domain = StructuredDataEncoder(structJson).hashDomain()
        println("domain: " + Numeric.toHexString(domain))
        println("fh : " + Numeric.toHexString(fh))

        val typeHash = Hash.sha3(Numeric.hexStringToByteArray("0x1901") + domain + fh)
        println("typeHash: " + Numeric.toHexString(typeHash))

        val kp = Credentials.create(privateKey).ecKeyPair
        val sign = Sign.signMessage(typeHash, kp, false)

        val retval = ByteArray(65)
        System.arraycopy(sign.r, 0, retval, 0, 32)
        System.arraycopy(sign.s, 0, retval, 32, 32)
        System.arraycopy(sign.v, 0, retval, 64, 1)
        return Numeric.toHexString(retval)
    }
}
