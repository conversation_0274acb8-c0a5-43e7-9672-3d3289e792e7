package com.rewardoor.app.utils

import org.ton.java.address.Address

object TonAddressUtils {
    
    fun toHexAddress(address: String): String {
        return try {
            val addr = Address.of(address)
            "0:${addr.toHex()}"
        } catch (e: Exception) {
            throw IllegalArgumentException("Invalid TON address: $address", e)
        }
    }
    
    fun isValidTonAddress(address: String): <PERSON><PERSON>an {
        return try {
            Address.of(address)
            true
        } catch (e: Exception) {
            false
        }
    }
    
    fun hexToUserFriendly(hexAddress: String): String {
        return try {
            val addr = Address.of(hexAddress)
            addr.toString()
        } catch (e: Exception) {
            throw IllegalArgumentException("Invalid hex address: $hexAddress", e)
        }
    }
    
    fun hexToNonBounceable(hexAddress: String): String {
        return try {
            val addr = Address.of(hexAddress)
            addr.toNonBounceable()
        } catch (e: Exception) {
            throw IllegalArgumentException("Invalid hex address: $hexAddress", e)
        }
    }
    
    fun isSameAddress(address1: String, address2: String): Bo<PERSON>an {
        return try {
            val hex1 = toHexAddress(address1)
            val hex2 = toHexAddress(address2)
            hex1 == hex2
        } catch (e: Exception) {
            false
        }
    }
}
