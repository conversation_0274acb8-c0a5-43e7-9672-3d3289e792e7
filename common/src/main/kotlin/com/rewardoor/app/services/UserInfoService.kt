package com.rewardoor.app.services

import com.rewardoor.app.dto.UserInfoDto
import com.rewardoor.model.User
import com.rewardoor.app.dto.*
import com.rewardoor.model.UserZK
import org.springframework.stereotype.Service
import java.time.Instant
import java.time.Duration

@Service
class UserInfoService(
    private val userService: UserService,
    private val userTwitterService: UserTwitterService,
    private val telegramService: TelegramService,
    private val dcService: DiscordService,
    private val suiService: SuiService,
    private val zkLoginService: ZKLoginService
) {
    fun getUserDtoInfo(user: User): UserInfoDto {
        val userId = user.userId
        val ut = userTwitterService.getUserInfo(userId)
        val uwt = if (ut != null) {
            UserTwitterDto(ut.twitterId, ut.twitterName, ut.twitterScreenName, ut.twitterProfileImage, ut.connected)
        } else {
            UserTwitterDto.default()
        }
        val tg = telegramService.getUserInfo(userId)
        val tgDto = if (tg != null) {
            UserTgDto(tg.tgId, tg.firstName, tg.lastName, tg.username, tg.photoUrl, tg.connected)
        } else {
            UserTgDto()
        }
        val dc = dcService.getUserDcInfo(user.userId)
        val dcDto = if (dc != null && dc.tokenExpire > Instant.now().minusMillis(Duration.ofDays(7).toMillis())
                .toEpochMilli()
        ) {
            UserDcDto(dc.dcId, dc.username, dc.avatar, dc.connected)
        } else {
            UserDcDto()
        }
        val zk = zkLoginService.getZKLoginById(userId)?: UserZK(userId, null, null, null, null, null)
        val userSui = userService.getSuiWalletByUserId(userId).let { UserSuiDto(it.suiWallet,
            it.binded) }
        val ton = userService.getTonWalletByUserId(user.userId)
        val tonDto = UserTonDto(ton.tonWallet, ton.binded)
        return UserInfoDto(user, emptyList(), uwt, tgDto, dcDto, UserZKDto.fromModel(zk), tonDto, userSui)
    }
}