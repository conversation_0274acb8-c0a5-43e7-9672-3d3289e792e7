package com.rewardoor.app.services

import com.rewardoor.IdGenerator
import com.rewardoor.app.dao.ParticipantRepository
import com.rewardoor.app.dao.UserRepository
import com.rewardoor.app.dao.UserSuiRepository
import com.rewardoor.app.dao.ZKLoginRepository
import com.rewardoor.model.Participant
import com.rewardoor.model.User
import com.rewardoor.model.UserZK
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.time.Instant

@Service
@Transactional
class ZKLoginService(private val zkLoginRepo: ZKLoginRepository,
                     private val userRepo: UserRepository,
                     private val userSuiRepo: UserSuiRepository,
                     private val idGenerator: IdGenerator,
                     private val participantRepo: ParticipantRepository) {
    fun addZKLogin(iss: String, sub: String, identity: String, salt: String, address: String = ""): UserZK {
        val current = getZKLogin(iss, identity)
        if (current != null) {
            zkLoginRepo.updateZKAddress(current.userId, address)

            val existingSuiWallet = userSuiRepo.getSuiUserWalletByUserId(current.userId)
            if (existingSuiWallet == null) {
                userSuiRepo.bindSuiWallet(current.userId, address, "")
            }
            return current
        }

        val userId = idGenerator.getNewId()
        userRepo.addUser(
                User(userId, User.AVATAR_BASE + userId, "", ""), userId
        )
        val isIgnore = zkLoginRepo.addZKLogin(userId, iss, sub, identity, salt, address)
        userSuiRepo.bindSuiWallet(userId, address, "")

        val participant = Participant(
                userId = userId,
                campaignId = 0L,
                wallet = "",
                nfts = emptyList(),
                points = emptyList(),
                pointNum = 0L,
                isJoin = false,
                isVisit = false,
                credentials = emptyList(),
                verifiedCredentials = emptyList(),
                participantDate = Instant.now()
        )
        participantRepo.createParticipant(participant)
        return UserZK(userId, iss, sub, identity, address, salt, !isIgnore)
    }

    fun getZKLogin(iss: String, identity: String): UserZK? {
        return zkLoginRepo.getZKLogin(iss, identity)
    }

    fun getZKLoginById(userId: Long): UserZK? {
        return zkLoginRepo.getZKLoginById(userId)
    }

    fun updateZKAddress(userId: Long, address: String) {
        zkLoginRepo.updateZKAddress(userId, address)

        // Also update Sui wallet info if address is not empty
        if (address.isNotBlank()) {
            // Check if this user already has a Sui wallet
            val existingSuiWallet = userSuiRepo.getSuiUserWalletByUserId(userId)
            if (existingSuiWallet == null || existingSuiWallet.suiWallet.isNullOrBlank()) {
                // Only bind if the wallet doesn't already exist for another user
                val existingWalletUser = userSuiRepo.findSuiWallet(address)
                if (existingWalletUser == null) {
                    // Bind the zkLogin address as a Sui wallet
                    userSuiRepo.bindSuiWallet(userId, address, "")
                }
            }
        }
    }
}