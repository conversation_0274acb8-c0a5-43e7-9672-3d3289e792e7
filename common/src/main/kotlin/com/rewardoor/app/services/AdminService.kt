package com.rewardoor.app.services

import com.rewardoor.IdGenerator
import com.rewardoor.app.dao.*
import com.rewardoor.app.utils.Signs
import com.rewardoor.model.Admin
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional
import mu.KotlinLogging

@Component
@Transactional
class AdminService(
    val adminRepo: AdminRepository,
    val idGenerator: IdGenerator,
    val userRepo: UserRepository,
    val projectRepo: ProjectRepository,
    val adminNonceRepo: AdminNonceRepository,
    val userTonRepo: UserTonRepository,
    val suiService: SuiService
) {
    private val logger = KotlinLogging.logger {}
    fun getAdminList(projectId: Long): List<Admin> {
        val adminList = adminRepo.getAdmins(projectId)
        if (adminList.isEmpty()) {
            val project = projectRepo.getProjectById(projectId)!!
            val creator = userRepo.findUserById(project.creatorId)!!
            val creatorEvmAddress = creator.wallet
            val creatorTonAddress = creator.ton.hexAddress
            val creatorSuiAddress = creator.suiAddress
            println("evm is $creatorEvmAddress ton $creatorTonAddress sui address $creatorSuiAddress")
            val adminWallet = if (!creatorTonAddress.isNullOrEmpty()) {
                creatorTonAddress
            } else if (creatorSuiAddress.isNotEmpty()) {
                creatorSuiAddress
            } else {
                creatorEvmAddress
            }
            val owner = Admin(
                userId = creator.userId,
                wallet = adminWallet,
                projectId = project.projectId,
                creatorId = creator.userId,
                isOwner = 1,
                status = 1
            )
            addAdmin(owner)
        }
        return adminRepo.getAdmins(projectId)
    }

    fun getAdminByWalletAndProjectId(wallet: String, projectId: Long): Admin? {
        return adminRepo.getAdminByWalletAndProjectId(wallet, projectId)
    }

    fun getAdminByWallet(wallet: String): Admin? {
        return adminRepo.getAdminByWallet(wallet)
    }

    fun isAdminExist(admin: Admin): Boolean {
        val checkAdmin = getAdminByWalletAndProjectId(admin.wallet, admin.projectId)
        return checkAdmin?.status == 1
    }

    fun addAdmin(admin: Admin): Admin {
        val user = userRepo.findUserByWallet(admin.wallet)
        if (user != null) {
            admin.userId = user.userId
        } else {
            val tonUser =
                userTonRepo.findTonWallet(admin.wallet) // eg: eqcdldj_mufgguki3fkiuonttu2mivnjbxoiqyglcwk5ogxs,uqcdldj_mufgguki3fkiuonttu2mivnjbxoiqyglcwk5olgp
            if (tonUser != null) {
                admin.wallet = admin.wallet
                admin.userId = tonUser.userId
            }
        }
        val project = projectRepo.getProjectById(admin.projectId)!!
        admin.creatorId = project.creatorId
        admin.status = 1
        if (isAdminExist(admin)) {
            return admin
        }
        val checkAdmin = getAdminByWalletAndProjectId(admin.wallet, admin.projectId)
        when {
            checkAdmin == null -> adminRepo.addAdmin(admin)
            checkAdmin.status == 2 -> { //has been deleted
                admin.status = 1 // 1 : effected
                adminRepo.updateAdmin(admin)
            }
        }
        return admin
    }

    fun isOtherProjectAdmin(admin: Admin): Boolean {
        val projects = projectRepo.getAllProjects()
        val ownerIds = projects.map { it.creatorId }.distinct()
        val ownerWallets = ownerIds.map { ownerId ->
            val user = userRepo.findUserById(ownerId)
            user?.wallet
        }.filterNotNull().distinct()
        val adminWallets = adminRepo.getAllAdmins().filter { it.status == 1 }.map { it.wallet }.distinct()
        val allWallets = (adminWallets + ownerWallets).distinct()
        return allWallets.contains(admin.wallet)
    }

    fun isOtherProjectAdminSimple(admin: Admin): Boolean {
        var userId = userRepo.findUserByWallet(admin.wallet)?.userId
        if (userId == null) {
           userId = userTonRepo.findTonWallet(admin.wallet)?.userId
        }
        if (userId == null) {
            return false
        }
        val project = projectRepo.getUserProjects(userId)
        if (project.isNotEmpty()) {
            return true
        }
        val adminByUserId = adminRepo.getAdminByUserId(userId)
        if (adminByUserId != null && adminByUserId.status == 1) {
            return true
        }
        return false
    }

    fun deleteAdmin(admin: Admin): Admin? {
        if (admin.isOwner != 1) {
            admin.status = 2  // 2 : deleted
            adminRepo.updateAdmin(admin)
        }
        return adminRepo.getAdminByWalletAndProjectId(admin.wallet, admin.projectId)
    }

    fun initCreatorAdmin() {
        val projects = projectRepo.getAllProjects()
        for (project in projects) {
            val creatorId = project.creatorId
            val user = userRepo.findUserById(creatorId)!!
            val admin = Admin(
                userId = creatorId,
                wallet = user.wallet,
                projectId = project.projectId,
                creatorId = creatorId,
                isOwner = 1,
                status = 1
            )
            addAdmin(admin)
        }
    }

    fun createAdminNonce(
        userId: Long,
        projectId: Long,
        address: String,
        op: String
    ): String {
        val user = userRepo.findUserById(userId)!!
        val evmAddress = user.wallet
        val tonAddress = user.ton.tonWallet
        val suiAddress = user.suiAddress
        val adminWallet = if (tonAddress != "") {
            tonAddress ?: ""
        } else if (suiAddress != "") {
            suiAddress
        } else {
            evmAddress
        }
        val nonce = "$adminWallet is $op admin $address of $projectId"
        adminNonceRepo.addAdminNonce(userId, projectId, address, op, nonce)
        return nonce
    }

    /**
     * Verifies a signature based on the chain type
     *
     * @param userId The user ID
     * @param projectId The project ID
     * @param address The signer's address
     * @param targetAddress The target address
     * @param op The operation
     * @param signMessage The signature message
     * @param chain The blockchain chain type (eth, ton, sui, etc.)
     * @return true if the signature is valid, false otherwise
     */
    fun verifySign(
        userId: Long,
        projectId: Long,
        address: String,
        targetAddress: String,
        op: String,
        signMessage: String,
        chain: String? = null
    ): Boolean {
        val (id, nonce) = adminNonceRepo.getLatestNonce(userId, projectId, targetAddress, op) ?: return false

        val isValid = when (chain?.lowercase()) {
            "sui" -> {
                logger.info { "Verifying SUI signature for user $userId" }
                val user = userRepo.findUserById(userId)
                if (user?.suiAddress.isNullOrEmpty()) {
                    logger.warn { "User $userId has no SUI address registered" }
                    false
                } else {
//                    address == user.suiAddress
                    suiService.verifySuiSignature(address, "", signMessage, nonce)
                }
            }
            "ton" -> {
                logger.info { "Verifying TON signature for user $userId" }
                val user = userRepo.findUserById(userId)
                val tonWallet = user?.ton?.tonWallet
                if (tonWallet.isNullOrEmpty()) {
                    logger.warn { "User $userId has no TON wallet registered" }
                    false
                } else {
                    address == tonWallet
                }
            }
            else -> {
                logger.info { "Verifying EVM signature for user $userId" }
                val recovered = Signs.getAddressUsedToSignHashedMessage(signMessage, nonce)
                recovered.equals(address, ignoreCase = true)
            }
        }

        if (isValid) {
            adminNonceRepo.updateSign(id, nonce)
            return true
        }

        return false
    }
}