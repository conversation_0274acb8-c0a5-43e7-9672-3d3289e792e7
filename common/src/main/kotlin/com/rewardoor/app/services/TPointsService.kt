package com.rewardoor.app.services

import com.rewardoor.app.dao.TPointsConsumeRepository
import com.rewardoor.app.dao.TPointsVanguardRepository
import com.rewardoor.app.dao.TgLuckyDrawTimesRepository
import com.rewardoor.app.dao.UserRepository
import com.rewardoor.enums.TPointsBuyCardsLevel
import com.rewardoor.enums.TPointsIncreaseGrowthLevel
import com.rewardoor.enums.TPointsIncreaseLimitLevel
import com.rewardoor.model.TPointsConsume
import com.rewardoor.model.TPointsVanguard
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.time.Instant
import java.time.LocalDate
import java.time.ZoneId

@Service
@Transactional
class TPointsService(
    val tPointsConsumeRepo: TPointsConsumeRepository,
    val luckyDrawService: LuckyDrawService,
    val luckyDrawTimesRepo: TgLuckyDrawTimesRepository,
    val tPointsVanguardRepo: TPointsVanguardRepository,
    val tgInvitationService: TgInvitationService,
    val tgUserService: TelegramService,
    val userRepo: UserRepository
) {

    private val limitedBuyCardNumPerDay = 10

    fun buyCards(userId: Long, consumeType: Int, level: Int): Int {
        val tPointsConsume = TPointsConsume(
            userId = userId,
            consumeType = consumeType,
            level = level,
            tPointsNum = getLevelBuyCardPoints(consumeType, level),
            createTime = Instant.now(),
            updateTime = Instant.now()
        )
        val insertCount = tPointsConsumeRepo.addTPointsConsume(tPointsConsume)
        if (insertCount == 1 && consumeType == 3) {
            val addLuckyDrawTimes = getLevelBuyCardNum(level)
            val tgUserId = tgUserService.getUserInfo(userId)?.tgId!!
            tgInvitationService.addInitialCountOrIncreaseOfInvite(userId, tgUserId, addLuckyDrawTimes)

        }
        return insertCount
    }

    fun isHasEnoughTPointsToBuyCards(userId: Long, consumeType: Int, level: Int): Boolean {
        val tPointsNum = getUserTPointsNum(userId)
        val tPointsCost = getLevelBuyCardPoints(consumeType, level)
        return tPointsNum >= tPointsCost
    }

    //Each user is limited to purchasing 10 cards per day.
    fun isAbleToBuyCards(userId: Long, level: Int): Boolean {
        val todayBuyCardsRecords = getTodayBuyCardsRecords(userId)
        var todayBuyCardsNum = 0

        for (record in todayBuyCardsRecords) {
            val cardNum = getLevelBuyCardNum(record.level)
            todayBuyCardsNum += cardNum
        }

        return todayBuyCardsNum + getLevelBuyCardNum(level) <= limitedBuyCardNumPerDay
    }

    fun getLevelBuyCardNum(level: Int): Int {
        val levelCardNumMap = mapOf(
            1 to TPointsBuyCardsLevel.LEVEL_1.cardCnt,
            2 to TPointsBuyCardsLevel.LEVEL_2.cardCnt,
            3 to TPointsBuyCardsLevel.LEVEL_3.cardCnt
        )
        return levelCardNumMap[level] ?: 0
    }

    fun getLevelBuyCardPoints(consumeType: Int, level: Int): Int {
        when (consumeType) {
            1 -> { //increase card upper limit
                return TPointsIncreaseLimitLevel.getByCode(level).pointsNum
            }

            2 -> { // increase card growth speed
                return TPointsIncreaseGrowthLevel.getByCode(level).pointsNum
            }

            3 -> { //buy cards
                val levelCardNumMap = mapOf(
                    1 to TPointsBuyCardsLevel.LEVEL_1.pointsNum,
                    2 to TPointsBuyCardsLevel.LEVEL_2.pointsNum,
                    3 to TPointsBuyCardsLevel.LEVEL_3.pointsNum
                )

                return levelCardNumMap[level] ?: 0
            }

            else -> return 0
        }
    }

    // when consumeType = 1/2 , get level
    fun getLimitOrGrowthMaxLevel(userId: Long, consumeType: Int): Int {
        val tPointsByCardList =
            tPointsConsumeRepo.getTPointsConsumeById(userId)?.filter { it.consumeType == consumeType }
        if (tPointsByCardList.isNullOrEmpty()) {
            return 0
        }
        val maxLevel = tPointsByCardList.maxByOrNull { it.level }?.level
        return maxLevel ?: 0
    }

    fun getTodayBuyCardsRecords(userId: Long): List<TPointsConsume> {
        val tPointsByCardList = tPointsConsumeRepo.getTPointsConsumeById(userId)?.filter { it.consumeType == 3 }
        if (!tPointsByCardList.isNullOrEmpty()) {
            return tPointsByCardList.filter {
                it.createTime?.atZone(ZoneId.of("UTC"))?.toLocalDate() == LocalDate.now()
            }
        }
        return emptyList()
    }

    data class UserPointsInfo(
        val userId: Long,
        val tPointsNum: Int,
        val tonAddress: String,
        val tgName: String
    )

    fun getTopLimitTPointsUser(limit: Int): List<UserPointsInfo> {
        val tPointsUserIds = luckyDrawService.getAllUserIds().take(limit + 100)
        val resultList = mutableListOf<UserPointsInfo>()
        for (userId in tPointsUserIds) {
            val tPointsNum = getUserTPointsNum(userId)
            val user = userRepo.findUserById(userId)!!
            if (user.ton.binded && user.tgName != "") {
                resultList.add(UserPointsInfo(userId, tPointsNum, user.ton.tonWallet ?: "", user.tgName))
            }
        }
        val topLimit = resultList
            .sortedByDescending { it.tPointsNum } // Sort by the tPointsNum
            .take(limit) // Take the top 'limit' results
        return topLimit
    }

    fun getUserTPointsNum(userId: Long): Int {
        val tPointsGetNum = luckyDrawService.getUserTPoints(userId) ?: 0
        val tPointsConsumeList = tPointsConsumeRepo.getTPointsConsumeById(userId)
        var tPointsConsumeNum = 0
        if (tPointsConsumeList != null) {
            tPointsConsumeNum = tPointsConsumeList.sumOf { it.tPointsNum }
        }
        val tPointsNum = tPointsGetNum - tPointsConsumeNum
        return if (tPointsNum > 0) tPointsNum else 0
    }

    fun createUserVanguard(tPointsVanguard: TPointsVanguard): Int {
        return tPointsVanguardRepo.addTPointsVanguard(tPointsVanguard)
    }

    fun getUserVanguardById(userId: Long): TPointsVanguard? {
        return tPointsVanguardRepo.getTPointsVanguardById(userId)
    }

    fun updateUserVanguard(tPointsVanguard: TPointsVanguard): Int {
        return tPointsVanguardRepo.updateTPointsVanguard(tPointsVanguard)
    }

}