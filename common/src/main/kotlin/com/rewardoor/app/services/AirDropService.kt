package com.rewardoor.app.services

import com.rewardoor.IdGenerator
import com.rewardoor.app.dao.AirDropRepository
import com.rewardoor.app.dao.AirDropUserInfoRepository
import com.rewardoor.model.AirDrop
import com.rewardoor.model.UserAirDropInfo
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
class AirDropService(
    val idGenerator: IdGenerator,
    val airDropRepo: AirDropRepository,
    val airDropUserInfoRepo: AirDropUserInfoRepository
) {
    fun getAirDrop(airDropId: Long): AirDrop? {
        val airDropInfo = airDropRepo.getAirDropById(airDropId)
        return airDropInfo
    }

    fun updateAirDrop(airDrop: AirDrop): AirDrop? {
        val airDropId = airDrop.airDropId
        val oldAirDrOP = getAirDrop(airDropId)
        if (oldAirDrOP == null) {
            return null
        }
        airDropRepo.updateAirDrop(airDrop, airDropId)
        return getAirDrop(airDropId)
    }

    fun getUserAirDropInfo(userId: Long, airDropId: Long): UserAirDropInfo? {
        val userAirDropInfo = airDropUserInfoRepo.getUserAirDropInfoByUserId(userId, airDropId)
        return userAirDropInfo
    }

    fun getUserAirDropInfoByAddress(address: String, airDropId: Long): UserAirDropInfo? {
        val userAirDropInfo = airDropUserInfoRepo.getUserAirDropInfoByAddress(address, airDropId)
        return userAirDropInfo
    }

    fun getUserAirDropInfoByHexAddress(hexAddress: String, airDropId: Long): UserAirDropInfo? {
        val userAirDropInfo = airDropUserInfoRepo.getUserAirDropInfoByHexAddress(hexAddress, airDropId)
        return userAirDropInfo
    }

    fun updateHexUserAirDropInfo(hexAddress: String, airDropId: Long, newUserId: Long, newAddress: String) {
        airDropUserInfoRepo.updateUserAirDropInfoByHex(hexAddress, airDropId, newUserId, newAddress)
    }

    fun updateUserAirDropInfo(userAirDropInfo: UserAirDropInfo): UserAirDropInfo? {
        val userId = userAirDropInfo.userId
        val address = userAirDropInfo.address
        val airDropId = userAirDropInfo.airDropId
        val oldAirDropInfo = getUserAirDropInfoByAddress(address, airDropId)
        if (oldAirDropInfo == null) {
            return null
        }
        println("now airDrop claimtype is ${userAirDropInfo.claimedType}, ${userAirDropInfo.address}, ${userAirDropInfo.userId}, ${userAirDropInfo.airDropId}")
        airDropUserInfoRepo.updateUserAirDropInfo(userAirDropInfo)
        return getUserAirDropInfoByAddress(address, airDropId)
    }

    fun createUserAirDropInfoBatch(userAirDropInfoList: List<UserAirDropInfo>, batchSize: Int = 500): Int {
        return airDropUserInfoRepo.createUserAirDropInfoBatch(userAirDropInfoList, batchSize)
    }

}