package com.rewardoor.app.services

import com.github.scribejava.core.model.OAuth2AccessToken
import com.github.scribejava.core.oauth.OAuth20Service
import com.rewardoor.app.dao.DiscordUserRepository
import com.rewardoor.app.dao.DiscordVoiceAttendanceRepository
import com.rewardoor.app.services.CredentialService
import com.rewardoor.app.utils.BoolResult
import com.rewardoor.app.utils.DcUtils
import com.rewardoor.model.UserDiscord
import com.rewardoor.model.DiscordVoiceChannel
import discord4j.common.ReactorResources
import discord4j.common.util.Snowflake
import discord4j.core.DiscordClient
import discord4j.core.event.domain.VoiceStateUpdateEvent
import discord4j.discordjson.json.PartialGuildData
import discord4j.discordjson.json.RoleData
import discord4j.discordjson.json.UserData
import discord4j.rest.service.UserService
import org.springframework.beans.factory.annotation.Value
import org.springframework.data.redis.core.RedisTemplate
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.net.URI
import java.time.Duration
import java.time.Instant
import java.util.concurrent.ConcurrentHashMap

@Service
@Transactional
class DiscordService(
    @Value("\${dc.app_id}") private val appId: String,
    @Value("\${dc.app_secret}") private val appSecret: String,
    @Value("\${dc.bot_token}") private val botToken: String,
    val dc20Service: OAuth20Service,
    val discordUserRepository: DiscordUserRepository,
    val discordUserVoiceRepository: DiscordVoiceAttendanceRepository,
    val credentialService: CredentialService,
    private val resource: ReactorResources,
    private val dcClient: DiscordClient,
    private val redisTemplate: RedisTemplate<Any, Any>

) {
    private val log = mu.KotlinLogging.logger {}

//    private val userVoiceSessionMap = ConcurrentHashMap<String, Instant>()

    fun addUserDcInfo(userId: Long, dcUser: UserData, token: OAuth2AccessToken) {
        val du = UserDiscord(
            userId,
            dcUser.id().asString(),
            dcUser.username(),
            dcUser.avatar().orElse(""),
            dcUser.globalName().orElse(""),
            token.accessToken,
            token.refreshToken,
            System.currentTimeMillis() + token.expiresIn * 1000,
            true
        )
        if (discordUserRepository.getUserDcInfo(userId) != null) {
            discordUserRepository.updateDcUser(du)
        } else {
            discordUserRepository.addDcUser(du)
        }
    }

    fun getDcUserCnt(): Long {
        return discordUserRepository.getDcUserCnt()
    }

    fun getUserDcInfo(userId: Long): UserDiscord? {
        return discordUserRepository.getUserDcInfo(userId)
    }

    fun updateDcUserId(userId: Long, newUserId: Long) {
        return discordUserRepository.updateDcUserId(userId, newUserId)
    }

    fun getRolesByInviteLink(link: String): List<RoleData> {
        val guild = getDcServerInfoFromInviteLink(link)
        return dcClient.getGuildById(Snowflake.of(guild!!.id())).roles.collectList().block().orEmpty()
    }

    fun getGuildByUrl(url: String): String {
        val segments = URI(url).path.split("/")
        val chIndex = segments.indexOf("channels")
        if (chIndex == -1) return ""
        return segments[chIndex + 1]
    }

    fun getDcTitleByDcId(dcId: String): String {
        return dcClient.getGuildById(Snowflake.of(dcId)).data.block()?.name().orEmpty()
    }

    fun getGuildMemberCountByUrl(userId: Long, url: String): Int {
        val guildId = getGuildByUrl(url)
        return getGuildMemberCount(userId, guildId)
    }

    fun getGuildMemberCount(userId: Long, guildId: String): Int {
        val dcUser = getUserDcInfo(userId) ?: return 0
        var token = dcUser.accessToken
        if (dcUser.tokenExpire < System.currentTimeMillis()) {
            token = refreshToken(dcUser)
        }
        try {
            val dcUserService = UserService(DcUtils.createDefaultRouter(token, resources = resource))
            val count = dcUserService.getCurrentUserGuilds(
                mapOf("before" to guildId.toLong() + 1, "after" to guildId.toLong() - 1, "with_counts" to true)
            )
                .collectList().block()?.firstOrNull { it.id().asString() == guildId }?.approximateMemberCount()
                ?: return 0
            return if (count.isAbsent) 0 else count.get()
        } catch (e: Exception) {
            log.error("get guild member count error", e)
            return 0
        }
    }

    fun isUserGuildAdminByLink(userId: Long, link: String): BoolResult {
        val guildId = getGuildByUrl(link)
        return isUserGuildAdmin(userId, guildId)
    }

    fun isUserGuildAdmin(userId: Long, guildId: String): BoolResult {
        val adminFlag = 8L
        val dcUser = getUserDcInfo(userId) ?: return BoolResult.Failure("Please connect to discord first")
        var token = dcUser.accessToken
        if (dcUser.tokenExpire < System.currentTimeMillis()) {
            token = refreshToken(dcUser)
        }
        try {
            val dcUserService = UserService(DcUtils.createDefaultRouter(token, resources = resource))
            dcUserService.getCurrentUserGuilds(
                mapOf("before" to guildId.toLong() + 1, "after" to guildId.toLong() - 1)
            )
                .collectList().block()?.forEach {
                    if (it.id().asString() == guildId && !it.permissions().isAbsent) {
                        if (it.permissions().get().and(adminFlag) == adminFlag)
                            return BoolResult.Success()
                    }
                }
        } catch (e: Exception) {
            log.error("verify user guild admin error", e)
            return BoolResult.Failure("Please invite the bot to your server and set it as an admin.")
        }
        return BoolResult.Failure("@${dcUser.username} is not the owner or admin for this server, and this action must be completed by a user who has that permission.")
    }

    fun verifyUserGuildMembership(dcUserId: String, guildId: String): Boolean {
        if (dcUserId.isEmpty()) return false
        try {
            return dcClient.getGuildById(Snowflake.of(guildId)).getMember(Snowflake.of(dcUserId))
                .block()?.user() != null
        } catch (e: Exception) {
            log.error("verify user guild membership error", e)
            return false
        }
    }

    fun verifyUserGuildRole(dcUserId: String, guildId: Long, roleId: Long): Boolean {
        if (dcUserId.isEmpty()) return false
        try {
            return dcClient.getGuildById(Snowflake.of(guildId)).getMember(Snowflake.of(dcUserId)).block()?.roles()
                ?.any { it.asLong() == roleId } ?: false
        } catch (e: Exception) {
            log.error("verify user guild role error", e)
            return false
        }
    }

    fun getDcServerInfoFromInviteLink(link: String): PartialGuildData? {
        val inviteCode = URI(link).path.split("/").last()
        try {
            val invite = dcClient.getInvite(inviteCode).block()
            return invite?.guild()?.get()
        } catch (e: Exception) {
            log.error("get dc invite info error", e)
            return null
        }
    }

    fun refreshToken(ud: UserDiscord): String {
        val token = dc20Service.refreshAccessToken(ud.refreshToken)
        ud.accessToken = token.accessToken
        ud.refreshToken = token.refreshToken
        ud.tokenExpire = System.currentTimeMillis() + token.expiresIn * 1000
        discordUserRepository.updateDcToken(ud)
        return token.accessToken
    }

    fun getInfoByUrl(url: String, roleId: String): Map<String, Any> {
        try {
            val info = getDcServerInfoFromInviteLink(url) ?: return mapOf(
                "link" to url,
                "code" to 4001,
                "message" to "Invalid discord link"
            )
            val roles = dcClient.getGuildById(Snowflake.of(info.id())).roles.collectList().block().orEmpty()
            val roleIds = roleId.split(",")
            val rn = roles.filter { it.id().asString() in roleIds }.joinToString(",") { it.name() }
            return mapOf(
                "data" to mapOf("link" to url, "serverName" to info.name(), "roleName" to rn),
                "code" to 200,
                "message" to "OK"
            )
        } catch (e: Exception) {
            log.error(e) { "Error getting discord server info from invite $url" }
            return mapOf("link" to url, "code" to 4001, "message" to "Error getting discord server info from $url")
        }
    }

    fun getUserInfoByDcId(asString: String): UserDiscord? {
        return discordUserRepository.getUserDcInfoByDcId(asString)
    }

    fun getChannelIdFromInviteLink(inviteLink: String): String {
        val inviteCode = URI(inviteLink).path.split("/").last()
        val key = "dc:invite:$inviteCode"
        val cached = redisTemplate.opsForValue().get(key)
        if (cached != null) {
            return cached.toString()
        }
        var cid: String
        try {
            val invite = dcClient.getInvite(inviteCode).block()
            cid = invite?.channel()?.id()?.asString() ?: ""
        } catch (e: Exception) {
            log.error("Failed to get channel ID from invite link", e)
            cid = ""
        }
        redisTemplate.opsForValue().set(key, cid)
        return cid
    }

    fun handleVoiceStateUpdate(event: VoiceStateUpdateEvent) {
        log.info("handleVoiceStateUpdate")

        val dcUserId = event.current.userId.asString()
        val guildId = event.current.guildId.asString()
        val channelId = event.current.channelId.orElse(null)?.asString()

        val oldChannelId = event.old.flatMap { it.channelId }.orElse(null)?.asString()

        val channelList = getCredentialChannelIdsWithCache()

        // User Enter
        if (channelId != null && (oldChannelId == null || oldChannelId != channelId) && channelId in channelList) {
            log.info("User Enter")
            log.info("User $dcUserId joined voice channel $channelId in guild $guildId")
//            userVoiceSessionMap["$dcUserId:$guildId:$channelId"] = Instant.now()
            discordUserVoiceRepository.insertAttendance(
                userId = 0,
                dcUserId = dcUserId,
                guildId = guildId,
                channelId = channelId,
                joinTime = Instant.now(),
                duration = 0
            )
        }

        // User Leave
        if (oldChannelId != null && (channelId == null || oldChannelId != channelId)) {
            val leaveTime = Instant.now()
            try {
                processVoiceLeaveEvent(dcUserId, guildId, oldChannelId, leaveTime, channelList)
            } catch (e: Exception) {
                log.error("leave voice channel fail", e)
            }
        }
    }

    private val credentialChannelIdsCache = ConcurrentHashMap<String, Pair<List<String>, Long>>()
    private val CACHE_TTL_MS = 60000L * 30 // 1分钟缓存

    fun getCredentialChannelIdsWithCache(): List<String> {
        val cacheKey = "credential_channel_ids"
        val cachedValue = credentialChannelIdsCache[cacheKey]

        // just return
        if (cachedValue != null && System.currentTimeMillis() - cachedValue.second < CACHE_TTL_MS) {
            return cachedValue.first
        }

        // invalid cache, fetch from service
        val channelList = try {
            val credentialList = credentialService.getCredentialListByLabelType(87)
            credentialList.mapNotNull { credential ->
                try {
                    getChannelIdFromInviteLink(credential.link)
                } catch (e: Exception) {
                    log.error("Failed to get channel ID from invite link: ${credential.link}", e)
                    null
                }
            }
        } catch (e: Exception) {
            log.error("Failed to get credential list", e)
            emptyList()
        }

        // cache update
        credentialChannelIdsCache[cacheKey] = Pair(channelList, System.currentTimeMillis())
        return channelList
    }

    fun getCredentialChannelIds(): List<String> {
        val credentialList = credentialService.getCredentialListByLabelType(87)
        val channelList = credentialList.map { credential ->
            getChannelIdFromInviteLink(credential.link)
        }
        return channelList
    }

    fun processVoiceLeaveEvent(dcUserId: String, guildId: String, channelId: String, leaveTime: Instant, channelList: List<String>) {
        val userId = getUserInfoByDcId(dcUserId)?.userId

        val activeRecord = discordUserVoiceRepository.getActiveAttendance(dcUserId, guildId, channelId)

        log.info("dcUserId $dcUserId - userId $userId joinTime [$activeRecord?.joinTime] joinTime [$leaveTime]")

        if(activeRecord?.joinTime != null && channelId in channelList ) {
            discordUserVoiceRepository.updateAttendance(
                dcUserId = dcUserId,
                guildId = guildId,
                channelId = channelId,
                leaveTime = leaveTime,
                duration = Duration.between(activeRecord?.joinTime, leaveTime).seconds
            )
        }
    }

    fun verifyUserVoiceAttendance(
        userId: Long,
        dcUserId: String,
        link: String,
        guildId: String,
        minAttendanceTime: Long,
        startTime: Long,
        endTime: Long,
    ): Boolean {
        val channelId = getChannelIdFromInviteLink(link)

        return discordUserVoiceRepository.getUserAttendanceDuration(
            minAttendanceTime * 60,
            userId,
            dcUserId,
            guildId,
            channelId,
            startTime,
            endTime
        )
    }
}
