package com.rewardoor.app.services

import com.rewardoor.model.TgUserPushSetting
import org.springframework.stereotype.Service
import com.rewardoor.IdGenerator
import com.rewardoor.app.dao.*
import com.rewardoor.model.PushResultInfo
import com.rewardoor.model.TelegramUserPush
import org.springframework.transaction.annotation.Transactional
import java.time.Instant

@Service
@Transactional
class TgUserPushService(
    val idGenerator: IdGenerator, val tgUserPushSetRepo: TgUserPushSetRepository,
    val sbtWhiteListRepo: SBTWhiteListRepository,
    private val userRepository: UserRepository,
    private val userWiseScoreRepository: UserWiseScoreRepository,
    private val telegramUserPushRepository: TelegramUserPushRepository,
    private val telegramUserRepository: TelegramUserRepository
) {

    fun createPush(tgUserPushSetting: TgUserPushSetting): TgUserPushSetting {
        val pushId = idGenerator.getNewId()
        tgUserPushSetting.tgPushId = pushId
        tgUserPushSetRepo.insertTgUserPushSetting(tgUserPushSetting)
        val pushBasicInfoList = tgUserPushSetting.pushBasicInfoList
        if (pushBasicInfoList != null) {
            tgUserPushSetRepo.insertPushBasicInfos(pushBasicInfoList, pushId)
        }
        return tgUserPushSetting
    }

    fun getPushSettingById(tgPushId: Long): TgUserPushSetting? {
        val pushSetting = tgUserPushSetRepo.getTgUserPushSettingById(tgPushId)
        val basicInfo = tgUserPushSetRepo.getPushBasicInfosByTgPushId(tgPushId)
        if (pushSetting != null) {
            pushSetting.pushBasicInfoList = basicInfo
        }
        if (pushSetting?.hasPushed == 1) {
            pushSetting.pushResultInfo = getPushResultInfo(tgPushId)
        }
        return pushSetting
    }

    fun getPushSettingList(): List<TgUserPushSetting>? {
        val settingList = tgUserPushSetRepo.getTgUserPushSettingList()
        if (settingList != null) {
            for (setting in settingList) {
                val tgPushId = setting.tgPushId
                val basicInfo = tgUserPushSetRepo.getPushBasicInfosByTgPushId(tgPushId)
                setting.pushBasicInfoList = basicInfo
                if (setting.hasPushed == 1) {
                    setting.pushResultInfo = getPushResultInfo(tgPushId)
                }
            }
        }
        return settingList
    }

    fun hasInited(tgPushId: Long): Boolean {
        val hasInited = telegramUserPushRepository.getTgUserPushByPushId(tgPushId) != null
        return hasInited
    }

    fun initPushUsers(tgPushId: Long): Int {
        if (hasInited(tgPushId)) {
            return -1
        }
        val pushSetting = getPushSettingById(tgPushId)
        val userPushList = mutableListOf<TelegramUserPush>()
        if (pushSetting != null) {
            when (pushSetting.userType) {
                0 -> {
                    // todo : all user的话不入库，否则数据库压力太大
                }
                // sbt holder
                1 -> {
                    val activityId = pushSetting.activityId
                    if (activityId != null) {
                        val userIdList = sbtWhiteListRepo.getUserIdListByActivityId(activityId)
                        for (userId in userIdList) {
                            val tgId = userRepository.findTgIdByUserId(userId)
                            if (tgId != null) {
                                val tgUserPush = TelegramUserPush(
                                    tgPushSettingId = tgPushId,
                                    tgId = tgId,
                                    userId = userId,
                                    pushType = pushSetting.pushType,
                                    participateType = 0
                                )
                                userPushList.add(tgUserPush)
                            }
                        }
                    }
                }

                // top wise score user
                2 -> {
                    val topWiseScoreType = pushSetting.topWiseScoreType
                    var topLimit = 0
                    when (topWiseScoreType) {
                        // top 1000,5000,10000,50000,100000
                        1 -> topLimit = 1000
                        2 -> topLimit = 5000
                        3 -> topLimit = 10000
                        4 -> topLimit = 50000
                        5 -> topLimit = 100000
                    }
                    val userIdList = userWiseScoreRepository.getTopScoreUser(topLimit)
                    for (userId in userIdList) {
                        val tgId = userRepository.findTgIdByUserId(userId)
                        if (tgId != null) {
                            val tgUserPush = TelegramUserPush(
                                tgPushSettingId = tgPushId,
                                tgId = tgId,
                                userId = userId,
                                pushType = pushSetting.pushType,
                                participateType = 0
                            )
                            userPushList.add(tgUserPush)
                        }
                    }
                }

                // wise score limit
                3 -> {
                    val wiseScoreLimit = pushSetting.wiseScoreLimit
                    val wiseScoreType = pushSetting.wiseScoreType
                    val hasGroups = pushSetting.hasGroups
                    val basicInfoList = pushSetting.pushBasicInfoList
                    val groupNum = basicInfoList?.size ?: 0
                    if (wiseScoreLimit != null && wiseScoreType != null) {
                        val userIdList =
                            userWiseScoreRepository.getUserIdsByScoreCondition(wiseScoreType, wiseScoreLimit)

                        // 如果需要分组 (hasGroups == 1 && groupNum > 0)
                        if (hasGroups == 1 && groupNum > 0) {
                            val shuffledUserIds = userIdList.shuffled()

                            // 计算每组的基础人数
                            val baseGroupSize = shuffledUserIds.size / groupNum
                            val remainder = shuffledUserIds.size % groupNum

                            // 初始化分组列表
                            val groupedUserIds = MutableList(groupNum) { mutableListOf<Long>() }

                            // 将基础人数分配到每组
                            for ((index, userId) in shuffledUserIds.withIndex()) {
                                groupedUserIds[index % groupNum].add(userId)
                            }

                            // 将余数用户分配到前几组
                            for (i in 0 until remainder) {
                                groupedUserIds[i].add(shuffledUserIds[baseGroupSize * groupNum + i])
                            }

                            // 遍历分组后的用户，并分配 groupId
                            for ((groupId, userGroup) in groupedUserIds.withIndex()) {
                                for (userId in userGroup) {
                                    val tgId = userRepository.findTgIdByUserId(userId)
                                    if (tgId != null) {
                                        val tgUserPush = TelegramUserPush(
                                            tgPushSettingId = tgPushId,
                                            tgId = tgId,
                                            userId = userId,
                                            pushType = pushSetting.pushType,
                                            participateType = 0,
                                            groupId = groupId // 分配 groupId
                                        )
                                        userPushList.add(tgUserPush)
                                    }
                                }
                            }
                        } else {
                            // 如果不需要分组，则按原逻辑处理
                            for (userId in userIdList) {
                                val tgId = userRepository.findTgIdByUserId(userId)
                                if (tgId != null) {
                                    val tgUserPush = TelegramUserPush(
                                        tgPushSettingId = tgPushId,
                                        tgId = tgId,
                                        userId = userId,
                                        pushType = pushSetting.pushType,
                                        participateType = 0,
                                        groupId = 0
                                    )
                                    userPushList.add(tgUserPush)
                                }
                            }
                        }
                    }
                }

                // white list
                4 -> {
                    val whiteList = pushSetting.whiteList
                    if (!whiteList.isNullOrEmpty()) {
                        for (tgId in whiteList) {
                            val user = telegramUserRepository.getTgUserByTgId(tgId)
                            if (user != null) {
                                val tgUserPush = TelegramUserPush(
                                    tgPushSettingId = tgPushId,
                                    tgId = tgId,
                                    userId = user.userId,
                                    pushType = pushSetting.pushType,
                                    participateType = 0,
                                    groupId = 0
                                )
                                userPushList.add(tgUserPush)
                            }
                        }
                    }
                }
            }
            if (userPushList.size > 0) {
                println("初始化用户条数：${userPushList.size}")
                val totalInsertCnt = telegramUserPushRepository.addTgUserPushBatch(userPushList, 1000)
                println("初始化完成，插入条数：${totalInsertCnt}")
                return totalInsertCnt
            }
        }
        return 0
    }

    fun getPushResultInfo(tgPushId: Long): PushResultInfo {
        val tgPush = tgUserPushSetRepo.getTgUserPushSettingById(tgPushId)!!
        val pushBasicInfo = tgUserPushSetRepo.getPushBasicInfosByTgPushId(tgPushId)
        val tgPushResultList = tgUserPushSetRepo.getTgUserPushResultById(tgPushId)
        val hasPushed = tgPush.hasPushed
        var pushStartDate: Instant? = null
        if (!pushBasicInfo.isNullOrEmpty()) {
            pushStartDate = pushBasicInfo[0].pushDate
        }
        var succeedPushCount = 0L
        var failedPushCount = 0L
        if (tgPushResultList != null) {
            succeedPushCount = tgPushResultList.sumOf { it.succeedPushCount }
            failedPushCount = tgPushResultList.sumOf { it.failedPushCount }
        }
        if (tgPush.userType == 0) {
            val tgUserTotalCnt = telegramUserRepository.getTgUserCnt()
            return PushResultInfo(
                tgPushId = tgPushId,
                totalPushCount = tgUserTotalCnt,
                succeedPushCount = succeedPushCount,
                failedPushCount = failedPushCount,
                pushStartDate = pushStartDate,
                hasPushed = hasPushed
            )
        }
        val totalPushCount = telegramUserPushRepository.getTgUserPushCntByPushId(tgPushId)
//        val succeedPushCount = telegramUserPushRepository.getTgUserPushCntByPushIdAndParticipateType(tgPushId, 1)
//        val failedPushCount = telegramUserPushRepository.getTgUserPushCntByPushIdAndParticipateType(tgPushId, -1)
        return PushResultInfo(
            tgPushId = tgPushId,
            totalPushCount = totalPushCount,
            succeedPushCount = succeedPushCount,
            failedPushCount = failedPushCount,
            pushStartDate = pushStartDate,
            hasPushed = hasPushed
        )
    }

}