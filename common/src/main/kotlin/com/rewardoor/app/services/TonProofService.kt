package com.rewardoor.app.services

import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.google.common.hash.Hashing
import com.google.crypto.tink.signature.Ed25519PublicKey
import com.google.crypto.tink.subtle.Ed25519Verify
import com.google.crypto.tink.util.Bytes
import com.rewardoor.app.dto.TonProofItem
import com.rewardoor.app.dao.*
import com.rewardoor.model.LuckyDrawResult
import com.rewardoor.model.Participant
import com.rewardoor.model.User
import org.apache.commons.codec.binary.Hex
import org.springframework.data.redis.core.StringRedisTemplate
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import org.springframework.web.reactive.function.client.WebClient
import org.ton.java.address.Address
import org.ton.java.cell.CellBuilder
import org.ton.java.cell.CellSlice
import org.ton.java.smartcontract.wallet.Contract
import org.ton.java.tlb.types.StateInit
import org.ton.java.tonconnect.WalletAccount
import org.ton.java.tonlib.Tonlib
import org.ton.java.utils.Utils
import reactor.core.publisher.Mono
import java.nio.ByteBuffer
import java.nio.ByteOrder
import java.time.Instant
import java.util.*


@Service
@Transactional
class TonProofService(private val redisTemplate: StringRedisTemplate,
                      private val idGenerator: DbIdGenerator,
                      private val userRepo: UserRepository,
                      private val participantRepo: ParticipantRepository,
                      private val userTonRepository: UserTonRepository,
                      private val userLuckyDrawRepo: UserLuckyDrawRepository
) {
    private val logger = mu.KotlinLogging.logger {}
    private val webClient = WebClient.builder().build()
    private val tonConsoleBearer = "AFXEZ3ACHWCQPGIAAAACDBX3ACNADH4DWOIU3DOWOQMPYF7M5QIP5ZZFE5WI227KRIKIQ2Y"
    private val mapper = jacksonObjectMapper()
    val tonProofPrefix = "ton-proof-item-v2/"
    val tonConnectPrefix = "ton-connect"

    fun generatePayload(): String {
        val value = UUID.randomUUID().toString().replace("-", "")
        val expire = Instant.now().minus(1, java.time.temporal.ChronoUnit.DAYS).epochSecond
        val key = "ton_proof_payload:$value:$expire"
        return Base64.getUrlEncoder().encodeToString(key.toByteArray())
    }

    fun registerTgUser(wallet: String, publicKey: String): Long {
        val current = userTonRepository.findTonWallet(wallet)
        if (current != null) {
            return current.userId
        }

        val newId = idGenerator.getNewId()
        userRepo.addUser(
                User(
                        newId,
                        User.AVATAR_BASE + newId,
                        "",
                        ""
                ), newId
        )

        userTonRepository.bindTonWallet(newId, wallet, publicKey)
        val participant = Participant(
                userId = newId,
                campaignId = 0L,
                wallet = "",
                nfts = emptyList(),
                points = emptyList(),
                pointNum = 0L,
                isJoin = false,
                isVisit = false,
                credentials = emptyList(),
                verifiedCredentials = emptyList(),
                participantDate = Instant.now()
        )
        participantRepo.createParticipant(participant)
        val luckyDrawResult = LuckyDrawResult(
            userId = newId,
            fissionLevel = -1,
            tPointsNum = 5000,
            isEligibleToGenerateWiseScore = 0,
            isEligibleToGenerateSBT = 0,
            createTime = Instant.now(),
            updateTime = Instant.now()
        )
        userLuckyDrawRepo.createUserLuckyDraw(luckyDrawResult)
        return newId
    }

    private fun concat(a: ByteArray, b: ByteArray): ByteArray {
        val result = ByteArray(a.size + b.size)
        System.arraycopy(a, 0, result, 0, a.size)
        System.arraycopy(b, 0, result, a.size, b.size)
        return result
    }

    fun verifyProof(address: String, publicKey: String, item: TonProofItem, network: Int): Boolean {
        val addr = address.split(":")
        val chain = addr[0].toInt()
        val frAddress = addr[1]
        val tvmAddress = Address.of(address)

        val stateInit = StateInit.deserialize(CellSlice.beginParse(CellBuilder.beginCell().fromBocBase64(item.walletInit).endCell()))
        val calcState = StateInit.builder().code(stateInit.code).data(stateInit.data).build()
        val publicKeyRemote = getPublicKey(tvmAddress, network)
        val wantedKeys = if (publicKeyRemote == null) {
            logger.warn { "remote public key is null for address: $address" }
            getPublicKeyFromStateInit(calcState)
        } else {
            listOf(publicKeyRemote)
        }

        if (!wantedKeys.contains(publicKey.lowercase())) {
            logger.warn { "public key mismatch: wanted ${wantedKeys.joinToString(",")}, got $publicKey" }
            return false
        }

        if (calcState.address.toRaw() != tvmAddress.toRaw()) {
            logger.warn { "address mismatch: ${stateInit.address.toHex()} != ${tvmAddress.toHex()}" }
            return false
        }

        val wcBuffer: ByteBuffer = ByteBuffer.allocate(4)
        wcBuffer.order(ByteOrder.BIG_ENDIAN)
        wcBuffer.putInt(chain)

        val tsBuffer: ByteBuffer = ByteBuffer.allocate(8)
        tsBuffer.order(ByteOrder.LITTLE_ENDIAN)
        tsBuffer.putLong(item.proof.timestamp)

        val dlBuffer: ByteBuffer = ByteBuffer.allocate(4)
        dlBuffer.order(ByteOrder.LITTLE_ENDIAN)
        dlBuffer.putInt(item.proof.domain.lengthBytes) // Assuming Domain is a plain string for simplicity

        var m: ByteArray = tonProofPrefix.toByteArray()
        m = concat(m, wcBuffer.array())
        m = concat(m, Hex.decodeHex(frAddress)) // Assuming Address is a plain string for simplicity
        m = concat(m, dlBuffer.array())
        m = concat(m, item.proof.domain.value.toByteArray())
        m = concat(m, tsBuffer.array())
        m = concat(m, item.proof.payload.toByteArray()) // Assuming Payload is a plain string for simplicity

        val messageHash: ByteArray = Hashing.sha256().hashBytes(m).asBytes()

        var fullMes: ByteArray = byteArrayOf(0xff.toByte(), 0xff.toByte())
        fullMes = concat(fullMes, tonConnectPrefix.toByteArray())
        fullMes = concat(fullMes, messageHash)

        val resultHash: ByteArray = Hashing.sha256().hashBytes(fullMes).asBytes()

        val pkBytes = Hex.decodeHex(publicKey)
        try {
            val pk = Ed25519PublicKey.create(Bytes.copyFrom(pkBytes))
            val ed25519Verifier = Ed25519Verify.create(pk)
            ed25519Verifier.verify(Base64.getDecoder().decode(item.proof.signature), resultHash)
            return true
        } catch (e: Exception) {
            return false
        }
    }

    fun getPublicKey(address: Address, network: Int): String? {
        val accountId = address.toRaw()
        val uri = if (network == -3) { "https://testnet.tonapi.io/v2/accounts/$accountId/publickey" }
        else { "https://tonapi.io/v2/accounts/$accountId/publickey" }
        val response = webClient
            .get()
            .uri(uri)
            .header("Authorization", "Bearer $tonConsoleBearer")
            .retrieve()
            .bodyToMono(String::class.java)
            .onErrorResume { Mono.empty() }
            .block() ?: return null
        return mapper.readTree(response).get("public_key")?.asText()
    }

    fun getPublicKeyFromStateInit(stateInit: StateInit): List<String> {
        val keys = mutableListOf<String>()
        try {
            val v5Bytes = CellSlice.beginParse(stateInit.data).skipBits(1 + 32 + 32).loadBytes(256)
            keys.add(Utils.bytesToHex(v5Bytes))
        } catch (_: Exception) {}
        try { // v2,v3,v4
            val v4Bytes = CellSlice.beginParse(stateInit.data).skipBits(32 + 32).loadBytes(256)
            keys.add(Utils.bytesToHex(v4Bytes))
        } catch (_: Exception) {}
        try {
            val v1Bytes = CellSlice.beginParse(stateInit.data).skipBits(32).loadBytes(256)
            keys.add(Utils.bytesToHex(v1Bytes))
        } catch (_: Exception) {}
        return keys.toList()
    }
}