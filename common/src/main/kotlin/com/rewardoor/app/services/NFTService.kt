package com.rewardoor.app.services

import com.rewardoor.IdGenerator
import com.rewardoor.app.dao.NFTRepository
import com.rewardoor.model.NFT
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
@Transactional
class NFTService(
    val nftRepository: NFTRepository,
    val idGenerator: IdGenerator
) {

    fun createNFT(nft: NFT): NFT {
        nftRepository.createNFT(nft)
        return nftRepository.getNFTById(nft.nftId)!!
    }

    fun updateContract(nftId: Long, contract: String): Int {
        return nftRepository.updateContract(nftId, contract)
    }

    fun getNFTById(nftId: Long): NFT? {
        return nftRepository.getNFTById(nftId)
    }

    fun getNFTByPairId(nftId: Long, groupId: Long): NFT? {
        return nftRepository.getNFTByPairId(nftId, groupId)
    }

    fun getNFTByProjectId(projectId: Long): List<NFT> {
        return nftRepository.getNFTByProjectId(projectId)
    }

    fun getNFTByCreatorId(creatorId: Long): List<NFT> {
        return nftRepository.getNFTByCreatorId(creatorId)
    }
}