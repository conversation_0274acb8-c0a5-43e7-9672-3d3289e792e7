package com.rewardoor.app.services

import com.rewardoor.app.dao.ProjectApplyRepository
import com.rewardoor.model.ProjectApply
import org.springframework.data.redis.core.StringRedisTemplate
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
class ProjectApplyService(
    private val redisTemplate: StringRedisTemplate,
    private val projectApplyRepo: ProjectApplyRepository
) {
    @Transactional
    fun saveApply(apply: ProjectApply) {
        projectApplyRepo.addProjectApply(apply)
    }

    @Transactional
    fun addPrivilege(userId: Long) {
        projectApplyRepo.addUserPrivilege(userId)
    }

    @Transactional
    fun getUserPrivilege(userId: Long): Boolean {
        return projectApplyRepo.getUserPrivilege(userId)
    }

    fun verifyInviteCode(userId: Long, code: String): Boolean {
        val key = "project:invite:$userId"
        val v = redisTemplate.opsForValue().get(key) ?: return false
        return code == v
    }
}