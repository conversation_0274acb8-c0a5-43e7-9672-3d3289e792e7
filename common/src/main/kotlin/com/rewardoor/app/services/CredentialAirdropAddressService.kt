package com.rewardoor.app.services

import com.rewardoor.app.dao.CredentialAirdropAddressRepository
import com.rewardoor.model.CredentialAirdropAddress
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
@Transactional
class CredentialAirdropAddressService(
    val credentialAirdropAddressRepo: CredentialAirdropAddressRepository
) {
    fun addCredentialAir(credentialAirdropAdr: CredentialAirdropAddress): CredentialAirdropAddress? {
        credentialAirdropAddressRepo.addCredentialAddressData(credentialAirdropAdr)
        return getCredentialAddress(credentialAirdropAdr.userId, credentialAirdropAdr.credentialId)
    }

    fun getCredentialAddress(userId: Long, credentialId: Long) =
        credentialAirdropAddressRepo.getCredentialAirDropAddress(userId, credentialId)

    fun checkVerifyStatus(userId: Long, credentialId: Long): Boolean {
        val credentialAddress = getCredentialAddress(userId, credentialId) ?: return false
        return credentialAddress.verified
    }

    fun isEthereumAddressValid(address: String): Boolean {
        val regex = "^0x[a-fA-F0-9]{40}\$".toRegex()
        return regex.matches(address)
    }
}