package com.rewardoor.app.services

import com.rewardoor.app.dao.*
import com.rewardoor.model.*
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
@Transactional
class CompanyService(
    val companyRepository: CompanyRepository,
    val layerOneRepo: LayerOneRepository,
    val projectService: ProjectService,
    val campaignRepo: CampaignRepository,
    val participantRepo: ParticipantRepository,
    val companyLayerOneRelationRepo: CompanyLayerOneRelationRepository,
    val pointRepo: PointRepository,
    val userRepo: UserRepository,
    val credentialGroupService: CredentialGroupService,
    val campaignService: CampaignService,
    private val userService: UserService
) {

    fun getCompanyWithProjects(companyId: Long, layerOneId: Long? = null): CompanyWithProjects? {
        val companyInfo = getCompanyInfoById(companyId)
        val layerOneRelations = companyLayerOneRelationRepo.getLayerOneListById(companyInfo?.companyId ?: 0)
        val layerOneList = layerOneRelations.map { relation ->
            val layerInfo = getLayerInfo(relation.layerOneId)
            layerInfo.let { info ->
                LayerOne(
                    layerOneId = relation.layerOneId,
                    name = info.name,
                    icon = info.icon,
                    url = info.url,
                    status = relation.status
                )
            }
        }
        val projects = projectService.getProjectsForCompany(companyInfo?.companyId ?: 0, layerOneId)
        if (companyInfo != null) {
            return CompanyWithProjects(
                company = companyInfo,
                layerOneList = layerOneList,
                projects = projects
            )
        }
        return null
    }

    fun getCompanyInfoById(companyId: Long): Company? {
        val configMap = companyRepository.getCompanyConfig(companyId)
        val company = companyRepository.getInfoById(companyId)

        company.let {
            it?.homePoster = configMap["home_poster"] ?: ""
            it?.homePosterLink = configMap["home_poster_link"]
            it?.pointBgImage = configMap["point_bg_image"]
        }
        return company
    }

    fun getLayerInfo(layerOneId: Long): LayerOne {
        return layerOneRepo.getLayerOneInfoById(layerOneId)
    }

    fun getCompanyLeaderboard(companyId: Long): CompanyLeaderboard? {
        val minPoints = 5000
        val companyInfo = getCompanyInfoById(companyId) ?: return null

        val projects = projectService.getProjectsForCompany(companyId)
        val allUserPoints = mutableListOf<UserProject>()

        for (project in projects) {
            val asset = getPointsByProjectId(project.projectId)
            allUserPoints.addAll(asset.userPoints)
        }

        val top500Users = allUserPoints
            .groupBy { it.userId }
            .map { (userId, userProjects) ->
                userId to userProjects.sumOf { it.pointNum }
            }
            .filter { (_, totalPoints) -> totalPoints >= minPoints }
            .sortedByDescending { (_, totalPoints) -> totalPoints }
            .take(500)

        if (top500Users.isEmpty()) {
            return CompanyLeaderboard(
                companyName = companyInfo.companyName,
                companyId = companyInfo.companyId,
                leaderboard = emptyList()
            )
        }

        // limit 500
        val userMap = userRepo.findUsersByIds(top500Users.map { it.first })
            .associateBy { it.userId }

        var currentRank = 1
        val userCompanyPoints = top500Users
            .mapNotNull { (userId, totalPoints) ->
                val user = userMap[userId] ?: return@mapNotNull null

                val (address, addressType) = when {
                    user.ton.tonWallet != null -> user.ton.tonWallet!! to 1
                    user.evm.evmWallet != null -> user.evm.evmWallet!! to 0
                    user.tgName.isNotEmpty() -> user.tgName to 2
                    user.twitterName.isNotEmpty() -> user.twitterName to 3
                    else -> "" to -1
                }

                if (address.isEmpty()) return@mapNotNull null

                UserCompany(
                    rank = currentRank++,
                    userId = userId,
                    address = address,
                    addressType = addressType,
                    projectId = allUserPoints.find { it.userId == userId }?.projectId ?: 0,
                    pointNum = totalPoints
                )
            }

        return CompanyLeaderboard(
            companyName = companyInfo.companyName,
            companyId = companyInfo.companyId,
            leaderboard = userCompanyPoints
        )
    }

    fun getPointsByProjectId(projectId: Long): Asset {
        val campaigns = campaignRepo.getCampaignByProjectId(projectId)
//        val campaignIds = campaigns.map { it.campaignId }

//        val allParticipants = participantRepo.getAllParticipantsByCampaignIds(campaignIds)

//        val allGroups = credentialGroupService.getGroupsByCampaignIds(campaignIds)
        val allPoints = pointRepo.getPointByProjectId(projectId)
//        val pointIdList = allPoints.map { it.pointId }.distinct()

        val projectPoints = mutableListOf<UserCampaign>()

        for (campaign in campaigns) {
            val campaignId = campaign.campaignId
//            val participants = allParticipants.filter { it.campaignId == campaignId }
            val userIdList = participantRepo.getAllParticipantIds(campaignId)
//            val userIdList = participants.distinctBy { it.userId }.map { it.userId }
            val userPoints = mutableListOf<UserCampaign>()
            val campaignPointIdList =
                allPoints.filter { credentialGroupService.getCredentialGroupById(it.groupId)!!.campaignId == campaignId }
                    .map { it.pointId }.distinct()

            // 批量查询所有用户的 claimed 数据
            val claimedPoints = participantRepo.findClaimedPointsByUserIds(userIdList, campaignPointIdList)
            val uniqueClaimedPoints = claimedPoints.distinctBy { it.userId to it.pointId }
            val userIdToPointsMap = uniqueClaimedPoints.groupBy { it.userId }

            for (userId in userIdList) {
                val userPointsList = userIdToPointsMap[userId] ?: emptyList()
                val totalPointNum = userPointsList.sumOf { point ->
                    val pointNum = allPoints.find { it.pointId == point.pointId }?.number ?: 0L
                    pointNum
                }
                val userCampaign = UserCampaign(
                    userId = userId,
                    address = "",
                    campaignId = campaignId,
                    pointNum = totalPointNum
                )
                userPoints.add(userCampaign)
            }
//
//
//            for (userId in userIdList) {
//                var totalPointNum = 0L
////                val groups = allGroups[campaignId] ?: listOf()
//                for (point in allPoints) {
//                    if (participantRepo.getUserIdPointsClaimed(userId, point.pointId) > 0) {
//                        val pointNum = point.number
//                        totalPointNum += pointNum
//                    }
//                }
//
//                val userCampaign = UserCampaign(
//                    userId = userId,
//                    address = "",
//                    campaignId = campaignId,
//                    pointNum = totalPointNum
//                )
//                userPoints.add(userCampaign)
//            }
            projectPoints.addAll(userPoints)
        }

        val userIdPointNumMap: Map<Long, Long> = projectPoints.groupBy { it.userId }
            .mapValues { entry -> entry.value.sumOf { it.pointNum.toInt() }.toLong() }

//        val userIdAddressMap: Map<Long, String> = projectPoints.associateBy({ it.userId }, { it.address })

        val userProjectPoints: List<UserProject> = userIdPointNumMap.map { (userId, pointNum) ->
            UserProject(
                userId = userId,
                address = "",
                projectId = projectId,
                pointNum = pointNum
            )
        }

        return Asset(
            projectId = projectId,
            userPoints = userProjectPoints.sortedByDescending { it.pointNum }
        )
    }

    fun getCompanyOnboardCampaign(companyId: Long, user: User, address: String): CampaignTotal? {
        val configMap = companyRepository.getCompanyConfig(companyId)

        val onboardCampaignId = configMap["onboard_campaign_id"]
        val userId = user.userId
        if (onboardCampaignId != null) {
            val onboardCampaign =
                campaignService.getVerifiedCamByCredential(onboardCampaignId.toLong(), userId, address)
            if (onboardCampaign?.campaign?.status !== CampaignStatus.DELETED && onboardCampaign?.campaign?.status != CampaignStatus.REJECTED) {
                return onboardCampaign
            }
        }
        return null
    }
}