package com.rewardoor.app.services

import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.jayway.jsonpath.JsonPath
import com.rewardoor.app.dao.*
import com.rewardoor.app.utils.scanKeys
import com.rewardoor.enums.SocialType
import com.rewardoor.enums.WiseScoreLevel
import com.rewardoor.model.*
import org.apache.http.client.utils.URIBuilder
import org.apache.http.message.BasicNameValuePair
import org.json.JSONArray
import org.json.JSONObject
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.annotation.Profile
import org.springframework.core.env.Environment
import org.springframework.data.redis.core.ScanOptions
import org.springframework.data.redis.core.StringRedisTemplate
import org.springframework.http.MediaType
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import org.springframework.web.reactive.function.BodyInserters
import org.springframework.web.reactive.function.client.ExchangeStrategies
import org.springframework.web.reactive.function.client.WebClient
import reactor.core.publisher.Mono
import java.io.BufferedReader
import java.math.BigDecimal
import java.net.URI
import java.nio.charset.StandardCharsets
import java.text.DecimalFormat
import java.time.Instant
import java.time.LocalDate
import java.time.ZoneId
import org.ton.java.address.Address

@Service
@Transactional
class WiseScoreService(
    val participantRepo: ParticipantRepository,
    val userWiseScoreRepo: UserWiseScoreRepository,
    val sbtWhiteListRepo: SBTWhiteListRepository,
    val telegramService: TelegramService,
    val twitterService: UserTwitterService,
    val taskVerifyService: TaskVerifyService,
    val discordService: DiscordService,
    val wiseTaskService: WiseTaskService,
    val userTonRepo: UserTonRepository,
    val userService: UserService,
    val credentialService: CredentialService,
    val coinAndTokenRepo: CoinAndTokenRepository,
    val redisTemplate: StringRedisTemplate,
    val wiseScoreInviteRepo: WiseScoreInviteRepository,
    val sbtRepo: SBTRewardRepository,
    val env: Environment,
    @Value("\${tg.x-api-key}") val xApiKey: String,
//    @Value("\${etherScan.api-key}") val ethApiKey: String,
    @Value("\${twitter.ut_key}") val utoolKey: String,
    @Value("\${tg.ton-x-api-key}") val tonXApiKey: String,
    @Value("\${tg.ton-x-partner-id}") val tonXPartnerId: String,
    @Value("\${coinMarket.api_key}") val coinMarketApiKey: String,
    private val credentialRepository: CredentialRepository,
    private val suiSbtRepository: SuiSbtRepository,
    private val userRepository: UserRepository,
    private val userSuiRepository: UserSuiRepository,
    private val campaignRepository: CampaignRepository,
    private val projectRepository: ProjectRepository,
) {
    private val log = mu.KotlinLogging.logger { }
    private final val strategies = ExchangeStrategies.builder()
        .codecs { codecs -> codecs.defaultCodecs().maxInMemorySize(20 * 1024 * 1024) }
        .build()
    private val webClient = WebClient.builder().exchangeStrategies(strategies).build()
    private val mapper = jacksonObjectMapper()

    private val tonSocietySBTActivityId = 371
    private val ethUri = URI("https://api.binance.com/api/v3/ticker/price?symbol=ETHUSDT")
    private val tonUri = URI("https://fapi.binance.com/fapi/v1/ticker/price?symbol=TONUSDT")

    // 6.9 add  - sui = 20947
    private val tonTokenIds =
        "30846,28704,31061,30117,9103,29704,31698,27894,25672,31697,28850,29705,30116,31252,27311,30118,29069,31267,23156,32106,28792,20947"  // crypto IDs, id from https://api.coinmarketcap.com/data-api/v3/cryptocurrency/market-pairs/latest?slug=bemo-staked-ton&start=1&limit=10&category=spot&centerType=all&sort=cmc_rank_advanced&direction=desc&spotUntracked=true
    private val tonTokenUri = URI("https://pro-api.coinmarketcap.com/v1/cryptocurrency/quotes/latest?id=$tonTokenIds")

    //    private val ethApiKey = "B88663Z7CUY5JDUE12H7E8MWKP9WW5XFGI"
    private val ethApiKey = "**********************************"
    private val tonConsoleBearer = "AFXEZ3ACHWCQPGIAAAACDBX3ACNADH4DWOIU3DOWOQMPYF7M5QIP5ZZFE5WI227KRIKIQ2Y"
    private val updateTimeGap = 10 * 60 * 1000
    private val minimumUSDT = 0.001
    private val evmBaseWealthScore = 300
    private val tonBaseWealthScore = 300
    private val suiBaseWealthScore = 300

    data class StormTradeActivity(
        var amount: BigDecimal = BigDecimal.ZERO,
        var timestamp: Instant = Instant.EPOCH,
        var currency: String = ""
    )

    data class Activity(val id: String, val EQ: String, val UQ: String)


    //wealth score

    fun getEvmBalance(user: UserEvm): String {
        val address = user.evmWallet
        try {
            val response = webClient
                .get()
                .uri(
                    "https://api.etherscan.io/api?module=account&action=balance&address=${address}&tag=latest&apikey=${ethApiKey}}"
                )
                .retrieve()
                .bodyToMono(String::class.java)
                .block();
            val jsonObject = JSONObject(response.toString())
            val result = jsonObject.getLong("result")
            val ethNum = result / 1_000_000_000_000_000_000.0
            val df = DecimalFormat("0.0000000")
            val tokenValue = ethNum * getCoinPrice("ETH")
            return df.format(tokenValue)
        } catch (e: Exception) {
            println("get evm balance error $e")
            return "0"
        }
    }

    fun getTonBalance(userTon: UserTon): String {
        val tonAddress = userTon.tonWallet
        try {
            val response = webClient
                .get()
                .uri("https://toncenter.com/api/v2/getAddressBalance?address=${tonAddress}")
                .header("x-api-key", xApiKey)
                .retrieve()
                .bodyToMono(String::class.java)
                .block()
            val jsonObject = JSONObject(response.toString())
            val result = jsonObject.getLong("result")
            val tonNum = result / 1_000_000_000.0
            val df = DecimalFormat("0.0000000")
            val tokenValue = tonNum * getCoinPrice("TON")
            return df.format(tokenValue)
        } catch (e: Exception) {
            println("get ton balance error $e")
            return "0"
        }
    }

    fun getSuiBalance(userSui: UserSui): String {
        val suiAddress = userSui.suiWallet
        val rpcUrl = "https://fullnode.mainnet.sui.io"
        val requestBody = """
            {
                "jsonrpc": "2.0",
                "method": "suix_getBalance",
                "params": ["$suiAddress"],
                "id": 1
            }
        """.trimIndent()
        var suiBalance = 0.0
        try {
            val response = webClient.post()
                .uri(rpcUrl)
                .header("Content-Type", "application/json")
                .bodyValue(requestBody)
                .retrieve()
                .bodyToMono(String::class.java)
                .block()
            println(response.toString())
            val json = JSONObject(response)
            val result = json.getJSONObject("result")
            suiBalance = result.getLong("totalBalance") / **********.0
            val suiPrice = getJettonsPrice()["SUI"] ?: 0.0
            val tokenValue = suiBalance * suiPrice
            val df = DecimalFormat("0.0000000")
            return df.format(tokenValue)
        } catch (e: Exception) {
            println("get sui balance error $e")
            return "0"
        }
    }

    fun getJettonsWealthScore(userTon: UserTon): Map<String, Int> {
        val tonAddress = userTon.tonWallet
        val jettonBalanceMap = mutableMapOf<String, Double>()
        val jettonPriceMap = getJettonsPrice().filter { it.key != "SUI" }
        val wealthScoreMap = mutableMapOf<String, Int>()
        try {
            val response = webClient
                .get()
                .uri("https://tonapi.io/v2/accounts/$tonAddress/jettons")
                .retrieve()
                .bodyToMono(String::class.java)
                .block()

            if (response != null) {
                val jsonObject = JSONObject(response)
                val balancesArray = jsonObject.getJSONArray("balances")

                for (i in 0 until balancesArray.length()) {
                    val balanceObject = balancesArray.getJSONObject(i)
                    val balance = balanceObject.getString("balance").toDouble() / 1_000_000_000
                    val jettonObject = balanceObject.getJSONObject("jetton")
                    val symbol = jettonObject.getString("symbol")
                    jettonBalanceMap[symbol] = balance
                }

                for ((symbol, price) in jettonPriceMap) {
                    if (jettonBalanceMap.containsKey(symbol)) {
                        val balance = jettonBalanceMap[symbol]!!
                        print("tonWallet: $tonAddress token: $symbol price: $price balance: $balance")
                        val wealthScore =
                            (if (price * balance > minimumUSDT) tonBaseWealthScore + price * balance else tonBaseWealthScore).toInt()
                        wealthScoreMap[symbol] = wealthScore
                    }
                }

            }
        } catch (e: Exception) {
            println("Error fetching jetton balances: $e")
        }
        return wealthScoreMap
    }

    fun getCoinPrice(coinType: String): Double {
        // fixed tsTON price temporarily
        if (coinType == "tsTON") {
            return 5.27
        }
//        for stag
//        if(coinType == "stTON") {
//            return 5.422507519893173
//        }
        val (price, updateTime) = coinAndTokenRepo.getTokenPriceData(coinType)
        if (price == null && updateTime == null) {
            val price = getCoinPriceFromApi(coinType)
            coinAndTokenRepo.addTokenPrice(coinType, price)
            return price
        } else {
            val currentDate = LocalDate.now()
            val updateDate = updateTime?.atZone(ZoneId.systemDefault())?.toLocalDate()

            if (updateDate != null && currentDate.isAfter(updateDate)) {
                val newPrice = getCoinPriceFromApi(coinType)
                coinAndTokenRepo.updateTokenPrice(coinType, newPrice)
                return newPrice
            } else {
                return price ?: 0.0
            }
        }
    }

    fun getCoinPriceFromApi(tokenName: String): Double {
        val url = when (tokenName) {
            "ETH" -> ethUri
            "TON" -> tonUri
            else -> URI("")
        }
        val response = webClient
            .get()
            .uri(url)
            .retrieve()
            .bodyToMono(String::class.java)
            .block()

        // 转化为 JSONObject
        val jsonObject = JSONObject(response.toString())
        println(jsonObject.toString())
        val price = jsonObject.getString("price").toDouble()
        return price
    }

    fun getJettonsPrice(): Map<String, Double> {
        val (price, updateTime) = coinAndTokenRepo.getTokenPriceData("NOT")
        if (price == null && updateTime == null) {
            val priceMap = getTonJettonsPriceFromApi()
            for (entry in priceMap.entries) {
                coinAndTokenRepo.addTokenPrice(entry.key, entry.value)
            }
            return priceMap
        } else {
            val currentDate = LocalDate.now()
            val updateDate = updateTime?.atZone(ZoneId.systemDefault())?.toLocalDate()

            if (updateDate != null && currentDate.isAfter(updateDate)) {
                val newPriceMap = getTonJettonsPriceFromApi()
                for (entry in newPriceMap.entries) {
                    var tokenSymbol = entry.key
                    if (tokenSymbol == "DUST") {
                        tokenSymbol = "SCALE"
                    }
                    println("key symbol ${entry.key}, value is ${entry.value}")
                    if (entry.value > 0) {
                        coinAndTokenRepo.updateTokenPrice(tokenSymbol, entry.value)
                    }
                }
                return newPriceMap
            } else {
                return coinAndTokenRepo.getJettonsPriceData()
            }
        }
    }

    fun getTonJettonsPriceFromApi(): Map<String, Double> {
        val tokenPrices = mutableMapOf<String, Double>()
        try {
            val response = webClient
                .get()
                .uri(tonTokenUri)
                .header("X-CMC_PRO_API_KEY", coinMarketApiKey)
                .retrieve()
                .bodyToMono(String::class.java)
                .block()

            val jsonObject = JSONObject(response)
            val dataObject = jsonObject.getJSONObject("data")

            val idsArray = tonTokenIds.split(",")
            for (id in idsArray) {
                val cryptoObject = dataObject.getJSONObject(id)
                val symbol = cryptoObject.getString("symbol")
                val quoteObject = cryptoObject.getJSONObject("quote")
                val usdObject = quoteObject.getJSONObject("USD")
                val priceString = usdObject.optString("price")
                val price = if (priceString.isNullOrEmpty()) {
                    0.0
                } else {
                    priceString.toDouble()
                }
                tokenPrices[symbol] = price
            }
        } catch (e: Exception) {
            println("Error fetching crypto prices: $e")
        }
        return tokenPrices
    }

    //engagement score
    fun getUserVerifiedCredentialNum(userId: Long): Int {
        return participantRepo.getByUser(userId).size
    }

    // engagement credential score on sui
    fun getUserVerifiedCredentialNumOnSui(userId: Long): Int {
        val credentials = participantRepo.getByUser(userId)
        return credentials.count { credential ->
            val campaign = campaignRepository.getCampaignById(credential.campaignId)
            val projectChain = campaign?.let {
                projectRepository.getProjectById(it.projectId)?.chain
            }
            projectChain == "sui"
        }
    }

    data class UserSbtOnSui(
        val basicSbtScore: Int = 0,
        val premiumSbtScore: Int = 0,
        val sbtScore: Int = 0,
        val sbtList: List<SuiSbtReward> = emptyList()
    ) {
    }

    // engagement sbt score on sui
    fun getUserSbtScoreOnSui(userId: Long): UserSbtOnSui {
        val userSbtRewards = participantRepo.getSBTRewardsByUserId(userId)
        var basicSbtNum = 0
        var premiumSbtNum = 0
        for (sbtReward in userSbtRewards) {
            val sbtId = sbtReward.rewardId
            val suiSbt = suiSbtRepository.getSuiSbtRewardById(sbtId)
            if (suiSbt != null) {
                val sbtCategory = suiSbt.category
                if (sbtCategory == 5) {
                    premiumSbtNum += 1
                } else {
                    basicSbtNum += 1
                }
            }
        }
        val sbtScore = basicSbtNum * 5000 + premiumSbtNum * 10000
        return UserSbtOnSui(
            basicSbtScore = basicSbtNum * 5000,
            premiumSbtScore = premiumSbtNum * 10000,
            sbtScore = sbtScore
        )
    }

    // get evm transactions
    fun getEvmTransactions(userId: Long): Int {
        val evmUser = userService.getEvmUserById(userId)!!
        val address = evmUser.evmWallet
        try {
            val response = webClient
                .get()
                .uri("https://api.etherscan.io/api?module=proxy&action=eth_getTransactionCount&address=${address}&tag=latest&apikey=${ethApiKey}")
                .retrieve()
                .bodyToMono(String::class.java)
                .block();
            val jsonObject = JSONObject(response.toString())
            var resultInHex = jsonObject.getString("result") // 获取"result"字段的值
            resultInHex = if (resultInHex.startsWith("0x")) resultInHex.substring(2) else resultInHex
            return resultInHex.toInt(16)
        } catch (e: Exception) {
            println("get evm transactions error $e")
            return 0
        }
    }

    //get ton transactions,limit 100
    fun getTonTransactions(userId: Long): Int {
        val tonUser = userTonRepo.getTonUserWalletByUserId(userId)!!
        val tonAddress = tonUser.tonWallet
//        val limit = 100
        val limit = 50
        try {
            val response = webClient
                .get()
                .uri("https://toncenter.com/api/v3/transactions?account=${tonAddress}&sort=desc")
                .header("x-api-key", xApiKey)
                .retrieve()
                .bodyToMono(String::class.java)
                .block()

            val jsonObject = JSONObject(response.toString())
            val transactions = jsonObject.getJSONArray("transactions") // 获取 'result' 数组
            return transactions.length()
        } catch (e: Exception) {
            println("get ton transaction error")
            return 0
        }

    }

    fun getTwitterFollowingNum(twitterUser: UserTwitterInfo): Int? {
        var followerNum = 0
        if (twitterUser != null) {
            val twitterId = twitterUser.twitterId
            val uri = URIBuilder("https://twitter.good6.top/api/base/apitools/usersByIdRestIds")
                .setParameters(BasicNameValuePair("apiKey", utoolKey), BasicNameValuePair("userIds", twitterId))
                .build()
            val response = webClient.get()
                .uri(uri)
                .retrieve()
                .bodyToMono(String::class.java)
                .block()
//            println("respnse is ${response.toString()}")
            val content = mapper.readTree(response)
            if (content["msg"].asText() != "SUCCESS") return null
            val data = content.get("data")?.asText() ?: return null
            if (data == "Not Found") return null
//            println("data is $data")

            // 将 `data` 解析为 JsonNode
            val rootNode = mapper.readTree(data)

            // 访问 followers_count
            val followersCount = rootNode
                .path("data")               // 进入 "data"
                .path("users")              // 进入 "users" 数组
                .path(0)                    // 取第一个用户
                .path("result")             // 进入 "result"
                .path("legacy")             // 进入 "legacy"
                .path("followers_count")    // 获取 "followers_count"
                .asInt(0)                   // 转换为 Int，默认值为 0

//            println("Followers count: $followersCount")
            followerNum = followersCount
        }
        return followerNum
    }

    fun calculateScore(score: Int): Int {
        return when {
            score < 10000 -> score
            score in 10000 until 100000 -> 10000 + (score - 10000) / 2
            score in 100000 until 1000000 -> 10000 + (100000 - 10000) / 2 + (score - 100000) / 4
            score in 1000000 until 10000000 -> 280000 + (score - 1000000) / 100
            else -> 370000 + (score - 10000000) / 1000
        }
    }

    fun getTgAndEvmUserIds(): List<Long> {
        val tgUserIdList = userTonRepo.getTonUserIds()
        val tgAndEvmUserList = userService.getEvmUsersByUserIdList(tgUserIdList)
        println("tgUserIdList is " + tgUserIdList.size + " tgAndEvmUserList is " + tgAndEvmUserList?.size)
        if (tgAndEvmUserList != null) {
            val tgAndEvmUserIdList = tgAndEvmUserList.filter { it.evmWallet != "" }.map { it.userId }
            println("tgAndEvmUserIdList is " + tgAndEvmUserIdList.size)
            return tgAndEvmUserIdList
        }
        return emptyList()
    }

    fun getTop500WealthUserIds(): List<Long>? {
        val tgAndEvmUserIds = getTgAndEvmUserIds()
        val userIdWealthList = mutableListOf<Pair<Long, Int>>()
        for (userId in tgAndEvmUserIds) {
            val wealthScore = getWealthScore(userId)
            userIdWealthList.add(Pair(userId, wealthScore.score))
        }
        return userIdWealthList.sortedByDescending { it.second }.take(500).map { it.first }
    }

    fun getTop500WealthWiseScore(): List<UserWiseScore> {
        val top500WealthUserIds = getTop500WealthUserIds()
        println("top500WealthUserIds is " + top500WealthUserIds!!.size)
        val existWiseScoreIds = userWiseScoreRepo.getAllScoreUser().map { it.userId }
        val wiseScores = mutableListOf<UserWiseScore>()
        val newIdList = top500WealthUserIds.filterNot { existWiseScoreIds.contains(it) }
        for (userId in newIdList) {
            val wiseScore = addScoreResultTmp(userId)
            wiseScores.add(wiseScore)
        }
        return wiseScores
    }

    fun getWealthScore(userId: Long): WealthScore {
        val evmUser = userService.getEvmUserById(userId)
        var evmBaseScore = 0
        var tonBaseScore = 0
        var suiBaseScore = 0
        var evmScore = 0
        val tonUser = userTonRepo.getTonUserWalletByUserId(userId)
        var tonScore = 0
        val suiUser = userSuiRepository.getSuiUserWalletByUserId(userId)
        var suiScore = 0
        var jettonsScore = 0
        val tonJettonsWealthScore = mutableMapOf<String, Int>()
        if (evmUser != null) {
            val evmWealthDouble = getEvmBalance(evmUser).toDouble()
            if (evmWealthDouble >= minimumUSDT) {
                evmBaseScore = evmBaseWealthScore
            }
            evmScore = evmBaseScore + evmWealthDouble.toInt()
            println("evmScore is " + evmScore)
        }
        if (tonUser != null) {
            val tonWealthDouble = getTonBalance(tonUser).toDouble()
            if (tonWealthDouble >= minimumUSDT) {
                tonBaseScore = tonBaseWealthScore
            }
            tonScore = tonBaseScore + getTonBalance(tonUser).toDouble().toInt()
            println("tonScore is " + tonScore)

            tonJettonsWealthScore.putAll(getJettonsWealthScore(tonUser))
            jettonsScore = tonJettonsWealthScore.values.sum()
            println("jettonsScore is " + jettonsScore)
        }
        if (suiUser != null) {
            val suiWealthDouble = getSuiBalance(suiUser).toDouble()
            if (suiWealthDouble >= minimumUSDT) {
                suiBaseScore = suiBaseWealthScore
            }
            suiScore = suiBaseScore + suiWealthDouble.toInt()
            println("sui wealth score is " + suiScore)
        }
        return WealthScore(
            score = calculateScore(evmScore + tonScore + jettonsScore),
            ethWealthScore = evmScore,
            tonWealthScore = tonScore,
            suiWealthScore = suiScore,
            jettonsWealthScore = tonJettonsWealthScore
        )
    }

    fun getIdentityScore(userId: Long): IdentityScore {
        val user = userService.getUserById(userId)
        val twitterUser = twitterService.getUserInfo(userId)
        val telegramUser = telegramService.getUserInfo(userId)
        val discordUser = discordService.getUserDcInfo(userId)
        val tonUser = userService.getTonWalletByUserId(userId)
        val tonAddressNum = tonUser.let { if (!it.tonWallet.isNullOrEmpty()) 1 else 0 } ?: 0
        val suiAddressNum = if (user?.suiAddress?.isNotEmpty() == true) 1 else 0
        var checkNotCoinHolder = 0
        if (tonAddressNum > 0) {
            val scoreUser = getScoreById(userId)
            if (scoreUser != null) {
                checkNotCoinHolder = scoreUser.isNotCoinHolder
                if (checkNotCoinHolder == 0) {
                    checkNotCoinHolder = if (checkNotCoinHolder(userId)) 1 else 0
                }
            }
        }
        val tgPremium = if (wiseTaskService.checkPremiumTask(userId)) 1 else 0
//        val userTgPremiumTask = taskRepo.getUserTask(userId, "tg:premium")
//        if (userTgPremiumTask.isNullOrEmpty()) {
//            tgPremium = if (telegramService.getUserTgInfo(userId)?.isPremium == true) 1 else 0
//        } else {
//            tgPremium = 1
//        }
        val addressNum = user?.let { if (it.wallet != "") 1 else 0 } ?: 0
        val socialNum = listOf(twitterUser, telegramUser, discordUser).count { it != null }
        return IdentityScore(
            score = (addressNum + tonAddressNum + suiAddressNum + socialNum + tgPremium + checkNotCoinHolder) * 10000,
            ethAddressScore = addressNum * 10000,
            tonAddressScore = tonAddressNum * 10000,
            suiAddressScore = suiAddressNum * 10000,
            socialScore = socialNum * 10000,
            xBindScore = if (twitterUser != null) 10000 else 0,
            tgBindScore = if (telegramUser != null) 10000 else 0,
            dcBindScore = if (discordUser != null) 10000 else 0,
            tgPremiumScore = tgPremium * 10000,
            notCoinHolderScore = checkNotCoinHolder * 10000
//            availableScore = (8 - addressNum - tonAddressNum - suiAddressNum - socialNum - tgPremium - checkNotCoinHolder) * 10000
        )
    }

    // todo : add telegram group users and discord subscribers
    fun getSocialScore(userId: Long): SocialScore {
        val twitterUser = twitterService.getUserInfo(userId)
        var twitterFollowerNum = 0

        val tgUserLinks = userWiseScoreRepo.getUserShareLink(userId, SocialType.TELEGRAM.code)

        val tgUserNum = tgUserLinks.sumOf {
            telegramService.getTgMemberCountByUrl(it.shareLink)
        }

        val dcLinks = userWiseScoreRepo.getUserShareLink(userId, SocialType.DISCORD.code)
        val dcUserNum = dcLinks.sumOf {
            discordService.getGuildMemberCountByUrl(userId, it.shareLink)
        }

        if (twitterUser != null) {
            twitterFollowerNum = getTwitterFollowingNum(twitterUser) ?: 0
        }
//        val inviteCount = wiseScoreInviteRepo.getInvitedCount(userId)
//        val inviteScore = inviteCount * 1000
        val inviteScore = getWiseScoreInviteScore(userId) + getWiseScoreInviteStags2Score(userId)
        if (userId == 744551865841L) {
            twitterFollowerNum = 1000
            return SocialScore(
                score = calculateScore(twitterFollowerNum + 500 + 200 + inviteScore),
                twitterFollowerScore = twitterFollowerNum,
                tgUserScore = 500,
                dcUserScore = 200,
                wiseInviteScore = inviteScore
            )
        }
        return SocialScore(
            score = calculateScore(twitterFollowerNum + tgUserNum + dcUserNum + inviteScore),
            twitterFollowerScore = twitterFollowerNum,
            tgUserScore = tgUserNum,
            dcUserScore = dcUserNum,
            wiseInviteScore = inviteScore
        )
    }

    // event stage 1 invite score
    fun getWiseScoreInviteScore(userId: Long): Int {
        val wiseTotalScore = getScoreById(userId)?.totalScore ?: 0
        val inviteTotalTimes = wiseScoreInviteRepo.getInviteCode(userId, 1)?.totalTimes ?: 0
        var inviteCount = wiseScoreInviteRepo.getInviteesCount(userId, 1)
        var inviteScore = 0
        val wiseScoreLevel = WiseScoreLevel.values().first { wiseTotalScore < it.totalScore }
        val levelInviteTotalTimes = wiseScoreLevel.inviteTotalTimes
        // If the number of invitations exceeds the current level invitation limit, the invitation limit will be updated.
        if (inviteTotalTimes in 1 until levelInviteTotalTimes) {
            val wiseInviteCode = wiseScoreInviteRepo.getInviteCode(userId, 1)!!
            wiseScoreInviteRepo.updateInviteTotalTimes(userId, wiseInviteCode.inviteCode, levelInviteTotalTimes)
        }
        when (wiseScoreLevel) {
            WiseScoreLevel.LEVEL_0 -> {
                inviteCount = if (inviteCount > 3) 3 else inviteCount
                inviteScore = inviteCount * 1000
            }

            WiseScoreLevel.LEVEL_1 -> {
                inviteCount = if (inviteCount > 3) 3 else inviteCount
                inviteScore = inviteCount * 1000
            }

            WiseScoreLevel.LEVEL_2 -> {
                if (inviteCount <= 3) {
                    inviteScore = inviteCount * 1000
                } else {
                    inviteCount = if (inviteCount > 20) 20 else inviteCount
                    inviteScore = 3 * 1000 + (inviteCount - 3) * 500
                }
            }

            WiseScoreLevel.LEVEL_3 -> {
                when {
                    inviteCount <= 3 -> {
                        inviteScore = inviteCount * 1000
                    }

                    inviteCount <= 20 -> {
                        inviteScore = 3 * 1000 + (inviteCount - 3) * 500
                    }

                    inviteCount <= 50 -> {
                        inviteScore = 3 * 1000 + 17 * 500 + (inviteCount - 20) * 200
                    }

                    else -> {
                        inviteScore = 3 * 1000 + 17 * 500 + 30 * 200
                        inviteCount = 50 // Limit to 50 invites for scoring
                    }
                }
            }

            WiseScoreLevel.LEVEL_4 -> {
                when {
                    inviteCount <= 3 -> {
                        inviteScore = inviteCount * 1000
                    }

                    inviteCount <= 20 -> {
                        inviteScore = 3 * 1000 + (inviteCount - 3) * 500
                    }

                    inviteCount <= 50 -> {
                        inviteScore = 3 * 1000 + 17 * 500 + (inviteCount - 20) * 200
                    }

                    inviteCount <= 100 -> {
                        inviteScore = 3 * 1000 + 17 * 500 + 30 * 200 + (inviteCount - 50) * 100
                    }

                    else -> {
                        inviteScore = 3 * 1000 + 17 * 500 + 30 * 200 + 50 * 100
                        inviteCount = 100 // Limit to 100 invites for scoring
                    }
                }
            }

            WiseScoreLevel.LEVEL_5 -> {
                when {
                    inviteCount <= 3 -> {
                        inviteScore = inviteCount * 1000
                    }

                    inviteCount <= 20 -> {
                        inviteScore = 3 * 1000 + (inviteCount - 3) * 500
                    }

                    inviteCount <= 50 -> {
                        inviteScore = 3 * 1000 + 17 * 500 + (inviteCount - 20) * 200
                    }

                    inviteCount <= 100 -> {
                        inviteScore = 3 * 1000 + 17 * 500 + 30 * 200 + (inviteCount - 50) * 100
                    }

                    inviteCount <= 200 -> {
                        inviteScore = 3 * 1000 + 17 * 500 + 30 * 200 + 50 * 100 + (inviteCount - 100) * 50
                    }

                    else -> {
                        inviteScore = 3 * 1000 + 17 * 500 + 30 * 200 + 50 * 100 + 100 * 50
                        inviteCount = 200 // Limit to 200 invites for scoring
                    }
                }
            }

            else -> {
                // do nothing
            }
        }
        return inviteScore
    }

    // event stage 2 invite score
    fun getWiseScoreInviteStags2Score(userId: Long): Int {
        val phase1InviteCount = wiseScoreInviteRepo.getInviteesCount(userId, 1)
        val phase2InviteCount = wiseScoreInviteRepo.getInviteesCount(userId, 2)

        val totalInviteCount = phase1InviteCount + phase2InviteCount

        return when {
            phase2InviteCount <= 0 -> 0
            totalInviteCount <= 3 -> {
                phase2InviteCount * 1000
            }

            totalInviteCount <= 20 -> {
                phase2InviteCount * 500
            }

            totalInviteCount <= 50 -> {
                phase2InviteCount * 200
            }

            totalInviteCount <= 100 -> {
                phase2InviteCount * 100
            }

            else -> {
                phase2InviteCount * 50
            }
        }
    }

    fun getEngagementScore(userId: Long): EngagementScore {
        val credentialNum = getUserVerifiedCredentialNum(userId)
        val evmUser = userService.getEvmUserById(userId)
        var evmTransactions = 0
        if (evmUser != null) {
            evmTransactions = getEvmTransactions(userId)
        }
        var tonTransactions = 0
        var notCoinTransactionScore = 0
        var tonStakeScore = 0
        var tonLiquidityScore = 0
        val tonUser = userTonRepo.getTonUserWalletByUserId(userId)
        if (tonUser != null) {
            tonTransactions = getTonTransactions(userId)
            val userScore = getScoreById(userId)
            if (tonTransactions > 0) {
                if (userScore != null) {
                    val hasNotCoinTransaction = userScore.hasNotCoinTransaction > 0
                    if (hasNotCoinTransaction) {
                        notCoinTransactionScore = 50000
                    } else {
                        notCoinTransactionScore = if (checkUserNotCoinEvents(userId)) 50000 else 0
                    }
                }
            }
            if (userScore != null) {
                val hasTonStake = userScore.hasTonStake > 0
                val hasTonLiquidityProvide = userScore.hasTonLiquidityProvide > 0
                if (hasTonStake) {
                    tonStakeScore = 5000
                } else {
                    tonStakeScore = if (checkUserStakeEvents(userId)) 5000 else 0
                }
                if (hasTonLiquidityProvide) {
                    tonLiquidityScore = 10000
                } else {
                    tonLiquidityScore = if (checkUserLiquidityProvideEvents(userId)) 10000 else 0
                }
            }

        }
        return EngagementScore(
            score = (credentialNum + evmTransactions + tonTransactions) * 1000 + notCoinTransactionScore + tonStakeScore + tonLiquidityScore,
            credentialScore = credentialNum * 1000,
            evmTransactionsScore = evmTransactions * 1000,
            tonTransactionsScore = tonTransactions * 1000,
            notCoinTransactionScore = notCoinTransactionScore,
            tonStakeScore = tonStakeScore,
            tonLiquidityProvideScore = tonLiquidityScore
        )
    }

    fun getEngagementScoreOnSui(userId: Long): EngagementScore {
        val credentialNum = getUserVerifiedCredentialNumOnSui(userId)
        val sbtScore = getUserSbtScoreOnSui(userId)
        val premiumAvailableScore = 1 * 10000 - sbtScore.premiumSbtScore
        return EngagementScore(
            score = (credentialNum) * 1000 + sbtScore.sbtScore,
            credentialScore = credentialNum * 1000,
            sbtScore = sbtScore.sbtScore,
            basicSbtScore = sbtScore.basicSbtScore,
            premiumSbtScore = sbtScore.premiumSbtScore,
            evmTransactionsScore = 0,
            tonTransactionsScore = 0,
            notCoinTransactionScore = 0,
            tonStakeScore = 0,
            tonLiquidityProvideScore = 0
//            premiumAvailableScore = premiumAvailableScore
        )
    }

    fun getUserWiseScore(userId: Long): UserWiseScore {
        val socialScore = getSocialScore(userId)
        val wealthScore = getWealthScore(userId)
        val identityScore = getIdentityScore(userId)
        val engagementScore = getEngagementScore(userId)
        val totalScore = wealthScore.score + identityScore.score + socialScore.score + engagementScore.score
        val user = userService.getUserById(userId)!!
        val evmUser = userService.getEvmUserById(userId)
        val tonUser = userTonRepo.getTonUserWalletByUserId(userId)
        val tgUser = telegramService.getUserInfo(userId)
        val resultAddress = when {
            tonUser != null -> tonUser.tonWallet!!
            evmUser != null -> evmUser.evmWallet!!
            else -> tgUser?.username
        }
        val resultAddressType = when {
            tonUser != null -> 1
            evmUser != null -> 0
            else -> 2
        }
        return UserWiseScore(
            userId = userId,
            address = resultAddress,
            addressType = resultAddressType,
            avatar = user.displayAvatar,
            totalScore = totalScore,
            wealthScore = wealthScore,
            identityScore = identityScore,
            socialScore = socialScore,
            engagementScore = engagementScore,
            isNotCoinHolder = if (identityScore.notCoinHolderScore > 0) 1 else 0,
            hasNotCoinTransaction = if (engagementScore.notCoinTransactionScore > 0) 1 else 0,
            hasTonStake = if (engagementScore.tonStakeScore > 0) 1 else 0,
            hasTonLiquidityProvide = if (engagementScore.tonLiquidityProvideScore > 0) 1 else 0,
            updateTime = Instant.now()
        )
    }


    class RewardInfo(
        val rewardId: Long,
        val groupId: Long,
        val credentialId: Long,
        val score: Int,
        val suiSbtReward: SuiSbtReward, // 可选，如果需要包含完整的 suiSbtReward 信息
        val credential: Credential
    )

    fun getCredentialsForPerks(): List<Credential> {
        val credentialList = credentialService.getCredentialListByLabelType(76)

        return credentialList.filter { credential ->
            try {
                val jsonObject = JSONObject(credential.options)
                jsonObject.getBoolean("showInPerk")
            } catch (e: Exception) {
                false // 如果解析失败，默认不显示
            }
        }
    }


    fun getWiseScoreSBTPerks(userId: Long, totalScore: Int): List<WiseScoreSBTPerk> {
        val credentialList = getCredentialsForPerks()
        val rewards = credentialList.mapNotNull { credential ->
            try {
                val jsonObject = JSONObject(credential.options)
                val score = jsonObject.optInt("score", 0) // 默认值为0
                val suiSbtReward = suiSbtRepository.getSuiSbtRewardByGroupId(credential.groupId)

                RewardInfo(
                    rewardId = suiSbtReward[0].suiSbtId,
                    groupId = credential.groupId,
                    credentialId = credential.credentialId,
                    score = score,
                    suiSbtReward = suiSbtReward[0],
                    credential = credential,
                )
            } catch (e: Exception) {
                // 解析失败时返回 null，会被 mapNotNull 过滤掉
                null
            }
        }

        val result = mutableListOf<WiseScoreSBTPerk>()
        // 1. 获取用户已有的 user_reward 记录
        // 2. 获取用户信息（用于创建 credential 记录）
        val suiUser = userSuiRepository.getSuiUserWalletByUserId(userId)
        val wallet = suiUser?.suiWallet

        // 3. 处理每个 rewardId
        for (reward in rewards) {
            var userReward: UserReward? = null
            var shouldCreatePerk = false

            val existingUserReward =
                participantRepo.getUserSuiSbtRewardByUidAndReward(userId, reward.rewardId, reward.groupId)

            println("existingUserReward - ${existingUserReward?.rewardId}")

            // 检查是否已有记录
            if (existingUserReward != null) {
                // 已有记录，直接使用
                println("已有记录，直接使用 rewardId:${existingUserReward.rewardId} - groupId:${existingUserReward.groupId}")
                userReward = existingUserReward
                shouldCreatePerk = true
            } else {
                // 无记录，检查分数是否满足条件
                println("无记录，检查分数是否满足条件 -rewardId/sbtId: ${reward.rewardId} - credentialId: ${reward.credentialId} score: ${reward.score}")
                if (totalScore >= reward.score) {
                    // 满足条件，创建新记录
                    try {
                        // 添加 user_credential_new 记录
                        println("添加 user_credential_new 记录 ${wallet}")
                        val userCredential = wallet?.let {
                            UserCredential(
                                userId = userId,
                                address = it,
                                credentialId = reward.credentialId,
                                campaignId = reward.credential.campaignId,
                                status = 1,
                                participantDate = Instant.now(),
                                labelType = reward.credential.labelType
                            )
                        }
                        if (userCredential != null) {
                            participantRepo.addResult(userCredential)
                        }

                        // 添加 user_reward 记录
                        println("添加 user_reward 记录 ${wallet}")
                        val newUserReward = UserReward(
                            rewardId = reward.rewardId,
                            rewardType = 4,
                            userId = userId,
                            groupId = reward.groupId,
                            claimType = 1
                        )

                        // 检查是否已存在（防止并发重复插入）
                        val exists = participantRepo.getUserSbtReward(reward.rewardId, userId, reward.groupId) != null
                        if (!exists) {
                            participantRepo.addUserRewardResult(newUserReward)
                            userReward = newUserReward
                        }
                        shouldCreatePerk = true
                    } catch (e: Exception) {
                        throw e
                    }
                } else {
                    // 分数不满足条件，但仍需要返回 SBT 信息，只是 claimedType 为空
                    println("分数不满足条件，返回未领取状态的 SBT 信息")
                    shouldCreatePerk = true
                    // userReward 保持为 null，表示未领取状态
                }
            }

            // 4. 拼装返回数据
            if (shouldCreatePerk) {
                val perk = WiseScoreSBTPerk(
                    projectUrl = projectRepository.getProjectById(reward.credential.projectId)!!.projectUrl,
                    suiSbtId = reward.suiSbtReward.suiSbtId,
                    suiSbtActivityId = reward.suiSbtReward.suiSbtActivityId,
                    sbtName = reward.suiSbtReward.sbtName,
                    sbtDesc = reward.suiSbtReward.sbtDesc,
                    sbtUrl = reward.suiSbtReward.sbtUrl,
                    suiSbtObjectId = reward.suiSbtReward.objectId,
                    rewardType = 4,
                    claimedType = userReward?.claimType ?: 0,
                    requiredScore = reward.score,
                    credential = reward.credential,
                )
                result.add(perk)
            }
        }

        return result
    }


    fun getEngagementSBTList(userId: Long): List<WiseScoreSBTPerk> {
        val stagSbtIdList = listOf<Long>(
            805468137310L,
            769250376807L,
            769250096802L
        )
        val prodSbtIdList = listOf<Long>(
            805687122604580L,
            772989062587290L,
            805688972604587L
        )

        val sbtIdList = if (env.activeProfiles.contains("prod")) {
            prodSbtIdList
        } else {
            stagSbtIdList
        }

        // 2. 根据 SBT ID 列表获取对应的 SuiSbtReward
        val suiSbtRewards = suiSbtRepository.getSuiSbtRewardByIds(sbtIdList)

        // 3. 构造 RewardInfo 列表
        val rewards = suiSbtRewards.mapNotNull { suiSbtReward ->
            try {
                // 通过 groupId 获取对应的 credential
                val credentialList = credentialRepository.getCredentialByGroupId(suiSbtReward.groupId ?: 0L)
                val credential = if (credentialList.isNotEmpty()) credentialList[0] else null

                if (credential != null) {
                    RewardInfo(
                        rewardId = suiSbtReward.suiSbtId,
                        groupId = suiSbtReward.groupId ?: 0L,
                        credentialId = credential.credentialId,
                        score = 0, // 固定为0，不需要分数要求
                        suiSbtReward = suiSbtReward,
                        credential = credential
                    )
                } else {
                    // 如果没有对应的 credential，跳过这个 suiSbtReward
                    println("No credential found for groupId: ${suiSbtReward.groupId}")
                    null
                }
            } catch (e: Exception) {
                println("Error processing suiSbtReward ${suiSbtReward.suiSbtId}: ${e.message}")
                null
            }
        }

        val result = mutableListOf<WiseScoreSBTPerk>()

        // 4. 获取用户信息
        val suiUser = userSuiRepository.getSuiUserWalletByUserId(userId)
        val wallet = suiUser?.suiWallet

        // 5. 处理每个 reward
        for (reward in rewards) {
            try {
                // 6. 检查用户是否已有该 reward 的记录
                val existingUserReward = participantRepo.getUserSbtReward(reward.rewardId, userId, reward.groupId)

                println("Processing rewardId: ${reward.rewardId}, groupId: ${reward.groupId}")
                println("existingUserReward: ${existingUserReward?.rewardId}")

                // 7. 确定 claimType
                val claimType = existingUserReward?.claimType ?: 0

                // 8. 构造 WiseScoreSBTPerk 对象
                val perk = WiseScoreSBTPerk(
                    projectUrl = projectRepository.getProjectById(reward.credential.projectId)?.projectUrl ?: "",
                    suiSbtId = reward.suiSbtReward.suiSbtId,
                    suiSbtActivityId = reward.suiSbtReward.suiSbtActivityId,
                    sbtName = reward.suiSbtReward.sbtName,
                    sbtDesc = reward.suiSbtReward.sbtDesc,
                    sbtUrl = reward.suiSbtReward.sbtUrl,
                    suiSbtObjectId = reward.suiSbtReward.objectId,
                    rewardType = 4,
                    claimedType = claimType,
                    requiredScore = 0, // 固定为0，不需要分数要求
                    credential = reward.credential
                )

                result.add(perk)
                println("Added perk for rewardId: ${reward.rewardId}, claimedType: $claimType")

            } catch (e: Exception) {
                println("Error processing reward ${reward.rewardId}: ${e.message}")
                // 可以选择继续处理其他 reward 或者抛出异常
            }
        }

        return result
    }

    fun getUserWiseScoreOnSui(userId: Long): UserWiseScore {
        val socialScore = getSocialScore(userId)
        val wealthScore = getWealthScore(userId)
        val identityScore = getIdentityScore(userId)
        val engagementScore = getEngagementScoreOnSui(userId)
        val totalScore = wealthScore.score + identityScore.score + socialScore.score + engagementScore.score
        val user = userService.getUserById(userId)!!
        val userSuiAddress = user.suiAddress
        val wiseScoreSBTPerkList = getWiseScoreSBTPerks(userId, totalScore)
        val engagementSBTList = getEngagementSBTList(userId)
        return UserWiseScore(
            userId = userId,
            address = userSuiAddress,
            addressType = 3,
            avatar = user.displayAvatar,
            totalScore = totalScore,
            wealthScore = wealthScore,
            identityScore = identityScore,
            socialScore = socialScore,
            engagementScore = engagementScore,
            wiseScoreSBTPerkList = wiseScoreSBTPerkList,
            engagementSBTList = engagementSBTList,
            isNotCoinHolder = if (identityScore.notCoinHolderScore > 0) 1 else 0,
            hasNotCoinTransaction = if (engagementScore.notCoinTransactionScore > 0) 1 else 0,
            hasTonStake = if (engagementScore.tonStakeScore > 0) 1 else 0,
            hasTonLiquidityProvide = if (engagementScore.tonLiquidityProvideScore > 0) 1 else 0,
            updateTime = Instant.now()
        )
    }

    // 2. 配置常量
    object SbtConfig {

        // Staging 环境配置
        private val STAGING_CONFIG = mapOf(
            "left" to listOf(814911537337L),
            "right" to listOf(814410067329L, 814912787344L)
        )

        // Production 环境配置
        private val PROD_CONFIG = mapOf(
            "left" to listOf(805687122604580L),
            "right" to listOf(772989062587290L, 805688972604587L)
        )

        // 根据环境获取配置
        fun getConfig(isProd: Boolean): Map<String, List<Long>> {
            return if (isProd) {
                PROD_CONFIG
            } else {
                STAGING_CONFIG
            }
        }

        // 获取所有配置的 sbtIds
        fun getAllSbtIds(isProd: Boolean): List<Long> {
            return getConfig(isProd).values.flatten()
        }

        // 根据 sbtId 获取对应的 position
        fun getPositionBySbtId(sbtId: Long, isProd: Boolean): String? {
            return getConfig(isProd).entries.find { (_, sbtIds) ->
                sbtIds.contains(sbtId)
            }?.key
        }
    }

    class SbtConfigInfo(
        val projectUrl: String,
        val rewardId: Long,
        val groupId: Long,
        val credentialId: Long,
        val suiSbtReward: SuiSbtReward, // 可选，如果需要包含完整的 suiSbtReward 信息
        val credential: Credential,
        val position: String?
    )

    fun getSbtMapDataWithPosition(): List<SbtConfigInfo> {
        return try {
            // 获取所有配置的 sbtIds
            val isProd = env.activeProfiles.contains("prod")
            val allSbtIds = SbtConfig.getAllSbtIds(isProd)

            // 根据 SBT ID 列表获取对应的 SuiSbtReward
            val suiSbtRewards = suiSbtRepository.getSuiSbtRewardByIds(allSbtIds)

            // 构造 RewardInfo 列表
            val rewardList = mutableListOf<SbtConfigInfo>()

            suiSbtRewards.forEach { suiSbtReward ->
                try {
                    // 通过 groupId 获取对应的 credential
                    val credentialList = credentialRepository.getCredentialByGroupId(suiSbtReward.groupId ?: 0L)
                    val credential = if (credentialList.isNotEmpty()) credentialList[0] else null

                    if (credential != null) {
                        // 根据 sbtId 获取对应的 position
                        val position = SbtConfig.getPositionBySbtId(suiSbtReward.suiSbtId, isProd)

                        // 构造 RewardInfo 对象
                        val rewardInfo = SbtConfigInfo(
                            rewardId = suiSbtReward.suiSbtId,
                            groupId = suiSbtReward.groupId ?: 0L,
                            credentialId = credential.credentialId,
                            suiSbtReward = suiSbtReward,
                            credential = credential,
                            position = position,
                            projectUrl = projectRepository.getProjectById(credential.projectId)!!.projectUrl,
                        )
                        rewardList.add(rewardInfo)
                        println("Added RewardInfo for sbtId: ${suiSbtReward.suiSbtId}, position: $position")
                    } else {
                        println("No credential found for sbtId: ${suiSbtReward.suiSbtId}, groupId: ${suiSbtReward.groupId}")
                    }
                } catch (e: Exception) {
                    println("Error processing suiSbtReward ${suiSbtReward.suiSbtId}: ${e.message}")
                }
            }
            println("Total RewardInfo entries created: ${rewardList.size}")
            rewardList

        } catch (e: Exception) {
            println("Error in getSbtMapDataWithPosition: ${e.message}")
            emptyList()
        }
    }

    fun getScoreById(userId: Long): UserWiseScore? {
        return userWiseScoreRepo.getScoreByUserId(userId)
    }

    fun getSuiScoreById(userId: Long): UserWiseScore? {
        return userWiseScoreRepo.getSuiScoreByUserId(userId)
    }

    fun getSBTById(userId: Long): UserSBTList? {
        return sbtWhiteListRepo.getUserSbtById(userId)
    }

    fun addUserSBTWhiteList(userSBTList: UserSBTList): Int {
        val userSBTResult = getSBTById(userSBTList.userId)
        if (userSBTResult != null) {
            return 0
        }
        return sbtWhiteListRepo.createUserSBT(userSBTList)
    }

    fun addScoreResult(userId: Long, chain: String = ""): UserWiseScore {
        val isSuiChain = chain == "sui"
        val scoreResult = if (isSuiChain) getSuiScoreById(userId) else getScoreById(userId)
        if (scoreResult != null) {
            val uScore = if (isSuiChain) getUserWiseScoreOnSui(userId) else getUserWiseScore(userId)
            if (scoreResult.identityScore!!.score == 0) {
                uScore.isFirstCreate = true
            }
            val createTime = scoreResult.createTime!!
            val currentTime = System.currentTimeMillis()
            if (currentTime - createTime.toEpochMilli() > updateTimeGap) {
                uScore.rank = if (isSuiChain) getUserSuiRank(userId) else getUserRank(userId)
            }
            if (!isSuiChain) {
                userWiseScoreRepo.updateUserWiseScore(uScore)
            } else {
                userWiseScoreRepo.updateUserSuiWiseScore(uScore)
            }
            return uScore
        }
        if (!isSuiChain) {
            val uScore = getUserWiseScore(userId)
            uScore.isFirstCreate = true
            userWiseScoreRepo.addUserWiseScore(uScore)
            return uScore
        } else {
            val uScore = getUserWiseScoreOnSui(userId)
            uScore.isFirstCreate = true
            userWiseScoreRepo.addUserSuiWiseScore(uScore)
            return uScore
        }
    }

    fun addBlankScoreResult(user: User): UserWiseScore {
        var addressType = 0
        var address = ""
        if (user.ton.tonWallet != null) {
            addressType = 1
            address = user.ton.tonWallet!!
        } else if (user.tgName.isNotEmpty()) {
            addressType = 2
            address = user.tgName
        } else if (user.evm.evmWallet != null) {
            addressType = 0
            address = user.evm.evmWallet!!
        }
        val uScore = UserWiseScore(
            userId = user.userId,
            address = address,
            addressType = addressType,
            avatar = user.displayAvatar,
            totalScore = 0,
            wealthScore = WealthScore(0, 0, 0),
            identityScore = IdentityScore(0, 0, 0, 0, 0),
            socialScore = SocialScore(0, 0, 0, 0),
            engagementScore = EngagementScore(0, 0, 0, 0, 0),
            isNotCoinHolder = 0,
            hasNotCoinTransaction = 0,
            hasTonStake = 0,
            hasTonLiquidityProvide = 0
        )

        userWiseScoreRepo.addUserWiseScore(uScore)
        return uScore
    }

    fun addScoreResultTmp(userId: Long): UserWiseScore {
        val uScore = getUserWiseScore(userId)
        userWiseScoreRepo.addUserWiseScoreTmp(uScore)
        return uScore
    }

    fun addAllTmpToScore() {
        val tmpScores = userWiseScoreRepo.getAllScoreTmp()
        for (score in tmpScores) {
            userWiseScoreRepo.addUserWiseScore(score)
        }
    }

    fun getTop100Score(): List<UserWiseScore> {
        return userWiseScoreRepo.getTop100ScoreUser()
    }

    fun getTop100SocialScore(): List<UserWiseScore> {
        return userWiseScoreRepo.getTop100SocialScoreUser()
    }

    fun getTop100EngageScore(): List<UserWiseScore> {
        return userWiseScoreRepo.getTop100EngageScoreUser()
    }

    fun getTop500ScoreFromRankRedis(): List<UserWiseScore> {
        val gk = "top1000-user:*"
        val scanOptions = ScanOptions.scanOptions().match(gk).count(1000).build()
        val redisConnection = redisTemplate.connectionFactory?.connection
        val keys = mutableListOf<String>()
        redisConnection?.use { connection ->
            val cursor = connection.scan(scanOptions) // 使用 SCAN 命令
            cursor.use {
                while (it.hasNext()) {
                    val key = String(it.next()) // 将字节数组转换为字符串
                    keys.add(key) // 收集匹配的键
                }
            }
        }
        val keyValueMap = mutableMapOf<Long, Int>()
        keys.forEach { key ->
            val value = redisTemplate.opsForValue().get(key) // 获取键对应的值
            println("key is $key value is $value")
            if (value != null) {
                try {
                    // 提取 uid 和 score
                    val uid = key.split("top1000-user:")[1].toLong()
                    val score = value.split("-")[1].toInt()
                    keyValueMap[uid] = score
                } catch (e: Exception) {
                    println("Error processing key: $key, value: $value, error: ${e.message}")
                }
            }
        }
        val sortedEntries = keyValueMap.entries
            .sortedByDescending { it.value }
            .take(500)
        return sortedEntries.mapIndexed { index, entry ->
            val uid = entry.key
            val tonAdd = userTonRepo.getTonUserWalletByUserId(uid)?.tonWallet ?: ""
            UserWiseScore(
                userId = entry.key,
                address = tonAdd,
                addressType = 1,
                totalScore = entry.value,
                rank = index + 1 // 设置排名
            )
        }
    }

    fun getTop1000SocialScoreFromRankRedis(): List<UserWiseScore> {
        val gk = "top1000-social-user:*"
        val scanOptions = ScanOptions.scanOptions().match(gk).count(1000).build()
        val redisConnection = redisTemplate.connectionFactory?.connection
        val keys = mutableListOf<String>()
        redisConnection?.use { connection ->
            val cursor = connection.scan(scanOptions) // 使用 SCAN 命令
            cursor.use {
                while (it.hasNext()) {
                    val key = String(it.next()) // 将字节数组转换为字符串
                    keys.add(key) // 收集匹配的键
                }
            }
        }
        val keyValueMap = mutableMapOf<Long, Int>()
        keys.forEach { key ->
            val value = redisTemplate.opsForValue().get(key) // 获取键对应的值
            if (value != null) {
                try {
                    // 提取 uid 和 score
                    val uid = key.split("top1000-social-user:")[1].toLong()
                    val score = value.split("-")[1].toInt()
                    keyValueMap[uid] = score
                } catch (e: Exception) {
                    println("Error processing key: $key, value: $value, error: ${e.message}")
                }
            }
        }
        val sortedEntries = keyValueMap.entries
            .sortedByDescending { it.value }
            .take(500)
        return sortedEntries.mapIndexed { index, entry ->
            val uid = entry.key
            val user = userRepository.findUserById(uid)!!
            val suiAdd = user.suiAddress
            val tonAdd = user.ton.tonWallet
            val evmAdd = user.evm.evmWallet
            val tgId = user.tgName
            val (showAdd, addressType) = when {
                suiAdd.isNotEmpty() -> suiAdd to 3
                !tonAdd.isNullOrEmpty() -> tonAdd to 1
                !evmAdd.isNullOrEmpty() -> evmAdd to 0
                else -> tgId to 2
            }
            val socialScore = SocialScore(
                score = entry.value
            )
            UserWiseScore(
                userId = entry.key,
                address = showAdd,
                addressType = addressType,
                socialScore = socialScore,
                rank = index + 1 // 设置排名
            )
        }
    }

    fun getTop1000EngageScoreFromRankRedis(): List<UserWiseScore> {
        val gk = "top1000-engage-user:*"
        val scanOptions = ScanOptions.scanOptions().match(gk).count(1000).build()
        val redisConnection = redisTemplate.connectionFactory?.connection
        val keys = mutableListOf<String>()
        redisConnection?.use { connection ->
            val cursor = connection.scan(scanOptions) // 使用 SCAN 命令
            cursor.use {
                while (it.hasNext()) {
                    val key = String(it.next()) // 将字节数组转换为字符串
                    keys.add(key) // 收集匹配的键
                }
            }
        }
        val keyValueMap = mutableMapOf<Long, Int>()
        keys.forEach { key ->
            val value = redisTemplate.opsForValue().get(key) // 获取键对应的值
            if (value != null) {
                try {
                    // 提取 uid 和 score
                    val uid = key.split("top1000-engage-user:")[1].toLong()
                    val score = value.split("-")[1].toInt()
                    keyValueMap[uid] = score
                } catch (e: Exception) {
                    println("Error processing key: $key, value: $value, error: ${e.message}")
                }
            }
        }
        val sortedEntries = keyValueMap.entries
            .sortedByDescending { it.value }
            .take(500)
        return sortedEntries.mapIndexed { index, entry ->
            val uid = entry.key
            val user = userRepository.findUserById(uid)!!
            val suiAdd = user.suiAddress
            val tonAdd = user.ton.tonWallet
            val evmAdd = user.evm.evmWallet
            val tgId = user.tgName
            val (showAdd, addressType) = when {
                suiAdd.isNotEmpty() -> suiAdd to 3
                !tonAdd.isNullOrEmpty() -> tonAdd to 1
                !evmAdd.isNullOrEmpty() -> evmAdd to 0
                else -> tgId to 2
            }
            val engageScore = EngagementScore(
                score = entry.value
            )
            UserWiseScore(
                userId = entry.key,
                address = showAdd,
                addressType = addressType,
                engagementScore = engageScore,
                rank = index + 1 // 设置排名
            )
        }
    }

    fun getUserRank(userId: Long): Int {
        if (redisTemplate.opsForValue().get("top1000-user:$userId") != null) {
            return redisTemplate.opsForValue().get("top1000-user:$userId")!!.split("-")[0].toInt()
        }
        // userId在列表中不存在，返回-1
        return -1
    }

    fun getUserSuiRank(userId: Long): Int {
        if (redisTemplate.opsForValue().get("top500-sui-user:$userId") != null) {
            return redisTemplate.opsForValue().get("top500-sui-user:$userId")!!.split("-")[0].toInt()
        }
        // userId在列表中不存在，返回-1
        return -1
    }

    fun getUserRankScoreFromRedis(userId: Long): Int {
        if (redisTemplate.opsForValue().get("top1000-user:$userId") != null) {
            return redisTemplate.opsForValue().get("top1000-user:$userId")!!.split("-")[1].toInt()
        }
        // userId在列表中不存在，返回-1
        return -1
    }

    fun addUserShareLink(userDcTgShareLink: UserDcTgShareLink): Pair<List<UserDcTgShareLink>?, String> {
        val userId = userDcTgShareLink.userId
        val socialType = userDcTgShareLink.socialType
        val link = userDcTgShareLink.shareLink
        val userShareLinks = userWiseScoreRepo.getUserShareLink(userId, socialType)

        if (socialType == SocialType.DISCORD.code && isDcValidUrl(link)) {
            val existGuilds = userShareLinks.filter { it.socialType == SocialType.DISCORD.code }
                .map { discordService.getGuildByUrl(it.shareLink) }.toSet()
            val guild = discordService.getGuildByUrl(link)
            if (!existGuilds.contains(guild)) {
                val isAdmin = discordService.isUserGuildAdminByLink(userId, link)
                if (!isAdmin.result) {
                    return null to isAdmin.message
                }
                userWiseScoreRepo.addUserShareLink(userId, socialType, link)
            }
        } else if (socialType == SocialType.TELEGRAM.code && isTgValidUrl(link)) {
            val tgLinks =
                userShareLinks.filter { it.socialType == SocialType.TELEGRAM.code }.map { it.shareLink }.toSet()
            if (!tgLinks.contains(link)) {
                val isAdmin = telegramService.isUserAdministratorByUrl(userId, link)
                if (!isAdmin) {
                    return null to "Please submit a telegram group/channel which you're the owner or admin."
                }
                userWiseScoreRepo.addUserShareLink(userId, socialType, link)
            }
        }
        return getUserShareLinks(userId) to ""
    }

    fun getUserSBTLink(
        userId: Long,
        activityId: Int = tonSocietySBTActivityId,
        sbtId: Long = -1,
        groupId: Long
    ): Map<String, String> {
        val tgUser = userService.getTonWalletByUserId(userId)
        val tonAddress = tgUser.tonWallet ?: ""
        if (sbtId != -1L) {
            val reward = participantRepo.getUserSbtReward(sbtId, userId, groupId)
            val hasHoldSbtCredential = credentialRepository.getCredentialByGroupId(groupId)
                .any { it.labelType == 66 }
            if ((reward == null || reward.claimType < 1) && !hasHoldSbtCredential) {
                return mapOf("status" to "error", "link" to "Task Not Finished")
            }
        }
        val userSbt = sbtWhiteListRepo.getUserSbtByUidAddressActivityId(userId, tonAddress, activityId)
        val sbtLink = userSbt?.sbtLink
        val addressSbtLink = sbtWhiteListRepo.getUserSbtByAddress(tonAddress)?.sbtLink
        println("sbt link is $sbtLink && addressSbtLink is $addressSbtLink && activityId is $activityId")

        // [STAG ENV]
        if (!env.activeProfiles.contains("prod")) {
            if (userSbt != null) {
                if (userSbt.claimedType != SBTRewardClaimType.CLAIMED) {
                    // val claimedType = getSBTClaimedStatus(activityId, tonAddress)

                    if ((userSbt.claimedType ?: 0) < 3) {
                        sbtWhiteListRepo.updateUserSBTClaimedType(userId, tonAddress, activityId, 3)
                        addUserReward(3, sbtId, userId, groupId, 3)
                    }
                }
            }
            return mapOf("status" to "Minting", "link" to "sbtLink", "message" to "stag mock minted")
        }

        val ogSbtList = arrayOf(
            "https://id.ton.org/t-book-connector",
            "https://id.ton.org/t-book-builder",
            "https://id.ton.org/t-book-influencer",
            "https://id.ton.org/t-book-traiblazer",
            "https://id.ton.org/t-book-visionary",
            "https://id.ton.org/t-book-legend"
        )

        // [PROD ENV]
        if (tonAddress != "") {
            if (!sbtLink.isNullOrEmpty() && sbtLink.trim() !in ogSbtList) {
                // update sbt claimed type
                if (userSbt.claimedType != SBTRewardClaimType.CLAIMED) {
                    val claimedType = getSBTClaimedStatus(activityId, tonAddress)
                    if ((userSbt.claimedType ?: 0) < claimedType) {
                        sbtWhiteListRepo.updateUserSBTClaimedType(userId, tonAddress, activityId, claimedType)
                        addUserReward(3, sbtId, userId, groupId, claimedType)
                    }
                }
                return mapOf("status" to "hasMinted", "link" to sbtLink)
            }
//            else if (sbtLink.isNullOrEmpty() && !addressSbtLink.isNullOrEmpty()) {
//                // user has unbounded ton wallet, then login by tg
//                val user = userService.getUserById(userId)!!
//                val userSBTList = UserSBTList(
//                    userId = userId,
//                    address = tonAddress,
//                    addressType = 1,
//                    avatar = user.displayAvatar,
//                    activityId = activityId,
//                    sbtLink = addressSbtLink
//                )
//                sbtWhiteListRepo.createUserSBT(userSBTList)
//                return mapOf("status" to "hasMinted", "link" to addressSbtLink)
//            }
            else {
                try {
                    val response = webClient
                        .post()
                        .uri("https://id.ton.org/v1/activities/$activityId/rewards")
                        .header("x-api-key", tonXApiKey)
                        .header("x-partner-id", tonXPartnerId)
                        .header("Content-Type", "application/json")
                        .body(Mono.just(mapOf("wallet_address" to tonAddress)), Map::class.java)
                        .retrieve()
                        .onStatus({ status -> status.is4xxClientError }, { clientResponse ->
                            clientResponse.bodyToMono(String::class.java).flatMap { errorBody ->
                                println("Error response from server: $errorBody")
                                Mono.error(RuntimeException(errorBody))
                            }
                        })
                        .bodyToMono(String::class.java)
                        .block()
                    val json = JSONObject(response)
                    val rewardLink = if (json.getString("status") == "success") {
                        json.getJSONObject("data").getString("reward_link")
                    } else {
                        ""
                    }
                    if (rewardLink != "") {
                        addUserReward(3, sbtId, userId, groupId, 3)
                    }
                    insertSBTLinkRecord(rewardLink, userId, tonAddress, activityId)
                    return mapOf("status" to "Minting", "link" to rewardLink)
                } catch (e: Exception) {
                    val errorMessage = e.message ?: "Unknown error"
                    val json = JSONObject(errorMessage)
                    return if (json.getString("status") == "error") {
                        if (json.getString("message") == "reward link with such activity id and wallet address already created"
                            || json.getString("message").contains("allowlist entry with such activity id")
                        ) {
                            val mintedUrl = getUserMintedSBTUrl(userId, tonAddress, activityId)
                            if (mintedUrl.isNotEmpty()) {
                                mapOf("status" to "Minting", "link" to mintedUrl)
                            } else {
                                mapOf("status" to "error", "link" to json.getString("message"))
                            }
                        }
                        mapOf("status" to "error", "link" to json.getString("message"))
                    } else {
                        mapOf("status" to "error", "link" to errorMessage)
                    }
                }
            }
        } else {
            return mapOf("status" to "error", "link" to "Invalid ton address")
        }
    }

    fun getSuiUserRewardByUidAndTx(userId: Long, txHash: String): UserReward? {
        return participantRepo.getUserSuiSbtRewardByUidAndTx(userId, txHash)
    }

    fun addUserReward(
        rewardType: Int,
        rewardId: Long,
        userId: Long,
        groupId: Long,
        claimType: Int,
        suiSbtObjectId: String = "",
        txHash: String = ""
    ) {
        val userReward = UserReward(
            rewardId = rewardId,
            rewardType = rewardType,
            suiSbtObjectId = suiSbtObjectId,
            txHash = txHash,
            userId = userId,
            groupId = groupId,
            claimType = claimType
        )
        if (participantRepo.getClaimTypeByUserId(
                rewardId,
                userId,
                groupId
            ) != null
        ) {
            if (participantRepo.getClaimTypeByUserId(rewardId, userId, groupId) != claimType) {
                participantRepo.updateUserReward(userReward)
            }
        } else {
            println(" add reward " + userReward.claimType + " " + rewardId + " userId " + userId)
            participantRepo.addUserRewardResult(userReward)
        }
    }

    fun getUserMintedSBTUrl(userId: Long, tonAddress: String, activityId: Int): String {
        val response = webClient
            .get()
            .uri("https://id.ton.org/v1/activities/$activityId/rewards/$tonAddress")
            .header("x-api-key", tonXApiKey)
            .header("x-partner-id", tonXPartnerId)
            .header("Content-Type", "application/json")
            .retrieve()
            .onStatus({ status -> status.is4xxClientError }, { clientResponse ->
                clientResponse.bodyToMono(String::class.java).flatMap { errorBody ->
                    println("Error response from server: $errorBody")
                    Mono.error(RuntimeException(errorBody))
                }
            })
            .bodyToMono(String::class.java)
            .block()
        val json = JSONObject(response)
        val rewardLink = if (json.getString("status") == "success") {
            json.getJSONObject("data").getString("reward_link")
        } else {
            ""
        }
        insertSBTLinkRecord(rewardLink, userId, tonAddress, activityId)
        return rewardLink
    }


    fun getSBTClaimedStatus(activityId: Int, tonAddress: String): Int {
        if (activityId == 0) {
            return SBTRewardClaimType.NOT_GENERATE_LINK
        }
        var status = ""
        try {
            val response = webClient
                .get()
                .uri("https://id.ton.org/v1/activities/$activityId/rewards/$tonAddress/status")
                .header("x-api-key", tonXApiKey)
                .header("x-partner-id", tonXPartnerId)
                .header("Content-Type", "application/json")
                .retrieve()
                .onStatus({ status -> status.is4xxClientError }, { clientResponse ->
                    clientResponse.bodyToMono(String::class.java).flatMap { errorBody ->
                        println("Error response from server: $errorBody")
                        Mono.error(RuntimeException(errorBody))
                    }
                })
                .bodyToMono(String::class.java)
                .block()
            val json = JSONObject(response)
            status = if (json.getString("status") == "success") {
                json.getJSONObject("data").getString("status")
            } else {
                ""
            }
        } catch (e: Exception) {
            println("$tonAddress get activity $activityId sbt claim status error: $e ")
        }
        return when (status) {
            "NOT_CLAIMED" -> {
                SBTRewardClaimType.ELIGIBLE
            }

            "CLAIMED" -> {
                SBTRewardClaimType.MINTING
            }

            "MINTED" -> {
                SBTRewardClaimType.MINTING
            }

            "RECEIVED" -> {
                SBTRewardClaimType.CLAIMED

            }

            else -> {
                SBTRewardClaimType.DEFAULT
            }
        }

    }

    fun insertSBTLinkRecord(rewardLink: String, userId: Long, tonAddress: String, activityId: Int) {
        if (rewardLink != "") {
            val sbtWhiteList =
                sbtWhiteListRepo.getUserSbtByUidAddressActivityId(userId, tonAddress, activityId)
            if (sbtWhiteList != null) {
                sbtWhiteListRepo.updateUserSBTLink(
                    userId,
                    tonAddress,
                    activityId,
                    rewardLink
                )
            } else {
                val user = userService.getUserById(userId)!!
                val userSBTList = UserSBTList(
                    userId = userId,
                    address = tonAddress,
                    addressType = 1,
                    avatar = user.displayAvatar,
                    activityId = activityId,
                    sbtLink = rewardLink
                )
                println(userSBTList.toString())
                sbtWhiteListRepo.createUserSBT(userSBTList)
            }
        }
    }


    fun getUserShareLinks(userId: Long): List<UserDcTgShareLink> {
        return userWiseScoreRepo.getAllUserShareLink(userId)
    }

    fun isDcValidUrl(url: String): Boolean {
        val uri = URI(url)
        val host = uri.host
        return (host.equals("discord.com", true) || host.equals("discord.gg", true))
    }

    fun isTgValidUrl(url: String): Boolean {
        val uri = URI(url)
        val host = uri.host
        return (host.equals("t.me", true) || host.equals("telegram.org", true))
    }

    fun appendDiscordInfo(userId: Long, link: UserDcTgShareLink) {
        val guildId = discordService.getGuildByUrl(link.shareLink)
        link.memberCount = discordService.getGuildMemberCount(userId, guildId)
        link.title = discordService.getDcTitleByDcId(guildId)
    }

    fun appendTelegramInfo(link: UserDcTgShareLink) {
        link.memberCount = telegramService.getTgMemberCountByUrl(link.shareLink)
        link.title = telegramService.getTgInfo(link.shareLink)?.title().orEmpty()
    }

    fun checkNotCoinHolder(userId: Long): Boolean {
        val tonWallet = userService.getTonWalletByUserId(userId).tonWallet ?: return false
        try {
            val response = webClient
                .get()
                .uri(
                    "https://tonapi.io/v2/accounts/${tonWallet}/jettons"
                )
                .retrieve()
                .bodyToMono(String::class.java)
                .block();
            if (!response.isNullOrEmpty()) {
                val jsonObject = JSONObject(response)
                val balancesArray = jsonObject.getJSONArray("balances")

                for (i in 0 until balancesArray.length()) {
                    val balanceObject = balancesArray.getJSONObject(i)
                    val jettonObject = balanceObject.getJSONObject("jetton")
                    val symbol = jettonObject.getString("symbol")

                    if (symbol == "NOT") {
                        val balance = balanceObject.getString("balance").toBigInteger()
                        println("user is NotCoin holder，wallet = $tonWallet，balance = $balance")
                        return true
                    }
                }
            }
            return false
        } catch (e: Exception) {
            println(" check NotCoin holder error $e")
            return false
        }
    }

    fun checkUserNotCoinEvents(userId: Long): Boolean {
        val tonWallet = userService.getTonWalletByUserId(userId).tonWallet
        val eventsLimit = 100
        val response = webClient
            .get()
            .uri(
                "https://tonapi.io/v2/accounts/${tonWallet}/events?limit=${eventsLimit}"
            )
            .header("Authorization", "Bearer $tonConsoleBearer")
            .retrieve()
            .bodyToMono(String::class.java)
            .block();
        val json = JSONObject(response)
        try {
            val events = json.getJSONArray("events")

            for (i in 0 until events.length()) {
                val event = events.getJSONObject(i)
                val actions = event.getJSONArray("actions")

                for (j in 0 until actions.length()) {
                    val action = actions.getJSONObject(j)

                    if (action.getString("type") == "JettonTransfer") {
                        val jettonTransfer = action.getJSONObject("JettonTransfer")
                        val jetton = jettonTransfer.getJSONObject("jetton")
                        val name = jetton.getString("name")

                        if (name == "Notcoin" && jettonTransfer.getString("amount")
                                .toBigInteger() > 0.toBigInteger()
                        ) {
                            println("the user has transferred Notcoin, amount : ${jettonTransfer.getString("amount")}")
                            return true
                        }
                    }

                    if (action.getString("type") == "JettonSwap") {
                        val jettonSwap = action.getJSONObject("JettonSwap")
                        val amountOut = jettonSwap.getString("amount_out")
                        val amountIn = jettonSwap.getString("amount_in")
                        var name = ""
                        var amount = ""
                        if (amountOut != "") {
                            val jetton = jettonSwap.getJSONObject("jetton_master_out")
                            name = jetton.getString("name")
                            amount = amountOut
                        } else if (amountIn != "") {
                            val jetton = jettonSwap.getJSONObject("jetton_master_in")
                            name = jetton.getString("name")
                            amount = amountIn
                        }
                        if (name == "Notcoin" && amount.toBigInteger() > 0.toBigInteger()) {
                            println("the user has swap Notcoin, amount : ${amount}")
                            return true
                        }
                    }
                }
            }
        } catch (e: Exception) {
            println("check user NotCoin events error : $e")
        }
        return false
    }

    fun checkUserStakeEvents(userId: Long): Boolean {
        val tonWallet = userService.getTonWalletByUserId(userId).tonWallet
        val eventsLimit = 100
        val response = webClient
            .get()
            .uri(
                "https://tonapi.io/v2/accounts/${tonWallet}/events?limit=${eventsLimit}"
            )
            .header("Authorization", "Bearer $tonConsoleBearer")
            .retrieve()
            .bodyToMono(String::class.java)
            .block();
        val json = JSONObject(response)
        try {
            val events = json.getJSONArray("events")

            for (i in 0 until events.length()) {
                val event = events.getJSONObject(i)
                val actions = event.getJSONArray("actions")

                for (j in 0 until actions.length()) {
                    val action = actions.getJSONObject(j)

                    if (action.getString("type") == "DepositStake") {
                        val depositStake = action.getJSONObject("DepositStake")
                        val amount = depositStake.getLong("amount")
                        val pool = depositStake.getJSONObject("pool")
                        val poolName = pool.optString("name", "")
                        if (depositStake != null && amount > 0 && poolName == "Tonstakers") {
                            println("user has stake event, amount : ${amount}")
                            return true
                        }
                    }
                }
            }
        } catch (e: Exception) {
            println("check user stake events error : $e")
        }
        return false
    }

    fun checkUserLiquidityProvideEvents(userId: Long): Boolean {
        val tonWallet = userService.getTonWalletByUserId(userId).tonWallet
        val eventsLimit = 100
        val response = webClient
            .get()
            .uri(
                "https://tonapi.io/v2/accounts/${tonWallet}/events?limit=${eventsLimit}"
            )
            .header("Authorization", "Bearer $tonConsoleBearer")
            .retrieve()
            .bodyToMono(String::class.java)
            .block();
        val json = JSONObject(response)
        try {
            val events = json.getJSONArray("events")

            for (i in 0 until events.length()) {
                val event = events.getJSONObject(i)
                val actions = event.getJSONArray("actions")

                for (j in 0 until actions.length()) {
                    val action = actions.getJSONObject(j)


                    //stonFi ProvideLiquidity
                    if (action.getString("type") == "JettonTransfer") {
                        val jettonTransfer = action.getJSONObject("JettonTransfer")
                        if (jettonTransfer.has("comment")) {
                            val comment = jettonTransfer.getString("comment")

                            if (comment == "Call: StonfiProvideLiquidity") {
                                println("user has stonFi Liquidity Provide, comment : ${comment}")
                                return true
                            }
                        }
                    }

                    //DeDust ProvideLiquidity
                    if (action.getString("type") == "JettonMint") {
                        val jettonMint = action.getJSONObject("JettonMint")
                        val jetton = jettonMint.getJSONObject("jetton")
                        if (jetton.has("symbol")) {
                            val symbol = jetton.getString("symbol")
                            val name = jetton.getString("name")
                            if (symbol == "LP") {
                                println("user has deDust Liquidity Provide, name : ${name}")
                                return true
                            }
                        }
                    }

                }
            }
        } catch (e: Exception) {
            println("check user Liquidity Provide events error : $e")
        }
        return false
    }

    data class LiquidityFlags(
        var hasProvideUSDT: Boolean = false,
        var hasProvideTON: Boolean = false,
        var hasProvideTsTON: Boolean = false,
        var hasProvideStTON: Boolean = false
    ) {
        fun hasValidLiquidity(): Boolean =
            hasProvideUSDT && (hasProvideTON || hasProvideStTON || hasProvideTsTON)

        fun updateFromJetton(name: String, symbol: String) {
            when {
                name == "Tether USD" || symbol == "USD₮" ->
                    hasProvideUSDT = true

                name == "Tonstakers TON" || symbol == "tsTON" ->
                    hasProvideTsTON = true

                name == "Staked TON" || symbol == "stTON" ->
                    hasProvideStTON = true

                symbol == "pTON" || symbol == "UKWN" ->
                    hasProvideTON = true
            }
        }
    }

    // Ston.fi Provide Liquidity
    fun checkUserStonFiLiquidityProvideEvents(userId: Long): Boolean {
        val tonWallet = userService.getTonWalletByUserId(userId).tonWallet
        val eventsLimit = 100
        val response = webClient
            .get()
            .uri(
                "https://tonapi.io/v2/accounts/${tonWallet}/events?limit=${eventsLimit}"
            )
            .header("Authorization", "Bearer $tonConsoleBearer")
            .retrieve()
            .bodyToMono(String::class.java)
            .block();
        val json = JSONObject(response)
        try {
            val events = json.getJSONArray("events")

            val stonFiAddressV1 = "0:779dcc815138d9500e449c5291e7f12738c23d575b5310000f6a253bd607384e"

            val v1Flags = LiquidityFlags()
            val v2Flags = LiquidityFlags()

            for (i in 0 until events.length()) {
                val event = events.getJSONObject(i)
                val actions = event.getJSONArray("actions")

                for (j in 0 until actions.length()) {
                    val action = actions.getJSONObject(j)

                    if (action.getString("type") == "JettonTransfer") {
                        val jettonTransfer = action.getJSONObject("JettonTransfer")
                        val jetton = jettonTransfer.getJSONObject("jetton")
                        val symbol = jetton.optString("symbol", "")
                        val name = jetton.optString("name", "")
                        val comment = jettonTransfer.optString("comment", "")

                        // ProvideLiquidity V1
                        val recipient = jettonTransfer.getJSONObject("recipient")
                        val recipientName = recipient.optString("name", "")
                        val recipientAddress = recipient.optString("address", "")
                        if ((recipientName == "STON.fi Dex" && recipientAddress == stonFiAddressV1)
                            && comment == "Call: StonfiProvideLiquidity"
                        ) {
                            v1Flags.updateFromJetton(name, symbol)
                        }

                        // ProvideLiquidity V2
                        if (comment == "Call: StonfiProvideLpV2") {
                            v2Flags.updateFromJetton(name, symbol)
                        }
                    }
                }

                if (v1Flags.hasValidLiquidity()) {
                    println("user has ston.fi Liquidity Provide V1")
                    return true
                }

                if (v2Flags.hasValidLiquidity()) {
                    println("user has ston.fi Liquidity Provide V2")
                    return true
                }
            }
        } catch (e: Exception) {
            println("check user ston.fi  Liquidity Provide events error : $e")
        }
        return false
    }

    // Dedust Provide Liquidity
    // stTON/USDT 、 TON/USDT、tsTON/USDT
    fun checkUserDedustLiquidityProvideEvents(userId: Long): Boolean {
        val tonWallet = userService.getTonWalletByUserId(userId).tonWallet
        val eventsLimit = 100
        val response = webClient
            .get()
            .uri(
                "https://tonapi.io/v2/accounts/${tonWallet}/events?limit=${eventsLimit}"
            )
            .header("Authorization", "Bearer $tonConsoleBearer")
            .retrieve()
            .bodyToMono(String::class.java)
            .block();
        val json = JSONObject(response)
        try {
            val events = json.getJSONArray("events")

            val stTonUsdtPoolAddress = "0:a6f76cc50642defea7050e9ed606f23a245483b26e166c33ef67bc4d77b9cf2f"
            val tonUsdtPoolAddress = "0:3e5ffca8ddfcf36c36c9ff46f31562aab51b9914845ad6c26cbde649d58a5588"
            val tsTonUsdtPoolAddress = "0:6487b31ce35d564d8174a34f3932dc09a58a6f1a164e301a61848173129ce554"

            for (i in 0 until events.length()) {
                val event = events.getJSONObject(i)
                val actions = event.getJSONArray("actions")

                for (j in 0 until actions.length()) {
                    val action = actions.getJSONObject(j)

                    if (action.getString("type") == "JettonMint") {
                        val jettonMint = action.getJSONObject("JettonMint")
                        val jetton = jettonMint.getJSONObject("jetton")
                        val name = jetton.optString("name", "")
                        val addresss = jetton.optString("address", "")
                        val symbol = jetton.optString("symbol", "")
                        if (symbol == "LP" && name == "DeDust Pool: stTON/USDT" && addresss == stTonUsdtPoolAddress) {
                            println("【Dedust】 $name")
                            return true
                        }
                        if (symbol == "LP" && name == "DeDust Pool: TON/USDT" && addresss == tonUsdtPoolAddress) {
                            println("【Dedust】 $name")
                            return true
                        }
                        if (symbol == "LP" && name == "DeDust Pool: tsTON/USDT" && addresss == tsTonUsdtPoolAddress) {
                            println("【Dedust】 $name")
                            return true
                        }
                    }
                }
            }
        } catch (e: Exception) {
            println("check user Liquidity Provide events error : $e")
        }
        return false
    }


    fun readResourceFile(filePath: String): String {
        // 获取资源文件的输入流
        val inputStream = object {}.javaClass.getResourceAsStream(filePath)
            ?: throw IllegalArgumentException("File not found: $filePath")

        // 读取文件内容为字符串
        return inputStream.bufferedReader(StandardCharsets.UTF_8).use(BufferedReader::readText)
    }

    fun extractFieldsFromJson(jsonContent: String): List<Activity> {
        val jsonArray = JSONArray(jsonContent)
        val activities = mutableListOf<Activity>()

        for (i in 0 until jsonArray.length()) {
            val jsonObject = jsonArray.getJSONObject(i)
            val id = jsonObject.getString("id")
            val EQ = jsonObject.getString("EQ")
            val UQ = jsonObject.getString("UQ")
            activities.add(Activity(id, EQ, UQ))
        }

        return activities
    }

    fun updateSBTLink(): Map<Long, String> {
        val jsonContent = readResourceFile("/static/temp.json")

        val activities = extractFieldsFromJson(jsonContent)

        val newSBTLinksMap = mutableMapOf<Long, String>()

        for (activity in activities) {
            val eqTonUser = userService.getTonWalletByAddress(activity.EQ)
            val uqTonUser = userService.getTonWalletByAddress(activity.UQ)
            val tonUser = eqTonUser ?: uqTonUser
            if (tonUser != null) {
                val sbtList = sbtWhiteListRepo.getUserSbtById(tonUser.userId)
                if (sbtList != null) {
                    if (sbtList.address == tonUser.tonWallet && sbtList.sbtLink.isNullOrEmpty()) {
                        val newSBTLink = "https://id.ton.org/welcome/" + activity.id
                        newSBTLinksMap[sbtList.userId] = newSBTLink
                        println("userId is ${sbtList.userId}, address is ${sbtList.address}, sbt link is ${sbtList.sbtLink}, new link is $newSBTLink")
//                        sbtWhiteListRepo.updateUserSBTLink(sbtList.userId, sbtList.address!!, newSBTLink)
                    }
                } else {
                    val newSBTLink = "https://id.ton.org/welcome/" + activity.id
                    newSBTLinksMap[tonUser.userId] = "new$newSBTLink"
                }
            }
        }
        return newSBTLinksMap
    }

    fun checkUserStormTradeEvents(userId: Long): Boolean {
        val tonWallet = userService.getTonWalletByUserId(userId).tonWallet
        val eventsLimit = 100
        val response = webClient
            .get()
            .uri(
                "https://tonapi.io/v2/accounts/${tonWallet}/events?limit=${eventsLimit}"
            )
            .header("Authorization", "Bearer $tonConsoleBearer")
            .retrieve()
            .bodyToMono(String::class.java)
            .block();
        val json = JSONObject(response)
        try {
            val events = json.getJSONArray("events")
            val stormTradeActivity = StormTradeActivity()

            for (i in 0 until events.length()) {
                val event = events.getJSONObject(i)
                val actions = event.getJSONArray("actions")
                val timestamp = Instant.ofEpochSecond(event.getLong("timestamp"))

                for (j in 0 until actions.length()) {
                    val action = actions.getJSONObject(j)

                    // storm trade vault -> TON
                    if (action.getString("type") == "SmartContractExec") {
                        val smartContractExec = action.getJSONObject("SmartContractExec")
                        val operation = smartContractExec.getString("operation")
                        val amount = BigDecimal(smartContractExec.getLong("ton_attached"))

                        if (operation == "StormVaultStake") {
                            stormTradeActivity.timestamp = timestamp
                            stormTradeActivity.currency = "TON"
                            stormTradeActivity.amount = amount
                            return true
//                            println("找到满足条件的 stormTrade stake 【${stormTradeActivity.currency}】 交易, amount :  ${stormTradeActivity.amount}")
                        }
                    }

                    // storm trade vault -> USDT
                    if (action.getString("type") == "JettonTransfer") {
                        val jettonTransfer = action.getJSONObject("JettonTransfer")
                        val comment = jettonTransfer.optString("comment", "")
                        val jetton = jettonTransfer.getJSONObject("jetton")
                        val currency = jetton.optString("name", "")
                        val amount = BigDecimal(jettonTransfer.getString("amount"))

                        if (comment == "Call: 0xc89a3ee4") {
                            stormTradeActivity.timestamp = timestamp
                            stormTradeActivity.currency = currency
                            stormTradeActivity.amount = amount
                            return true
//                            println("找到满足条件的 stormTrade stake 【${currency}】 交易, amount :  ${stormTradeActivity.amount}")
                        }
                    }
                }
            }
        } catch (e: Exception) {
            println("check user stormTrade events error : $e")
        }
        return false
    }

    // Storm Trade: open a trade
    fun checkOpenATradeStormTradeEvents(userId: Long): Boolean {
        val tonWallet = userService.getTonWalletByUserId(userId).tonWallet
        val response = webClient
            .get()
            .uri(
                "https://api5.storm.tg/rewards/traders/rp/${tonWallet}"
            )
            .retrieve()
            .bodyToMono(String::class.java)
            .block();

        val json = JSONObject(response)
        val events = json.getJSONArray("events")

        try {
            var hasOpenPosition = false
            for (i in 0 until events.length()) {
                val event = events.getJSONObject(i)
                if (event.getString("code") == "OPEN_1_POSITION" && event.getBoolean("isActive")) {
                    println("Found active OPEN_1_POSITION event")
                    hasOpenPosition = true
                    break
                }
            }
            if (hasOpenPosition) {
                println("User has an active OPEN_1_POSITION event")
                return true
            }
        } catch (e: Exception) {
            println("check user open a trade events error : $e")
        }
        return false
    }

    // EVAA - borrow USDT
    fun checkUserEvaaBorrowEvents(userId: Long): Boolean {
        val tonWallet = userService.getTonWalletByUserId(userId).tonWallet
        val eventsLimit = 100
        val response = webClient
            .get()
            .uri(
                "https://tonapi.io/v2/accounts/${tonWallet}/events?limit=${eventsLimit}"
            )
            .header("Authorization", "Bearer $tonConsoleBearer")
            .retrieve()
            .bodyToMono(String::class.java)
            .block();
        val json = JSONObject(response)
        val evaaAddress = "0:bcad466a47fa565750729565253cd073ca24d856804499090c2100d95c809f9e"
        try {
            val events = json.getJSONArray("events")
            for (i in 0 until events.length()) {
                val event = events.getJSONObject(i)
                val actions = event.getJSONArray("actions")

                var hasEvaaWithdrawMaster = false
                var hasEvaaBorrowUSDT = false
//                var hasEvaaBorrowTON = false

                for (j in 0 until actions.length()) {
                    val action = actions.getJSONObject(j)

                    if (action.getString("type") == "SmartContractExec") {
                        val smartContractExec = action.getJSONObject("SmartContractExec")
                        val operation = smartContractExec.getString("operation")
                        val contract = smartContractExec.getJSONObject("contract")
                        if (contract.optString("address") == evaaAddress && operation == "0x00000002") {
                            hasEvaaWithdrawMaster = true
                            println("[evaa] 0. has [withdraw master] in SmartContractExec")
                        }
                    }

                    if (action.getString("type") == "JettonTransfer") {
                        val jettonTransfer = action.getJSONObject("JettonTransfer")
                        val sender = jettonTransfer.getJSONObject("sender")
                        if (sender.optString("address") == evaaAddress) {
                            val jetton = jettonTransfer.getJSONObject("jetton")
                            val symbol = jetton.optString("symbol")
                            val symbolName = jetton.optString("name")
                            if (symbol == "USD₮" || symbolName == "Tether USD") {
                                // Borrow USDT
                                hasEvaaBorrowUSDT = true
                                println("1. [evaa] Borrow USDT verified")
                            }
                        }
                    }

                    if (hasEvaaWithdrawMaster && (hasEvaaBorrowUSDT)) {
                        println("2. [evaa] Borrow verified: USDT ${hasEvaaBorrowUSDT}")
                        return true
                    }
                }
            }
        } catch (e: Exception) {
            println("check user EvaaBorrow events error : $e")
        }
        return false
    }

    // EVAA - supply stTon or tsTon
    fun checkUserEvaaSupplyEvents(userId: Long): Boolean {
        val tonWallet = userService.getTonWalletByUserId(userId).tonWallet
        val eventsLimit = 100
        val response = webClient
            .get()
            .uri(
                "https://tonapi.io/v2/accounts/${tonWallet}/events?limit=${eventsLimit}"
            )
            .header("Authorization", "Bearer $tonConsoleBearer")
            .retrieve()
            .bodyToMono(String::class.java)
            .block();
        val json = JSONObject(response)

        val evaaAddress = "0:bcad466a47fa565750729565253cd073ca24d856804499090c2100d95c809f9e"

        try {
            val events = json.getJSONArray("events")
            for (i in 0 until events.length()) {
                val event = events.getJSONObject(i)
                val actions = event.getJSONArray("actions")

                var hasEvaaSupplyStTON = false
                var hasEvaaSupplyTsTON = false

                for (j in 0 until actions.length()) {
                    val action = actions.getJSONObject(j)

                    if (action.getString("type") == "JettonTransfer") {
                        val jettonTransfer = action.getJSONObject("JettonTransfer")
                        val recipient = jettonTransfer.getJSONObject("recipient")

                        val comment = jettonTransfer.optString("comment", "")
                        val recipientAddress = recipient.getString("address")

                        if (recipientAddress == evaaAddress && comment == "Call: 0x00000001") {

                            val jetton = jettonTransfer.getJSONObject("jetton")
                            val symbol = jetton.optString("symbol", "")
                            val symbolName = jetton.optString("name", "")
                            // supply stTON
                            if (symbol == "stTON" || symbolName == "Staked TON") {
                                println("[evaa] supply stTON")
                                hasEvaaSupplyStTON = true
                            }
                            // supply tsTON // (symbolName == "Tonstakers TON" 还没校验)
                            if (symbol == "tsTON" || symbolName == "Tonstakers TON") {
                                println("[evaa] supply tsTON")
                                hasEvaaSupplyTsTON = true
                            }
                        }
                    }

                    if (hasEvaaSupplyStTON || hasEvaaSupplyTsTON) {
                        println("end. SupplyStTON: ${hasEvaaSupplyStTON} or SupplyTsTON ${hasEvaaSupplyTsTON}")
                        return true
                    }
                }
            }
        } catch (e: Exception) {
            println("check user EvaaBorrow events error : $e")
        }
        return false
    }

    // Bemo - stake Ton
    fun checkUserBemoStakeTonEvents(userId: Long): Boolean {
        val tonWallet = userService.getTonWalletByUserId(userId).tonWallet
        val eventsLimit = 100
        val response = webClient
            .get()
            .uri(
                "https://tonapi.io/v2/accounts/${tonWallet}/events?limit=${eventsLimit}"
            )
            .header("Authorization", "Bearer $tonConsoleBearer")
            .retrieve()
            .bodyToMono(String::class.java)
            .block();
        val json = JSONObject(response)

        try {
            val events = json.getJSONArray("events")
            for (i in 0 until events.length()) {
                val event = events.getJSONObject(i)
                val actions = event.getJSONArray("actions")

                for (j in 0 until actions.length()) {
                    val action = actions.getJSONObject(j)

                    if (action.getString("type") == "TonTransfer") {
                        val tonTransfer = action.getJSONObject("TonTransfer")
                        val recipient = tonTransfer.getJSONObject("recipient")
                        val recipientName = recipient.optString("name", "")
                        val recipientAddress = recipient.optString("address", "")
                        if (recipientName == "stTON master" || recipientAddress == "0:cd872fa7c5816052acdf5332260443faec9aacc8c21cca4d92e7f47034d11892") {
                            println("bemo stake Ton verified")
                            return true
                        }
                    }
                    if (action.getString("type") == "JettonMint") {
                        val iettonMint = action.getJSONObject("JettonMint")
                        val jetton = iettonMint.getJSONObject("jetton")
                        val jettonAddress = jetton.optString("address", "")
                        if (jettonAddress == "0:cd872fa7c5816052acdf5332260443faec9aacc8c21cca4d92e7f47034d11892" || jettonAddress == "0:92c4664f1ea6b74ed9ce0e031a9fc0843348dfe87a58faea27fcd31e1608caaa") {
                            println("bemo stake Ton verified - v2+v3")
                            return true
                        }
                    }

                }
            }
        } catch (e: Exception) {
            println("check user bemo stake events error : $e")
        }
        return false
    }

    fun getTonAmountInSameDecimals(amount: Double, currency: String): Double {
        val priceInTon = getCoinPrice(currency) / getCoinPrice("TON")
        return amount * priceInTon
    }

    // ====================== Degen DeFi ======================

    val degenStartDate: Long = 1726070400 // 2024.09.12 00:00
    val degenEndDate: Long = 1764547200 // 2025.12.01 00:00

    val startDateInISO8601 = "2024-09-12T00:00:00Z"
    val endDateInISO8601 = "2024-11-17T00:00:00Z"

    data class VerificationResult(
        val isVerified: Boolean,
        val totalTvl: Double = 0.0
    )

    // 1. Hipo Stake TON 【maxAttempts max 10 times】
    private fun checkEventsForHipoStake(events: JSONArray, hipoAddess: String): VerificationResult {
        var totalTonTVL = 0.0

        for (i in 0 until events.length()) {
            val event = events.getJSONObject(i)
            val actions = event.getJSONArray("actions")

            for (j in 0 until actions.length()) {
                val action = actions.getJSONObject(j)
                if (action.getString("type") == "SmartContractExec" && action.optString("status") == "ok") {
                    val smartContractExec = action.getJSONObject("SmartContractExec")
                    val operation = smartContractExec.getString("operation")
                    val contract = smartContractExec.getJSONObject("contract")
                    val tonAttached = smartContractExec.optLong("ton_attached")
                    if (contract.optString("name") == "Hipo Treasury" && contract.optString("address") == hipoAddess) {
                        if (tonAttached > 0L) {
                            totalTonTVL += tonAttached
                        }
                        return VerificationResult(
                            isVerified = true,
                            totalTvl = totalTonTVL
                        )
                    }
                }
            }
        }
        return VerificationResult(isVerified = false)
    }

    fun checkHipoStakeTonEvents(userId: Long, credentialId: Long): Boolean {
        val tonWallet = userService.getTonWalletByUserId(userId).tonWallet
        val eventsLimit = 100
        val maxAttempts = 10

        val hipoAddress = "0:8bc991cfe177bc7e9721433efa3befd199485a55cffd040a06c89af026b71bcf"

        var totalTonTVL = 0.0
        var foundVerifiedEvent = false

        try {
            var currentEndDate = degenEndDate
            var attemptCount = 0

            while (attemptCount < maxAttempts) {
                val events =
                    fetchEvents("https://tonapi.io/v2/accounts/${tonWallet}/events?limit=${eventsLimit}&start_date=${degenStartDate}&end_date=${currentEndDate}&initiator=true")

                if (events.length() == 0) {
                    break
                }
                val verificationResult = checkEventsForHipoStake(events, hipoAddress)
                if (verificationResult.isVerified) {
                    foundVerifiedEvent = true
                    if (verificationResult.totalTvl > 0) {
                        totalTonTVL += verificationResult.totalTvl
                    }
                    break
                }
                val firstEventTimestamp = events.getJSONObject(events.length() - 1).getLong("timestamp")
                if (firstEventTimestamp >= degenEndDate) {
                    break
                }
                currentEndDate = firstEventTimestamp
                attemptCount++
            }
        } catch (e: Exception) {
            println("check user hipo stake ton events error : $e")
        }

        if (foundVerifiedEvent && totalTonTVL > 0) {
            participantRepo.updateCredentialTVLAmount(userId, credentialId, totalTonTVL.toLong())
        }
        return foundVerifiedEvent
    }

    // 2. Aqua protocol
    fun checkAquaProtocolDepositAndMintEvents(userId: Long, credentialId: Long): Boolean {
        val tonWallet = userService.getTonWalletByUserId(userId).tonWallet
        val eventsLimit = 100

        val response = webClient
            .get()
            .uri(
                "https://tonapi.io/v2/accounts/${tonWallet}/events?limit=${eventsLimit}&start_date=${degenStartDate}&end_date=${degenEndDate}"
            )
            .header("Authorization", "Bearer $tonConsoleBearer")
            .retrieve()
            .bodyToMono(String::class.java)
            .block();
        val json = JSONObject(response)

        val aquaUSDAddress = "0:160f2c40452977a25d86d5130b3307a9af7bfa4deaf996cde388096178ab2182"

        var totalTonTVL = 0.0
        var foundVerifiedEvent = false

        try {
            val events = json.getJSONArray("events")
            for (i in 0 until events.length()) {
                val event = events.getJSONObject(i)
                val actions = event.getJSONArray("actions")

                for (j in 0 until actions.length()) {
                    val action = actions.getJSONObject(j)
                    if (action.getString("type") == "JettonTransfer" && action.optString("status") == "ok") {
                        val jettonTransfer = action.getJSONObject("JettonTransfer")
                        val recipientObj = jettonTransfer.getJSONObject("recipient")
                        val address = recipientObj.optString("address")
                        val amount = jettonTransfer.optDouble("amount")

                        val jettonObj = jettonTransfer.getJSONObject("jetton")
                        val currency = jettonObj.optString("symbol")
                        if (address == aquaUSDAddress && (currency == "tsTON" || currency == "stTON")) {
                            foundVerifiedEvent = true
                            if (amount > 0) {
                                totalTonTVL += getTonAmountInSameDecimals(amount, currency)
                            }
                        }
                    }
                }
            }
            if (foundVerifiedEvent && totalTonTVL > 0) {
                println("tonAttached - ${totalTonTVL}")
                participantRepo.updateCredentialTVLAmount(userId, credentialId, totalTonTVL.toLong())
            }
        } catch (e: Exception) {
            println("check user Aqua protocol deposit ton events error : $e")
        }
        return foundVerifiedEvent
    }

    fun countTonSLPInTon(tonSlpAmount: Double): Double {
        val rs = redisTemplate.opsForValue()
            .get("lp_ratio_0:e926764ff3d272c73ddeb836975c5521c025ad68e7919a25094e2de3198805f1")
        log.info { "new lp ratio fetched: $rs" }
        if (rs == null) return 1.4 // fallback
        val rate = rs.toLong()
        val tonValue = tonSlpAmount * (rate / **********)
        return tonValue
    }

    // 3. JVault stake ton-slp
    fun checkJVaultStakeTonSlpEvents(userId: Long, credentialId: Long): Boolean {
        val tonWallet = userService.getTonWalletByUserId(userId).tonWallet
        val eventsLimit = 100

        val response = webClient
            .get()
            .uri(
                "https://tonapi.io/v2/accounts/${tonWallet}/events?limit=${eventsLimit}&start_date=${degenStartDate}&end_date=${degenEndDate}"
            )
            .header("Authorization", "Bearer $tonConsoleBearer")
            .retrieve()
            .bodyToMono(String::class.java)
            .block();
        val json = JSONObject(response)

        val tONStormTONSLPAddress = "0:8d636010dd90d8c0902ac7f9f397d8bd5e177f131ee2cca24ce894f15d19ceea"
        val tONStormStakeStormsAddress = "0:6ca2c99c66b0fa1478a303ba9618bc39c28fda1fc50de37e618bddf98c9fd24c"

        var totalTonTVL = 0.0
        var foundVerifiedEvent = false

        try {
            val events = json.getJSONArray("events")
            for (i in 0 until events.length()) {
                val event = events.getJSONObject(i)
                val actions = event.getJSONArray("actions")
                for (j in 0 until actions.length()) {
                    val action = actions.getJSONObject(j)
                    if (action.getString("type") == "JettonTransfer" && action.optString("status") == "ok") {
                        val jettonTransfer = action.getJSONObject("JettonTransfer")
                        val jetton = jettonTransfer.getJSONObject("jetton")
                        val jettonAddress = jetton.getString("address")
                        val jettonName = jetton.getString("name")
                        val jettonSymbol = jetton.getString("symbol")
                        val amount = jettonTransfer.optDouble("amount")
                        if (jettonAddress == tONStormTONSLPAddress && jettonName == "TON Storm LP" && jettonSymbol == "TON-SLP") {
                            foundVerifiedEvent = true
                            if (amount > 0) {
                                totalTonTVL += countTonSLPInTon(amount)
                            }
                        }
                        if (jettonAddress == tONStormStakeStormsAddress && jettonName == "STORM" && jettonSymbol == "STORM") {
                            foundVerifiedEvent = true
                            if (amount > 0) {
                                totalTonTVL += countTonSLPInTon(amount)
                            }
                        }

                    }
                }
            }
            if (foundVerifiedEvent && totalTonTVL > 0) {
                println("totalTonTVL - ${totalTonTVL}")
                participantRepo.updateCredentialTVLAmount(userId, credentialId, totalTonTVL.toLong())
            }
        } catch (e: Exception) {
            println("check user JVault stake ton-slp events error : $e")
        }
        return foundVerifiedEvent
    }

    private fun checkEventsForDAOLamaDeposit(events: JSONArray, defiAddress: String): VerificationResult {
        var totalTonTVL = 0.0

        for (i in 0 until events.length()) {
            val event = events.getJSONObject(i)
            val actions = event.getJSONArray("actions")
            for (j in 0 until actions.length()) {
                val action = actions.getJSONObject(j)
                if (action.getString("type") == "SmartContractExec" && action.optString("status") == "ok") {
                    val smartContractExec = action.getJSONObject("SmartContractExec")
                    val operation = smartContractExec.getString("operation")
                    val contract = smartContractExec.getJSONObject("contract")
                    // ton amount
                    val tonAttached = smartContractExec.optLong("ton_attached")
                    if (contract.optString("address") == defiAddress) {
                        if (tonAttached > 0) {
                            totalTonTVL += tonAttached
                        }
                        return VerificationResult(
                            isVerified = true,
                            totalTvl = totalTonTVL
                        )
                    }
                }
            }
        }
        return VerificationResult(isVerified = false)
    }

    // 4. DAOLama Deposit TON
    fun checkDAOLamaDepositTonEvents(userId: Long, credentialId: Long): Boolean {
        val tonWallet = userService.getTonWalletByUserId(userId).tonWallet
        val eventsLimit = 100
        val maxAttempts = 5

        val dAOLamaTonDlpAddress = "0:a4793bce49307006d3f4e97d815fb4c78ff7655faecf8606111ae29f8d6b41f4"

        var totalTonTVL = 0.0
        var foundVerifiedEvent = false

        try {
            var currentEndDate = degenEndDate
            var attemptCount = 0

            while (attemptCount < maxAttempts) {
                val events =
                    fetchEvents("https://tonapi.io/v2/accounts/${tonWallet}/events?limit=${eventsLimit}&start_date=${degenStartDate}&end_date=${currentEndDate}&initiator=true")
                if (events.length() == 0) {
                    break
                }
                val verificationResult = checkEventsForDAOLamaDeposit(events, dAOLamaTonDlpAddress)
                if (verificationResult.isVerified) {
                    foundVerifiedEvent = true
                    if (verificationResult.totalTvl > 0) {
                        totalTonTVL += verificationResult.totalTvl
                    }
                    break
                }
                val firstEventTimestamp = events.getJSONObject(events.length() - 1).getLong("timestamp")
                if (firstEventTimestamp >= degenEndDate) {
                    break
                }
                currentEndDate = firstEventTimestamp
                attemptCount++
            }
        } catch (e: Exception) {
            println("check user daolama events error : $e")
        }

        if (foundVerifiedEvent && totalTonTVL > 0) {
            participantRepo.updateCredentialTVLAmount(userId, credentialId, totalTonTVL.toLong())
        }
        return foundVerifiedEvent
    }

    // 5. TON Hedge Deposit USDT 【maxAttempts max 10 times】
    private fun checkEventsForTONHedgeDepositUsdt(
        events: JSONArray,
        tONHedgeLPTokenAddress: String
    ): VerificationResult {
        var totalTonTVL = 0.0

        for (i in 0 until events.length()) {
            val event = events.getJSONObject(i)
            val actions = event.getJSONArray("actions")

            for (j in 0 until actions.length()) {
                val action = actions.getJSONObject(j)
                if (action.getString("type") == "JettonTransfer" && action.optString("status") == "ok") {
                    val jettonTransfer = action.getJSONObject("JettonTransfer")
                    val recipientObj = jettonTransfer.getJSONObject("recipient")
                    val recipientAddress = recipientObj.optString("address")
                    val jetton = jettonTransfer.getJSONObject("jetton")
                    val jettonName = jetton.getString("name")
                    val jettonSymbol = jetton.getString("symbol")
                    val comment = jettonTransfer.optString("comment")
                    val amount = jettonTransfer.optDouble("amount")
                    if (recipientAddress == tONHedgeLPTokenAddress && jettonName == "Tether USD" && jettonSymbol == "USD₮" && comment == "Call: 0xc89a3ee4") {
                        if (amount > 0) {
                            totalTonTVL += (amount / getCoinPrice("TON")) * 1000
                        }
                        return VerificationResult(
                            isVerified = true,
                            totalTvl = totalTonTVL
                        )
                    }

                }
            }
        }
        return VerificationResult(isVerified = false)
    }

    fun checkTONHedgeDepositUsdtEvents(userId: Long, credentialId: Long): Boolean {
        val tonWallet = userService.getTonWalletByUserId(userId).tonWallet
        val eventsLimit = 100
        val maxAttempts = 10

        val tONHedgeLPTokenAddress = "0:57668d751f8c14ab76b3583a61a1486557bd746beeebbd4b2a65418b3fdb5471"

        var totalTonTVL = 0.0
        var foundVerifiedEvent = false

        try {
            var currentEndDate = degenEndDate
            var attemptCount = 0

            while (attemptCount < maxAttempts) {
                val events =
                    fetchEvents("https://tonapi.io/v2/accounts/${tonWallet}/events?limit=${eventsLimit}&start_date=${degenStartDate}&end_date=${currentEndDate}&initiator=true")
                if (events.length() == 0) {
                    break
                }
                val verificationResult = checkEventsForTONHedgeDepositUsdt(events, tONHedgeLPTokenAddress)
                if (verificationResult.isVerified) {
                    foundVerifiedEvent = true
                    if (verificationResult.totalTvl > 0) {
                        totalTonTVL += verificationResult.totalTvl
                    }
                    break
                }
                val firstEventTimestamp = events.getJSONObject(events.length() - 1).getLong("timestamp")
                if (firstEventTimestamp >= degenEndDate) {
                    break
                }
                currentEndDate = firstEventTimestamp
                attemptCount++
            }
        } catch (e: Exception) {
            println("check user TON Hedge Deposit USDT events error : $e")
        }

        if (foundVerifiedEvent && totalTonTVL > 0) {
            println("totalTonTVL - ${totalTonTVL}")
            participantRepo.updateCredentialTVLAmount(userId, credentialId, totalTonTVL.toLong())
        }
        return foundVerifiedEvent
    }

    // 6. Settle TON Buy Mid-Risk Index with ton
    fun checkSettleTonEvents(userId: Long, credentialId: Long): Boolean {
        val tonWallet = userService.getTonWalletByUserId(userId).tonWallet
        val eventsLimit = 100

        val response = webClient
            .get()
            .uri("https://settleton.finance/apiV2/user/${tonWallet}")
            .retrieve()
            .bodyToMono(String::class.java)
            .block();
        val json = JSONObject(response)

        var foundVerifiedEvent = false

        val reward = json.optDouble("base_rewards_per_hour")
        if (reward > 0) {
            foundVerifiedEvent = true
        }

//        val settleTonAddress = "0:c47788ec4345895a7acf5881f1ca28ad5738ec7b9370dd0254888f01a7fd4a8d"
//        try {
//            val events = json.getJSONArray("events")
//            for (i in 0 until events.length()) {
//                val event = events.getJSONObject(i)
//                val actions = event.getJSONArray("actions")
//
//                var foundSTL_MID_1 = false
//                var foundSettleTonAddress = false
//
//                for (j in 0 until actions.length()) {
//                    val action = actions.getJSONObject(j)
//                    if (action.getString("type") == "JettonTransfer" && action.optString("status") == "ok") {
//                        val jettonTransfer = action.getJSONObject("JettonTransfer")
//                        val jetton = jettonTransfer.getJSONObject("jetton")
//                        val jettonSymbol = jetton.getString("symbol")
//                        if (jettonSymbol == "STL_MID_1") {
//                            foundSTL_MID_1 = true
//                        }
//                    }
//                    if(action.getString("type") == "SmartContractExec" && action.optString("status") == "ok") {
//                        val smartContractExec = action.getJSONObject("SmartContractExec")
//                        val operation = smartContractExec.getString("operation")
//                        val ton_attached = smartContractExec.optDouble("ton_attached")
//                        val contract = smartContractExec.getJSONObject("contract")
//                        val address = contract.getString("address")
//                        if(operation == "0x00000001" && address == settleTonAddress) {
//                            foundSettleTonAddress = true
//                            tonAmount = ton_attached
//                        }
//                    }
//
//                    if(foundSettleTonAddress && foundSTL_MID_1) {
//                        foundVerifiedEvent = true
//                        totalTonTVL += tonAmount
//                    }
//                }
//            }
//            if (foundVerifiedEvent && totalTonTVL > 0) {
//                println("totalTonTVL - ${totalTonTVL}")
//                participantRepo.updateCredentialTVLAmount(userId, credentialId, totalTonTVL.toLong())
//            }
//        } catch (e: Exception) {
//            println("check user [Settle TON Buy Mid-Risk Index with ton] events error : $e")
//        }
        return foundVerifiedEvent
    }

    // 7. Parraton Deposit tsTON/USDT
    fun checkParratonDepositTsTonOrUsdtEvents(userId: Long, credentialId: Long): Boolean {

        val tonWallet = userService.getTonWalletByUserId(userId).tonWallet
        val eventsLimit = 100

        val response = webClient
            .get()
            .uri(
                "https://tonapi.io/v2/accounts/${tonWallet}/events?limit=${eventsLimit}&start_date=${degenStartDate}&end_date=${degenEndDate}"
            )
            .header("Authorization", "Bearer $tonConsoleBearer")
            .retrieve()
            .bodyToMono(String::class.java)
            .block();
        val json = JSONObject(response)

        val parratonDepositAddress = "0:2b3c1f89500d2127c97f45778599a3745b48deb4d8fd22d6f3460adad9ac7133"
        val tsTonUsdtPoolAddress = "0:6487b31ce35d564d8174a34f3932dc09a58a6f1a164e301a61848173129ce554"

        var foundVerifiedEvent = false
        var totalTonTVL = 0.0

        try {
            val events = json.getJSONArray("events")
            for (i in 0 until events.length()) {
                val event = events.getJSONObject(i)
                val actions = event.getJSONArray("actions")

                for (j in 0 until actions.length()) {
                    val action = actions.getJSONObject(j)
                    if (action.getString("type") == "JettonTransfer" && action.optString("status") == "ok") {
                        val JettonTransfer = action.getJSONObject("JettonTransfer")
                        val recipientObj = JettonTransfer.getJSONObject("recipient")
                        val recipientAddress = recipientObj.getString("address")
                        val jetton = JettonTransfer.getJSONObject("jetton")
                        val name = jetton.optString("name", "")
                        val addresss = jetton.optString("address", "")
                        val symbol = jetton.optString("symbol", "")
                        val amount = JettonTransfer.optDouble("amount")
                        if (recipientAddress == parratonDepositAddress && symbol == "LP" && name == "DeDust Pool: tsTON/USDT" && addresss == tsTonUsdtPoolAddress) {
                            // amount 为 LP 的值， LP 的值和 tsTON 相等，因为是 pools 对，所以乘以2
                            totalTonTVL += (getTonAmountInSameDecimals(amount, "tsTON") * 2)
                            foundVerifiedEvent = true

                        }
                    }
                }
            }
            if (foundVerifiedEvent && totalTonTVL > 0) {
                println("totalTonTVL - ${totalTonTVL}")
                participantRepo.updateCredentialTVLAmount(userId, credentialId, totalTonTVL.toLong())
            }
        } catch (e: Exception) {
            println("check user [Parraton Deposit tsTON/USDT] events error : $e")
        }
        return foundVerifiedEvent
    }

    // 8. TonStable Deposit tsTON or stTON
    fun checkTonStableDepositTsTonOrSttonEvents(userId: Long, credentialId: Long): Boolean {
        val tonWallet = userService.getTonWalletByUserId(userId).tonWallet
        val eventsLimit = 100

        val response = webClient
            .get()
            .uri(
                "https://tonapi.io/v2/accounts/${tonWallet}/events?limit=${eventsLimit}&start_date=${degenStartDate}&end_date=${degenEndDate}"
            )
            .header("Authorization", "Bearer $tonConsoleBearer")
            .retrieve()
            .bodyToMono(String::class.java)
            .block();
        val json = JSONObject(response)

        val tonStableDepositAddress = "0:b606de2fc1c4a00b000194e7e097be466c6b82d06a515361ac64aaaa307bbe4f"

        var foundVerifiedEvent = false
        var totalTonTVL = 0.0

        try {
            val events = json.getJSONArray("events")
            for (i in 0 until events.length()) {
                val event = events.getJSONObject(i)
                val actions = event.getJSONArray("actions")

                for (j in 0 until actions.length()) {
                    val action = actions.getJSONObject(j)
                    if (action.getString("type") == "JettonTransfer" && action.optString("status") == "ok") {
                        val jettonTransfer = action.getJSONObject("JettonTransfer")
                        val recipientObj = jettonTransfer.getJSONObject("recipient")
                        val recipientAddress = recipientObj.optString("address")
                        val comment = jettonTransfer.optString("comment")
                        val amount = jettonTransfer.getDouble("amount")
                        val jetton = jettonTransfer.getJSONObject("jetton")
                        val currency = jetton.optString("symbol")
                        if (recipientAddress == tonStableDepositAddress && comment == "Call: 0x9843c87a"
                            && (currency == "tsTON" || currency == "stTON")
                        ) {
                            // tsTON & stTON 转换成 TON
                            totalTonTVL += getTonAmountInSameDecimals(amount, currency)
                            foundVerifiedEvent = true
                        }
                    }
                }
            }
            if (foundVerifiedEvent && totalTonTVL > 0) {
                println("totalTonTVL - ${totalTonTVL}")
                participantRepo.updateCredentialTVLAmount(userId, credentialId, totalTonTVL.toLong())
            }
        } catch (e: Exception) {
            println("check user TonStable Deposit tsTON or stTON events error : $e")
        }
        return foundVerifiedEvent
    }

    // 9. TonPools Deposit TON
    fun checkTonPoolsDepositTONEvents(userId: Long, credentialId: Long): Boolean {
        val tonWallet = userService.getTonWalletByUserId(userId).tonWallet
        val eventsLimit = 100

        val response = webClient
            .get()
            .uri(
                "https://tonapi.io/v2/accounts/${tonWallet}/events?limit=${eventsLimit}&start_date=${degenStartDate}&end_date=${degenEndDate}"
            )
            .header("Authorization", "Bearer $tonConsoleBearer")
            .retrieve()
            .bodyToMono(String::class.java)
            .block();
        val json = JSONObject(response)

        val TonPoolsDepositAddress = "0:3bcbd42488fe31b57fc184ea58e3181594b33b2cf718500e108411e115978be1"

        var foundVerifiedEvent = false
        var totalTonTVL = 0.0

        try {
            val events = json.getJSONArray("events")
            for (i in 0 until events.length()) {
                val event = events.getJSONObject(i)
                val actions = event.getJSONArray("actions")

                for (j in 0 until actions.length()) {
                    val action = actions.getJSONObject(j)
                    if (action.getString("type") == "SmartContractExec" && action.optString("status") == "ok") {
                        val smartContractExec = action.getJSONObject("SmartContractExec")
                        val contractObj = smartContractExec.getJSONObject("contract")
                        val contractAddress = contractObj.optString("address")
                        val operation = smartContractExec.optString("operation")
                        val amount = smartContractExec.getLong("ton_attached")
                        if (contractAddress == TonPoolsDepositAddress && operation == "0x21eeb607") {
                            totalTonTVL += amount
                            foundVerifiedEvent = true
                        }
                    }
                }
            }
            if (foundVerifiedEvent && totalTonTVL > 0) {
                println("totalTonTVL - ${totalTonTVL}")
                participantRepo.updateCredentialTVLAmount(userId, credentialId, totalTonTVL.toLong())
            }
        } catch (e: Exception) {
            println("check user TonPools Deposit TON events error : $e")
        }
        return foundVerifiedEvent
    }

    // 10. TONStakers Stake some ton
    fun checkTonStakersStakeTonEvents(userId: Long, credentialId: Long): Boolean {
        val tonWallet = userService.getTonWalletByUserId(userId).tonWallet
        val eventsLimit = 100

        val response = webClient
            .get()
            .uri(
                "https://tonapi.io/v2/accounts/${tonWallet}/events?limit=${eventsLimit}&start_date=${degenStartDate}&end_date=${degenEndDate}"
            )
            .header("Authorization", "Bearer $tonConsoleBearer")
            .retrieve()
            .bodyToMono(String::class.java)
            .block();
        val json = JSONObject(response)

        var totalTonTVL = 0.0
        var foundVerifiedEvent = false

        try {
            val events = json.getJSONArray("events")

            for (i in 0 until events.length()) {
                val event = events.getJSONObject(i)
                val actions = event.getJSONArray("actions")

                for (j in 0 until actions.length()) {
                    val action = actions.getJSONObject(j)

                    if (action.getString("type") == "DepositStake" && action.optString("status") == "ok") {
                        val depositStake = action.getJSONObject("DepositStake")
                        val amount = depositStake.getLong("amount")
                        val pool = depositStake.getJSONObject("pool")
                        val poolName = pool.optString("name", "")
                        if (depositStake != null && amount > 0 && poolName == "Tonstakers") {
                            println("user has stake event, amount : ${amount}")
                            foundVerifiedEvent = true
                            totalTonTVL += amount
                        }
                    }
                }
            }

            if (foundVerifiedEvent && totalTonTVL > 0) {
                println("totalTonTVL - ${totalTonTVL}")
                participantRepo.updateCredentialTVLAmount(userId, credentialId, totalTonTVL.toLong())
            }
        } catch (e: Exception) {
            println("check user TONStakers Stake TON events error : $e")
        }
        return foundVerifiedEvent
    }

    // 11. [Volume] Tradoor Long / Short on Perps
    fun checkTradoorLongOrShortEvents(userId: Long, credentialId: Long): Boolean {
        val tonWallet = userService.getTonWalletByUserId(userId).tonWallet
        val eventsLimit = 100

        val response = webClient
            .get()
            .uri(
                "https://tonapi.io/v2/accounts/${tonWallet}/events?limit=${eventsLimit}&start_date=${degenStartDate}&end_date=${degenEndDate}"
            )
            .header("Authorization", "Bearer $tonConsoleBearer")
            .retrieve()
            .bodyToMono(String::class.java)
            .block();
        val json = JSONObject(response)

        val tradoorLongOrShortAddress = "0:ff1338c9f6ed1fa4c264a19052bff64d10c8ad028628f52b2e0f4b357a12227e"

        var foundVerifiedEvent = false
        var totalTonTVL = 0.0

        try {
            val events = json.getJSONArray("events")
            for (i in 0 until events.length()) {
                val event = events.getJSONObject(i)
                val actions = event.getJSONArray("actions")

                for (j in 0 until actions.length()) {
                    val action = actions.getJSONObject(j)

                    if (action.getString("type") == "JettonTransfer" && action.optString("status") == "ok") {
                        val jettonTransfer = action.getJSONObject("JettonTransfer")
                        val jetton = jettonTransfer.getJSONObject("jetton")
                        val currency = jetton.optString("symbol")
                        val comment = jettonTransfer.getString("comment")
                        val amount = jettonTransfer.getDouble("amount")
                        val recipientObj = jettonTransfer.getJSONObject("recipient")
                        val recipientAddress = recipientObj.getString("address")
                        if (recipientAddress == tradoorLongOrShortAddress && comment != "Call: 0x00000002" && currency == "USD₮") {
                            val amountInTon = (amount / getCoinPrice("TON")) * 1000
                            totalTonTVL += amountInTon
                            foundVerifiedEvent = true
                        }
                    }
                }
            }
            if (foundVerifiedEvent && totalTonTVL > 0) {
                println("totalTonTVL - ${totalTonTVL}")
                participantRepo.updateCredentialTVLAmount(userId, credentialId, totalTonTVL.toLong())
            }
        } catch (e: Exception) {
            println("check user TonPools Deposit TON events error : $e")
        }
        return foundVerifiedEvent
    }

    // 12. [Volume] Gaspump token transfer
    private fun updateGaspumpVolume(userId: Long, credentialId: Long, tonWallet: String?) {
        val response = webClient
            .get()
            .uri(
                "https://api.gas111.com/api/v1/internal/users/volume-stats?wallet_address=${tonWallet}&start_date=${startDateInISO8601}&end_date=${endDateInISO8601}"
            )
            .header("Authorization", "Bearer $tonConsoleBearer")
            .retrieve()
            .bodyToMono(String::class.java)
            .block();
        val json = JSONObject(response)
        val tonvalue = json.optDouble("volume_ton")
        if (tonvalue > 0) {
            participantRepo.updateCredentialTVLAmount(userId, credentialId, (tonvalue * **********).toLong())
        }
    }

    fun checkGaspumpTokenTransfetEvents(userId: Long, credentialId: Long): Boolean {
        val tonWallet = userService.getTonWalletByUserId(userId).tonWallet
        val addr = Address.of(tonWallet)
        val nonBounceableTonWallet = addr.toNonBounceable()
        val response = webClient
            .get()
            .uri(
                "https://api.gas111.com/api/v1/internal/users/has-transactions?wallet_address=${nonBounceableTonWallet}"
            )
            .retrieve()
            .bodyToMono(String::class.java)
            .block();
        val json = JSONObject(response)
        try {
            val hasTransactions = json.optBoolean("has_transactions")
            if (hasTransactions) {
                updateGaspumpVolume(userId, credentialId, tonWallet)
            }
            return hasTransactions
        } catch (e: Exception) {
            println("check user Gaspump token transfer events error : $e")
            return false
        }
    }

    // 13. [Volume] Rainbowswap Swap Token
    fun checkRainbowswapSwapTokenEvents(userId: Long, credentialId: Long): Boolean {
        val tonWallet = userService.getTonWalletByUserId(userId).tonWallet

        val response = webClient
            .get()
            .uri(
                "https://api.rainbow.ag/analytics/swap-volume?address=${tonWallet}&afterTimestamp=${degenStartDate * 1000}"
            )
            .retrieve()
            .bodyToMono(String::class.java)
            .block();

        try {
            val hasTransactions = response?.toDouble()!! > 0
            if (hasTransactions) {
                // number representing the USD volume of successful swaps within the range
                val amount = ((response.toDouble() / getCoinPrice("TON")) * **********).toLong()
                participantRepo.updateCredentialTVLAmount(userId, credentialId, amount)
            }
            return hasTransactions
        } catch (e: Exception) {
            println("check user Rainbowswap Swap Token events error : $e")
            return false
        }
    }

    // 14. [Volume] DexDiamonds Swap tsTON to TON:
    // Cancel

    // ====================== Normie TOP 5 DeFi ======================

    private fun fetchEvents(
        uri: String
    ): JSONArray {
        val response = webClient
            .get()
            .uri(uri)
            .header("Authorization", "Bearer $tonConsoleBearer")
            .retrieve()
            .bodyToMono(String::class.java)
            .block()
        return JSONObject(response).getJSONArray("events")
    }

    // 1. Blum: Boost Blum in Open League
    private fun checkEventsForBlumTransfer(events: JSONArray, blumAddress: String): Boolean {
        for (i in 0 until events.length()) {
            val event = events.getJSONObject(i)
            val actions = event.getJSONArray("actions")

            for (j in 0 until actions.length()) {
                val action = actions.getJSONObject(j)
                if (action.getString("type") == "TonTransfer" && action.optString("status") == "ok") {
                    val tonTransfer = action.getJSONObject("TonTransfer")
                    val recipientObj = tonTransfer.getJSONObject("recipient")
                    val recipientAddress = recipientObj.optString("address")
                    if (recipientAddress == blumAddress) {
                        return true
                    }
                }
            }
        }
        return false
    }

    fun checkNormieBlumEvents(userId: Long, credentialId: Long): Boolean {
        val tonWallet = userService.getTonWalletByUserId(userId).tonWallet
        val eventsLimit = 100

        val blumAddress = "0:3d9cdbd448b01e6c9c73847bf9b313162bf457af19383185d1dfd24c6eabeb73"
        // 找 10 次
        val maxAttempts = 10

        var foundVerifiedEvent = false

        try {
            var currentEndDate = degenEndDate
            var attemptCount = 0

            while (attemptCount < maxAttempts) {

                val events =
                    fetchEvents("https://tonapi.io/v2/accounts/${tonWallet}/events?limit=${eventsLimit}&start_date=${degenStartDate}&end_date=${currentEndDate}&initiator=true")

                // If no events returned, break
                if (events.length() == 0) {
                    foundVerifiedEvent = false
                    break
                }

                // Check current batch of events
                if (checkEventsForBlumTransfer(events, blumAddress)) {
                    // ✅ verified
                    foundVerifiedEvent = true
                    break
                }

                // Get timestamp of final event for next iteration
                val firstEventTimestamp = events.getJSONObject(events.length() - 1).getLong("timestamp")

                // If first event's timestamp is >= degenEndDate, no need to continue
                if (firstEventTimestamp >= degenEndDate) {
                    foundVerifiedEvent = false
                    break
                }
                currentEndDate = firstEventTimestamp
                attemptCount++
            }
        } catch (e: Exception) {
            println("check user Boost Blum in Open League events error : $e")
        }

        return foundVerifiedEvent
    }

    // 2. Catizen: Complete Daily Check-in
    fun checkNormieCatizenEvents(userId: Long, credentialId: Long): Boolean {
        val tonWallet = userService.getTonWalletByUserId(userId).tonWallet
        val eventsLimit = 100

        val response = webClient
            .get()
            .uri(
                "https://tonapi.io/v2/accounts/${tonWallet}/events?limit=${eventsLimit}&start_date=${degenStartDate}&end_date=${degenEndDate}"
            )
            .header("Authorization", "Bearer $tonConsoleBearer")
            .retrieve()
            .bodyToMono(String::class.java)
            .block();
        val json = JSONObject(response)

        val catizenAddress = "0:0c97a0557a00b420e64f2e12f35811c29cae223b81b978dbb8aa7fcd2909e821"

        var foundVerifiedEvent = false

        try {
            val events = json.getJSONArray("events")
            for (i in 0 until events.length()) {
                val event = events.getJSONObject(i)
                val actions = event.getJSONArray("actions")

                for (j in 0 until actions.length()) {
                    val action = actions.getJSONObject(j)
                    if (action.getString("type") == "SmartContractExec" && action.optString("status") == "ok") {
                        val smartContractExec = action.getJSONObject("SmartContractExec")
                        val contractObj = smartContractExec.getJSONObject("contract")
                        val contractAddress = contractObj.optString("address")
                        val operation = smartContractExec.optString("operation")
                        if (contractAddress == catizenAddress && operation == "0x61153514") {
                            foundVerifiedEvent = true
                        }
                    }
                }
            }
        } catch (e: Exception) {
            println("check user Complete Catizen Daily Check-in events error : $e")
        }
        return foundVerifiedEvent
    }

    // 3. Yescoin: Complete Daily Check-in
    private fun checkEventsForYescoinTransfer(
        events: JSONArray,
        yescoinAddressA: String,
        yescoinAddressB: String
    ): Boolean {
        for (i in 0 until events.length()) {
            val event = events.getJSONObject(i)
            val actions = event.getJSONArray("actions")

            for (j in 0 until actions.length()) {
                val action = actions.getJSONObject(j)
                if (action.getString("type") == "TonTransfer" && action.optString("status") == "ok") {
                    val tonTransfer = action.getJSONObject("TonTransfer")
                    val recipientObj = tonTransfer.getJSONObject("recipient")
                    val recipientAddress = recipientObj.optString("address")
                    if ((recipientAddress == yescoinAddressA) || (recipientAddress == yescoinAddressB)) {
                        return true
                    }
                }
            }
        }
        return false
    }

    fun checkNormieYescoinEvents(userId: Long, credentialId: Long): Boolean {
        val tonWallet = userService.getTonWalletByUserId(userId).tonWallet
        val eventsLimit = 100
        val maxAttempts = 10

        val yescoinAddressA = "0:293af16c881e0350f3194b6ab5a37a66cfd4dd100b318506764dba22e6f0a183"
        val yescoinAddressB = "0:2b0c0a5a932524157da54893a6a7b152f7bca5754b0183057cd0269f2a22ff17"

        var foundVerifiedEvent = false

        try {
            var currentEndDate = degenEndDate
            var attemptCount = 0

            while (attemptCount < maxAttempts) {
                // Fetch events for current time range
                val events =
                    fetchEvents("https://tonapi.io/v2/accounts/${tonWallet}/events?limit=${eventsLimit}&start_date=${degenStartDate}&end_date=${currentEndDate}&initiator=true")

                // If no events returned, break
                if (events.length() == 0) {
                    foundVerifiedEvent = false
                    break
                }
                // Check current batch of events
                if (checkEventsForYescoinTransfer(events, yescoinAddressA, yescoinAddressB)) {
                    foundVerifiedEvent = true
                    break
                }
                // Get timestamp of first event for next iteration
                val firstEventTimestamp = events.getJSONObject(events.length() - 1).getLong("timestamp")

                // If first event's timestamp is >= degenEndDate, no need to continue
                if (firstEventTimestamp >= degenEndDate) {
                    foundVerifiedEvent = false
                    break
                }

                // Update start date for next request
                currentEndDate = firstEventTimestamp
                attemptCount++
            }
        } catch (e: Exception) {
            println("check user Complete Yescoin Daily Check-in events error : $e")
        }
        return foundVerifiedEvent
    }

    // 4. Gamee: Provide Liquidity for WAT/TON
    fun checkNormieGameeEvents(userId: Long, credentialId: Long): Boolean {
        val tonWallet = userService.getTonWalletByUserId(userId).tonWallet
        val eventsLimit = 100

        val response = webClient
            .get()
            .uri(
                "https://tonapi.io/v2/accounts/${tonWallet}/events?limit=${eventsLimit}&start_date=${degenStartDate}&end_date=${degenEndDate}"
            )
            .header("Authorization", "Bearer $tonConsoleBearer")
            .retrieve()
            .bodyToMono(String::class.java)
            .block();
        val json = JsonPath.parse(response)
        try {
            val evts = json.read<List<Map<String, Any>>>("$.events[*]")
            val jettons = evts.map { event ->
                JsonPath.read<List<String>>(
                    event,
                    "$.actions[?(@.JettonTransfer.comment == 'Call: StonfiProvideLiquidity')].JettonTransfer.jetton.address"
                )
            }.filter { it.isNotEmpty() }
            val hasLP = jettons.map { lp ->
                // check WATCoin
                lp.contains("0:84ab3db1dfe51bfc43b8639efdf0d368a8ac35b4ffed27a6fdcbe5f40b8bafb3")
            }
            return hasLP.contains(true)
        } catch (e: Exception) {
            log.error("check user Complete Gamee: Provide Liquidity for WAT/TON events error", e)
            return false
        }
    }

    // 5. Getgems: Complete a trade in any NFT collection
    fun checkNormieGetgemsEvents(userId: Long, credentialId: Long): Boolean {
        val tonWallet = userService.getTonWalletByUserId(userId).tonWallet
        val eventsLimit = 100

        val response = webClient
            .get()
            .uri(
                "https://tonapi.io/v2/accounts/${tonWallet}/events?limit=${eventsLimit}&start_date=${degenStartDate}&end_date=${degenEndDate}"
            )
            .header("Authorization", "Bearer $tonConsoleBearer")
            .retrieve()
            .bodyToMono(String::class.java)
            .block();
        val json = JsonPath.parse(response)
        try {
            val senders =
                json.read<List<String>>("$.events[*].actions[?(@.type == 'NftItemTransfer')].NftItemTransfer.sender.address")
            if (senders.isEmpty()) {
                return false
            }
            val body = mapOf("account_ids" to senders)
            val accountsResponse = webClient
                .post()
                .uri(
                    "https://tonapi.io/v2/accounts/_bulk?currency=usd"
                )
                .header("Authorization", "Bearer $tonConsoleBearer")
                .contentType(MediaType.APPLICATION_JSON)
                .body(BodyInserters.fromValue(body))
                .retrieve()
                .bodyToMono(String::class.java)
                .block();
            val accounts = JsonPath.parse(accountsResponse)
            val ifs = accounts.read<List<String>>("$.accounts[*].interfaces[*]")
            return ifs.contains("nft_sale_getgems_v3")
        } catch (e: Exception) {
            log.error("check user Complete Getgems a trade in any NFT collection events error", e)
            return false
        }
    }

    // 6. Boost TON Station in Open League
    fun checkNormieTonStationInOpenLeagueEvents(userId: Long, credentialId: Long): Boolean {
        val tonWallet = userService.getTonWalletByUserId(userId).tonWallet
        val eventsLimit = 100

        val response = webClient
            .get()
            .uri(
                "https://tonapi.io/v2/accounts/${tonWallet}/events?limit=${eventsLimit}&start_date=${degenStartDate}&end_date=${degenEndDate}"
            )
            .header("Authorization", "Bearer $tonConsoleBearer")
            .retrieve()
            .bodyToMono(String::class.java)
            .block();
        val json = JSONObject(response)

        val tonStationAddress = "0:bb9b5bacf3083c0040f627f2fcaaafe6b624926d766e5eef74ab48d8a95a9188"

        var foundVerifiedEvent = false

        try {
            val events = json.getJSONArray("events")
            for (i in 0 until events.length()) {
                val event = events.getJSONObject(i)
                val actions = event.getJSONArray("actions")

                for (j in 0 until actions.length()) {
                    val action = actions.getJSONObject(j)
                    if (action.getString("type") == "TonTransfer" && action.optString("status") == "ok") {
                        val tonTransfer = action.getJSONObject("TonTransfer")
                        val recipientObj = tonTransfer.getJSONObject("recipient")
                        val contractAddress = recipientObj.optString("address")
                        if (contractAddress == tonStationAddress) {
                            foundVerifiedEvent = true
                        }
                    }
                }
            }
        } catch (e: Exception) {
            println("check user Boost TON Station events error : $e")
        }
        return foundVerifiedEvent
    }

    fun checkNormieTokenHold(userId: Long, labelType: Int): Boolean {

        val tokenAddresses = mapOf(
            // Cati
            41 to "0:fe72f474373e97032441bdb873f9a6d3ad10bab08e6dbc7befa5e42b695f5400",
            // Fanton
            42 to "0:c207089411e7812c786a216d7ad7239b1af3bbfc1a99783dd7479136cbf7f07e",
            // RabBitcoin
            43 to "0:83ee5aebc6939cab903947a9d32de17592e21b97504a09ee4e9da9c67b7701e0",
            // Fanzee
            44 to "0:c224bd22407a1f70106f1411dd546db7dd18b657890234581f453fa0a8077739",
            // Pumpers
            45 to "0:0336261f4d3e8a3521b5fc38ee30d203de5ec60d8231a4b3f2e42d512bedd7cc",
            // Random.tg
            46 to "0:85f68631a37ef10865f50d285913079a54625ee8ab9ca74ced28eeaf0ac44034",
            // GRAM
            47 to "0:b8ef4f77a17e5785bd31ba4da50abd91852f2b8febee97ad6ee16d941f939198",
            // JetTon
            48 to "0:105e5589bc66db15f13c177a12f2cf3b94881da2f4b8e7922c58569176625eb5",
            // TonUP
            49 to "0:af69fd0932bbfa04ebcfa4081e7b4f2e40cfee46951dfff7e103b366f2dc33bd",
            // swap.coffee
            50 to "0:a5d12e31be87867851a28d3ce271203c8fa1a28ae826256e73c506d94d49edad"
        )

        val contractAddress = tokenAddresses[labelType] ?: return false

        val tonWallet = userService.getTonWalletByUserId(userId).tonWallet

        val response = webClient
            .get()
            .uri(
                "https://tonapi.io/v2/accounts/${tonWallet}/jettons"
            )
            .header("Authorization", "Bearer $tonConsoleBearer")
            .retrieve()
            .bodyToMono(String::class.java)
            .block();
        val json = JSONObject(response)

        try {
            val balances = json.getJSONArray("balances")
            for (i in 0 until balances.length()) {
                val balance = balances.getJSONObject(i)
                val jetton = balance.getJSONObject("jetton")
                if (jetton.getString("address") == contractAddress) {
                    println("jettonName: ${jetton.getString("name")}")
                    return true
                }
            }
            return false
        } catch (e: Exception) {
            println("check user token hold (labelType: ${labelType}) events error : $e")
            return false
        }
    }


    // Hold TON Coin
    fun checkHoldTonCoinInTonEvents(userId: Long, credentialId: Long): Boolean {
        val tonWallet = userService.getTonWalletByUserId(userId).tonWallet

        val response = webClient
            .get()
            .uri(
                "https://tonapi.io/v2/accounts/${tonWallet}"
            )
            .header("Authorization", "Bearer $tonConsoleBearer")
            .retrieve()
            .bodyToMono(String::class.java)
            .block();
        val json = JSONObject(response)

        try {
            val balance = json.optLong("balance")
            val status = json.getString("status")
            if (status == "active" && balance > 0) {
                return true
            }
            return false
        } catch (e: Exception) {
            println("check user Hold TON Coin events error : $e")
            return false
        }
    }

    data class StakeInfo(
        val address: String,
        val amount: Long
    )

    data class LiquidityPair(
        var token1: Long,
        var token2: Long,
        var token1InUSDT: Double? = null,
        var totalValueInUsdt: Double? = null,
        var totalValueInTon: Double? = null,
    )

    data class LiquidityPairGroup(
        var pairs: MutableList<LiquidityPair> = mutableListOf()
    )

    fun countLiquidityPairsSummary(liquidityPairs: Map<String, LiquidityPairGroup>): Double {
        var allActionstotalValueInTon = 0.0

        liquidityPairs.forEach { (pairName, group) ->
            println("  $pairName:")
            group.pairs.forEachIndexed { index, pair ->
                val actualAmount1 = pair.token1
                val actualAmount2 = pair.token2

                val tonPrice = getCoinPrice("TON")
                val stTonPrice = getCoinPrice("stTON")
                val tsTonPrice = getCoinPrice("tsTON")

                if (actualAmount1 > 0 && actualAmount2 > 0) {
                    when (pairName) {
                        "TON/USDT" -> {
                            pair.token1InUSDT = (pair.token1.toDouble() * tonPrice / 1000L)
                            // 以 usdt 相加
                            pair.totalValueInUsdt = pair.token1InUSDT!! + pair.token2
                            // 转成 9 精度 的 Ton
                            pair.totalValueInTon = (pair.totalValueInUsdt!! * 1000) / tonPrice
                            allActionstotalValueInTon = allActionstotalValueInTon + pair.totalValueInTon!!
                        }

                        "stTON/USDT" -> {
                            pair.token1InUSDT = (pair.token1.toDouble() * stTonPrice / 1000L)
                            // 以 usdt 相加
                            pair.totalValueInUsdt = pair.token1InUSDT!! + pair.token2
                            // 转成 9 精度 的 Ton
                            pair.totalValueInTon = (pair.totalValueInUsdt!! * 1000) / tonPrice
                            allActionstotalValueInTon = allActionstotalValueInTon + pair.totalValueInTon!!
                        }

                        "tsTON/USDT" -> {
                            pair.token1InUSDT = (pair.token1.toDouble() * tsTonPrice / 1000L)
                            // 以 usdt 相加
                            pair.totalValueInUsdt = pair.token1InUSDT!! + pair.token2
                            // 转成 9 精度 的 Ton
                            pair.totalValueInTon = (pair.totalValueInUsdt!! * 1000) / tonPrice
                            allActionstotalValueInTon = allActionstotalValueInTon + pair.totalValueInTon!!
                        }
                    }
                    println(" final = ${allActionstotalValueInTon.toLong()}")
                }
            }
        }

        return allActionstotalValueInTon
    }


    fun getUserStakeEventsAmount(
        addressList: Array<TVLAddressInfo>,
        credentialId: Long,
        labelType: Int
    ): List<StakeInfo> {
        val stakeInfoList = mutableListOf<StakeInfo>()
        val eventsLimit = 100
        addressList.forEach { item ->
            var addressStakeAmount = 0L
            // stag1: 1725926400 - **********  (2024-09-10 08:00 - 2024-10-08 11:42) (UTC)
            // stag2: ********** - **********  (2024-10-08 11:42 - 2024-11-08 12:00) (UTC)
            // stag3: ********** - **********  (2024-11-08 12:00 - 2024-12-10 12:00) (UTC)
            // stag4: ********** - **********  (2024-12-10 12:00 - 2024-12-31 23:59) (UTC)
            val startDate = **********
            val endDate = **********
            try {
                val response = webClient
                    .get()
                    .uri("https://tonapi.io/v2/accounts/${item.address}/events?limit=${eventsLimit}&start_date=${startDate}&end_date=${endDate}")
                    .header(
                        "Authorization",
//                        "AFXEZ3ACHWCQPGIAAAACDBX3ACNADH4DWOIU3DOWOQMPYF7M5QIP5ZZFE5WI227KRIKIQ2Y"
                        "Bearer AFXEZ3ACLYBRD2QAAAADNN7DQN6XCHW5F42B2BLDUGGIA24QSWFV36YFQPBI7JPLDDRLMWI"
                    )
                    .retrieve()
                    .bodyToMono(String::class.java)
                    .block()
                val json = JSONObject(response)
                val events = json.getJSONArray("events")
                println("getUserStakeEventsAmount - https://tonapi.io/v2/accounts/${item.address}/events?limit=${eventsLimit}&start_date=${startDate}&end_date=${endDate} - ${events.length()}")

                // for dedust
                val stTonUsdtPoolAddress = "0:a6f76cc50642defea7050e9ed606f23a245483b26e166c33ef67bc4d77b9cf2f"
                val tonUsdtPoolAddress = "0:3e5ffca8ddfcf36c36c9ff46f31562aab51b9914845ad6c26cbde649d58a5588"
                val tsTonUsdtPoolAddress = "0:6487b31ce35d564d8174a34f3932dc09a58a6f1a164e301a61848173129ce554"

                val tsTonPriceInTon = getCoinPrice("tsTON") / getCoinPrice("TON")
                val stTonPriceInTon = getCoinPrice("stTON") / getCoinPrice("TON")

                val totalLiquidityPairs = mutableMapOf(
                    "TON/USDT" to LiquidityPairGroup(),
                    "stTON/USDT" to LiquidityPairGroup(),
                    "tsTON/USDT" to LiquidityPairGroup()
                )

                for (i in 0 until events.length()) {
                    val event = events.getJSONObject(i)
                    val actions = event.getJSONArray("actions")

                    // StonFi pair TVL
                    if (labelType == CredentialLabelType.PROVIDE_LIQUIDITY_ON_STON_FI) {

                        val event = events.getJSONObject(i)
                        val eventId = event.getString("event_id")

                        val actions = event.getJSONArray("actions")
                        val eventLiquidityPairs = mutableMapOf(
                            "TON/USDT" to LiquidityPairGroup(),
                            "stTON/USDT" to LiquidityPairGroup(),
                            "tsTON/USDT" to LiquidityPairGroup()
                        )

                        var usdtAmount = 0L
                        var tonAmount = 0L
                        var stTonAmount = 0L
                        var tsTonAmount = 0L

                        val stonFiAddress = "0:779dcc815138d9500e449c5291e7f12738c23d575b5310000f6a253bd607384e"

                        for (j in 0 until actions.length()) {
                            val action = actions.getJSONObject(j)
                            if (action.getString("type") == "JettonTransfer") {
                                val jettonTransfer = action.getJSONObject("JettonTransfer")
                                val jetton = jettonTransfer.getJSONObject("jetton")
                                val symbol = jetton.optString("symbol", "")
                                val name = jetton.optString("name", "")
                                val comment = jettonTransfer.optString("comment", "")
                                val recipient = jettonTransfer.optJSONObject("recipient")
                                val recipientName = recipient.optString("name", "")
                                val recipientAddress = recipient.optString("address", "")

                                if ((recipientName == "STON.fi Dex" && recipientAddress == stonFiAddress) || (comment == "Call: StonfiProvideLpV2")) {
                                    val amount = jettonTransfer.getLong("amount")

                                    // 1. 取 amount
                                    when {
                                        name == "Tether USD" || symbol == "USD₮" -> usdtAmount = amount
                                        name == "Tonstakers TON" || symbol == "tsTON" -> tsTonAmount = amount
                                        name == "Staked TON" || symbol == "stTON" -> stTonAmount = amount
                                        symbol == "UKWN" || symbol == "pTON" -> tonAmount = amount
                                    }
                                }
                            }
                        }

                        // 2. 判断是否合法 pair
                        if (usdtAmount > 0 && (tonAmount > 0 || stTonAmount > 0 || tsTonAmount > 0)) {
                            if (tonAmount > 0) {
                                eventLiquidityPairs["TON/USDT"]?.pairs?.add(
                                    LiquidityPair(
                                        token1 = tonAmount,
                                        token2 = usdtAmount,
                                    )
                                )
                            }
                            if (stTonAmount > 0) {
                                eventLiquidityPairs["stTON/USDT"]?.pairs?.add(
                                    LiquidityPair(
                                        token1 = stTonAmount,
                                        token2 = usdtAmount
                                    )
                                )
                            }
                            if (tsTonAmount > 0) {
                                eventLiquidityPairs["tsTON/USDT"]?.pairs?.add(
                                    LiquidityPair(
                                        token1 = tsTonAmount,
                                        token2 = usdtAmount
                                    )
                                )
                            }
                        }
                        if (eventLiquidityPairs.isNotEmpty()) {
                            eventLiquidityPairs.forEach { (pairName, eventGroup) ->
                                totalLiquidityPairs[pairName]?.pairs?.addAll(eventGroup.pairs)
                            }
                        }

                    }

                    // Dedust pair TVL
                    if (labelType == CredentialLabelType.PROVIDE_LIQUIDITY_ON_DEDUST) {
                        var isStTonUsdtPool = false
                        var isTonUsdtPool = false
                        var isTsTonUsdtPool = false

                        var usdtAmount = 0L
                        var tonAmount = 0L
                        var stTonAmount = 0L
                        var tsTonAmount = 0L

                        for (j in 0 until actions.length()) {
                            val action = actions.getJSONObject(j)

                            when (action.getString("type")) {
                                "JettonMint" -> {
                                    val jettonMint = action.getJSONObject("JettonMint")
                                    val jetton = jettonMint.getJSONObject("jetton")
                                    val name = jetton.optString("name", "")
                                    val address = jetton.optString("address", "")
                                    val symbol = jetton.optString("symbol", "")

                                    when {
                                        symbol == "LP" && name == "DeDust Pool: stTON/USDT" && address == stTonUsdtPoolAddress -> {
                                            isStTonUsdtPool = true
                                        }

                                        symbol == "LP" && name == "DeDust Pool: TON/USDT" && address == tonUsdtPoolAddress -> {
                                            isTonUsdtPool = true
                                        }

                                        symbol == "LP" && name == "DeDust Pool: tsTON/USDT" && address == tsTonUsdtPoolAddress -> {
                                            isTsTonUsdtPool = true
                                        }
                                    }
                                }

                                "JettonTransfer" -> {
                                    val jettonTransfer = action.getJSONObject("JettonTransfer")
                                    val jetton = jettonTransfer.getJSONObject("jetton")
                                    val symbol = jetton.optString("symbol", "")
                                    val comment = jettonTransfer.optString("comment", "")
                                    val amount = jettonTransfer.optString("amount", "0").toLong()
                                    if (comment == "Call: 0x40e108d6" && symbol == "USD₮") {
                                        usdtAmount = amount
                                    }
                                    when (symbol) {
                                        "stTON" -> stTonAmount = amount
                                        "tsTON" -> tsTonAmount = amount
                                    }
                                }

                                "TonTransfer" -> {
                                    val tonTransfer = action.getJSONObject("TonTransfer")
                                    val amount = tonTransfer.optString("amount", "0").toLong()
                                    tonAmount = amount
                                }
                            }
                        }

                        val tonPrice = getCoinPrice("TON")
                        val tsTONPrice = getCoinPrice("tsTON")
                        val stTONPrice = getCoinPrice("stTON")

                        if (isStTonUsdtPool) {
                            val token1InUSDT = stTonAmount.toDouble() * stTONPrice / 1000L
                            val totalValueInUsdt = token1InUSDT + usdtAmount
                            val valueInTon = (totalValueInUsdt * 1000) / tonPrice
                            addressStakeAmount += valueInTon.toLong()
                        } else if (isTonUsdtPool) {
                            val token1InUSDT = tonAmount.toDouble() * tonPrice / 1000L
                            val totalValueInUsdt = token1InUSDT + usdtAmount
                            val valueInTon = (totalValueInUsdt * 1000) / tonPrice
                            addressStakeAmount += valueInTon.toLong()
                        } else if (isTsTonUsdtPool) {
                            val token1InUSDT = tsTonAmount.toDouble() * tsTONPrice / 1000L
                            val totalValueInUsdt = token1InUSDT + usdtAmount
                            val valueInTon = (totalValueInUsdt * 1000) / tonPrice
                            addressStakeAmount += valueInTon.toLong()
                        }
                    }


                    // Other Defi TVL
                    for (j in 0 until actions.length()) {
                        val action = actions.getJSONObject(j)
                        when (labelType) {
                            // ton staker
                            CredentialLabelType.STAKE_SOME_TON_ON_TONSTAKERS -> {
                                if (action.getString("type") == "DepositStake") {
                                    val depositStake = action.getJSONObject("DepositStake")
                                    val amount = depositStake.getLong("amount")
                                    val pool = depositStake.getJSONObject("pool")
                                    val poolName = pool.optString("name", "")
                                    if (amount > 0 && poolName == "Tonstakers") {
                                        addressStakeAmount += amount
//                                println("[in]address - ${item.address}")
//                                println("[in]amount - ${amount}")
//                                println("[in]eventid - ${event.getString("event_id")}")
                                    }
                                }
                            }

                            // bemo
                            CredentialLabelType.STAKE_SOME_TON_ON_BEMO -> {
                                if (action.getString("type") == "TonTransfer") {
                                    val tonTransfer = action.getJSONObject("TonTransfer")
                                    val recipient = tonTransfer.optJSONObject("recipient")
                                    val amount = tonTransfer.optString("amount")
                                    val recipientAddress = recipient.optString("address", "")
                                    if (recipientAddress == "0:cd872fa7c5816052acdf5332260443faec9aacc8c21cca4d92e7f47034d11892") {
                                        addressStakeAmount += amount.toIntOrNull() ?: 0
                                    }
                                }
                                if (action.getString("type") == "JettonMint") {
                                    val iettonMint = action.getJSONObject("JettonMint")
                                    val jetton = iettonMint.getJSONObject("jetton")
                                    val jettonAddress = jetton.optString("address", "")
                                    val amount = iettonMint.optString("amount")
                                    if (jettonAddress == "0:cd872fa7c5816052acdf5332260443faec9aacc8c21cca4d92e7f47034d11892") {
                                        // Ton:stTon = 1:0.9
                                        addressStakeAmount += ((amount.toLong() / 0.9).toLong())
                                    }
                                }
                            }

                            // Evaa supply
                            CredentialLabelType.SUPPLY_STTON_OR_TSTON_IN_EVAA -> {
                                val evaaAddress =
                                    "0:bcad466a47fa565750729565253cd073ca24d856804499090c2100d95c809f9e"

                                if (action.getString("type") == "JettonTransfer") {
                                    val jettonTransfer = action.getJSONObject("JettonTransfer")
                                    val recipient = jettonTransfer.optJSONObject("recipient")
                                    val comment = jettonTransfer.optString("comment", "")

                                    if (recipient.has("address")) {
                                        val recipientAddress = recipient.getString("address")
                                        if (recipientAddress == evaaAddress && comment == "Call: 0x00000001") {
                                            val jetton = jettonTransfer.getJSONObject("jetton")
                                            val amount = jettonTransfer.getLong("amount")
                                            val symbol = jetton.optString("symbol", "")
                                            val symbolName = jetton.optString("name", "")

                                            // supply stTON
                                            if (symbol == "stTON" || symbolName == "Staked TON") {
                                                addressStakeAmount += (amount * stTonPriceInTon.toLong())
                                            }
                                            // supply tsTON
                                            if (symbol == "tsTON" || symbolName == "Tonstakers TON") {
                                                addressStakeAmount += (amount * tsTonPriceInTon.toLong())
                                            }
                                        }
                                    }
                                }
                            }

                            // stormtrade valut usdt or ton
                            CredentialLabelType.VAULT_USDT_OR_TON -> {
                                // storm trade vault -> TON
                                if (action.getString("type") == "SmartContractExec") {
                                    val smartContractExec = action.getJSONObject("SmartContractExec")
                                    val operation = smartContractExec.getString("operation")
                                    val amount = smartContractExec.getLong("ton_attached")
                                    if (operation == "StormVaultStake") {
                                        addressStakeAmount += amount
                                    }
                                }
                                // storm trade vault -> USDT
                                if (action.getString("type") == "JettonTransfer") {
                                    val jettonTransfer = action.getJSONObject("JettonTransfer")
                                    val jetton = jettonTransfer.getJSONObject("jetton")
                                    val comment = jettonTransfer.optString("comment", "")
                                    val amount = jettonTransfer.getLong("amount")
                                    val symbol = jetton.optString("symbol", "")
                                    if (comment == "Call: 0xc89a3ee4" && symbol == "USD₮") {
                                        val amountInTon = (amount / getCoinPrice("TON")) * 1000L
                                        addressStakeAmount += amountInTon.toLong()
                                    }
                                }
                            }
                        }
                    }
                }


                if (labelType == CredentialLabelType.PROVIDE_LIQUIDITY_ON_STON_FI) {
                    addressStakeAmount = countLiquidityPairsSummary(totalLiquidityPairs).toLong()
                }

                println("check after: user ${item.userId} and credential $credentialId has tvl $addressStakeAmount")

                if (addressStakeAmount > 0) {
                    println("user ${item.userId} and credential $credentialId has tvl $addressStakeAmount")
                    // stag1
                    // participantRepo.updateCredentialTVLAmount(item.userId, credentialId, addressStakeAmount)
                    // stag2
                    // participantRepo.updateCredentialTVLAmount2(item.userId, credentialId, addressStakeAmount)
                    // stag 3
//                    participantRepo.updateCredentialTVLAmount3(item.userId, credentialId, addressStakeAmount)
                    // stag 4
                    participantRepo.updateCredentialTVLAmount4(item.userId, credentialId, addressStakeAmount)
                    // total
                    participantRepo.updateCredentialTVLTotalAmount(item.userId, credentialId, addressStakeAmount)
                }
                stakeInfoList.add(StakeInfo(item.address, addressStakeAmount))
            } catch (e: Exception) {
                println("Check stake events error for address ${item.address}: $e")
                stakeInfoList.add(StakeInfo(item.address, 0L))
            }
        }
//        var totalAmount = 0L
//        for (stakeInfo in stakeInfoList) {
//            totalAmount += stakeInfo.amount
//        }
//        println(totalAmount / **********)
        return stakeInfoList
    }

    // Wonton buy mystery sliver packs
    fun checkWontonBuyMysterySliverPacksEvents(userId: Long): Boolean {
        val tonWallet = userService.getTonWalletByUserId(userId).tonWallet
        val eventsLimit = 100

        val startDate = 0
        val endDate = 0
//        &start_date=${startDate}&end_date=${endDate}

        val response = webClient
            .get()
            .uri(
                "https://tonapi.io/v2/accounts/${tonWallet}/events?limit=${eventsLimit}"
            )
            .header("Authorization", "Bearer $tonConsoleBearer")
            .retrieve()
            .bodyToMono(String::class.java)
            .block();
        val json = JSONObject(response)
        val wontonContractAddress = "0:ff4149d54c1cf01bef060a4aa7438c625b1534a0f667f4fa27febab431955d20"
        var foundVerifiedEvent = false
        try {
            val events = json.getJSONArray("events")
            for (i in 0 until events.length()) {
                val event = events.getJSONObject(i)
                val actions = event.getJSONArray("actions")
                for (j in 0 until actions.length()) {
                    val action = actions.getJSONObject(j)
                    if (action.getString("type") == "SmartContractExec" && action.optString("status") == "ok") {
                        val smartContractExec = action.getJSONObject("SmartContractExec")
                        val contract = smartContractExec.getJSONObject("contract")
                        val contractAddress = contract.optString("address")
                        if (contractAddress == wontonContractAddress) {
                            foundVerifiedEvent = true
                        }
                    }
                }
            }
        } catch (e: Exception) {
            println("check user buy mystery sliver packs on Wonton events error : $e")
        }
        return foundVerifiedEvent
    }

    // Hold $DUROV
    fun checkHoldDurovEvents(userId: Long): Boolean {
        val coinAddress = "0:74d8327471d503e2240345b06fe1a606de1b5e3c70512b5b46791b429dab5eb1"
        val symbolName = "durev"
        val isVerify = taskVerifyService.checkJettonCoinHold(userId, coinAddress, symbolName)
        return isVerify
    }

    // Hold $TINU
    fun checkHoldTinuEvents(userId: Long): Boolean {
        val coinAddress = "0:3a43774c81511f5d0f6d3412f67cc96312123f6b343e477bef3e08c6397c2817"
        val symbolName = "TINU"
        val isVerify = taskVerifyService.checkJettonCoinHold(userId, coinAddress, symbolName)
        return isVerify
    }

    // Tonco
    fun checkToncoSwapEvents(userId: Long, credentialId: Long): Boolean {
        val tonWallet = userService.getTonWalletByUserId(userId).tonWallet ?: return false
        try {
            val addr = Address.of(tonWallet)
            val hexAddress = addr.toHex()
            val body = mapOf("address" to "0:${hexAddress}")
            val response = webClient
                .post()
                .uri(
                    "https://api.tonco.io/intract/swap-task"
                )
                .contentType(MediaType.APPLICATION_JSON)
                .body(BodyInserters.fromValue(body))
                .retrieve()
                .bodyToMono(String::class.java)
                .block();
            val json = JSONObject(response)
            val data = json.getJSONObject("data")
            val isVerify = data.optBoolean("result")
            return isVerify
        } catch (e: Exception) {
            println("Tonco swap task error ${e.message}")
            return false
        }
    }

    fun checkToncoLiquidityEvents(userId: Long, credentialId: Long): Boolean {
        val tonWallet = userService.getTonWalletByUserId(userId).tonWallet ?: return false
        try {
            val addr = Address.of(tonWallet)
            val hexAddress = addr.toHex()
            val body = mapOf("address" to "0:${hexAddress}")
            val response = webClient
                .post()
                .uri(
                    "https://api.tonco.io/intract/liquidity-task"
                )
                .contentType(MediaType.APPLICATION_JSON)
                .body(BodyInserters.fromValue(body))
                .retrieve()
                .bodyToMono(String::class.java)
                .block();
            val json = JSONObject(response)
            val data = json.getJSONObject("data")
            val isVerify = data.optBoolean("result")
            return isVerify
        } catch (e: Exception) {
            println("Tonco liquidity task error ${e.message}")
            return false
        }
    }

    // labelType 60: Dig the stash on Magic Pot
    fun checkMagicPotDigTheStashEvents(userId: Long, credentialId: Long): Boolean {
        val tonWallet = userService.getTonWalletByUserId(userId).tonWallet
        val eventsLimit = 100

        val response = webClient
            .get()
            .uri(
                "https://tonapi.io/v2/accounts/${tonWallet}/events?limit=${eventsLimit}"
            )
            .header("Authorization", "Bearer $tonConsoleBearer")
            .retrieve()
            .bodyToMono(String::class.java)
            .block();
        val json = JSONObject(response)

        val magicPotAddress = "0:4105037aa6e333e6bb4d441862d289348ba987e74bc6f9e5e2cf9a0bc51f5ffa"

        var foundVerifiedEvent = false

        try {
            val events = json.getJSONArray("events")
            for (i in 0 until events.length()) {
                val event = events.getJSONObject(i)
                val actions = event.getJSONArray("actions")

                for (j in 0 until actions.length()) {
                    val action = actions.getJSONObject(j)
                    if (action.getString("type") == "TonTransfer" && action.optString("status") == "ok") {
                        val tonTransfer = action.getJSONObject("TonTransfer")
                        val recipient = tonTransfer.getJSONObject("recipient")
                        val recipientAddress = recipient.optString("address")
                        if (recipientAddress == magicPotAddress) {
                            foundVerifiedEvent = true
                        }
                    }
                }
            }
        } catch (e: Exception) {
            println("check user Dig the stash on Magic Pot events error : $e")
        }
        return foundVerifiedEvent
    }

    // labelType 61: receive USDT from Ustars
    fun checkReceiveUsdtFromUstarsEvents(userId: Long, credentialId: Long): Boolean {
        val tonWallet = userService.getTonWalletByUserId(userId).tonWallet
        val eventsLimit = 100

        val response = webClient
            .get()
            .uri(
                "https://tonapi.io/v2/accounts/${tonWallet}/events?limit=${eventsLimit}"
            )
            .header("Authorization", "Bearer $tonConsoleBearer")
            .retrieve()
            .bodyToMono(String::class.java)
            .block();
        val json = JSONObject(response)

        val ustarsAddress = "0:47e5bad465addedcb669651bc89319cdcbe3f8d6597bd3f026ae6285751f7431"

        var foundVerifiedEvent = false

        try {
            val events = json.getJSONArray("events")
            for (i in 0 until events.length()) {
                val event = events.getJSONObject(i)
                val actions = event.getJSONArray("actions")

                for (j in 0 until actions.length()) {
                    val action = actions.getJSONObject(j)
                    if (action.getString("type") == "JettonTransfer" && action.optString("status") == "ok") {
                        val jettonTransfer = action.getJSONObject("JettonTransfer")
                        val sender = jettonTransfer.getJSONObject("sender")
                        val senderAddress = sender.optString("address")
                        val jetton = jettonTransfer.getJSONObject("jetton")
                        val symbol = jetton.getString("symbol")
                        if (senderAddress == ustarsAddress && symbol == "USD₮") {
                            foundVerifiedEvent = true
                        }
                    }
                }
            }
        } catch (e: Exception) {
            println("check user receive USDT from Ustars events error : $e")
        }
        return foundVerifiedEvent
    }

    // labelType 62: [Swap.Coffee] Stake $CES or LP Tokens
    fun checkStakeCesOrLpTokensOnSwapCoffeeEvents(userId: Long, credentialId: Long): Boolean {
        val tonWallet = userService.getTonWalletByUserId(userId).tonWallet
        val eventsLimit = 100

        val response = webClient
            .get()
            .uri(
                "https://tonapi.io/v2/accounts/${tonWallet}/events?limit=${eventsLimit}"
            )
            .header("Authorization", "Bearer $tonConsoleBearer")
            .retrieve()
            .bodyToMono(String::class.java)
            .block();
        val json = JSONObject(response)

        val swapCoffeeStakeCESAddress = "0:29f90533937d696105883b981e9427d1ae411eef5b08eab83f4af89c495d27df"

        val swapCoffeeAddress = "0:a5d12e31be87867851a28d3ce271203c8fa1a28ae826256e73c506d94d49edad"
        val dedustPoolCESTONAddress = "0:123e245683bd5e93ae787764ebf22291306f4a3fcbb2dcfcf9e337186af92c83"
        val stonfiPoolCESTONAddres = "0:6a839f7a9d6e5303d71f51e3c41469f2c35574179eb4bfb420dca624bb989753"

        var foundVerifiedEvent = false

        try {
            val events = json.getJSONArray("events")
            for (i in 0 until events.length()) {
                val event = events.getJSONObject(i)
                val actions = event.getJSONArray("actions")

                for (j in 0 until actions.length()) {
                    val action = actions.getJSONObject(j)

                    // stake $CES
                    if (action.getString("type") == "JettonTransfer" && action.optString("status") == "ok") {
                        val jettonTransfer = action.getJSONObject("JettonTransfer")
                        val recipient = jettonTransfer.getJSONObject("recipient")
                        val recipientAddress = recipient.optString("address")
                        val jetton = jettonTransfer.getJSONObject("jetton")
                        val symbol = jetton.getString("symbol")
                        if (recipientAddress == swapCoffeeStakeCESAddress && symbol == "CES") {
                            foundVerifiedEvent = true
                            break
                        }
                    }

                    // Dedust LP TON/$CES
                    if (action.getString("type") == "JettonSwap" && action.optString("status") == "ok") {
                        val jettonSwap = action.getJSONObject("JettonSwap")
                        val router = jettonSwap.getJSONObject("router")
                        val routerAddress = router.optString("address")

                        val jettonMasterOut = jettonSwap.getJSONObject("jetton_master_out")
                        val jettonMasterOutAddress = jettonMasterOut.getString("address")
                        if (routerAddress == dedustPoolCESTONAddress && jettonMasterOutAddress == swapCoffeeAddress) {
                            foundVerifiedEvent = true
                            break
                        }
                    }

                }
            }
        } catch (e: Exception) {
            println("check user Stake CES on Swap Coffee or LP Token events error : $e")
        }
        return foundVerifiedEvent
    }

    // labelType 63: [Coffin] Supply token into vaults
    fun checkSupplyTokenIntoVaultsOnCoffinEvents(userId: Long, credentialId: Long): Boolean {
        val tonWallet = userService.getTonWalletByUserId(userId).tonWallet
        val eventsLimit = 100

        val response = webClient
            .get()
            .uri(
                "https://tonapi.io/v2/accounts/${tonWallet}/events?limit=${eventsLimit}"
            )
            .header("Authorization", "Bearer $tonConsoleBearer")
            .retrieve()
            .bodyToMono(String::class.java)
            .block();
        val json = JSONObject(response)

        val coffinAddress = "0:68cf02950f26bd20bdcac38991e40429878ca8d7912e31dc97f272e58de694c6"

        var foundVerifiedEvent = false

        try {
            val events = json.getJSONArray("events")
            for (i in 0 until events.length()) {
                val event = events.getJSONObject(i)
                val actions = event.getJSONArray("actions")

                for (j in 0 until actions.length()) {
                    val action = actions.getJSONObject(j)
                    if (action.getString("type") == "JettonTransfer" && action.optString("status") == "ok") {
                        val jettonTransfer = action.getJSONObject("JettonTransfer")
                        val recipient = jettonTransfer.getJSONObject("recipient")
                        val recipientAddress = recipient.optString("address")
                        if (recipientAddress == coffinAddress) {
                            foundVerifiedEvent = true
                        }
                    }
                }
            }
        } catch (e: Exception) {
            println("check user supply token into vaults on Coffin events error : $e")
        }
        return foundVerifiedEvent
    }

    // labelType 64: [TonPools] Stake TON or USDT
    fun checkStakeTonOrUsdtOnTonPoolsEvents(userId: Long, credentialId: Long): Boolean {
        val tonWallet = userService.getTonWalletByUserId(userId).tonWallet
        val eventsLimit = 100

        val response = webClient
            .get()
            .uri(
                "https://tonapi.io/v2/accounts/${tonWallet}/events?limit=${eventsLimit}"
            )
            .header("Authorization", "Bearer $tonConsoleBearer")
            .retrieve()
            .bodyToMono(String::class.java)
            .block();
        val json = JSONObject(response)

        val tonPoolsDepositTonAddress = "0:3bcbd42488fe31b57fc184ea58e3181594b33b2cf718500e108411e115978be1"
        val tonPoolsDepositUsftAddress = "0:eb4b3f56e2d8f09eacb5178cfe3b8564769f20d983fde1c9a1d765f945b6297a"

        var foundVerifiedEvent = false
        var totalTonTVL = 0.0

        try {
            val events = json.getJSONArray("events")
            for (i in 0 until events.length()) {
                val event = events.getJSONObject(i)
                val actions = event.getJSONArray("actions")

                // Deposit TON
                for (j in 0 until actions.length()) {
                    val action = actions.getJSONObject(j)
                    if (action.getString("type") == "SmartContractExec" && action.optString("status") == "ok") {
                        val smartContractExec = action.getJSONObject("SmartContractExec")
                        val contractObj = smartContractExec.getJSONObject("contract")
                        val contractAddress = contractObj.optString("address")
                        val amount = smartContractExec.getLong("ton_attached")
                        if (contractAddress == tonPoolsDepositTonAddress && amount > 0) {
                            foundVerifiedEvent = true
                            break
                        }
                    }

                    // Deposit USDT
                    if (action.getString("type") == "JettonTransfer" && action.optString("status") == "ok") {
                        val jettonTransfer = action.getJSONObject("JettonTransfer")
                        val recipientObj = jettonTransfer.getJSONObject("recipient")
                        val recipientAddress = recipientObj.optString("address")
                        val jetton = jettonTransfer.getJSONObject("jetton")
                        val symbol = jetton.optString("symbol")
                        if (recipientAddress == tonPoolsDepositUsftAddress && symbol == "USD₮") {
                            foundVerifiedEvent = true
                            break
                        }
                    }
                }
            }
//            if (foundVerifiedEvent && totalTonTVL > 0) {
//                println("totalTonTVL - ${totalTonTVL}")
//                participantRepo.updateCredentialTVLAmount(userId, credentialId, totalTonTVL.toLong())
//            }
        } catch (e: Exception) {
            println("check user TonPools Deposit TON or USDT events error : $e")
        }
        return foundVerifiedEvent
    }

    // labelType 65: [Farmix] Lend
    fun checkLendOnFarmixEvents(userId: Long, credentialId: Long): Boolean {
        val tonWallet = userService.getTonWalletByUserId(userId).tonWallet
        val eventsLimit = 100

        val response = webClient
            .get()
            .uri(
                "https://tonapi.io/v2/accounts/${tonWallet}/events?limit=${eventsLimit}"
            )
            .header("Authorization", "Bearer $tonConsoleBearer")
            .retrieve()
            .bodyToMono(String::class.java)
            .block();
        val json = JSONObject(response)

        val farmixTonsAddress = "0:be8e55fcdc36198125915b9abf5ee1cb5961503e9db11a673c042a1e59c90aa5"
        val farmixUsdtAddress = "0:fa81049609ac8787416f5274d79697e2cc85a2abb51e138818bd7198b4484860"
        val farmixNotAddress = "0:84ffa4debca1298fc393cf7ad9b750f96d1e9f10d41b48dd9b6d6d23cf16d618"

        var foundVerifiedEvent = false

        try {
            val events = json.getJSONArray("events")
            for (i in 0 until events.length()) {
                val event = events.getJSONObject(i)
                val actions = event.getJSONArray("actions")

                for (j in 0 until actions.length()) {
                    val action = actions.getJSONObject(j)
                    if (action.getString("type") == "JettonTransfer" && action.optString("status") == "ok") {
                        val jettonTransfer = action.getJSONObject("JettonTransfer")
                        val recipient = jettonTransfer.getJSONObject("recipient")
                        val recipientAddress = recipient.optString("address")
                        if (
                            (recipientAddress == farmixTonsAddress) ||
                            (recipientAddress == farmixUsdtAddress) ||
                            (recipientAddress == farmixNotAddress)
                        ) {
                            foundVerifiedEvent = true
                        }
                    }
                }
            }
        } catch (e: Exception) {
            println("check user supply token into vaults on Coffin events error : $e")
        }
        return foundVerifiedEvent
    }

    fun getUserSbtClaimedTypeByUidAddressActivityId(userId: Long, tonAddress: String, activityId: Int): Int {
        // sbts 共用一个activity id，取最大的claim type
        val sbts = sbtRepo.getSBTByActivityId(activityId)
        var maxClaimedType = 0
        for (sbt in sbts) {
            val rewardType = participantRepo.getUserSbtReward(sbt.sbtId, userId, sbt.groupId)?.claimType ?: 0
            if (rewardType > maxClaimedType) {
                maxClaimedType = rewardType
            }
        }
        return maxClaimedType
    }

    // labelType 66: Hold SBT
    fun verifyRequiredSbtHoldingsBySetting(userId: Long, credential: Credential): Boolean {
        val tonAddress = userService.getTonWalletByUserId(userId).tonWallet
        if (tonAddress.isNullOrEmpty()) {
            println("verify sbt holding task failed,user has no ton address ${userId}")
            return false
        }
        val options = credential.options
        val jsonObject = JSONObject(options)
        val requiredSbtAmount = jsonObject.getInt("requiredSbtAmount") // 3
        val sbtActivityIds = jsonObject.getString("sbtActivityIds") // "11,22,33,44,55"

        val sbtActivityIdList = sbtActivityIds.replace(" ", "").split(",")
        var validSbtCount = 0

        for (activityId in sbtActivityIdList) {
            val userSbt = sbtWhiteListRepo.getUserSbtByUidAddressActivityId(
                userId,
                tonAddress,
                activityId.toInt()
            )
            val claimedType =
                getUserSbtClaimedTypeByUidAddressActivityId(userId, tonAddress, activityId.toInt())

            // eligible or minting or claimed
            if (userSbt != null && (claimedType >= 2)) {
                validSbtCount++
            }
        }
        return validSbtCount >= requiredSbtAmount
    }

    // labelType 67: Stake Ton/stTon/tsTon to UTONC.
    fun checkStakeToUTONCEvents(userId: Long, credentialId: Long): Boolean {
        val tonWallet = userService.getTonWalletByUserId(userId).tonWallet
        val eventsLimit = 100

        val response = webClient
            .get()
            .uri(
                "https://tonapi.io/v2/accounts/${tonWallet}/events?limit=${eventsLimit}"
            )
            .header("Authorization", "Bearer $tonConsoleBearer")
            .retrieve()
            .bodyToMono(String::class.java)
            .block();
        val json = JSONObject(response)

        val tonAddress = "0:38c6eed2bf0e0a45fe1bad4caeec5a56f9acbcf91945b6a8ca6a0709c7348509"
        val tsTonAddress = "0:181e83440ca2b8f58501587ebe4e11fdd24b3e25a37f0c2cefd3849037e4ab4c"
        val stTonAddress = "0:acf25e67e7be0e533b719cc1293a03e1b0d8aec67453662437503c075a1fdaf9"

        val tsTonPriceInTon = getCoinPrice("tsTON") / getCoinPrice("TON")
        val stTonPriceInTon = getCoinPrice("stTON") / getCoinPrice("TON")

        var foundVerifiedEvent = false

        var totalTonTVL = 0.0

        try {
            val events = json.getJSONArray("events")
            for (i in 0 until events.length()) {
                val event = events.getJSONObject(i)
                val actions = event.getJSONArray("actions")

                for (j in 0 until actions.length()) {
                    val action = actions.getJSONObject(j)
                    if (action.getString("type") == "SmartContractExec" && action.optString("status") == "ok") {
                        val smartContractExec = action.getJSONObject("SmartContractExec")
                        val contract = smartContractExec.getJSONObject("contract")
                        val contractAddress = contract.optString("address")
                        val tonAttached = smartContractExec.optLong("ton_attached")
                        if (contractAddress == tonAddress) {
                            totalTonTVL += tonAttached
                            foundVerifiedEvent = true
                        }
                    }
                    if (action.getString("type") == "JettonTransfer" && action.optString("status") == "ok") {
                        val jettonTransfer = action.getJSONObject("JettonTransfer")
                        val recipient = jettonTransfer.getJSONObject("recipient")
                        val recipientAddress = recipient.optString("address")
                        val amount = jettonTransfer.optString("amount", "0").toLong()
                        val symbol = jettonTransfer.getJSONObject("jetton").optString("symbol")
                        if ((recipientAddress == tsTonAddress) || (recipientAddress == stTonAddress)) {
                            foundVerifiedEvent = true
                            if (symbol == "tsTON") {
                                totalTonTVL += (amount * tsTonPriceInTon)
                            }
                            if (symbol == "stTON") {
                                totalTonTVL += (amount * stTonPriceInTon)
                            }
                        }
                    }
                }
            }

            if (foundVerifiedEvent && totalTonTVL > 0) {
                participantRepo.updateCredentialTVLAmount(userId, credentialId, totalTonTVL.toLong())
            }
        } catch (e: Exception) {
            println("check user stake Ton/stTon/tsTon to UTONC events error : $e")
        }
        return foundVerifiedEvent
    }

    // labelType 70: Provide Liquidity for LAMBO/TON
    fun checkLPForLamboAndTonEvents(userId: Long, credentialId: Long): Boolean {
        val tonWallet = userService.getTonWalletByUserId(userId).tonWallet
        val eventsLimit = 100
        val response = webClient
            .get()
            .uri(
                "https://tonapi.io/v2/accounts/${tonWallet}/events?limit=${eventsLimit}"
            )
            .header("Authorization", "Bearer $tonConsoleBearer")
            .retrieve()
            .bodyToMono(String::class.java)
            .block();
        val json = JSONObject(response)
        try {
            val events = json.getJSONArray("events")

            val stonFiAddress = "0:031053133270be82ee6fd94d1963c0186868403a4f537040a0d533aab805b7af"

            for (i in 0 until events.length()) {
                val event = events.getJSONObject(i)
                val actions = event.getJSONArray("actions")

                for (j in 0 until actions.length()) {
                    val action = actions.getJSONObject(j)

                    if (action.getString("type") == "JettonTransfer" && action.optString("status") == "ok") {
                        val jettonTransfer = action.getJSONObject("JettonTransfer")
                        val jetton = jettonTransfer.getJSONObject("jetton")
                        val symbol = jetton.optString("symbol", "")
                        val comment = jettonTransfer.optString("comment", "")
                        val recipient = jettonTransfer.getJSONObject("recipient")
                        val recipientAddress = recipient.optString("address", "")

                        // ProvideLiquidity LAMBO & TON
                        if (comment == "Call: StonfiProvideLpV2" && recipientAddress == stonFiAddress && symbol == "LAMBO") {
                            return true
                        }
                    }
                }
            }
        } catch (e: Exception) {
            println("check user ston.fi Liquidity Provide LAMBO events error : $e")
        }
        return false
    }
}


@Service
@Profile("!prod")
class WiseCronJob(
    private val redisTemplate: StringRedisTemplate,
    private val wiseScoreService: WiseScoreService
) {
    //    @Scheduled(cron = "0 0 * * * ?")
    @Scheduled(cron = "0 0 0/12 * * ?")
    fun generateTop100Score() {
        println("start generate top100 score ")
        redisTemplate.delete(redisTemplate.scanKeys("top100-user:*"))
        val userList = wiseScoreService.getTop100Score()
        for ((index, user) in userList.withIndex()) {
            redisTemplate.opsForValue().set("top100-user:${user.userId}", (index + 1).toString())
        }
        val redisCount = redisTemplate.scanKeys("top100-user:*").size
        println("generate top100 score complete $redisCount")
    }
}