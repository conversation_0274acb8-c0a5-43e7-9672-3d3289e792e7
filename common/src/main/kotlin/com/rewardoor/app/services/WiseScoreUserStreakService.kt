package com.rewardoor.app.services

import com.rewardoor.app.dao.*
import com.rewardoor.enums.WiseScoreUserStreakSbt
import com.rewardoor.model.LuckyDrawResult
import com.rewardoor.model.SbtSetting
import com.rewardoor.model.WiseScoreUserCheckIn
import com.rewardoor.model.WiseScoreUserStreak
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.time.Instant
import java.time.LocalDate
import java.time.ZoneId
import java.time.temporal.ChronoUnit

@Service
class WiseScoreUserStreakService(
    private val userStreakRepo: WiseScoreUserStreakRepository,
    private val userLuckyDrawRepo: UserLuckyDrawRepository,
    private val userTonRepository: UserTonRepository,
    private val sbtRewardRepo: SBTRewardRepository,
    private val tonSocietySyncRepo: TonSocietySyncRepository,
    private val credentialGroupRepo: CredentialGroupRepository,
    private val participantRepo: ParticipantRepository,
    private val projectRepository: ProjectRepository,
    private val retroactiveRepository: RetroactiveInventoryRepository,
    val tPointsService: TPointsService,
    val retroactiveService: RetroactiveService
) {

    // 用户打卡
    fun userCheckIn(userId: Long): Int {
        val currentDate =
            Instant.now().atZone(ZoneId.systemDefault()).toLocalDate().atStartOfDay(ZoneId.systemDefault()).toInstant()
        val existingCheckIn = userStreakRepo.getUserCheckInByUidAndDate(userId, currentDate)

        return if (existingCheckIn == null) {
            val insertCount = userStreakRepo.addUserCheckIn(userId, currentDate)
            if (insertCount > 0) {
                // +300 tPoints
                val luckyDrawResult = LuckyDrawResult(
                    userId = userId,
                    fissionLevel = -3,
                    tPointsNum = 300,
                    isEligibleToGenerateWiseScore = 0,
                    isEligibleToGenerateSBT = 0,
                    createTime = Instant.now(),
                    updateTime = Instant.now()
                )
                userLuckyDrawRepo.createUserLuckyDraw(luckyDrawResult)
            }
            insertCount
        } else {
            0
        }
    }

    // 用户补签打卡
    fun userBackFillCheckIn(userId: Long, missDate: Instant): Int {
        val existingCheckIn = userStreakRepo.getUserCheckInByUidAndDate(userId, missDate)
        val userCheckIns = getUserCheckInRecord(userId)
        if (userCheckIns.isEmpty()) {
            return -1
        }
        // 获取用户打卡记录的最早日期
        val earliestCheckIn = userCheckIns.minOrNull() ?: Instant.EPOCH
        // 判断 missDate 是否早于最早的打卡记录
        if (missDate.isBefore(earliestCheckIn)) {
            println("userID: $userId Error: backDate ($missDate) is earlier than the earliest check-in date ($earliestCheckIn).")
            return -1
        }

        // 判断 missDate 是否晚于当前日期
        val localDate = LocalDate.now()
        val zoneId = ZoneId.systemDefault()
        if (missDate.isAfter(localDate.atStartOfDay(zoneId).toInstant())) {
            println("userID: $userId Error: backDate ($missDate) is later than the current date (${LocalDate.now()}).")
            return -1
        }

        // 判断 missDate 是否已经签过到
        val missDateStart = missDate.truncatedTo(ChronoUnit.DAYS) // missDate 当天的 00:00:00
        val missDateEnd = missDateStart.plus(1, ChronoUnit.DAYS) // missDate 当天的 23:59:59

        // 检查 userCheckIns 是否包含 missDate 当天的日期
        val isAlreadyCheckedIn = userCheckIns.any { checkIn ->
            checkIn >= missDateStart && checkIn < missDateEnd
        }

        if (isAlreadyCheckedIn) {
            println("userID: $userId has already checked in on the date of $missDate.")
            return -1
        }
        try {
            val invoice = retroactiveService.consumeRetroactiveCard(userId, 1, "")
        } catch (e: IllegalStateException) {
            when {
                e.message?.contains("has no retroactive card") == true -> {
                    // 用户无补签卡
                    return -2
                }

                e.message?.contains("has insufficient retroactive card") == true -> {
                    // 用户补签卡余额不足
                    return -3
                }

                else -> {
                    // 处理其他 IllegalStateException
                    println("未知错误: ${e.message}")
                }
            }
        }
        return if (existingCheckIn == null) {
            val insertCount = userStreakRepo.addUserCheckIn(userId, missDate)
            if (insertCount > 0) {
                // +300 tPoints
                val luckyDrawResult = LuckyDrawResult(
                    userId = userId,
                    fissionLevel = -4, // 补签
                    tPointsNum = 300,
                    isEligibleToGenerateWiseScore = 0,
                    isEligibleToGenerateSBT = 0,
                    createTime = Instant.now(),
                    updateTime = Instant.now()
                )
                userLuckyDrawRepo.createUserLuckyDraw(luckyDrawResult)
                userStreakRepo.addUserSignCard(userId, missDate, 0, 0.0)
            }
            insertCount
        } else {
            0
        }
    }

    // 获取用户历史打卡记录
    fun getUserCheckInRecord(userId: Long): List<Instant> {
        return userStreakRepo.getUserCheckIns(userId).map { it.checkInDate!! }
    }

    // 获取用户当前连续打卡天数
    fun getLastUserStreak(userId: Long): Int {
        val checkIns = userStreakRepo.getUserCheckIns(userId)
        if (checkIns.isEmpty()) return 0
        val today = Instant.now().atZone(ZoneId.systemDefault()).toLocalDate()

        var streak = 0
        var previousDate = today //默认从今天开始算

        // 倒序遍历打卡记录（从最近日期开始）
        for (checkIn in checkIns.reversed()) {
            val checkInDate = checkIn.checkInDate!!.atZone(ZoneId.systemDefault()).toLocalDate()

            if (streak == 0) {
                // 检查是否从今天或昨天开始
                if (checkInDate == today || checkInDate == today.minusDays(1)) {
                    streak = 1
                    previousDate = checkInDate
                } else {
                    // 如果最近一次打卡记录不连续，直接结束
                    break
                }
            } else {
                // 检查是否连续
                if (checkInDate == previousDate.minusDays(1)) {
                    streak++
                    previousDate = checkInDate
                } else {
                    // 如果不连续，停止计数
                    break
                }
            }
        }
        return streak
    }

    // 获取用户 历史最大连续打卡信息、当前连续打卡信息
    @Transactional
    fun getMaxUserStreakWithDates(userId: Long): WiseScoreUserStreak {
        val checkIns = userStreakRepo.getUserCheckIns(userId)
        if (checkIns.isEmpty()) return WiseScoreUserStreak(0, 0, 0)
        val today = Instant.now().atZone(ZoneId.systemDefault()).toLocalDate()
        var maxStreak = 0 // 历史最大连续天数
        var currentStreak = 1 // 当前连续天数，初始值为 1
        var maxStartDate: LocalDate? = null // 最大连续天数的起始日期
        var maxEndDate: LocalDate? = null // 最大连续天数的终止日期
        var currentStartDate: LocalDate? = null // 当前连续天数的起始日期
        var previousDate: LocalDate? = null
        var lastStreak = 0
        var isLastStreakActive = true // 标记是否正在计算最后一次连续打卡天数
        val lastCheckInDate = checkIns.last().checkInDate!!.atZone(ZoneId.systemDefault()).toLocalDate()

        // 遍历打卡记录（按日期降序）
        for (checkIn in checkIns.reversed()) {
            val checkInDate = checkIn.checkInDate!!.atZone(ZoneId.systemDefault()).toLocalDate()
            if (previousDate != null) {
                if (checkInDate == previousDate.minusDays(1)) {
                    // 如果是连续日期，增加当前连续天数
                    currentStreak++
                    currentStartDate = currentStartDate ?: checkInDate // 只在第一次连续时设置
                    // 如果正在计算最后一次连续打卡天数，增加 lastStreak
                    if (isLastStreakActive) {
                        lastStreak++
                    }
                } else if (checkInDate != previousDate) {

                    // 如果不连续，检查是否需要更新最大连续天数
                    if (currentStreak > maxStreak) {
                        maxStreak = currentStreak
                        maxStartDate = previousDate
                        maxEndDate = currentStartDate // 当前连续段的终止日期
                    }
                    // 重置当前连续天数
                    currentStreak = 1
                    currentStartDate = checkInDate

                    // 如果中断了连续性，停止计算最后一次连续打卡天数
                    isLastStreakActive = false
                }
            } else {
                // 第一次遍历时设置当前连续的起始日期
                currentStartDate = checkInDate
                // 初始化最后一次连续打卡天数
                if (checkInDate == today || checkInDate == today.minusDays(1)) {
                    lastStreak = 1
                }
            }
            // 更新上一个打卡日期
            previousDate = checkInDate
        }

        // 最后再检查一次，确保最后一段连续天数被记录
        if (currentStreak > maxStreak) {
            maxStreak = currentStreak
            maxStartDate = previousDate // previousDate 是最后一段连续的结束日期
            maxEndDate = currentStartDate
        }
        // 如果最后一次连续天数是当前连续天数，更新 lastStreak
        if (isLastStreakActive) {
            lastStreak = currentStreak
        }
        val current = retroactiveRepository.getUserCardInventoryWithLock(userId)
        return WiseScoreUserStreak(
            userId,
            currentStreak = lastStreak,
            maxStreak = maxStreak,
            maxStreakBeginDate = maxStartDate?.atStartOfDay(ZoneId.systemDefault())?.toInstant(),
            maxStreakEndDate = maxEndDate?.atStartOfDay(ZoneId.systemDefault())?.toInstant(),
            lastCheckInDate = lastCheckInDate?.atStartOfDay(ZoneId.systemDefault())?.toInstant(),
            availableRetroactiveCards = current?.balanceCards ?: 0,
            purchasableRetroactiveCards = getMissedCheckInDays(userId, checkIns).size,
            totalSignCardsUsed = current?.usedCards ?: 0,
            checkInRecord = checkIns.map { it.checkInDate!! }
        )
    }

    fun getMissedCheckInDays(userId: Long, checkIns: List<WiseScoreUserCheckIn>): List<LocalDate> {
        val userCheckInDates = checkIns
            .filter { it.userId == userId && it.checkInDate != null }
            .map { it.checkInDate!! }
        if (userCheckInDates.isEmpty()) {
            return emptyList()
        }
        val sortedDates = userCheckInDates
            .map { it.atZone(ZoneId.systemDefault()).toLocalDate() }
            .sorted()
        val earliestDate = sortedDates.first()
        val latestDate = LocalDate.now(ZoneId.systemDefault()) // today
        val missedDates = mutableListOf<LocalDate>()
        var currentDate = earliestDate
        while (currentDate < latestDate) {
            if (currentDate !in sortedDates) {
                missedDates.add(currentDate)
            }
            currentDate = currentDate.plusDays(1)
        }
        return missedDates
    }

    // insert or update user streak
    fun addOrUpdateUserStreak(userId: Long): WiseScoreUserStreak {
        val existUserStreak = userStreakRepo.getUserStreak(userId)
        val userStreak = getMaxUserStreakWithDates(userId)
        if (existUserStreak == null) {
            if (userStreak.userId != 0L) {
                userStreakRepo.addUserStreak(userStreak)
            }
        } else {
            val totalSignCardsUsed = existUserStreak.totalSignCardsUsed
            userStreak.totalSignCardsUsed = totalSignCardsUsed
            userStreakRepo.updateUserStreak(userStreak)
        }
        return userStreak
    }


    // 计算补签卡对应的价格，单位ton
    fun calculateSignCardPrice(totalSignCardsUsed: Int): Double {
        return when (totalSignCardsUsed) {
            in 0..1 -> 0.0
            in 2..3 -> 0.1
            in 4..8 -> 0.2
            in 9..30 -> 0.5
            in 31..40 -> 1.0
            else -> 2.0
        }
    }

    data class UserStreakRewards(
        val userId: Long,
        val totalTPoints: Int,
        val tPointsRecords: List<TPointsRecord> = emptyList(),
        val sbtSettings: List<SbtSetting> = emptyList(),
        val userMaxStreakDays: Int = 0
    ) {
    }

    data class TPointsRecord(
        val recordType: Int = 0, // 0 - default , 1 - check in , 2 - back-check in
        val num: Int = 0,
        val date: Instant? = null,
        val missDate: Instant? = null
    ) {
    }

    fun getUserRewards(userId: Long): UserStreakRewards {
        val userStreak = userStreakRepo.getUserStreak(userId)
        val userMaxStreakDays = userStreak?.maxStreak ?: 0
        val totalTPoints = tPointsService.getUserTPointsNum(userId)
        val records = userLuckyDrawRepo.getUserCheckInTPointsRecord(userId)
        val tPointsRecords = mutableListOf<TPointsRecord>()
        val missRecords = userStreakRepo.getUserBackCheckInRecord(userId)
        if (records != null) {
            for ((index, record) in records.withIndex()) {
                val missDate = if (index < missRecords.size) missRecords[index] else null
                val tPointsRecord = TPointsRecord(
                    recordType = if (record.fissionLevel == -3) 1 else 2,
                    num = record.tPointsNum,
                    date = record.createTime,
                    missDate = missDate
                )
                tPointsRecords.add(tPointsRecord)
            }
        }
        val streakSbts = WiseScoreUserStreakSbt.values().toList()
        val streakSbtSettings = mutableListOf<SbtSetting>()
        for (streakSbt in streakSbts) {
            val sbtId = streakSbt.sbtId
            val sbtSetting = getSbtSettingById(sbtId, userId)
            if (sbtSetting != null) {
                sbtSetting.streakDays = streakSbt.streakDays
                sbtSetting.credentialId = streakSbt.credentialId
                streakSbtSettings.add(sbtSetting)
            }
        }
        return UserStreakRewards(
            userId = userId,
            totalTPoints = totalTPoints,
            tPointsRecords = tPointsRecords,
            sbtSettings = streakSbtSettings,
            userMaxStreakDays = userMaxStreakDays
        )
    }

    fun getStreakLevel(days: Int): Int {
        return when {
            days == 0 -> 0 // 灰色
            days in 1..8 -> 1 // 橙色
            days in 9..29 -> 2 // "蓝色"
            days in 30..60 -> 3 // "紫色"
            days > 60 -> 4 // "金黄色"
            else -> 0
        }
    }

    fun getSbtSettingById(sbtId: Long, userId: Long): SbtSetting? {
        val tonAddress = userTonRepository.getTonUserWalletByUserId(userId)?.tonWallet
        val sbtReward = sbtRewardRepo.getSBTById(sbtId)
        val tonSyncHistory = tonSocietySyncRepo.getTonSyncHistoryBySBTId(sbtId)
        if (sbtReward != null) {
            val project = projectRepository.getProjectById(sbtReward.projectId)
            val sbtCampaignId =
                credentialGroupRepo.getCredentialGroupById(sbtReward.groupId)?.campaignId ?: 0L
            val sbtSetting = SbtSetting(
                sbtId = sbtReward.sbtId,
                sbtTitle = tonSyncHistory?.sbtCollectionTitle ?: "",
                sbtActivityId = sbtReward.activityId,
                sbtName = sbtReward.name,
                picUrl = sbtReward.picUrl,
                activityUrl = sbtReward.activityUrl,
                campaignId = tonSyncHistory?.campaignId ?: sbtCampaignId,
                projectUrl = project?.projectUrl ?: "",
                claimType = getUserSbtClaimedTypeByUidAddressActivityId(
                    userId,
                    tonAddress ?: "",
                    sbtReward.activityId
                ),
                sbtDescription = tonSyncHistory?.sbtDesc ?: ""
            )
            return sbtSetting
        }
        return null
    }

    fun getUserSbtClaimedTypeByUidAddressActivityId(userId: Long, tonAddress: String, activityId: Int): Int {
        // sbts 共用一个activity id，取最大的claim type
        val sbts = sbtRewardRepo.getSBTByActivityId(activityId)
        var maxClaimedType = 0
        for (sbt in sbts) {
            val rewardType = participantRepo.getUserSbtReward(sbt.sbtId, userId, sbt.groupId)?.claimType ?: 0
            if (rewardType > maxClaimedType) {
                maxClaimedType = rewardType
            }
        }
        return maxClaimedType
    }


}