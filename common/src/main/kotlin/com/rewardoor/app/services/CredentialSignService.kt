package com.rewardoor.app.services

import com.rewardoor.app.dao.CredentialSignRepository
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
@Transactional
class CredentialSignService(val credentialSignRepo: CredentialSignRepository) {
    fun addCredentialRawData(userId: Long, credentialId: Long, rawData: String) {
        val current = getCredentialSign(userId, credentialId)
        if (current != null) {
            updateCredentialRawData(userId, credentialId, rawData)
        } else {
            credentialSignRepo.addCredentialRawData(userId, credentialId, rawData)
        }
    }

    fun updateCredentialRawData(userId: Long, credentialId: Long, rawData: String) {
        credentialSignRepo.updateCredentialRawData(userId, credentialId, rawData)
    }

    fun updateCredentialVerified(userId: Long, credentialId: Long, sign: String) {
        credentialSignRepo.updateCredentialVerified(userId, credentialId, sign, 1)
    }

    fun getCredentialSign(userId: Long, credentialId: Long) = credentialSignRepo.getCredentialSign(userId, credentialId)

    fun checkVerifyStatus(userId: Long, credentialId: Long): Boolean {
        val credentialSign = getCredentialSign(userId, credentialId)?:return false
        return credentialSign.verified
    }
}