package com.rewardoor.app.services

import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.rewardoor.app.dao.*
import com.rewardoor.model.Credential
import com.rewardoor.model.Participant
import com.rewardoor.model.User
import com.rewardoor.model.UserTwitterInfo
import com.twitter.clientlib.TwitterCredentialsBearer
import com.twitter.clientlib.api.TwitterApi
import org.apache.http.client.utils.URIBuilder
import org.apache.http.message.BasicNameValuePair
import org.springframework.beans.factory.annotation.Value
import org.springframework.data.redis.core.StringRedisTemplate
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import org.springframework.web.reactive.function.client.ExchangeStrategies
import org.springframework.web.reactive.function.client.WebClient
import org.springframework.web.util.UriComponentsBuilder
import java.net.URI
import java.time.Duration
import java.time.Instant
import java.time.format.DateTimeFormatter

@Service
@Transactional
class UserTwitterService(
    val twitterUserRepo: TwitterUserRepository,
    val twitterMappingRepo: TwitterMappingRepo,
    val stateChallengeRepo: TwitterStateChallengeRepository,
    val userRepo: UserRepository,
    val participantRepo: ParticipantRepository,
    val idGenerator: DbIdGenerator,
    val redisTemplate: StringRedisTemplate,
    @Value("\${twitter.api.bearer}") val bearerToken: String,
    @Value("\${twitter.api.id}") val clientId: String,
    @Value("\${twitter.api.secret}") val clientSecret: String,
    @Value("\${twitter.ut_key}") val utoolKey: String
) {

    private val log = mu.KotlinLogging.logger {}
    private val key = "twitter_name_id_mapping"

    private final val strategies = ExchangeStrategies.builder()
        .codecs { codecs -> codecs.defaultCodecs().maxInMemorySize(20 * 1024 * 1024) }
        .build()
    private val webClient = WebClient.builder().exchangeStrategies(strategies).build()
    private val mapper = jacksonObjectMapper()

    fun addStateChallengeCache(state: String, challenge: String) {
        stateChallengeRepo.addStateChallengeCache(state, challenge)
    }

    fun getChallengeByState(state: String): String? {
        return stateChallengeRepo.getChallengeByState(state)
    }

    fun addUserRequestToken(userId: Long, state: String, challenge: String) {
        twitterUserRepo.addUserRequestToken(userId, state, challenge)
    }

    fun getTwitterUserCnt(): Long {
        return twitterUserRepo.getTwitterUserCnt()
    }

    fun getUserInfo(userId: Long): UserTwitterInfo? {
        return twitterUserRepo.getUserInfo(userId)
    }

    fun getUsersInfo(userIds: List<Long>): List<UserTwitterInfo> {
        return twitterUserRepo.getUsersInfo(userIds)
    }

    fun getUserInfoByTwitterId(twitterId: String): UserTwitterInfo? {
        return twitterUserRepo.getUserInfoByTwitterId(twitterId)
    }

    fun updateUserAccessToken(userId: Long, token: String, refreshToken: String) {
        twitterUserRepo.updateUserAccessToken(userId, token, refreshToken)
    }

    fun updateOrCreateUserInfo(info: UserTwitterInfo): Pair<UserTwitterInfo, Boolean> {
        val curInfo = getUserInfoByTwitterId(info.twitterId)
        if (curInfo != null) {
            info.userId = curInfo.userId
            updateUserInfo(info)
            return info to false
        }
        val newId = idGenerator.getNewId()
        userRepo.addUser(
            User(
                newId,
                User.AVATAR_BASE + newId,
                "",
                ""
            ), newId
        )
        info.userId = newId
        twitterUserRepo.addUserInfo(info)
        val participant = Participant(
            userId = newId,
            campaignId = 0L,
            wallet = "",
            nfts = emptyList(),
            points = emptyList(),
            pointNum = 0L,
            isJoin = false,
            isVisit = false,
            credentials = emptyList(),
            verifiedCredentials = emptyList(),
            participantDate = Instant.now()
        )
        participantRepo.createParticipant(participant)
        return info to true
    }

    fun updateUserInfo(info: UserTwitterInfo) {
        twitterUserRepo.updateUserInfo(info)
        addTwitterMapping(info.twitterName, info.twitterId)
    }

    fun createTwitterUserInfo(info: UserTwitterInfo) {
        twitterUserRepo.addUserInfo(info)
        addTwitterMapping(info.twitterName, info.twitterId)
    }

    fun addTwitterMapping(twitterName: String, twitterId: String) {
        redisTemplate.opsForHash<String, String>().put(key, twitterName, twitterId)
    }

    fun getTwitterIdFromMapping(twitterName: String): String? {
        val result = redisTemplate.opsForHash<String, String>().get(key, twitterName)
        if (result == null) {
            log.info { "getTwitterIdFromMapping miss: $twitterName" }
        }
        return result
    }

    fun checkSpaceActivity(
        utInfo: UserTwitterInfo,
        credential: TwitterCredentialsBearer,
        spaceId: String
    ): Boolean {
        val apiInstance = TwitterApi(credential)
        val response = apiInstance.spaces().findSpaceById(spaceId)
            .spaceFields(setOf("creator_id,host_ids,invited_user_ids,speaker_ids,topic_ids"))
            .execute()
        val speaked = response.data?.speakerIds?.contains(utInfo.twitterId) ?: false
        return speaked
    }

    fun checkTwitterRT(
        utInfo: UserTwitterInfo,
        credential: TwitterCredentialsBearer,
        twitId: String
    ): Boolean {
        val apiInstance = TwitterApi(credential)
        val response = apiInstance.users().tweetsIdRetweetingUsers(twitId)
            .execute()
        val rted = response.data?.map { it.id }?.contains(utInfo.twitterId)
        return rted ?: false
    }

    fun checkTwitterLike(
        utInfo: UserTwitterInfo,
        credential: TwitterCredentialsBearer,
        twitId: String
    ): Boolean {
        val apiInstance = TwitterApi(credential)
        val response = apiInstance.tweets().usersIdLikedTweets(utInfo.twitterId)
            .execute()
        val liked = response.data?.map { it.id }?.contains(twitId)
        return liked ?: false
    }

    fun verifyUserActivity(utInfo: UserTwitterInfo, action: Int, link: String, credentialId: Long): Boolean {
        val key = "verifyUserActivity:${utInfo.userId}:${credentialId}"
        if (redisTemplate.opsForValue().get(key) != null) {
            return true
        }
        val segments = URI(link).path.split("/")
        val target = segments.last()
        val credential = TwitterCredentialsBearer(bearerToken)
        try {
            val result = when (action) {
                1 -> checkLikeUsingThirdParty(utInfo.twitterId, target)
                2 -> checkReTwitUsingThirdParty(utInfo.twitterId, target)
                3 -> checkSpaceActivity(utInfo, credential, target)
                11 -> safeCheckFollow(utInfo.twitterId, segments[1])
                else -> throw IllegalArgumentException("Unknown action $action")
            }
            log.info(
                "verifyUserActivity: userId: {}, twitterId: {}, action: {}, target: {}, result: {}",
                utInfo.userId, utInfo.twitterId, action, target, result
            )
        } catch (e: Exception) {
            log.error("verifyUserActivity error", e)
            return true
        }
        redisTemplate.opsForValue().set(key, "1")
        return true
    }

    fun checkUserActivity(
        utInfo: UserTwitterInfo,
        credential: TwitterCredentialsBearer,
        action: String, target: String
    ) {
        val apiInstance = TwitterApi(credential)
//        apiInstance.addCallback {
//            updateUserAccessToken(userId, it.accessToken, it.refreshToken)
//        }
        when (action) {
            "like" -> apiInstance.users().tweetsIdLikingUsers(target).execute()
            "follow" -> checkFollow(utInfo.twitterId, target)
            "retweet" -> apiInstance.users().tweetsIdRetweetingUsers(target).execute()
            "space" -> apiInstance.spaces().findSpaceById(target).execute()
            else -> throw IllegalArgumentException("Unknown action $action")
        }
    }

    fun safeCheckFollow(fromId: String, target: String): Boolean {
        return try {
            checkFollow(fromId, target)
        } catch (e: Exception) {
            log.error("checkFollow error", e)
            true
        }
    }

    fun checkFollow(fromId: String, target: String): Boolean {
        var id = getTwitterIdFromMapping(target)
        if (id == null) {
            id = getAndSaveTwitterUserId(target)
        }
        if (id == null) {
            throw IllegalArgumentException("Unknown twitter user $target")
        }
        return checkFollowUsingThirdParty(fromId, id)
    }

    fun checkFollowUsingThirdParty(fromId: String, target: String): Boolean {
        val uri = URIBuilder("https://twitter.good6.top/api/base/apitools/followingsIds")
            .setParameters(BasicNameValuePair("apiKey", utoolKey), BasicNameValuePair("userId", fromId))
            .build()
        return sendThirdAndCheck(uri, target)
    }

    fun checkReTwitUsingThirdParty(fromId: String, target: String): Boolean {
        val uri = URIBuilder("https://twitter.good6.top/api/base/apitools/retweetersV2")
            .setParameters(BasicNameValuePair("apiKey", utoolKey), BasicNameValuePair("tweetId", target))
            .build()
        val response = webClient.get()
            .uri(uri)
            .retrieve()
            .bodyToMono(String::class.java)
            .block()
        val content = mapper.readTree(response)
        if (content["msg"].asText() != "SUCCESS") {
            log.warn { "checkReTwitUsingThirdParty failed not SUCCESS: $content" }
            return false
        }
        val data = content.get("data")
        if (data == null || data.asText().equals("Not Found")) {
            log.warn { "checkReTwitUsingThirdParty failed no data: $content" }
            return false
        }
        mapper.readTree(data.asText())?.get("data")?.get("retweeters_timeline")?.get("timeline")?.get("instructions")
            ?.forEach {
                it.get("entries")?.forEach { entry ->
                    val entryId = entry.get("entryId").asText()
                    if (entryId.startsWith("user-") && entryId.substring(5) == fromId) {
                        return true
                    }
                }
            }

        return false
    }

    fun checkLikeUsingThirdParty(fromId: String, target: String): Boolean {
        val uri = URIBuilder("https://twitter.good6.top/api/base/apitools/favoritersV2")
            .setParameters(
                BasicNameValuePair("apiKey", utoolKey),
                //BasicNameValuePair("userId", fromId),
                BasicNameValuePair("tweetId", target)
            )
            .build()
        val response = webClient.get()
            .uri(uri)
            .retrieve()
            .bodyToMono(String::class.java)
            .block()
        val content = mapper.readTree(response)
        if (content["msg"].asText() != "SUCCESS") {
            log.warn { "checkReTwitUsingThirdParty failed not SUCCESS: $content" }
            return false
        }
        val data = content.get("data")
        if (data == null || data.asText().equals("Not Found")) {
            log.warn { "checkReTwitUsingThirdParty failed no data: $content" }
            return false
        }
        mapper.readTree(data.asText())?.get("data")?.get("favoriters_timeline")?.get("timeline")?.get("instructions")
            ?.forEach {
                it.get("entries")?.forEach { entry ->
                    val entryId = entry.get("entryId").asText()
                    if (entryId.startsWith("user-") && entryId.substring(5) == fromId) {
                        return true
                    }
                }
            }

        return false
    }

    private fun sendThirdAndCheck(uri: URI, target: String): Boolean {
        val response = webClient.get()
            .uri(uri)
            .retrieve()
            .bodyToMono(String::class.java)
            .block()
        val content = mapper.readTree(response)
        if (content["msg"].asText() != "SUCCESS") return false
        val data = content.get("data") ?: return false
        if (data.asText().equals("Not Found")) return false
        val ids = mapper.readTree(data.asText()).get("ids")?.toList()?.map { it.asLong() } ?: return false
        return ids.contains(target.toLong())
    }

    fun getAndSaveTwitterUserId(name: String): String? {
        val uri = URIBuilder("https://twitter.good6.top/api/base/apitools/uerByIdOrNameShow")
            .setParameters(
                BasicNameValuePair("apiKey", utoolKey),
                BasicNameValuePair("screenName", name),
                BasicNameValuePair("userId", "0")
            )
            .build()
        val response = webClient.get()
            .uri(uri)
            .retrieve()
            .bodyToMono(String::class.java)
            .block()

        val context = mapper.readTree(response)
        if (context["msg"].asText() != "SUCCESS") return null
        val id = mapper.readTree(context.get("data").asText())?.get("id_str")?.asText() ?: return null

        addTwitterMapping(name, id)
        return id
    }

    fun getInfoByUrl(url: URI, labelType: Int): Map<String, Any?> {
        try {
            val pureUrl = UriComponentsBuilder.fromUri(url).replaceQuery(null).build().toString()
            val segments = url.path.split("/")
            if (url.path.contains("spaces")) {
                val apiInstance = TwitterApi(TwitterCredentialsBearer(bearerToken))

                val response = apiInstance.spaces().findSpaceById(segments[3])
                    .spaceFields(setOf("scheduled_start,started_at"))
                    .expansions(setOf("creator_id"))
                    .execute()
                return mapOf(
                    "code" to 200,
                    "data" to mapOf(
                        "userName" to response.includes?.users?.firstOrNull()?.name,
                        "link" to pureUrl,
                        "startAt" to response.data?.startedAt?.format(DateTimeFormatter.ofPattern("MM dd"))
                    ), "message" to "OK"
                )
            } else {
                val intentLink = Credential.intentLink(labelType, pureUrl)
                return mapOf(
                    "code" to 200,
                    "message" to "OK",
                    "data" to mapOf("userName" to segments[1], "link" to pureUrl, "intentLink" to intentLink)
                )
            }
        } catch (e: Exception) {
            log.error("getInfoByUrl error", e)
            return mapOf("code" to 4001, "message" to "Invalid url", "link" to url.toString())
        }
    }

}