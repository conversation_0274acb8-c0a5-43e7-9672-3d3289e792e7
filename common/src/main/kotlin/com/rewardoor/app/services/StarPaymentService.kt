package com.rewardoor.app.services

import com.pengrad.telegrambot.TelegramBot
import com.pengrad.telegrambot.model.*
import com.pengrad.telegrambot.request.AnswerPreCheckoutQuery
import com.rewardoor.app.dao.StarPaymentRepository
import com.rewardoor.model.StarInvoice
import com.rewardoor.model.StarPayment
import org.springframework.stereotype.Service
import org.springframework.beans.factory.annotation.Value
import org.springframework.transaction.annotation.Transactional

@Service
class StarPaymentService(
    private val starRepository: StarPaymentRepository,
    private val idGenerator: DbIdGenerator,
    private val retroactiveService: RetroactiveService,
    @Value("\${tg.mini_app.token}") private val tgBotToken: String
) {
    private val bot = TelegramBot(tgBotToken)

    @Transactional
    fun createPaymentInvoice(userId: Long, compensateAmount: Int): StarInvoice {
        val invoiceId = idGenerator.getNewId()
        val invoice = StarInvoice(
            invoiceId = invoiceId,
            payload = invoiceId.toString(),
            userId = userId,
            amount = compensateAmount,
            product = "Compensate Card"
        )
        starRepository.createInvoice(invoice)
        return invoice
    }

    @Transactional
    fun handlePreCheckoutQuery(query: PreCheckoutQuery) {
        val payload = query.invoicePayload()
        val invoice = starRepository.findInvoiceById(payload.toLong())
        if (invoice == null) {
            bot.execute(AnswerPreCheckoutQuery(query.id(), "Invoice Not Found: $payload"))
        }
        bot.execute(AnswerPreCheckoutQuery(query.id()))
    }

    @Transactional
    fun handleSuccessfulPayment(payment: SuccessfulPayment, chatId: Long) {
        val payload = payment.invoicePayload()
        val invoice = starRepository.findInvoiceById(payload.toLong())!!
        val newId = idGenerator.getNewId()
        val starPayment = StarPayment(
            paymentId = newId,
            telegramChargeId = payment.telegramPaymentChargeId(),
            userId = invoice.userId,
            starAmount = payment.totalAmount(),
            productAmount = invoice.amount,
            invoiceId = invoice.invoiceId
        )
        starRepository.create(starPayment)
        retroactiveService.addRetroactiveCard(invoice.userId, invoice.amount, newId)
    }

    @Transactional
    fun getPaymentsByUserId(userId: Long): List<StarPayment> {
        return starRepository.findUserPayments(userId)
    }
}