package com.rewardoor.app.services

import com.fasterxml.jackson.databind.JsonNode
import com.rewardoor.app.bean.ContractInfo
import mu.KotlinLogging
import org.springframework.beans.factory.annotation.Value
import org.springframework.http.HttpHeaders
import org.springframework.http.MediaType
import org.springframework.http.RequestEntity
import org.springframework.stereotype.Service
import org.springframework.web.client.RestTemplate
import org.springframework.web.client.exchange
import java.net.URI

@Service
class ContractService(
    private val restTemplate: RestTemplate,
    private val contactMaps: Map<Int, ContractInfo>,
    @Value("\${contract_caller.url}") private val callerUrl: String,
    @Value("\${contract_caller.secret}") private val callerSecret: String
) {
    private val logger = KotlinLogging.logger{}

    fun deployContract(name: String, symbol: String, base: String): String {
        logger.info { "callerUrl: $callerUrl, secret: $callerSecret" }
        val request = RequestEntity.post(URI.create("${callerUrl}/deployNew"))
            .contentType(MediaType.APPLICATION_JSON)
            .header(HttpHeaders.AUTHORIZATION, callerSecret)
            .body("""{"name": "$name", "symbol": "$symbol", "baseURI": "$base"}""")
        val result = restTemplate.exchange<JsonNode>(request).body!!
        return result["events"]["RewardoorBadgerDeployed"]["returnValues"]["addr"].asText()
    }

    fun getContractInfoByChain(chainId: Int): ContractInfo {
        return contactMaps[chainId]!!
    }

    fun getAll(): Map<Int, ContractInfo> {
        return contactMaps
    }
}