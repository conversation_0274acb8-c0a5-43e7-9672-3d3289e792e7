package com.rewardoor.app.services

import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.google.common.hash.Hashing
import com.rewardoor.app.dao.CallbackHistoryRepository
import com.rewardoor.app.dao.ProjectExternalRepository
import com.rewardoor.app.dao.ProjectRepository
import com.rewardoor.model.*
import org.springframework.beans.factory.annotation.Value
import org.springframework.http.MediaType.APPLICATION_JSON
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Service
import org.springframework.transaction.support.TransactionTemplate
import org.springframework.web.reactive.function.client.WebClient

@Service
class CallbackService(
    val callbackHistoryRepo: CallbackHistoryRepository,
    val projectExternalConfigRepo: ProjectExternalRepository,
    val projectRepository: ProjectRepository,
    val transactionTemplate: TransactionTemplate,
    @Value("\${tenant.go-plus.id}") val goPlusId: Long,
    @Value("\${tenant.go-plus.url}") val goPlusCbUrl: String
) {
    private val mapper = jacksonObjectMapper()

    @Async("callbackServiceExecutor")
    fun callback(user: User,
                 projectId: Long,
                 credentialId: Long,
                 completeTime: Long,
                 userDiscord: UserDiscord? = null,
                 userTwitterInfo: UserTwitterInfo? = null,
                 userTelegramInfo: UserTelegramInfo? = null) {
        if (projectId == goPlusId) {
            val history = try {
                callbackGoPlus(goPlusCbUrl, user, credentialId, completeTime/1000)
                CallbackHistory(projectId, credentialId, user.userId, goPlusCbUrl, "SUCCEED", 1, "{userId: ${user.userId}, credentialId: $credentialId}")
            } catch (ex: Exception) {
                log.error("Callback goPlus failed", ex)
                CallbackHistory(projectId, credentialId, user.userId, goPlusCbUrl, "FAILED", 1, "{userId: ${user.userId}, credentialId: $credentialId}")
            }
            addCallbackHistory(history)
            return
        }

        val externalConfig = transactionTemplate.execute {
            projectExternalConfigRepo.getProjectExternalConfig(projectId)
        } ?: return
        if (externalConfig.status != 1) return

        val project = transactionTemplate.execute {
            projectRepository.getProjectById(projectId)
        }
        val chain = project?.chain ?: "evm"

        val params = mutableMapOf<String, Any>(
            "credentialId" to credentialId,
            "completeTime" to completeTime
        )

        when (chain) {
            "evm" -> params["evmAddress"] = user.wallet
            "ton" -> params["tonAddress"] = user.ton.hexAddress.orEmpty()
            "sui" -> params["suiAddress"] = user.suiAddress
            else -> log.warn("Unsupported chain type: $chain")
        }

        if (userDiscord != null) {
            params["discordId"] = userDiscord.dcId
            params["discordGlobalName"] = userDiscord.globalName
        }

        if (userTwitterInfo != null) {
            params["twitterId"] = userTwitterInfo.twitterId
            params["twitterName"] = userTwitterInfo.twitterName
        }

        if (userTelegramInfo != null) {
            params["telegramId"] = userTelegramInfo.tgId
        }

        try {
            val result = httpClient.post()
                .uri(externalConfig.callbackUrl)
                .contentType(APPLICATION_JSON)
                .header("appId", externalConfig.projectId.toString())
                .header("X-App-Sign", signParamsFilterOutEmpty(params, externalConfig.appKey))
                .header("timestamp", System.currentTimeMillis().toString())
                .bodyValue(params)
                .retrieve()
                .bodyToMono(String::class.java)
                .block()
            log.info("Callback to {}, address: {}, result: {}", externalConfig.projectId, user.wallet, result)
            transactionTemplate.execute {
                addCallbackHistory(
                    CallbackHistory(
                        externalConfig.projectId,
                        credentialId,
                        user.userId,
                        externalConfig.callbackUrl,
                        "SUCCEED",
                        1,
                        mapper.writeValueAsString(params)
                    )
                )}
        } catch (e: Exception) {
            log.error("Callback failed", e)
            addCallbackHistory(
                CallbackHistory(
                externalConfig.projectId,
                credentialId,
                user.userId,
                externalConfig.callbackUrl,
                "FAILED",
                1,
                mapper.writeValueAsString(params)
            ))
        }
    }

    fun addCallbackHistory(history: CallbackHistory) {
        transactionTemplate.execute {
            val current = callbackHistoryRepo.getCallbackHistory(history.credentialId, history.userId)
            log.info { "add callback history: $history" }
            if (current == null) {
                callbackHistoryRepo.addCallbackHistory(history)
            } else {
                callbackHistoryRepo.updateTimes(history.credentialId, history.userId, current.times + 1, history.status)
            }
        }
    }

    fun signParamsFilterOutEmpty(params: Map<String, Any>, appKey: String): String {
        val sortedParams = params.filterNot { it.value.toString().isEmpty() }.toSortedMap()
        val paramsString = sortedParams.map { "${it.key}=${it.value}" }.joinToString("&") + "&$appKey"
        val sign = Hashing.md5().hashString(paramsString, Charsets.UTF_8).toString()
        log.info { "filter out params original: ${params.keys.joinToString(",")}" }
        log.info("sign params after filter out: $paramsString, sign: $sign")
        return sign
    }

    fun signParams(params: Map<String, Any>, appKey: String): String {
        val sortedParams = params.toSortedMap()
        val paramsString = sortedParams.map { "${it.key}=${it.value}" }.joinToString("&") + "&$appKey"
        val sign = Hashing.md5().hashString(paramsString, Charsets.UTF_8).toString()
        log.info("sign params: $paramsString, sign: $sign")
        return sign
    }

    companion object {
        private val httpClient = WebClient.create()
        private val log = mu.KotlinLogging.logger {}

        fun callbackGoPlus(url: String,
                           user: User,
                           credentialId: Long,
                           completeTime: Long
        ) {
            val params = mapOf(
                "userAddress" to user.wallet,
                "channelCode" to "TBOOK",
                "channelTaskId" to credentialId,
                "completeTime" to completeTime
            )
            val now = System.currentTimeMillis()
            val signParams = params + mapOf("manageKey" to "C6ebAfQKeDht3OOigsQ30tOTafShNQkZ", "timestamp" to now)
            val sortedParam = signParams.toSortedMap().map { "${it.key}${it.value}" }.joinToString("")
            val paramsString = "X-Address${user.wallet}X-Projecttbook${sortedParam}"
            val sign = Hashing.md5().hashString(paramsString, Charsets.UTF_8).toString()

            log.info("callback goPlus, sign: [{}] params: [{}]", sign, paramsString)

            val response = httpClient.post()
                .uri(url)
                .contentType(APPLICATION_JSON)
                //.header("appId", "100002")
                .header("sign", sign)
                .header("timestamp", now.toString())
                .header("manageId", "100003")
                .header("X-Address", user.wallet)
                .header("X-Project", "tbook")
                .bodyValue(params)
                .retrieve()
                .bodyToMono(String::class.java)
                .block()
            log.info("callback response: {}", response)
        }
    }
}