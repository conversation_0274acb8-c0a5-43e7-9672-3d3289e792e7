package com.rewardoor.app.services

import com.rewardoor.IdGenerator
import com.rewardoor.app.dao.CredentialRepository
import com.rewardoor.model.Credential
import org.springframework.data.redis.core.StringRedisTemplate
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
@Transactional
class CredentialService(
    val credentialRepo: CredentialRepository,
    val idGenerator: IdGenerator,
    val redisTemplate: StringRedisTemplate
) {
    private val logger = mu.KotlinLogging.logger {}

    fun createCredential(credential: Credential): Credential {
        val credentialId = idGenerator.getNewId()
        credential.credentialId = credentialId
        credentialRepo.createCredential(credential)
        return credentialRepo.getCredentialById(credentialId)!!
    }

    fun getCredentialById(credentialId: Long): Credential? {
        return credentialRepo.getCredentialById(credentialId)
    }

    fun updateCredential(credential: Credential): Credential? {
        val credentialId = credential.credentialId
        credentialRepo.updateCredential(credential)
        return getCredentialById(credentialId)
    }

    fun getAllCredentials(): List<Credential> {
        return credentialRepo.getAllCredentials()
    }

    fun getCredentialCnt(): Long {
        return credentialRepo.getCredentialCnt()
    }

    fun getCredentialCntFromRedis(): Long {
        val key = "total_credential_claimed_count:"
        val v = redisTemplate.opsForValue().get(key)
        if (v == null) {
            logger.info { "EMPTY result from $key" }
            return 0
        }
        logger.info { "total_credential_claimed_count : $v" }
        return v.toLong()
    }

    fun getCredentialByProjectId(projectId: Long): List<Credential> {
        return credentialRepo.getCredentialByProjectId(projectId)
    }

    fun getCredentialByCreatorId(creatorId: Long): List<Credential> {
        return credentialRepo.getCredentialByCreatorId(creatorId)
    }

    fun getCredentialEligibleCount(ids: List<Long>): Map<Long, Long> {
        return credentialRepo.getEligibleCredentialCount(ids)
    }

    fun getCredentialListByLabelType(labelType: Int): List<Credential> {
        return credentialRepo.getCredentialByLabelType(labelType)
    }
}
