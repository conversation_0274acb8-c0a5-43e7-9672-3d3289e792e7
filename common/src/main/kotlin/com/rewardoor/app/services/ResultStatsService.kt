package com.rewardoor.app.services

import com.rewardoor.app.dao.ResultReportRepository
import com.rewardoor.model.AddressPoint
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
@Transactional
class ResultStatsService(val resultReportRepository: ResultReportRepository) {
    fun getProjectPoints(id: Long): List<AddressPoint> {
        return resultReportRepository.getProjectPoints(id)
            .map { AddressPoint(it.key, it.value) }
    }

}