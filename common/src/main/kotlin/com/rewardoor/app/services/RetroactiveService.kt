package com.rewardoor.app.services

import com.rewardoor.app.dao.RetroactiveInventoryRepository
import com.rewardoor.model.RetroactiveCardInventory
import com.rewardoor.model.RetroactiveCardLedger
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
class RetroactiveService(
    private val retroactiveRepository: RetroactiveInventoryRepository,
    private val idGenerator: DbIdGenerator
) {
    @Transactional
    fun addRetroactiveCard(userId: Long, amount: Int, outId: Long) {
        val current = retroactiveRepository.getUserCardInventoryWithLock(userId)
        if (current == null) {
            val newId = idGenerator.getNewId()
            retroactiveRepository.addUserCardInventory(
                RetroactiveCardInventory(newId, userId, amount, amount, 0))
            val ledger = RetroactiveCardLedger(
                idGenerator.getNewId(), userId, outId.toString(), 0, amount.toLong(), 0, amount.toLong())
            retroactiveRepository.addCardLedger(ledger)
        } else {
            val ledger = RetroactiveCardLedger(
                idGenerator.getNewId(), userId, outId.toString(), 0, amount.toLong(),
                current.balanceCards.toLong(), (current.balanceCards + amount).toLong())
            retroactiveRepository.updateCardInventory(
                current.copy(totalCards = current.totalCards + amount,
                    balanceCards = current.balanceCards + amount))
            retroactiveRepository.addCardLedger(ledger)
        }
    }

    @Transactional
    fun consumeRetroactiveCard(userId: Long, amount: Int, outId: String): RetroactiveCardLedger {
        val current = retroactiveRepository.getUserCardInventoryWithLock(userId)
            ?: throw IllegalStateException("User $userId has no retroactive card")
        if (current.balanceCards < amount) {
            throw IllegalStateException("User $userId has insufficient retroactive card")
        }
        val ledger = RetroactiveCardLedger(
            idGenerator.getNewId(), userId, outId, amount.toLong(), 0,
            current.balanceCards.toLong(), (current.balanceCards - amount).toLong())
        retroactiveRepository.updateCardInventory(
            current.copy(usedCards = current.usedCards + amount,
                balanceCards = current.balanceCards - amount))
        retroactiveRepository.addCardLedger(ledger)
        return ledger
    }
}