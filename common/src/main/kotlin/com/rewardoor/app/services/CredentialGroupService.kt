package com.rewardoor.app.services

import com.fasterxml.jackson.databind.ObjectMapper
import com.google.gson.Gson
import com.rewardoor.IdGenerator
import com.rewardoor.app.dao.*
import com.rewardoor.app.utils.TgStarter
import com.rewardoor.enums.SocialType
import com.rewardoor.model.*
import org.json.JSONObject
import org.springframework.core.env.Environment
import org.springframework.data.redis.core.StringRedisTemplate
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.net.URI
import java.time.Instant


@Service
@Transactional
class CredentialGroupService(
    val credentialRepo: CredentialRepository,
    val credentialGroupRepo: CredentialGroupRepository,
    val idGenerator: IdGenerator,
    val nftRepo: NFTRepository,
    val pointRepo: PointRepository,
    val sbtRepo: SBTRewardRepository,
    val userTwitterService: UserTwitterService,
    val participantRepo: ParticipantRepository,
    val telegramService: TelegramService,
    val discordService: DiscordService,
    val credentialSignService: CredentialSignService,
    val credentialAirdropAddressService: CredentialAirdropAddressService,
    val tokenRepo: TokenRepository,
    val credentialService: CredentialService,
    val redisTemplate: StringRedisTemplate,
    private val wiseScoreService: WiseScoreService,
    private val taskVerifyService: TaskVerifyService,
    val tonSyncRepo: TonSocietySyncRepository,
    private val env: Environment,
    private val customCredentialService: CustomCredentialService,
    private val userRepo: UserRepository,
    private val campaignRepo: CampaignRepository,
    private val tPointsConsumeRepo: TPointsConsumeRepository,
    private val wiseInviteService: WiseInviteService,
    private val tPointsService: TPointsService,
    private val telegramUserPushRepository: TelegramUserPushRepository,
    private val wiseScoreUserStreakService: WiseScoreUserStreakService,
    private val suiSbtRepository: SuiSbtRepository,
) {
    private val logger = mu.KotlinLogging.logger {}
    private val mapper = ObjectMapper()
    fun createCredentialGroup(credentialGroup: CredentialGroup, campaignId: Long, userId: Long): CredentialGroup {
        val groupId = idGenerator.getNewId()
        val projectId = credentialGroup.projectId
        credentialGroup.id = groupId
        credentialGroupRepo.createCredentialGroup(credentialGroup)
//        val credentialList = Gson().fromJson(credentialGroup.credentialList, Array<Credential>::class.java).toList()
        val credentialList = credentialGroup.credentialList

//        val credentialList = credentialGroup.list
        for (credential in credentialList) {
//            val credentialId = credential.credentialId  --remove default credential
//            val defaultCredential = credentialRepo.getCredentialById(credentialId)!!
            val newCredentialId =
                if (credential.credentialId != 0L) credential.credentialId else (idGenerator.getNewId())
            val link = credential.link // eg: https://twitter.com/i/spaces/1ypKddpBgwVKW?s=20  ->  1ypKddpBgwVKW

            val url = URI(link)
            val segments = url.path.split("/")
            val spaceId = segments.last()
//            val regex = Regex("/spaces/(\\w+)\\?s=")
//            val matchResult = regex.find(link)
//            val spaceId = matchResult?.groupValues?.get(1)!!
            credential.credentialId = newCredentialId
            credential.groupId = groupId
            credential.projectId = projectId
//            credential.name = defaultCredential.name
//            credential.nameExp = defaultCredential.nameExp
//            credential.labelType = defaultCredential.labelType
//            credential.picUrl = defaultCredential.picUrl
//            credential.groupType = defaultCredential.groupType
            credential.spaceId = spaceId
            credential.campaignId = campaignId
            if (credential.labelType == 12) {
                credential.proposalId = link.split("/").lastOrNull().toString()
            }
            credential.ofMultiRoles()
            credentialRepo.createCredential(credential)
        }
//        val nftList = Gson().fromJson(credentialGroup.nftList, Array<NFT>::class.java).toList()
        val nftList = credentialGroup.nftList
//        val pointList = Gson().fromJson(credentialGroup.pointList, Array<Point>::class.java).toList()
        val pointList = credentialGroup.pointList
        val tokenList = credentialGroup.tokenList
        for (nft in nftList) {
            nft.groupId = groupId
            nft.projectId = projectId
            nftRepo.createNFTGroup(nft)
        }
        for (point in pointList) {
            val pointId = idGenerator.getNewId()
            point.pointId = pointId
            point.groupId = groupId
            point.projectId = projectId
            pointRepo.createPoint(point)
        }
        for (token in tokenList) {
            val tokenId = idGenerator.getNewId()
            token.tokenId = tokenId
            token.groupId = groupId
            token.projectId = projectId
            tokenRepo.createToken(token)
        }
        val existActivityId = credentialGroup.existActivityId
        if (existActivityId != 0) {
            val existSbt = sbtRepo.getSBTByActivityId(existActivityId)
            println("category is: ${existSbt[0].category}")
            if (existSbt.isNotEmpty()) {
                val originSbt = existSbt[existSbt.size - 1]
                val newSbtId = idGenerator.getNewId()
                val copySbt = SBTReward(
                    sbtId = newSbtId,
                    name = originSbt.name,
                    category = originSbt.category,
                    activityId = existActivityId,
                    activityUrl = originSbt.activityUrl,
                    picUrl = originSbt.picUrl,
                    number = originSbt.number,
                    methodType = originSbt.methodType,
                    unlimited = originSbt.unlimited,
                    rewardNum = originSbt.rewardNum,
                    projectId = projectId,
                    groupId = groupId
                )
                println("copySbt is: ${copySbt.category}")
                sbtRepo.createSBT(copySbt)
            }
        }
        for (sbt in credentialGroup.sbtList) {
            val sbtId = idGenerator.getNewId()
            sbt.sbtId = sbtId
            sbt.groupId = groupId
            sbt.projectId = projectId
            sbtRepo.createSBT(sbt)
        }
        // sui sbt create
        val existSuiSbtActivityId = credentialGroup.existSuiSbtActivityId
        val suiSbtReward = credentialGroup.suiSbtReward
        if (existSuiSbtActivityId != 0L) {
            val existSuiSbt = suiSbtRepository.getSuiSbtSyncById(existSuiSbtActivityId)
            if (existSuiSbt != null) {
                val newSbtId = idGenerator.getNewId()
                val copySbt = SuiSbtReward(
                    suiSbtId = newSbtId,
                    suiSbtActivityId = existSuiSbt.suiSbtActivityId,
                    sbtName = existSuiSbt.sbtName,
                    sbtDesc = existSuiSbt.sbtDesc,
                    sbtUrl = existSuiSbt.sbtUrl,
                    contractAddress = existSuiSbt.contractAddress,
                    objectId = existSuiSbt.objectId,
                    projectId = projectId,
                    creatorId = userId,
                    groupId = groupId,
                )
                suiSbtRepository.createSuiSbt(copySbt)
            }
        } else if (suiSbtReward != null) {
            val newSbtId = idGenerator.getNewId()
            suiSbtReward.suiSbtId = newSbtId
            suiSbtRepository.createSuiSbt(suiSbtReward)
        }
        return credentialGroupRepo.getCredentialGroupById(groupId)!!
    }

    fun blackPicUrlMapping(labelType: Int): String {
        return when (labelType) {
            1, 2, 3, 11 -> {  //twitter
                "https://rd-worker.xgamma.workers.dev/img/c0cf8d8fca56420ca9c6704b30b492bc"
            }

            4, 5 -> { //discord
                "https://rd-worker.xgamma.workers.dev/img/25ae23a2254d431d9226786edc9adc68"
            }

            6, 7 -> {  //telegram
                "https://rd-worker.xgamma.workers.dev/img/2511aaa3efe44490a170c2c13b12fba7"
            }

            8 -> {  //visit page
                "https://rd-worker.xgamma.workers.dev/img/e645e1f23f6f4d579f7278e411c7fd1e"
            }

            10 -> { //register
                "https://rd-worker.xgamma.workers.dev/img/9ea740903a8c415c85cfe3de5fa0f83b"
            }

            12 -> { //snapshot
                "https://rd-worker.xgamma.workers.dev/img/6a35e508cab542ed836d450a134dc528"
            }

            else -> {
                ""
            }
        }
    }


    fun whitePicUrlMapping(labelType: Int): String {
        return when (labelType) {
            1, 2, 3, 11 -> {  //twitter
                "https://rd-worker.xgamma.workers.dev/img/2d298a6a69844ac5a58c1963588a3293"
            }

            4, 5 -> { //discord
                "https://rd-worker.xgamma.workers.dev/img/5fe124fa86bf4cca9de589101503f9aa"
            }

            6, 7 -> {  //telegram
                "https://rd-worker.xgamma.workers.dev/img/417d714acda94bc586d0a17c24129a2e"
            }

            8 -> {  //visit page
                "https://rd-worker.xgamma.workers.dev/img/bd265e367217418eb8c1fcf4acbd97ac"
            }

            10 -> { //register
                "https://rd-worker.xgamma.workers.dev/img/68cb86e9972a428c9e22a0e9d498e39f"
            }

            12 -> { //snapshot
                "https://rd-worker.xgamma.workers.dev/img/97f2e04b3f5c415a8c0e617c79db9197"
            }

            else -> {
                ""
            }
        }
    }

    fun getCredentialGroupById(groupId: Long): CredentialGroup? {
        val group = credentialGroupRepo.getCredentialGroupById(groupId)!!
        addCredentialsAndRewards(group)
        return group
    }

    fun addCredentialsAndRewards(credentialGroup: CredentialGroup): CredentialGroup {
        val credentials = getCredentialsByGroupId(credentialGroup.id)!!
        val gson = Gson()
//        for (credential in credentials) {
//            val json = credential.getCredentialForm()
//            val credentialForm = gson.fromJson(json, FormList::class.java)
//            credential.list = credentialForm.list
//            if (credential.labelType == 4 || credential.labelType == 5) { //update discord server name
//                val discordServerName =
//                    discordService.getInfoByUrl(credential.link)?.get("serverName").toString()
//                credential.displayExp = credential.displayExp.replace("discordServerName", discordServerName)
//                credential.intentDisplayExp =
//                    credential.intentDisplayExp.replace("discordServerName", discordServerName)
//            }
//        }
//        val nfts = nftRepo.getNFTByGroupId(credentialGroup.id)
        val nfts = nftRepo.getNFTGroupByGroupId(credentialGroup.id)
        val points = pointRepo.getPointByGroupId(credentialGroup.id)
        val tokens = tokenRepo.getTokenByGroupId(credentialGroup.id)
        val sbts = sbtRepo.getSBTByGroupId(credentialGroup.id)
        val suiSbts = suiSbtRepository.getSuiSbtRewardByGroupId(credentialGroup.id)
//        credentialGroup.credentialList = mapper.writeValueAsString(credentials)
        credentialGroup.credentialList = credentials
//        credentialGroup.nftList = mapper.writeValueAsString(nfts)
        credentialGroup.nftList = nfts
//        credentialGroup.pointList = mapper.writeValueAsString(points)
        credentialGroup.pointList = points
        credentialGroup.tokenList = tokens
        credentialGroup.sbtList = sbts
        credentialGroup.suiSbtList = suiSbts
        if (sbts.isNotEmpty()) {
            val sbtCollections = tonSyncRepo.getTonSyncHistoryByGroupId(credentialGroup.id)
            credentialGroup.sbtCollection = sbtCollections
        }
        return credentialGroup
    }


    fun getCredentialGroupByProjectId(projectId: Long, creatorId: Long): List<CredentialGroup>? {
        val defaultGroups = getDefaultGroup(projectId, creatorId)
        val groupList = mutableListOf<CredentialGroup>()
        groupList.addAll(defaultGroups)
        val groups = credentialGroupRepo.getCredentialGroupByProjectId(projectId)
        for (group in groups) {
            addCredentialsAndRewards(group)
        }
        groupList.addAll(groups)
        return groupList
    }

    fun getDefaultGroup(projectId: Long, creatorId: Long): List<CredentialGroup> {
        val followerUser = "link"
        val retweetUser = "link"
        val followCredential =
            Credential(
                11,
                "Follow",
                "Follow <%= link %>",
                11,
                "",
                "https://rd-worker.xgamma.workers.dev/img/2d298a6a69844ac5a58c1963588a3293",
                projectId,
                creatorId,
                0,
                2,
                "",
                0,
                0,
                0,
                0,
                "Tweet Link",
                "Paste tweet link here"
            )
        val likeCredential =
            Credential(
                0,
                "Like a Tweet",
                "Like <%= link %>",
                1,
                "",
                "https://rd-worker.xgamma.workers.dev/img/2d298a6a69844ac5a58c1963588a3293",
                projectId,
                creatorId,
                0,
                2,
                "",
                0,
                0,
                0,
                0,
                "Tweet Link",
                "Paste tweet link here"
            )
        val retweetCredential =
            Credential(
                1,
                "Twitter Retweet",
                "Retweet <%= link %>",
                2,
                "",
                "https://rd-worker.xgamma.workers.dev/img/2d298a6a69844ac5a58c1963588a3293",
                projectId,
                creatorId,
                0,
                2,
                "",
                0,
                0,
                0,
                0,
                "Tweet Link",
                "Paste tweet link here"
            )
        val spaceCredential =
            Credential(
                2,
                "Speak in Twitter Spaces",
                "Speak <%= link %>",
                3,
                "",
                "https://rd-worker.xgamma.workers.dev/img/2d298a6a69844ac5a58c1963588a3293",
                projectId,
                creatorId,
                0,
                2,
                "",
                0,
                0,
                0,
                0,
                "Tweet Space Link",
                "Paste twitter space link here"
            )
        val joinDiscordCredential =
            Credential(
                3,
                "Join Server",
                "Join Server <%= link %>",
                4,
                "",
                "https://rd-worker.xgamma.workers.dev/img/5fe124fa86bf4cca9de589101503f9aa",
                projectId,
                creatorId,
                0,
                2,
                "",
                0,
                0,
                0,
                0,
                "Discord Server URL",
                "https://discord.gg/xxxx"
            )
        val verifyDiscordRole =
            Credential(
                4,
                "Verify Role",
                "Have <%= link %> member Role in Server",
                5,
                "",
                "https://rd-worker.xgamma.workers.dev/img/5fe124fa86bf4cca9de589101503f9aa",
                projectId,
                creatorId,
                0,
                2,
                "",
                0,
                0,
                0,
                0,
                "Discord Server URL",
                "https://discord.gg/xxxx"
            )
        val telegramGroupCredential =
            Credential(
                5,
                "Join Group",
                "Join Group <%= link %>",
                6,
                "",
                "https://rd-worker.xgamma.workers.dev/img/417d714acda94bc586d0a17c24129a2e",
                projectId,
                creatorId,
                0,
                2,
                "",
                0,
                0,
                0,
                0,
                "Public Group Invite Link",
                "Please paste the invite link to your telegram channel "
            )
        val telegramChannelCredential =
            Credential(
                6,
                "Join Channel",
                "Join Channel <%= link %>",
                7,
                "",
                "https://rd-worker.xgamma.workers.dev/img/417d714acda94bc586d0a17c24129a2e",
                projectId,
                creatorId,
                0,
                2,
                "",
                0,
                0,
                0,
                0,
                "Public Channel Invite Link",
                "Please paste the invite link to your telegram channel "
            )
        val visitPageCredential =
            Credential(
                credentialId = 7,
                name = "Visit a Page or Site",
                nameExp = "Visit a Page or Site",
                labelType = 8,
                picUrl = "https://rd-worker.xgamma.workers.dev/img/bd265e367217418eb8c1fcf4acbd97ac",
                projectId = projectId,
                creatorId = creatorId,
                groupType = 6,
                tipText = "Link",
                placeHolder = "Please paste the link the users need to visit"
            )
        val signCredential = Credential(
            10,
            "Register an Event",
            "Register an Event",
            10,
            "",
            "https://rd-worker.xgamma.workers.dev/img/68cb86e9972a428c9e22a0e9d498e39f",
            projectId,
            creatorId,
            0,
            6,
            "",
            0,
            0,
            0,
            0,
            "Message",
            "Name"
        )
        val snapShotCredential =
            Credential(
                12,
                "Vote on a Snapshot Proposal",
                "Vote on a Snapshot Proposal",
                12,
                "",
                "https://rd-worker.xgamma.workers.dev/img/97f2e04b3f5c415a8c0e617c79db9197",
                projectId,
                creatorId,
                0,
                1,
                "",
                0,
                0,
                0,
                0,
                "Snapshot Proposal URL",
                "Please enter a specific Snapshot Proposal"
            )
        val airDropCredential =
            Credential(
                13,
                "Airdrop Address Aggregation",
                "Airdrop Address Aggregation",
                13,
                "",
                "https://rd-worker.xgamma.workers.dev/img/55fdf68bcded4fcfacb9f103ade8ddfb",
                projectId,
                creatorId,
                groupId = 0,
                groupType = 7,
                spaceId = "",
                campaignId = 0,
                isVerified = 0,
                giveAway = 0,
                eligibleCount = 0,
                tipText = "Message",
                placeHolder = "Name"
            )
        val communityCredentials = listOf<Credential>(
            followCredential,
            likeCredential,
            retweetCredential,
            spaceCredential,
            joinDiscordCredential,
            verifyDiscordRole,
            telegramGroupCredential,
            telegramChannelCredential
        )
        val activityCredentials = listOf<Credential>(visitPageCredential, signCredential)
        val governanceCredentials = listOf<Credential>(snapShotCredential)
        val submitCredentials = listOf<Credential>(airDropCredential)
        val gson = Gson()
//        for (credential in communityCredentials) {
//            val json = credential.getCredentialForm()
//            val credentialForm = gson.fromJson(json, FormList::class.java)
//            credential.list = credentialForm.list
//        }
//        for (credential in activityCredentials) {
//            val json = credential.getCredentialForm()
//            val credentialForm = gson.fromJson(json, FormList::class.java)
//            credential.list = credentialForm.list
//        }
//        for (credential in governanceCredentials) {
//            val json = credential.getCredentialForm()
//            val credentialForm = gson.fromJson(json, FormList::class.java)
//            credential.list = credentialForm.list
//        }
//        for (credential in submitCredentials) {
//            val json = credential.getCredentialForm()
//            val credentialForm = gson.fromJson(json, FormList::class.java)
//            credential.list = credentialForm.list
//        }
        val communityGroup =
            CredentialGroup(2, "Community", 1, communityCredentials, 0, creatorId, 0, 0, emptyList(), emptyList())
        val activityGroup = CredentialGroup(
            6, "Activity", 2, activityCredentials, 0, creatorId, 0, 0, emptyList(),
            emptyList()
        )
        val governanceGroup = CredentialGroup(
            1, "Governance", 3, governanceCredentials, 0, creatorId, 0, 0, emptyList(),
            emptyList()
        )
        val submitGroup = CredentialGroup(
            groupType = 7,
            name = "Submit",
            id = 4,
            credentialList = submitCredentials,
            projectId = 0,
            creatorId = creatorId,
            campaignId = 0,
            status = 0,
            emptyList(),
            emptyList()
        )
        return listOf<CredentialGroup>(communityGroup, activityGroup, governanceGroup, submitGroup)
    }

    fun getCredentialGroupByCampaignId(campaignId: Long): List<CredentialGroup>? {
        val groupList = credentialGroupRepo.getCredentialGroupByCampaignId(campaignId)
        for (group in groupList) {
            addCredentialsAndRewards(group)
        }
        return groupList
    }

    fun getCredentialGroupsByCampaignIds(campaignIds: List<Long>): List<CredentialGroup>? {
        val groupList = credentialGroupRepo.getCredentialGroupsByCampaignIds(campaignIds)
        for (group in groupList) {
            addCredentialsAndRewards(group)
        }
        return groupList
    }

    fun getGroupsByCampaignId(campaignId: Long): List<CredentialGroup>? {
        val groupList = credentialGroupRepo.getCredentialGroupByCampaignId(campaignId)
        return groupList
    }

    fun getGroupsByCampaignIds(campaignIds: List<Long>): Map<Long, List<CredentialGroup>> {
        val allGroups = credentialGroupRepo.getCredentialGroupsByCampaignIds(campaignIds)
        return allGroups.groupBy { it.campaignId }
    }

    fun getCredentialsByGroupId(groupId: Long): List<Credential>? {
        return credentialRepo.getCredentialByGroupId(groupId)
    }

    fun updateCredentialGroup(credentialGroup: CredentialGroup): CredentialGroup? {
        val groupId = credentialGroup.id
        val oldGroup = getCredentialGroupById(groupId)!!
        val oldCredentials = oldGroup.credentialList
        val oldNfts = oldGroup.nftList
        val oldPoints = oldGroup.pointList
        val oldSBTRewards = oldGroup.sbtList
        val oldTokens = oldGroup.tokenList
        credentialGroup.credentialList.forEach { credential ->
            println("aaaaa: " + credential.credentialId + "  " + credential.labelType)
        }
        credentialGroupRepo.updateCredentialGroup(credentialGroup)
//        val credentialList = Gson().fromJson(credentialGroup.credentialList, Array<Credential>::class.java).toList()
        val credentialList = credentialGroup.credentialList
        credentialList.forEach { credential ->
            println("bbbbb: " + credential.credentialId + "  " + credential.labelType)
        }
//        val nftList = Gson().fromJson(credentialGroup.nftList, Array<NFT>::class.java).toList()
        val nftList = credentialGroup.nftList
//        val pointList = Gson().fromJson(credentialGroup.pointList, Array<Point>::class.java).toList()
        val pointList = credentialGroup.pointList
        val sbtList = credentialGroup.sbtList
        val deleteCredentials = oldCredentials.filter { oldCredential ->
            !credentialList.any { newCredential ->
                newCredential.credentialId == oldCredential.credentialId
            }
        }
        val deleteCredentialIds = deleteCredentials.map { it.credentialId }
        if (deleteCredentialIds.isNotEmpty()) {
            credentialRepo.deleteCredentialsByIdList(deleteCredentialIds)
        }
        val deleteNfts = oldNfts.filter { oldNft ->
            !nftList.any { newNft ->
                newNft.nftId == oldNft.nftId
            }
        }
        val deleteNftIdsToGroupIds = deleteNfts.associate { it.nftId to it.groupId }
        if (deleteNftIdsToGroupIds.isNotEmpty()) {
            nftRepo.deleteNFTByPairIds(deleteNftIdsToGroupIds)
        }
        val deletePoints = oldPoints.filter { oldPoint ->
            !pointList.any { newPoint ->
                newPoint.pointId == oldPoint.pointId
            }
        }
        val deletePointIds = deletePoints.map { it.pointId }
        if (deletePointIds.isNotEmpty()) {
            pointRepo.deletePointsByIds(deletePointIds)
        }
//        val deleteSBTs = oldSBTRewards.filter { oldSBT ->
//            !sbtList.any { newSBT ->
//                newSBT.sbtId == oldSBT.sbtId
//            }
//        }
//        val deleteSBTIds = deleteSBTs.map { it.sbtId }
//        if (deleteSBTIds.isNotEmpty()) {
//            sbtRepo.deleteSBTsByIds(deleteSBTIds)
//        }
        var newCredentials = mutableListOf<Credential>()
        for (credential in credentialList) {
            if (credential.credentialId == 0L) { //add a new credential
                credential.groupId = credentialGroup.id
                credential.projectId = credentialGroup.projectId
                credential.campaignId = credentialGroup.campaignId
                credential.creatorId = credentialGroup.creatorId
                newCredentials.add(credentialService.createCredential(credential))
            } else {
                credentialRepo.updateCredential(credential)
                val newCredential = credentialRepo.getCredentialById(credential.credentialId)!!
                newCredentials.add(newCredential)
            }
        }
        credentialGroup.credentialList = newCredentials
        // 取消update nft/points
        var newNFTs = mutableListOf<NFT>()
        for (nft in nftList) {
            if (nft.groupId == 0L) {  // nft新建时id是前端传的，所以改为取group id = 0判断是新增
                nft.groupId = credentialGroup.id
                nft.projectId = credentialGroup.projectId
                nftRepo.createNFTGroup(nft)
            }
            nftRepo.updateNFT(nft)
            val newNFT = nftRepo.getNFTByPairId(nft.nftId, credentialGroup.id)!!
            newNFTs.add(newNFT)
        }
        credentialGroup.nftList = newNFTs
        val newPoints = mutableListOf<Point>()
        for (point in pointList) {
            pointRepo.updatePoint(point)
            val newPoint = pointRepo.getPointById(point.pointId)!!
            newPoints.add(newPoint)
        }
        credentialGroup.pointList = newPoints
//        val newSBTs = mutableListOf<SBTReward>()
//        for (sbt in sbtList) {
//            sbtRepo.updateSBT(sbt)
//            val newSBT = sbtRepo.getSBTById(sbt.sbtId)!!
//            newSBTs.add(newSBT)
//        }
//        credentialGroup.sbtList = newSBTs
        return credentialGroup
    }

    fun deleteCredentialGroup(credentialGroup: CredentialGroup): CredentialGroup? {
        credentialGroupRepo.updateCredentialGroup(credentialGroup)
        return credentialGroupRepo.getCredentialGroupById(credentialGroup.id)
    }

    fun verifyCredential(user: User, utInfo: UserTwitterInfo?, credential: Credential): Boolean {
//        val justChecked = redisTemplate.opsForValue().get("verify:${user.userId}:${credential.credentialId}") != null
//        if (justChecked) {
//            return false
//        }
//        redisTemplate.opsForValue().set("verify:${user.userId}:${credential.credentialId}", "1", Duration.ofMinutes(1))
        //添加一条user_id & credential_id的映射
        val userTwitterName = if (user.isTwitterLogin) user.twitterName else ""
        val userEvmAddress = user.wallet
        val userSuiAddress = user.suiAddress
        val userCredential = UserCredential(
            userId = user.userId,
            address = if (userSuiAddress.isNotEmpty()) userSuiAddress else if (userTwitterName.isNotEmpty()) userTwitterName else userEvmAddress,
            credentialId = credential.credentialId,
            campaignId = credential.campaignId,
            status = 1,
            participantDate = Instant.now(),
            isTwitterLogin = user.isTwitterLogin,
            labelType = credential.labelType
        )
        if (userCredential.address == "" && !user.ton.tonWallet.isNullOrEmpty()) {  //ton address
            userCredential.address = user.ton.tonWallet!!
            userCredential.socialType = SocialType.TON.code
        }
        var isVerified = false
        if ((credential.labelType <= 3 || credential.labelType == 11) && utInfo != null) { // twitter credential type
            userCredential.socialId = utInfo.twitterId.toString()
            userCredential.socialType = SocialType.TWITTER.code
            isVerified = userTwitterService.verifyUserActivity(
                utInfo,
                credential.labelType,
                credential.link,
                credential.credentialId
            )
        } else if (credential.labelType == 4) { //Join Discord Service
            val guildData = discordService.getDcServerInfoFromInviteLink(credential.link)!!
            val guildId = guildData.id().toString()
            val dcUser = discordService.getUserDcInfo(user.userId)
            isVerified = discordService.verifyUserGuildMembership(dcUser?.dcId.orEmpty(), guildId)
            if (dcUser != null) {
                userCredential.socialId = dcUser.dcId
                userCredential.socialType = SocialType.DISCORD.code
            }
        } else if (credential.labelType == 87) {
            val options = credential.options

            val jsonObject = JSONObject(options)
            val link = credential.link

            val minAttendanceTime = jsonObject.optString("minimumAttendanceTime").toLongOrNull() ?: 0L
            val startTime = jsonObject.optString("discordStartTime").toLongOrNull() ?: 0L
            val endTime = jsonObject.optString("discordEndTime").toLongOrNull() ?: Long.MAX_VALUE

            val guildData = discordService.getDcServerInfoFromInviteLink(credential.link)!!
            val guildId = guildData.id().toString()

            val dcUser = discordService.getUserDcInfo(user.userId)
            val dcUserId = dcUser?.dcId.orEmpty()

            isVerified =
                discordService.verifyUserVoiceAttendance(
                    user.userId,
                    dcUserId,
                    link,
                    guildId,
                    minAttendanceTime,
                    startTime,
                    endTime
                )
        } else if (credential.labelType == 5) { //Verify Discord role
            val guildData = discordService.getDcServerInfoFromInviteLink(credential.link)!!
            val guildId = guildData.id().asLong()
            val dcUser = discordService.getUserDcInfo(user.userId)
            val roleIds = getExtraDcRoleIds(credential.extraInfo).plus(credential.roleId)
            for (roleId in roleIds) {
                isVerified = discordService.verifyUserGuildRole(dcUser?.dcId.orEmpty(), guildId, roleId.toLong())
                if (isVerified) {
                    break
                }
            }
            if (dcUser != null) {
                userCredential.socialId = dcUser.dcId
                userCredential.socialType = SocialType.DISCORD.code
            }
        } else if (credential.labelType == 6 || credential.labelType == 7) { // Join Telegram Group  or Join Telegram Channel
            val link = getOriginalLink(credential)
            val target = URI(link).path.split("/").last()
            val channelId = "@$target"
            isVerified = telegramService.verifyUserInChannel(user.userId, channelId)
            val tgUser = telegramService.getUserInfo(user.userId)
            if (tgUser != null) {
                userCredential.socialId = tgUser.tgId.toString()
                userCredential.socialType = SocialType.TELEGRAM.code
            }
        } else if (credential.labelType == 8 || credential.labelType == 12 || credential.labelType == 72) {  //Visit a Page or Site || Snapshot || Share to story
            val userCredential = getUserCredential(user.userId, credential.credentialId)
            if (userCredential != null) {
                return true //不用重新添加user_id & credential_id的映射
            }
        } else if (credential.labelType == 10) {
            isVerified = credentialSignService.checkVerifyStatus(user.userId, credential.credentialId)
        } else if (credential.labelType == 13) {
            isVerified = credentialAirdropAddressService.checkVerifyStatus(user.userId, credential.credentialId)
        } else if (credential.labelType == 14) {
            // stake ton on Tonstakers
            isVerified = wiseScoreService.checkUserStakeEvents(user.userId)
        } else if (credential.labelType == 15) {
            // stake ton on Bemo
            isVerified = wiseScoreService.checkUserBemoStakeTonEvents(user.userId)
        } else if (credential.labelType == 16) {
            // Borrow USDT in EVAA
            isVerified = wiseScoreService.checkUserEvaaBorrowEvents(user.userId)
        } else if (credential.labelType == 17) {
            // Provide Liquidity for TON + USDT or ts/stTON + USDT on ston.fi
            isVerified = wiseScoreService.checkUserStonFiLiquidityProvideEvents(user.userId)
        } else if (credential.labelType == 18) {
            // Provide Liquidity for TON + USDT or ts/stTON + USDT on dedust
            isVerified = wiseScoreService.checkUserDedustLiquidityProvideEvents(user.userId)
        } else if (credential.labelType == 19) {
            // Vault USDT or TON
            isVerified = wiseScoreService.checkUserStormTradeEvents(user.userId)
        } else if (credential.labelType == 20) {
            // Open a trade in any trading pair for storm trade
//            isVerified = wiseScoreService.checkOpenATradeStormTradeEvents(user.userId)
            // todo : open a trade is not necessary to check, click to verify
            isVerified = true
        } else if (credential.labelType == 21) {
            // Supply stTON or tsTON in EVAA
            isVerified = wiseScoreService.checkUserEvaaSupplyEvents(user.userId)
        } else if (credential.labelType == 22) {
            // subscribe to telegram premium
            isVerified = telegramService.getUserTgInfo(user.userId)?.isPremium ?: false
        } else if (credential.labelType == 23) {
            // connect TON Wallet
            isVerified = user.ton.binded
        } else if (credential.labelType == 86) {
            // connect Sui Wallet
            isVerified = user.sui.binded
        } else if (credential.labelType == 24) {
            // connect evm wallet
            isVerified = user.evm.binded
        } else if (credential.labelType == 25) {
            // [Degen Defi] 1. hipo stake ton
            isVerified = wiseScoreService.checkHipoStakeTonEvents(user.userId, credential.credentialId)
        } else if (credential.labelType == 26) {
            // [Degen Defi] 2. Aqua protocol Deposit tsTON or stTON and mint
            isVerified = wiseScoreService.checkAquaProtocolDepositAndMintEvents(user.userId, credential.credentialId)
        } else if (credential.labelType == 27) {
            // [Degen Defi] 3. JVault stake strom or ton-slp
            isVerified = wiseScoreService.checkJVaultStakeTonSlpEvents(user.userId, credential.credentialId)
        } else if (credential.labelType == 28) {
            // [Degen Defi] 4. DAOLama Deposit TON
            isVerified = wiseScoreService.checkDAOLamaDepositTonEvents(user.userId, credential.credentialId)
        } else if (credential.labelType == 29) {
            // [Degen Defi] 5. TON Hedge Deposit USDT
            isVerified = wiseScoreService.checkTONHedgeDepositUsdtEvents(user.userId, credential.credentialId)
        } else if (credential.labelType == 30) {
            // [Degen Defi] 6. Settle TON Buy Mid-Risk Index with ton
            isVerified = wiseScoreService.checkSettleTonEvents(user.userId, credential.credentialId)
        } else if (credential.labelType == 31) {
            // [Degen Defi] 7. Parraton Deposit tsTON/USDT
            isVerified = wiseScoreService.checkParratonDepositTsTonOrUsdtEvents(user.userId, credential.credentialId)
        } else if (credential.labelType == 32) {
            // [Degen Defi] 8. TonStable Deposit tsTON or stTON
            isVerified = wiseScoreService.checkTonStableDepositTsTonOrSttonEvents(user.userId, credential.credentialId)
        } else if (credential.labelType == 33) {
            // [Degen Defi] 9. TonPools Deposit TON
            isVerified = wiseScoreService.checkTonPoolsDepositTONEvents(user.userId, credential.credentialId)
        } else if (credential.labelType == 52) {
            // [Degen Defi] 10. TONStakers Stake TON
            isVerified = wiseScoreService.checkTonStakersStakeTonEvents(user.userId, credential.credentialId)
        } else if (credential.labelType == 34) {
            // [Degen Defi] 11.Tradoor Long / Short on Perps
            isVerified = wiseScoreService.checkTradoorLongOrShortEvents(user.userId, credential.credentialId)
        } else if (credential.labelType == 54) {
            // [Degen Defi] 12.Gaspump token transfer
            isVerified = wiseScoreService.checkGaspumpTokenTransfetEvents(user.userId, credential.credentialId)
        } else if (credential.labelType == 55) {
            // [Degen Defi] 13.Rainbowswap Swap Token
            isVerified = wiseScoreService.checkRainbowswapSwapTokenEvents(user.userId, credential.credentialId)
        } else if (credential.labelType == 35) {
            // [Normie Defi] BOOST_BLUM_IN_OPEN_LEAGUE
            isVerified = wiseScoreService.checkNormieBlumEvents(user.userId, credential.credentialId)
        } else if (credential.labelType == 36) {
            // [Normie Defi] COMPLETE_DAILY_CHECK_IN_ON_CATIZEN
            isVerified = wiseScoreService.checkNormieCatizenEvents(user.userId, credential.credentialId)
        } else if (credential.labelType == 37) {
            // [Normie Defi] COMPLETE_DAILY_CHECK_IN_ON_YESCOIN
            isVerified = wiseScoreService.checkNormieYescoinEvents(user.userId, credential.credentialId)
        } else if (credential.labelType == 38) {
            // [Normie Defi] PROVIDE_LIQUIDITY_FOR_WAT_OR_TON_ON_GAMEE
            isVerified = wiseScoreService.checkNormieGameeEvents(user.userId, credential.credentialId)
        } else if (credential.labelType == 39) {
            // [Normie Defi] COMPLETE_A_TRADE_IN_ANY_NFT_COLLECTION_ON_GETGEMS
            isVerified = wiseScoreService.checkNormieGetgemsEvents(user.userId, credential.credentialId)
        } else if (credential.labelType in 41..50) {
            isVerified = wiseScoreService.checkNormieTokenHold(user.userId, credential.labelType)
        } else if (credential.labelType == 51) {
            // hold ton coin in ton
            isVerified = wiseScoreService.checkHoldTonCoinInTonEvents(user.userId, credential.credentialId)
        } else if (credential.labelType == 53) {
            // Boost TON Station in Open League
            isVerified = wiseScoreService.checkNormieTonStationInOpenLeagueEvents(user.userId, credential.credentialId)
        } else if (credential.labelType == 56) {
            // Wonton buy mystery sliver packs
            isVerified = wiseScoreService.checkWontonBuyMysterySliverPacksEvents(user.userId)
        } else if (credential.labelType == 57) {
            // Hold $durov in pavel durov
            isVerified = wiseScoreService.checkHoldDurovEvents(user.userId)
        } else if (credential.labelType == 58) {
            // Tonco Swap
            isVerified = wiseScoreService.checkToncoSwapEvents(user.userId, credential.credentialId)
        } else if (credential.labelType == 59) {
            // Tonco liquidity
            isVerified = wiseScoreService.checkToncoLiquidityEvents(user.userId, credential.credentialId)
        } else if (credential.labelType == 60) {
            // [Magic Pot] Dig the stash on Magic Pot
            isVerified = wiseScoreService.checkMagicPotDigTheStashEvents(user.userId, credential.credentialId)
        } else if (credential.labelType == 61) {
            // [Ustars] receive USDT
            isVerified = wiseScoreService.checkReceiveUsdtFromUstarsEvents(user.userId, credential.credentialId)
        } else if (credential.labelType == 62) {
            // [Swap.Coffee] Stake $CES or LP Tokens
            isVerified =
                wiseScoreService.checkStakeCesOrLpTokensOnSwapCoffeeEvents(user.userId, credential.credentialId)
        } else if (credential.labelType == 63) {
            // [Coffin] Supply token into vaults
            isVerified = wiseScoreService.checkSupplyTokenIntoVaultsOnCoffinEvents(user.userId, credential.credentialId)
        } else if (credential.labelType == 64) {
            // [TonPools] Stake TON or USDT
            isVerified = wiseScoreService.checkStakeTonOrUsdtOnTonPoolsEvents(user.userId, credential.credentialId)
        } else if (credential.labelType == 65) {
            // [Farmix] Lend
            isVerified = wiseScoreService.checkLendOnFarmixEvents(user.userId, credential.credentialId)
        } else if (credential.labelType == 66) {
            // Verify if users hold specific number of SBTs from given Activity IDs
            isVerified = wiseScoreService.verifyRequiredSbtHoldingsBySetting(user.userId, credential)
            val options = credential.options
            val jsonObject = JSONObject(options)
            // Compatible with the original campaign page verify logic
            if (isVerified && jsonObject.optString("link").isNullOrBlank()) {
                val groupId = credential.groupId
                val sbts = sbtRepo.getSBTByGroupId(groupId)
                if (sbts.isEmpty()) {
                    logger.warn("No SBTs found for groupId: $groupId")
                    return false
                }
                val sbtId = sbts[0].sbtId
                val userReward = UserReward(
                    rewardId = sbtId, rewardType = 3, userId = user.userId, groupId = groupId, claimType = 1
                )
                if (participantRepo.getClaimTypeByUserId(sbtId, user.userId, groupId) == null) {
                    println("add reward " + userReward.claimType + " " + sbtId + " userId " + user.userId)
                    participantRepo.addUserRewardResult(userReward)
                }
            }
        } else if (credential.labelType == 67) {
            // Stake Ton/stTon/tsTon to UTONC.
            isVerified = wiseScoreService.checkStakeToUTONCEvents(user.userId, credential.credentialId)
        } else if (credential.labelType == 68) {
            // Hold $TINU
            isVerified = wiseScoreService.checkHoldTinuEvents(user.userId)
        } else if (credential.labelType == 69) {
            // TODO: Hold Custom Jetton
            val tonAddress = user.ton.tonWallet
            if (tonAddress.isNullOrEmpty()) {
                println("verify custom hold jetton failed,user has no ton address ${user.userId}")
                isVerified = false
            } else {
                val options = credential.options
                val jsonObject = JSONObject(options)
                isVerified =
                    taskVerifyService.checkHoldCustomJettonEvents(user.userId, credential.credentialId, jsonObject)
            }
        } else if (credential.labelType == 70) {
            // Provide Liquidity for LAMBO/TON
            isVerified = wiseScoreService.checkLPForLamboAndTonEvents(user.userId, credential.credentialId)
        } else if (credential.labelType == 71) {
            // Invite user get SBT
            isVerified = wiseInviteService.checkInviteResultEvents(user.userId, credential)
        } else if (credential.labelType == 73) {
            // twitter connect
            isVerified = user.twitterName != ""
        } else if (credential.labelType == 75) {
            // T_POINTS_EXCHANGE - top 500
            val isInTop500List = telegramUserPushRepository.getTgUserPushByUidAndPushType(
                user.userId,
                "Exchange 80K TPoints for SBT and rewards"
            ) != null
            val userTPoints = tPointsService.getUserTPointsNum(user.userId)
            val has80KTPoints = userTPoints >= 80000
            isVerified = isInTop500List && has80KTPoints
            if (isVerified) {
                val tPointsConsume = TPointsConsume(
                    userId = user.userId,
                    consumeType = -2,
                    level = -2,
                    tPointsNum = 80000,
                    createTime = Instant.now(),
                    updateTime = Instant.now()
                )
                if (tPointsConsumeRepo.getTPointsConsumeByIdAndTypeAndNum(user.userId, -2, 80000) == null) {
                    tPointsConsumeRepo.addTPointsConsume(tPointsConsume)
                }
                telegramUserPushRepository.updateTgUserPushParticipateType(
                    user.userId,
                    "Exchange 80K TPoints for SBT and rewards",
                    1
                )
            }

        } else if (credential.labelType == 74) {
            // T_POINTS_EXCHANGE - top 50
            val isInTop50List = telegramUserPushRepository.getTgUserPushByUidAndPushType(
                user.userId,
                "Top 50 - Exchange 80K TPoints for SBT and rewards"
            ) != null
            val userTPoints = tPointsService.getUserTPointsNum(user.userId)
            val has80KTPoints = userTPoints >= 80000
            isVerified = isInTop50List && has80KTPoints
            if (isVerified) {
                val tPointsConsume = TPointsConsume(
                    userId = user.userId,
                    consumeType = -3,
                    level = -3,
                    tPointsNum = 80000,
                    createTime = Instant.now(),
                    updateTime = Instant.now()
                )
                if (tPointsConsumeRepo.getTPointsConsumeByIdAndTypeAndNum(user.userId, -3, 80000) == null) {
                    tPointsConsumeRepo.addTPointsConsume(tPointsConsume)
                }
                telegramUserPushRepository.updateTgUserPushParticipateType(
                    user.userId,
                    "Top 50 - Exchange 80K TPoints for SBT and rewards",
                    1
                )
            }
        } else if (credential.labelType == 76) {
            val wiseScoreFromDB = wiseScoreService.getScoreById(user.userId)
            val wiseScore = wiseScoreFromDB?.totalScore ?: 0
            val suiScore = wiseScoreService.getSuiScoreById(user.userId)?.totalScore ?: 0
            val options = credential.options
            val score = options.substringAfter("\"score\":").substringBefore(",").trim().toInt()
            val jsonObject = JSONObject(options)
            val network = jsonObject.optString("network")
            if(network == "sui") {
                println("[sui] wise score is $wiseScore and score limit is $score")
                isVerified = suiScore >= score
            } else {
                println("[ton]wise score is $wiseScore and score limit is $score")
                isVerified = wiseScore >= score
            }
        } else if (credential.labelType == 81) {
            // check in n days
            val options = credential.options
            val maxDayStreaks = options.substringAfter("\"maxDayStreaks\":\"").substringBefore("\"").trim()
            val userMaxStreak = wiseScoreUserStreakService.getMaxUserStreakWithDates(user.userId).maxStreak
            println("maxDayStreaks is $maxDayStreaks and userMaxStreak is $userMaxStreak")
            isVerified = userMaxStreak >= maxDayStreaks.toInt()
            if (isVerified) {
                val sbtId = sbtRepo.getSBTByGroupId(credential.groupId)[0].sbtId
                addUserReward(3, sbtId, user.userId, credential.groupId, 1)
            }
        } else if (credential.labelType == 82) {
            isVerified = taskVerifyService.checkBeetrootUsdtEvent(user.userId, credential.credentialId)
        } else if (credential.labelType == 83) {
            isVerified = taskVerifyService.checkAkedoGameEvent(user.userId, credential.credentialId)
        } else if (credential.labelType == 84) {
            isVerified = taskVerifyService.checkAnyCraftEvent(user.userId, credential.credentialId)
        } else if (credential.labelType == 85) {
            isVerified = taskVerifyService.checkBAZAEvent(user.userId, credential.credentialId)
        } else if (credential.labelType == 40) {
            // custom credential
            val tonAddress = user.ton.tonWallet

            val options = credential.options
            val jsonObject = JSONObject(options)
            val condition = jsonObject.getInt("condition")
            // 1. ton wallet
            if (condition == 1) {
                if (tonAddress.isNullOrEmpty()) {
                    println("verify custom credential failed,user has no ton address ${user.userId}")
                    isVerified = false
                } else {
                    val verifyApiLink = jsonObject.optString("verifyApiLink")
                    isVerified = customCredentialService.verifyCustomCredentialByApi(
                        credential.verifyApiLink.ifEmpty { verifyApiLink },
                        credential.credentialId,
                        1,
                        tonAddress,
                        telegramId = 0
                    )
                }
                // 2. telegram id
            } else if (condition == 2) {
                val tgId = userRepo.findTgIdByUserId(user.userId)
                if (tgId == null || tgId == 0L) {
                    println("verify custom credential failed,user has no telegram id ${user.userId}")
                    isVerified = false
                } else {
                    val verifyApiLink = jsonObject.getString("verifyApiLink")
                    isVerified = customCredentialService.verifyCustomCredentialByApi(
                        credential.verifyApiLink.ifEmpty { verifyApiLink },
                        credential.credentialId,
                        2,
                        "",
                        tgId
                    )
                }

            }

        }
        val isUserCredentialExist = getUserCredential(user.userId, credential.credentialId) != null
        if (isVerified && !isUserCredentialExist) {
//            //添加一条user_id & credential_id的映射
//            val userCredential = UserCredential(
//                userId = user.userId,
//                address = if (user.isTwitterLogin) user.twitterName else user.wallet,
//                credentialId = credential.credentialId,
//                campaignId = credential.campaignId,
//                status = 1,
//                participantDate = Instant.now(),
//                isTwitterLogin = user.isTwitterLogin
//            )
            addUserCredential(userCredential)
        }
        // gameBuild - combine verify + claim logic
        val gameBuildCampaignId = if (env.activeProfiles.contains("prod")) 123456789L else 576403963875L
        if (credential.campaignId == gameBuildCampaignId) {
            val groupId = credential.groupId
            val pointId = getCredentialGroupById(groupId)!!.pointList[0].pointId
            addUserReward(2, pointId, user.userId, credential.groupId, 4)
        }
        return isVerified
    }

    data class NormieVerifyResult(
        val campaignId: Long,
        val campaignName: String,
        val credentialId: Long,
        val credentialName: String,
        val labelType: Int,
        val isVerified: Boolean,
        val sbt: SBTReward
    )

    fun verifyWholeNormieCredentials(user: User, campaignIds: List<Long>): List<NormieVerifyResult> {
        val tonBind = user.ton.binded
        val credentials = mutableListOf<Credential>()
        val normieVerifyResult = mutableListOf<NormieVerifyResult>()
        // todo : add normie badge, default - verified
        val normieSBT = SBTReward(
            sbtId = 0L,
            name = "Normie Badge",
            activityId = 600,
            activityUrl = "https://id.ton.org/normie-airdrop",
            picUrl = "https://static.tbook.vip/img/a05874e1677140f2b680b96eb9105554",
            projectId = 0,
            claimedType = 2,
            uniqueLink = "https://id.ton.org/normie-airdrop"
        )
        val normieBadgeResult = NormieVerifyResult(
            campaignId = 0L,
            campaignName = "Normie campaign",
            credentialId = 0L,
            credentialName = "Normie Badge",
            labelType = 0,
            isVerified = true,
            sbt = normieSBT
        )
        normieVerifyResult.add(normieBadgeResult)
        for (campaignId in campaignIds) {
            // filter connect ton-wallet credential
            val credentialList = credentialRepo.getCredentialByCampaignId(campaignId)
            credentials.addAll(credentialList)
        }
        val campaigns = campaignRepo.getCampaignsByIds(campaignIds).associateBy { it.campaignId }
        for (credential in credentials) {
            // normie credentials
            if (credential.labelType in 35..53 || credential.labelType == 23) {
                val userCredentialResult = participantRepo.getUserCredential(user.userId, credential.credentialId)
                val isVerified = if (userCredentialResult != null) true else verifyCredential(user, null, credential)
                val campaignId = credential.campaignId
                val campaign = campaigns[campaignId]
                val groups = getCredentialGroupByCampaignId(campaignId)!!
                val groupId = groups[0].id
                if (credential.labelType != 23) {
                    val sbt = groups[0].sbtList[0]
                    if (isVerified && tonBind) {
                        val claimType =
                            participantRepo.getClaimTypeByUserId(
                                sbt.sbtId,
                                user.userId,
                                groupId
                            ) //查看该用户已经claim或者miss过reward
                        if (claimType == null || claimType == 0) {
                            addUserReward(3, sbt.sbtId, user.userId, groupId, 1)
                            sbt.claimedType = 1
                        } else if (claimType != SBTRewardClaimType.CLAIMED) {
                            val newClaimType =
                                wiseScoreService.getSBTClaimedStatus(sbt.activityId, user.ton.tonWallet!!)
                            if (newClaimType > claimType) {
                                addUserReward(3, sbt.sbtId, user.userId, groupId, newClaimType)
                                sbt.claimedType = newClaimType
                            } else {
                                sbt.claimedType = claimType
                            }
                        } else {
                            sbt.claimedType = claimType
                        }
                    }
                    val normieVerify = NormieVerifyResult(
                        campaignId = campaignId,
                        campaignName = campaign!!.name,
                        credentialId = credential.credentialId,
                        credentialName = credential.name,
                        labelType = credential.labelType,
                        isVerified = isVerified,
                        sbt = sbt
                    )
                    normieVerifyResult.add(normieVerify)
                }
            }
        }
        return normieVerifyResult
    }

    fun getDefaultNormieBadgeResult(): NormieVerifyResult {
        val normieSBT = SBTReward(
            sbtId = 0L,
            name = "Normie Badge",
            activityId = 600,
            activityUrl = "https://id.ton.org/normie-airdrop",
            picUrl = "https://static.tbook.vip/img/a05874e1677140f2b680b96eb9105554",
            projectId = 0,
            claimedType = 2,
            uniqueLink = "https://id.ton.org/normie-airdrop"
        )
        val normieBadgeResult = NormieVerifyResult(
            campaignId = 0L,
            campaignName = "Normie campaign",
            credentialId = 0L,
            credentialName = "Normie Badge",
            labelType = 0,
            isVerified = true,
            sbt = normieSBT
        )
        return normieBadgeResult
    }

    fun verifyNormieCampaign(user: User, campaignId: Long): List<NormieVerifyResult> {
        val normieVerifyResult: MutableList<NormieVerifyResult> = mutableListOf()
        val tonBind = user.ton.binded
        val credentials = credentialRepo.getCredentialByCampaignId(campaignId)
        if (campaignId == 0L) { // normie default badge
            normieVerifyResult.add(getDefaultNormieBadgeResult())
            return normieVerifyResult
        }
        val campaign = campaignRepo.getCampaignById(campaignId)
        for (credential in credentials) {
            // normie credentials
            if (credential.labelType in 35..53 || credential.labelType == 23) {
                val userCredentialResult = participantRepo.getUserCredential(user.userId, credential.credentialId)
                val isVerified = if (userCredentialResult != null) true else verifyCredential(user, null, credential)
                val groups = getCredentialGroupByCampaignId(campaignId)!!
                val groupId = groups[0].id
                if (credential.labelType != 23) {
                    val sbt = groups[0].sbtList[0]
                    if (isVerified && tonBind) {
                        val claimType =
                            participantRepo.getClaimTypeByUserId(
                                sbt.sbtId,
                                user.userId,
                                groupId
                            ) //查看该用户已经claim或者miss过reward
                        if (claimType == null || claimType == 0) {
                            addUserReward(3, sbt.sbtId, user.userId, groupId, 1)
                            sbt.claimedType = 1
                        } else if (claimType != SBTRewardClaimType.CLAIMED) {
                            val newClaimType =
                                wiseScoreService.getSBTClaimedStatus(sbt.activityId, user.ton.tonWallet!!)
                            if (newClaimType > claimType) {
                                addUserReward(3, sbt.sbtId, user.userId, groupId, newClaimType)
                                sbt.claimedType = newClaimType
                            } else {
                                sbt.claimedType = claimType
                            }
                        } else {
                            sbt.claimedType = claimType
                        }
                    }
                    val normieVerify = NormieVerifyResult(
                        campaignId = campaignId,
                        campaignName = campaign!!.name,
                        credentialId = credential.credentialId,
                        credentialName = credential.name,
                        labelType = credential.labelType,
                        isVerified = isVerified,
                        sbt = sbt
                    )
                    normieVerifyResult.add(normieVerify)
                }
            }
        }
        return normieVerifyResult.toList()
    }

    fun addUserReward(rewardType: Int, rewardId: Long, userId: Long, groupId: Long, claimType: Int) {
        val userReward = UserReward(
            rewardId = rewardId, rewardType = rewardType, userId = userId, groupId = groupId, claimType = claimType
        )
        if (participantRepo.getClaimTypeByUserId(
                rewardId,
                userId,
                groupId
            ) != null
        ) {
            if (participantRepo.getClaimTypeByUserId(rewardId, userId, groupId) != claimType) {
                participantRepo.updateUserReward(userReward)
            }
        } else {
            println(" add reward " + userReward.claimType + " " + rewardId + " userId " + userId)
            participantRepo.addUserRewardResult(userReward)
        }
    }

    @Transactional
    fun addUserCredential(userCredential: UserCredential) {
        participantRepo.addResult(userCredential)
//        val newUserCredential = getUserCredential(userCredential.userId, userCredential.credentialId)
//        return newUserCredential
    }

    fun getUserCredential(userId: Long, credentialId: Long): UserCredential? {
        return participantRepo.getUserCredential(userId, credentialId)
    }

    fun getCredentialByUser(user: User, credentialId: Long): Credential {
        val credential = credentialRepo.getCredentialById(credentialId)!!
        val uc = participantRepo.getUserCredential(user.userId, credentialId)
        if (uc != null)
            credential.isVerified = 1
        return credential
    }

    fun getByUserId(userId: Long): List<UserCredential> {
        return participantRepo.getByUser(userId)
    }

    fun getAllUserCredentials(): List<UserCredential> {
        return participantRepo.getAllUserCredentials()
    }

    fun updateUserCredential(userCredential: UserCredential): Int {
        return participantRepo.updateUserCredential(userCredential)
    }

    fun getExtraDcRoleIds(extraInfo: String): List<String> {
        if (extraInfo.isEmpty()) {
            return emptyList()
        }
        val extra = mapper.readTree(extraInfo)
        if (extra.isEmpty) return emptyList()
        return extra.get("roleIds")?.let { it.map { it.asText() } }.orEmpty()
    }

    fun getSBTById(sbtId: Long): SBTReward? {
        return sbtRepo.getSBTById(sbtId)
    }

    fun getOriginalLink(credential: Credential): String {
        if (credential.link.isNotEmpty()) return credential.link
        try {
            val options = mapper.readTree(credential.options)
            return options["url"].asText()
        } catch (e: Exception) {
            logger.error("decode options error: ${credential.options}", e)
            return credential.link
        }

        val link = credential.link
        val regex = Regex("start=cr_(\\d+)")
        val matchResult = regex.find(link)
        return if (matchResult != null) {
            val group = matchResult.groupValues[1]
            redisTemplate.opsForValue().get(TgStarter.formatKey(group.toLong())) ?: link
        } else {
            link
        }
    }
}