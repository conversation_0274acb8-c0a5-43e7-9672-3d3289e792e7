package com.rewardoor.app.services

import com.rewardoor.app.dao.UserLuckyDrawRepository
import com.rewardoor.app.dao.WiseScoreTaskRepository
import com.rewardoor.model.LuckyDrawResult
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.time.Instant

@Service
@Transactional
class WiseTaskService(
    private val taskRepo: WiseScoreTaskRepository,
    private val userLuckyDrawRepository: UserLuckyDrawRepository,
    private val telegramService: TelegramService
) {
    private val log = mu.KotlinLogging.logger { }
    fun addTask(userId: Long, taskName: String, points: Int) {
        val task = taskRepo.getUserTask(userId, taskName)
        if (task != null) {
            log.info { "User $userId already has task $taskName"}
            return
        }

        val cnt = taskRepo.addWiseScoreTask(userId, taskName)
        if (cnt == 0) {
            log.info { "O inserted to add task $taskName for user $userId" }
            return
        }
        val luckyDrawResult = LuckyDrawResult(
            userId = userId,
            fissionLevel = -1,
            tPointsNum = points,
            isEligibleToGenerateWiseScore = 0,
            isEligibleToGenerateSBT = 0,
            createTime = Instant.now(),
            updateTime = Instant.now()
        )
        userLuckyDrawRepository.createUserLuckyDraw(luckyDrawResult)
    }

    fun getUserTasks(userId: Long): List<String> {
        return taskRepo.getUserTasks(userId)
    }

    fun checkPremiumTask(userId: Long): Boolean {
        val userTask = taskRepo.getUserTask(userId, "tg:premium")
        if (userTask != null) {
            return true
        }

        val premium = telegramService.getUserTgInfo(userId)?.isPremium?:false
        if (premium) {
            addTask(userId, "tg:premium", 1000)
        }
        return premium
    }
}