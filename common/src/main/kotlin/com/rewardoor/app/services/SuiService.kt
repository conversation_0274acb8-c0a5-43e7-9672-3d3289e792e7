package com.rewardoor.app.services

import com.rewardoor.IdGenerator
import com.rewardoor.app.dao.SuiSbtRepository
import com.rewardoor.model.SuiSbtReward
import com.rewardoor.model.SuiSbtSync
import org.springframework.beans.factory.annotation.Value
import org.springframework.http.MediaType
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import org.springframework.web.reactive.function.client.WebClient
import org.ton.java.mnemonic.Ed25519
import java.util.HexFormat

@Service
class SuiService(
    @Value("\${sui-verify.host}") private val suiVerifyHost: String,
    @Value("\${sui-verify.auth}") private val suiVerifyAuth: String,
    @Value("\${airdrop.ton_secret_key}") private val secretKey: String,
    val idGenerator: IdGenerator,
    private val suiSbtRepository: SuiSbtRepository
) {
    private val log = mu.KotlinLogging.logger {}
    private val webClient = WebClient.create()

    /**
     * Verifies a Sui wallet signature
     *
     * @param address The Sui wallet address
     * @param publicKey The public key of the Sui wallet (hex encoded)
     * @param signature The signature (base64 encoded)
     * @param message The original message that was signed
     * @return true if the signature is valid, false otherwise
     */
    fun verifySuiSignature(address: String, publicKey: String, signature: String, message: String,
                           zkLogin: Boolean = false, network: String = "testnet"): Boolean {
        val body = mapOf(
            "address" to address,
            "signature" to signature,
            "message" to message,
            "isZk" to zkLogin,
            "network" to network,
        )
        try {
            val result = webClient.post()
                .uri(suiVerifyHost)
                .header("x-auth", suiVerifyAuth)
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(body)
                .retrieve()
                .bodyToMono(String::class.java)
                .block() ?: ""
            return result == "true"
        } catch (e: Exception) {
            log.error("Error verifying Sui signature", e)
            return false
        }
    }

    fun syncSuiSbt(suiSbtSync: SuiSbtSync): SuiSbtSync {
        // todo : 更新suiActivityId 获取逻辑
        val suiActivityId = idGenerator.getNewId()
        suiSbtSync.suiSbtActivityId = suiActivityId
        suiSbtRepository.createSuiSbtSync(suiSbtSync)
        return suiSbtSync
    }

    fun createSuiSbt(suiSbt: SuiSbtReward) {
        val suiSbtId = idGenerator.getNewId()
        suiSbt.suiSbtId = suiSbtId
        suiSbtRepository.createSuiSbt(suiSbt)
    }

    @Transactional
    fun updateSbtCollectionInfo(activityId: Long, sbtId: Long, objectId: String) {
        suiSbtRepository.updateCollectionId(sbtId, objectId)
        suiSbtRepository.updateSyncCollectionObjectId(activityId, objectId)
        suiSbtRepository.updateSbtActivityId(sbtId, activityId)
    }

    fun signSbtCollection(
        name: String, description: String, url: String,
        sbtId: Long, address: String, ts: Long
    ): String {
        val pureAddress = if (address.startsWith("0x")) address.substring(2) else address
        val signStr = "$name$description$url$pureAddress$sbtId$ts"
        val privateKey = HexFormat.of().parseHex(secretKey)
        val signBytes: ByteArray = Ed25519.sign(privateKey, signStr.toByteArray())
        val signature = HexFormat.of().formatHex(signBytes)
        return signature
    }

    fun signSbtClaim(
        collectionId: String, sbtId: Long, address: String, ts: Long
    ): String {
        val pureAddress = if (address.startsWith("0x")) address.substring(2) else address
        val pureCollectionId = if (collectionId.startsWith("0x")) collectionId.substring(2) else collectionId
        val signStr = "$pureCollectionId$ts$pureAddress$sbtId"
        val privateKey = HexFormat.of().parseHex(secretKey)
        val signBytes: ByteArray = Ed25519.sign(privateKey, signStr.toByteArray())
        val signature = HexFormat.of().formatHex(signBytes)
        return signature
    }
}
