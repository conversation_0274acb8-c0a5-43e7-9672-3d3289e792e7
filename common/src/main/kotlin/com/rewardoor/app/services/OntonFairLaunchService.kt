package com.rewardoor.app.services


import com.rewardoor.app.dao.*
import com.rewardoor.model.*
import org.json.JSONArray
import org.json.JSONObject
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import org.springframework.web.reactive.function.client.WebClient
import org.ton.java.address.Address
import org.ton.java.cell.Cell
import org.ton.java.cell.CellBuilder
import org.ton.java.mnemonic.Ed25519
import org.ton.java.smartcontract.wallet.v4.WalletV4R2
import org.ton.java.utils.Utils
import java.math.BigDecimal
import java.math.BigInteger
import java.math.RoundingMode
import java.time.Duration
import java.time.Instant
import java.util.*
import kotlin.math.exp

@Service
@Transactional
class OntonFairLaunchService(
    val wiseScoreService: WiseScoreService,
    val ontonFairLaunchRep: OntonFairLaunchRepository,
    val idGenerator: DbIdGenerator,
    @Value("\${ton.owner.private-key:}") private val ownerPrivateKey: String,
    @Value("\${ton.contract.address:}") private val contractAddress: String,
    @Value("\${ton.api.endpoint:https://testnet.toncenter.com}") private val tonApiEndpoint: String,
    @Value("\${ton.api.key:}") private val tonApiKey: String
) {

    // 常量定义
    companion object {
//        const val ROUND_DURATION_SECONDS = 7200L          // 每轮最长2小时 2 * 60 * 60
//        const val SETTLEMENT_DURATION_MINUTES = 60L      // 结算期10分钟 10 * 60

        const val ROUND_DURATION_SECONDS = 300L         // 每轮最长5分钟 5 * 60
        const val SETTLEMENT_DURATION_SECONDS = 60L     // 结算期1分钟

        const val ROUND_HARD_CAP_USDT = 40000.0         // 每轮硬顶40,000 USDT
        const val TOTAL_SOFT_CAP_USDT = 500000.0
        const val TOTAL_HARD_CAP_USDT = 2000000.0       // 总硬顶2,000,000 USDT

        const val ROUND_HARD_CAP_TOKEN = 2000000.0         // 每轮硬顶 2,000,000 200w
        const val TOTAL_HARD_CAP_TOKEN = 100000000.0   // 能卖出的 token 个数

        const val TOTAL_ROUNDS = 500                     // 总轮次数
        const val FIRST_ROUND_PRICE = 0.005               // 第一轮初始价格

        val FAIR_LAUNCH_START_TIME: Instant = Instant.parse("2025-07-25T13:10:00Z")
        val FAIR_LAUNCH_END_TIME: Instant = Instant.parse("2025-07-27T13:10:00Z")

        const val OP_UPDATE_ROUND = 0xaa8b8b89L         // 合约的 updateRound 操作码
    }

    private val webClient = WebClient.builder().build()

    fun getBasicInfo(): GetBasicInfoResponse {
        return GetBasicInfoResponse(
            firstRoundPrice = FIRST_ROUND_PRICE,
            tokenHardCap = TOTAL_HARD_CAP_TOKEN.toLong(),
            softCap = TOTAL_SOFT_CAP_USDT.toLong(),
            hardCap = TOTAL_HARD_CAP_USDT.toLong(),
            startTime = FAIR_LAUNCH_START_TIME,
            endTime = FAIR_LAUNCH_END_TIME
        )
    }


    /**
     * 获取当前轮次完整信息 & 拼接上 records 记录列表
     */
    fun getCurrentRoundInfo(simulateTime: String? = null): GetCurrentRoundResponse {

        val currentTime = parseTime(simulateTime) // now or simulate

        // 判断当前轮次
        val currentRoundNum = getCurrentRound(currentTime)

        // 如果在静默期，处理数据更新和下一轮初始化
        if (isInSettlementPeriod(currentRoundNum, currentTime)) {
            handleSettlementPeriod(currentRoundNum, currentTime)
        }

        // 获取历史记录
        val records = if (currentRoundNum > 1) {
            ontonFairLaunchRep.findByRoundNumLessThan(currentRoundNum).map { record ->
                RoundRecord(
                    roundNumber = record.roundNumber,
                    roundUnitPrice = record.price,
                    tonPrice = record.tonPrice,
                    totalRaised = record.totalRaised,
                    tokensSold = record.tokensSold,
                    startTime = record.startTime,
                    endTime = record.endTime
                )
            }
        } else {
            emptyList()
        }

        // 构建当前轮次信息
        val current = if (currentRoundNum in 1..TOTAL_ROUNDS) {
            val record = if (currentRoundNum > 1) {
                ontonFairLaunchRep.findByRoundNum(currentRoundNum - 1)
            } else {
                null
            }
            buildCurrentRoundInfo(currentRoundNum, currentTime, record)
        } else {
            val record = ontonFairLaunchRep.findByRoundNum(TOTAL_ROUNDS.toLong() - 1)
            // 返回最后一轮数据，并添加表示，isTotalEnd: 1
            record?.let {
                CurrentRoundInfo(
                    launchStartTime = FAIR_LAUNCH_START_TIME,
                    roundNumber = TOTAL_ROUNDS.toLong(),
                    roundUnitPrice = 0.0,
                    tonPrice = BigDecimal(0),
                    startTime = calculateRoundStartTime(TOTAL_ROUNDS.toLong()),
                    endTime = calculateRoundStartTime(TOTAL_ROUNDS.toLong()).plus(
                        Duration.ofSeconds(
                            ROUND_DURATION_SECONDS
                        )
                    ),
                    inSettling = true,
                    settlingDurationSecond = SETTLEMENT_DURATION_SECONDS, // 静默时长
                    roundDurationSecond = ROUND_DURATION_SECONDS,
                    totalRounds = TOTAL_ROUNDS,
                    isTotalEnd = 1,
                    lastRound = TOTAL_ROUNDS.toLong() - 1,
                    lastUnitPrice = it.price / 1_000_000.0
                )
            }
        }

        return GetCurrentRoundResponse(
            current = current,
            records = records
        )
    }

    /**
     * 构建当前轮次信息
     */
    private fun buildCurrentRoundInfo(
        roundNumber: Long,
        currentTime: Instant,
        lastRecord: OntonRoundRecordData?
    ): CurrentRoundInfo {
        val inSettling = isInSettlementPeriod(roundNumber, currentTime)
        val currentPrice = getRoundPrice(roundNumber)
        val tonPrice = BigDecimal.valueOf(wiseScoreService.getCoinPriceFromApi("TON"))

        println("currentPrice11 - $currentPrice")

        return CurrentRoundInfo(
            roundNumber = roundNumber,
            roundUnitPrice = currentPrice,
            tonPrice = tonPrice,
            startTime = calculateRoundStartTime(roundNumber),
            endTime = calculateRoundStartTime(roundNumber).plus(Duration.ofSeconds(ROUND_DURATION_SECONDS)),
            inSettling = inSettling,
            launchStartTime = FAIR_LAUNCH_START_TIME,
            settlingDurationSecond = SETTLEMENT_DURATION_SECONDS,
            roundDurationSecond = ROUND_DURATION_SECONDS,
            totalRounds = TOTAL_ROUNDS,
            isTotalEnd = 0,
            lastRound = if (roundNumber > 1) roundNumber - 1 else 0L,
            lastUnitPrice = if (lastRecord != null) (lastRecord.price.toDouble() / 1_000_000) else 0.0
        )
    }

    /**
     * 统一的轮次判断逻辑
     */
    fun getCurrentRound(currentTime: Instant): Long {
        // 1. 检查是否未开始
        println("1. getCurrentRound")
        if (currentTime.isBefore(FAIR_LAUNCH_START_TIME)) {
            return 0
        }

        println("2. getCurrentRound - $currentTime")

        try {
            // 2. 优先查询数据库中最近已结束的轮次
            val lastEndedRound = ontonFairLaunchRep.getLastEndedRound()

            if (lastEndedRound != null) {
                // 有已结束的轮次，基于其结束时间判断
                val lastEndTime = lastEndedRound.endTime
                println("3. lastEndTime - $lastEndTime")
                val settlementEndTime = lastEndTime.plus(Duration.ofSeconds(SETTLEMENT_DURATION_SECONDS))

                println("3. settlementEndTime - $settlementEndTime")
                println("3. currentTime - $currentTime")

                return if (currentTime.isAfter(settlementEndTime)) {
                    // 超过10分钟静默期，进入下一轮
                    println("4. 超过10分钟静默期，进入下一轮")
                    lastEndedRound.roundNumber + 1
                } else {
                    val round = lastEndedRound.roundNumber
                    println("4. 仍在10分钟静默期内 round - $round")
                    // 仍在10分钟静默期内
                    lastEndedRound.roundNumber
                }
            } else {
                // 没有已结束的轮次，表示第一轮还没结束
                return 1
            }
        } catch (e: Exception) {
            println("查询数据库失败，使用fallback逻辑: ${e.message}")
            return -1
            // fallback: 基于时间的理论计算
//            return calculateTheoreticalRoundFromTime(currentTime)
        }
    }

    /**
     * 处理静默期的数据更新和下一轮初始化
     */
    private fun handleSettlementPeriod(currentRoundNum: Long, currentTime: Instant) {
        println("currentRoundNum - $currentRoundNum")
        try {
            // 步骤1：更新当前轮次的销售数据（如果还未更新）
            updateCurrentRoundIfNeeded(currentRoundNum)

            // 步骤2：初始化下一轮数据（如果还未初始化）
            initNextRoundIfNeeded(currentRoundNum + 1)

        } catch (e: Exception) {
            println("处理静默期数据失败: ${e}")
        }
    }

    /**
     * 如果需要，更新当前轮次的销售数据
     */
    private fun updateCurrentRoundIfNeeded(roundNumber: Long) {
        try {
            val roundRecord = ontonFairLaunchRep.findByRoundNum(roundNumber)

            println("检查轮次 $roundNumber 是否需要更新数据")
            println("当前 tokensSold: ${roundRecord?.tokensSold}")

            if (roundRecord != null && roundRecord.tokensSold.toInt() == 0) {
                println("开始更新轮次 $roundNumber 的销售数据")

                // 调用合约获取销售数据
                val contractData = callContractForRoundData(roundNumber)

                // 更新数据库
                ontonFairLaunchRep.updateRoundTotal(roundNumber, contractData)

                println("轮次 $roundNumber 数据更新成功")
                
                // 更新合约
                try {
                    val updateResult = updateContractRound(roundNumber, contractData.tonPrice)
                    if (updateResult) {
                        println("合约轮次 $roundNumber 更新成功")
                    } else {
                        println("合约轮次 $roundNumber 更新失败")
                    }
                } catch (e: Exception) {
                    println("更新合约失败: ${e.message}")
                    e.printStackTrace()
                }
            } else {
                println("轮次 $roundNumber 数据已存在，无需更新")
            }
        } catch (e: Exception) {
            println("更新轮次 $roundNumber 数据失败: ${e.message}")
        }
    }

    /**
     * 静默期：如果需要，初始化下一轮数据
     */
    private fun initNextRoundIfNeeded(nextRoundNum: Long) {
        if (nextRoundNum > TOTAL_ROUNDS) return


        val nextRoundRecord = ontonFairLaunchRep.findByRoundNum(nextRoundNum)

        if (nextRoundRecord == null) {
            println("需要初始化轮次 $nextRoundNum")

            val startTime = calculateRoundStartTime(nextRoundNum)
            val endTime = startTime.plus(Duration.ofSeconds(ROUND_DURATION_SECONDS))
            val price = calculateRoundPrice(nextRoundNum)
            val tonPrice = BigDecimal.valueOf(wiseScoreService.getCoinPriceFromApi("TON"))

            println("初始化轮次 $nextRoundNum - 价格: $price")

            // 计算下一轮的数据
            val nextRoundData = OntonRoundRecordData(
                roundNumber = nextRoundNum,
                startTime = startTime,
                endTime = endTime,
                price = (price * 1_000_000).toLong(),
                totalRaisedTon = 0L,
                totalRaisedUsdt = 0L,
                tokensSold = 0L,
                purchaseCount = 0L,
                uniqueUsers = 0L,
                refundCount = 0L,
                refundedAmountTon = 0L,
                refundedAmountUsdt = 0L,
                tonPrice = tonPrice,
                totalRaised = BigDecimal.ZERO,
                isRoundEnded = 0,
                isHardCapReached = 0
            )

            // 初始化下一轮
            ontonFairLaunchRep.initRoundData(nextRoundData)

            println("已初始化轮次 $nextRoundNum")
        }
    }

    /**
     * 判断当前是否在静默期
     */
    private fun isInSettlementPeriod(roundNum: Long, currentTime: Instant): Boolean {
        if (roundNum <= 0 || roundNum > TOTAL_ROUNDS) return false

        println("5. isInSettlementPeriod round $roundNum")

        try {
            // 检查数据库中是否标记为硬顶达成
            val roundRecord = ontonFairLaunchRep.findByRoundNum(roundNum)
            if (roundRecord?.isHardCapReached == 1) {
                ontonFairLaunchRep.updateRoundEnded(roundNum)
                println("5. round: $roundNum 硬顶达成，进入静默期")

                // 更新合约状态
                try {
                    val tonPrice = BigDecimal.valueOf(wiseScoreService.getCoinPriceFromApi("TON"))
                    val updateResult = updateContractRound(roundNum, tonPrice)
                    if (updateResult) {
                        println("合约轮次 $roundNum 结束状态更新成功")
                    } else {
                        println("合约轮次 $roundNum 结束状态更新失败")
                    }
                } catch (e: Exception) {
                    println("更新合约结束状态失败: ${e.message}")
                    e.printStackTrace()
                }

                return true // 硬顶达成，进入静默期
            }

            // 检查时间是否超过2小时
            val roundStartTime = calculateRoundStartTime(roundNum)
            val timeInRound = Duration.between(roundStartTime, currentTime)

            if (timeInRound >= Duration.ofSeconds(ROUND_DURATION_SECONDS)) {
                ontonFairLaunchRep.updateRoundEnded(roundNum)
                println("5. round: $roundNum 时间到达，进入静默期")

                // 更新合约状态
                try {
                    val tonPrice = BigDecimal.valueOf(wiseScoreService.getCoinPriceFromApi("TON"))
                    val updateResult = updateContractRound(roundNum, tonPrice)
                    if (updateResult) {
                        println("合约轮次 $roundNum 结束状态更新成功")
                    } else {
                        println("合约轮次 $roundNum 结束状态更新失败")
                    }
                } catch (e: Exception) {
                    println("更新合约结束状态失败: ${e.message}")
                    e.printStackTrace()
                }

                return true // 时间到达，进入静默期
            }
        } catch (e: Exception) {
            println("判断静默期失败: ${e.message}")
        }

        return false
    }

    /**
     * 计算轮次开始时间 (上一轮的结束时间 + 10分钟静默）
     */
    private fun calculateRoundStartTime(roundNum: Long): Instant {
        if (roundNum == 1L) return FAIR_LAUNCH_START_TIME

        try {
            // 查找上一轮的实际结束时间
            val prevRoundRecord = ontonFairLaunchRep.findByRoundNum((roundNum - 1))
            if (prevRoundRecord != null) {
                return prevRoundRecord.endTime.plus(Duration.ofSeconds(SETTLEMENT_DURATION_SECONDS))
            }
        } catch (e: Exception) {
            println("获取上一轮结束时间失败，使用理论时间: ${e.message}")
        }

        // 使用理论时间计算
        val cycleDuration = Duration.ofSeconds(ROUND_DURATION_SECONDS + SETTLEMENT_DURATION_SECONDS)
        return FAIR_LAUNCH_START_TIME.plus(cycleDuration.multipliedBy((roundNum - 1).toLong()))
    }

    /**
     * 计算（理论）轮次开始时间
     */
//    private fun calculateDefaultStartTime(roundNum: Long): LocalDateTime {
//        val cycleDuration = Duration.ofMinutes(ROUND_DURATION_MINUTES + SETTLEMENT_DURATION_MINUTES)
//        return FAIR_LAUNCH_START_TIME.plus(cycleDuration.multipliedBy((roundNum - 1)))
//    }

    private fun parseTime(simulateTime: String?): Instant {
        return if (simulateTime != null) {
            // 期望输入格式为 "2025-07-23T16:00:00Z"
            Instant.parse(simulateTime)
        } else {
            Instant.now()
        }
    }

    /**
     * 当上一轮卖完时，计算拍卖第 n 轮次的 Token 价格
     *
     * @param n 当前轮次
     * @param pPrev 之前一轮的价格 p(n-1)
     * @param tPrev 之前 n-1 轮卖出的总 Token 数 t(n-1)
     * @param tTotal 总共的 Token supply t_total
     * @return 当前轮次的价格 p(n)
     */
    fun calculateTokenPriceWhenSoldOut(n: Int, pPrev: Double, tPrev: Double, tTotal: Double): Double {

        val k =
            2 // fomo - sensitivity, 2-5 ： https://docs.google.com/document/d/1H2uWQcqtxNqthLW1gDI_mrsNxtkcLaqM1uTBnSIQPps/edit?tab=t.0
        // 检查输入参数的有效性
        if (n <= 1) throw IllegalArgumentException("当前轮次 n 必须大于 1")
        if (pPrev <= 0) throw IllegalArgumentException("之前一轮的价格 p(n-1) 必须为正")
        if (tPrev < 0 || tTotal <= 0) throw IllegalArgumentException("Token 数量必须为非负，且总 supply 必须为正")
        if (tPrev > tTotal) throw IllegalArgumentException("之前卖出的 Token 数 t(n-1) 不能超过总 supply t_total")

        // 计算之前 n-1 轮的 Token 占比 t_ratio
        val tRatio = tPrev / tTotal

        // 计算当前轮次的价格 p(n)
        val pCurrent = pPrev * exp(k * tRatio)

        return pCurrent
    }

    /**
     * 当上一轮 没有 卖完时，计算拍卖第 n 轮次的 Token 价格
     *
     * @param n 当前轮次
     * @param pPrev 之前一轮的价格 p(n-1)
     * @param tPrevRound  仅第 n-1 轮卖出的总 Token 数
     * @param tPrevSupply 仅第 n-1 轮token的max supply
     * @return 当前轮次的价格 p(n)
     */
    fun calculateTokenPriceWhenNotSoldOut(
        n: Int,
        prePrice: Double,
        tPrevRoundToken: Double,
        roundHardCapToken: Double
    ): Double {
        // 检查输入参数的有效性
        if (n <= 1) throw IllegalArgumentException("当前轮次 n 必须大于 1")
        if (prePrice <= 0) throw IllegalArgumentException("之前一轮的价格 p(n-1) 必须为正")
        if (tPrevRoundToken < 0 || roundHardCapToken <= 0) throw IllegalArgumentException("Token 数量必须为非负，且供应量必须为正")
        if (tPrevRoundToken > roundHardCapToken) throw IllegalArgumentException("卖出的 Token 数 tPrevRound 不能超过最大供应量 tPrevSupply")

        // 计算价格增长比例
        val growthFactor = 1 + 0.1 * (tPrevRoundToken / roundHardCapToken)

        // 计算当前轮次的价格
        val pCurrent = prePrice * growthFactor

        println("pCurrent - $pCurrent")

        return pCurrent
    }


    // ==================== TODO: 合约调用相关方法 ====================

    /**
     * 调用合约获取指定轮次的销售数据
     */
    private fun callContractForRoundData(roundNum: Long): OntonRoundContractUpdateData {
        val contractAddress = "kQCUmdTvMaj1JmC-YhsSfbJdWsi_--8urSYN-DIpXYi_hgvu"
        val apiKey = "095b88d26e59825a615f2a431c8d89e80448e721917a9ae44218aa9554829687" // test net

        try {
            // 创建请求 JSON 数据
            val requestJson = JSONObject().apply {
                put("address", contractAddress) // 合约地址
                put("method", "round_summary")   // 方法名称

                // 构建 [["num", roundNum.toString()]] 作为嵌套数组
                val stackArray = JSONArray().apply {
                    put(JSONArray().apply {
                        put("num")
                        put(roundNum.toString())
                    })
                }

                put("stack", stackArray)
            }

            val response = webClient
                .post()
                .uri("https://testnet.toncenter.com/api/v2/runGetMethod")
                .header("X-API-Key", apiKey)
                .header("Content-Type", "application/json")
                .bodyValue(requestJson.toString())
                .retrieve()
                .bodyToMono(String::class.java)
                .block()

            val jsonObject = JSONObject(response)
            val result = jsonObject.getJSONObject("result")

            // 获取 stack 数组
            val stackArray = result.getJSONArray("stack")

            // 获取第一个元素(["tuple", {...}])
            val tupleObject = stackArray.getJSONArray(0).getJSONObject(1)

            // 获取 elements 数组
            val elementsArray = tupleObject.getJSONArray("elements")

            // 解析 elements 中的每个参数
            val price = elementsArray.getJSONObject(3).getJSONObject("number").getString("number").toLong()
            val totalRaisedTon = elementsArray.getJSONObject(4).getJSONObject("number").getString("number").toLong()
            val totalRaisedUsdt = elementsArray.getJSONObject(5).getJSONObject("number").getString("number").toLong()
            val tokensSold = elementsArray.getJSONObject(6).getJSONObject("number").getString("number").toLong()
            val purchaseCount = elementsArray.getJSONObject(7).getJSONObject("number").getString("number").toLong()
            val uniqueUsers = elementsArray.getJSONObject(8).getJSONObject("number").getString("number").toLong()
            val refundCount = elementsArray.getJSONObject(9).getJSONObject("number").getString("number").toLong()
            val refundedAmountTon = elementsArray.getJSONObject(10).getJSONObject("number").getString("number").toLong()
            val refundedAmountUsdt =
                elementsArray.getJSONObject(11).getJSONObject("number").getString("number").toLong()

            println("tokensSold - $tokensSold")

            // 获取 TON 价格
            val tonPrice = BigDecimal.valueOf(wiseScoreService.getCoinPriceFromApi("TON"))

            return OntonRoundContractUpdateData(
                totalRaisedTon = totalRaisedTon,
                totalRaisedUsdt = totalRaisedUsdt,
                tokensSold = tokensSold,
                purchaseCount = purchaseCount,
                uniqueUsers = uniqueUsers,
                refundCount = refundCount,
                refundedAmountTon = refundedAmountTon,
                refundedAmountUsdt = refundedAmountUsdt,
                tonPrice = tonPrice,
                totalRaised = BigDecimal.ZERO, // 不用了，用 tokens_sold
//                isRoundEnded = isRoundEnded,
//                isHardCapReached = isHardCapReached
            )

        } catch (e: Exception) {
            // 合约调用失败时的异常处理
            println("合约方法调用失败,轮次:$roundNum,错误:${e.message}")
            e.printStackTrace()

            // 可以选择抛出异常或返回默认值
            throw RuntimeException("获取轮次 $roundNum 数据失败: ${e.message}", e)

            // 或者返回空数据/默认数据（根据业务需求选择）
        }
    }


    /**
     * 调用合约获取指定轮次的销售数据 - 模拟版本
     */
//    private fun callContractForRoundData(roundNum: Long): OntonRoundContractUpdateData {
//        try {
//            // 模拟数据生成
//            val mockData = generateMockRoundData(roundNum)
//
//            println("模拟轮次 $roundNum 数据: totalRaisedTon=${mockData.totalRaisedTon}, totalRaisedUsdt=${mockData.totalRaisedUsdt}")
//
//            // 获取 TON 价格
//            val tonPrice = BigDecimal.valueOf(wiseScoreService.getCoinPriceFromApi("TON"))
//
//            return OntonRoundContractUpdateData(
//                totalRaisedTon = mockData.totalRaisedTon,
//                totalRaisedUsdt = mockData.totalRaisedUsdt,
//                tokensSold = mockData.tokensSold,
//                purchaseCount = mockData.purchaseCount,
//                uniqueUsers = mockData.uniqueUsers,
//                refundCount = mockData.refundCount,
//                refundedAmountTon = mockData.refundedAmountTon,
//                refundedAmountUsdt = mockData.refundedAmountUsdt,
//                tonPrice = tonPrice,
//                totalRaised = BigDecimal.ZERO, // 这个会在updateRoundTotal中计算
//            )
//
//        } catch (e: Exception) {
//            // 模拟数据生成失败时的异常处理
//            println("模拟数据生成失败,轮次:$roundNum,错误:${e.message}")
//            e.printStackTrace()
//            throw RuntimeException("获取轮次 $roundNum 模拟数据失败: ${e.message}", e)
//        }
//    }

    private fun generateMockRoundData(roundNum: Long): MockRoundData {

        val mockDataSets = listOf(
            // 数据组1: 40000 USDT
            MockRoundData(
                totalRaisedTon = 0L,                      // 0 TON (精度9)
                totalRaisedUsdt = 40_000_000_000L,        // 40000 USDT (精度6: 40000 * 1000000)
                tokensSold = 2_000_000_000_000_000L,  // 修改后的值
                purchaseCount = 80L,
                uniqueUsers = 58L,
                refundCount = 3L,
                refundedAmountTon = 0L,                  // 0 TON
                refundedAmountUsdt = 150_000_000L         // 150 USDT (精度6: 150 * 1000000)
            ),

            // 数据组2: 40000 USDT
            MockRoundData(
                totalRaisedTon = 0L,                      // 0 TON (精度9)
                totalRaisedUsdt = 40_000_000_000L,        // 40000 USDT (精度6: 40000 * 1000000)
                tokensSold = 2_000_000_000_000_000L,  // 修改后的值
                purchaseCount = 85L,
                uniqueUsers = 62L,
                refundCount = 4L,
                refundedAmountTon = 0L,                   // 0 TON
                refundedAmountUsdt = 200_000_000L         // 200 USDT (精度6: 200 * 1000000)
            ),

            // 数据组3: 40000 USDT
            MockRoundData(
                totalRaisedTon = 0L,                      // 0 TON (精度9)
                totalRaisedUsdt = 40_000_000_000L,        // 40000 USDT (精度6: 40000 * 1000000)
                tokensSold = 2_000_000_000_000_000L,  // 修改后的值
                purchaseCount = 90L,
                uniqueUsers = 65L,
                refundCount = 2L,
                refundedAmountTon = 0L,                   // 0 TON
                refundedAmountUsdt = 100_000_000L         // 100 USDT (精度6: 100 * 1000000)
            ),

            // 数据组4: 24000 USDT
            MockRoundData(
                totalRaisedTon = 0L,                      // 0 TON (精度9)
                totalRaisedUsdt = 24_000_000_000L,        // 24000 USDT (精度6: 24000 * 1000000)
                tokensSold = 2_000_000_000_000_000L,  // 修改后的值
                purchaseCount = 48L,
                uniqueUsers = 35L,
                refundCount = 1L,
                refundedAmountTon = 0L,                   // 0 TON
                refundedAmountUsdt = 50_000_000L          // 50 USDT (精度6: 50 * 1000000)
            ),

            // 数据组5: 32000 USDT
            MockRoundData(
                totalRaisedTon = 0L,                      // 0 TON (精度9)
                totalRaisedUsdt = 32_000_000_000L,        // 32000 USDT (精度6: 32000 * 1000000)
                tokensSold = 2_000_000_000_000_000L,  // 修改后的值
                purchaseCount = 64L,
                uniqueUsers = 45L,
                refundCount = 2L,
                refundedAmountTon = 0L,                   // 0 TON
                refundedAmountUsdt = 100_000_000L         // 100 USDT (精度6: 100 * 1000000)
            ),

            // 数据组6: 8000 USDT
            MockRoundData(
                totalRaisedTon = 0L,                      // 0 TON (精度9)
                totalRaisedUsdt = 8_000_000_000L,         // 8000 USDT (精度6: 8000 * 1000000)
                tokensSold = 2_000_000_000_000_000L,  // 修改后的值
                purchaseCount = 16L,
                uniqueUsers = 12L,
                refundCount = 0L,
                refundedAmountTon = 0L,                   // 0 TON
                refundedAmountUsdt = 0L                   // 0 USDT
            )
        )

        // 根据轮次选择对应的模拟数据，如果超出范围则循环使用
        val dataIndex = ((roundNum - 1) % mockDataSets.size).toInt()
        return mockDataSets[dataIndex]
    }


    /**
     * 模拟轮次数据结构
    //     */
    private data class MockRoundData(
        val totalRaisedTon: Long,        // TON总额 (精度9)
        val totalRaisedUsdt: Long,       // USDT总额 (精度6)
        val tokensSold: Long,            // 已售代币数量
        val purchaseCount: Long,         // 购买次数
        val uniqueUsers: Long,           // 唯一用户数
        val refundCount: Long,           // 退款次数
        val refundedAmountTon: Long,     // 退款TON金额
        val refundedAmountUsdt: Long     // 退款USDT金额
    )

    /**
     * 计算模拟数据的总价值（用于验证）
     * 假设TON价格为1 USDT
     */
    private fun calculateTotalValue(data: MockRoundData, tonPriceUsdt: Double = 1.0): Double {
        val tonValueInUsdt = (data.totalRaisedTon.toDouble() / 1_000_000_000) * tonPriceUsdt
        val usdtValue = data.totalRaisedUsdt.toDouble() / 1_000_000
        return tonValueInUsdt + usdtValue
    }


    /**
     * 获取轮次价格 - 优化版本
     * 优先从数据库获取，只在必要时计算
     */
    private fun getRoundPrice(roundNum: Long): Double {
        // 1. 优先从数据库获取已存储的价格
        try {
            val roundRecord = ontonFairLaunchRep.findByRoundNum(roundNum)
            if (roundRecord != null && roundRecord.price > 0) {
                val storedPrice = roundRecord.price.toDouble() / 1_000_000
                println("从数据库获取轮次 $roundNum 价格: $storedPrice")
                return storedPrice
            }
        } catch (e: Exception) {
            println("从数据库获取价格失败: ${e.message}")
        }

        // 2. 数据库中没有，则计算价格
        println("数据库中无价格数据，开始计算轮次 $roundNum 的价格")
        return calculateRoundPrice(roundNum)
    }


    /**
     * 计算轮次价格
     */
    private fun calculateRoundPrice(roundNum: Long): Double {
        if (roundNum == 1L) {
            return FIRST_ROUND_PRICE // 第一轮的初始价格
        }

        val currentRecord = ontonFairLaunchRep.findByRoundNum(roundNum)

        if (currentRecord != null && currentRecord.price > 0) {
            return currentRecord.price.toDouble()
        }

        try {
            // 获取上一轮的数据
            val prevRoundRecord = ontonFairLaunchRep.findByRoundNum(roundNum - 1)

            if (prevRoundRecord != null) {
                val isHardCapReached = prevRoundRecord.isHardCapReached == 1

                // 上一轮单价
                val prePrice = prevRoundRecord.price.toDouble() / 1_000_000

                if (isHardCapReached) {
                    // 上一轮售完 - 使用FOMO公式
                    val allPrevRecords = ontonFairLaunchRep.findByRoundNumLessThan(roundNum)
                    val tPrev = allPrevRecords.sumOf { it.tokensSold / 1_000_000_000.0 }
                    println("$roundNum - 售完情况 - tPrev: $tPrev, TOTAL_HARD_CAP_USDT: $TOTAL_HARD_CAP_TOKEN")
                    val tTotal = TOTAL_HARD_CAP_TOKEN

                    val newPrice = calculateTokenPriceWhenSoldOut(roundNum.toInt(), prePrice, tPrev, tTotal)
                    println("新价格(售完): $newPrice")
                    return newPrice
                } else {
                    // 上一轮未售完
                    val tPrevRoundToken = prevRoundRecord.tokensSold / 1_000_000_000.0
                    val roundHardCapToken = ROUND_HARD_CAP_TOKEN

                    val newPrice = calculateTokenPriceWhenNotSoldOut(
                        roundNum.toInt(),
                        prePrice,
                        tPrevRoundToken,
                        roundHardCapToken
                    )
                    return newPrice
                }
            }
        } catch (e: Exception) {
            println("计算轮次价格失败: ${e.message}")
        }

        return FIRST_ROUND_PRICE
    }


    data class PurchaseCalculation(
        val user: String,                         // Address 对应为 String
        val amount: BigInteger,                   // bigint 对应为 BigInteger
        val currency: Int,                        // currency 对应为 Int (0=TON, 1=USDT)
        val tokensToReceive: BigInteger,          // tokens_to_receive 对应为 BigInteger
        val currentPrice: BigInteger,             // current_price 对应为 BigInteger
        val currentRound: BigInteger,             // current_round 对应为 BigInteger
        val timestamp: BigInteger,                // timestamp 对应为 BigInteger
        val nonce: BigInteger,                     // nonce 对应为 BigInteger
        val usdtEquivalentAmount: BigInteger
    )

    fun genSign(cals: PurchaseCalculation): String {
        val tonKey =
            "c93ed5f88635fde1d98d0dac891b2dc079390fa054892659a38a6d257020bafa3f23bbe305ca208fd2d2bff11c503c1b4f53f676f1a5dd74d558ef478ee7ed7f"
        val cell: Cell = CellBuilder.beginCell()
            .storeAddress(Address.of(cals.user))
            .storeCoins(cals.amount)
            .storeUint(cals.currency, 8)
            .storeCoins(cals.tokensToReceive)
            .storeCoins(cals.currentPrice)
            .storeUint(cals.currentRound, 32)
            .storeUint(cals.timestamp, 64)
            .storeUint(cals.nonce, 64)
            .storeCoins(cals.usdtEquivalentAmount)
            .endCell()

        // 使用私钥对Cell的哈希值进行签名
        val hash = cell.hash()
        val privateKey = HexFormat.of().parseHex(tonKey)
        val signature: ByteArray = Ed25519.sign(privateKey, hash)
        println("hash is $hash and private key is $tonKey and signature is $signature")
        return HexFormat.of().formatHex(signature)
    }

    fun getUserBuyTokenAmount(address: String, currency: Int, amount: Long): Any? {
//        val hardCap = 2000000L * 1_000_000L // 2M * 10^6精度
//        val roundHardCap = hardCap * 0.02
//        val userMaxRoundPercent = 0.1 // 每一轮 每个用户能购买的Token总量占 10%
//        val userMaxRoundUsdtNum = roundHardCap * userMaxRoundPercent //  每一轮 每个用户能购买的token价值u的上限
        val tokenRoundHardCap = 2_000_000L * 1_000_000_000L // 每一轮的token硬顶


        val currentRound = getCurrentRound(Instant.now())
//        val currentRound = 1 // TODO
        // todo : 获取上一轮的价格，应该存在数据库里，
//        val pPrev = 0.01  // 上一轮价格， 假的
//        val pPrevRound = 100000.00 // 上一轮卖出的token数 , 假的
//        val pPrevMaxSupply = roundHardCap / pPrev //上一轮max supply

        var actualTokenNum = BigDecimal.ZERO

//        println("currentRound is $currentRound and userMaxRoundUsdtNum is $userMaxRoundUsdtNum")

        if (currentRound in 1..TOTAL_ROUNDS) {
            val currentInfo = ontonFairLaunchRep.findByRoundNum(currentRound)!!
            val currentPrice =
                currentInfo.price
//            val userMaxRoundUsdtNum = tokenRoundHardCap * 0.1 * currentInfo.price
            // currency 对应为 Int (0=TON, 1=USDT)
            if (currency == 0) {
//                val tonPrice = wiseScoreService.getCoinPriceFromApi("TON")
//                 todo : tonPrice 应该在每轮第一次获取时，写到数据库里，这一轮的ton价格不变. 只取一次
                val tonPrice = currentInfo.tonPrice
//                val maxTokenNum = BigDecimal.valueOf(userMaxRoundUsdtNum)
//                    .divide(BigDecimal.valueOf(currentPrice), 6, RoundingMode.HALF_UP)
                val maxTokenNum = BigDecimal.valueOf(tokenRoundHardCap * 1)
                val availableTokenNum = BigDecimal.valueOf(amount / 1000) // ton的精度从9转为u的精度6
                    .multiply(tonPrice)
                    .divide(BigDecimal.valueOf(currentPrice), 6, RoundingMode.HALF_UP)
                println("current price is ${currentInfo.price} actualTokenNum is $actualTokenNum and availableTokenNum is $availableTokenNum and maxTokenNum is $maxTokenNum")
                if (availableTokenNum <= maxTokenNum) {
                    actualTokenNum = availableTokenNum
                } else {
                    println("Reach the user's purchase limit")
                    return "-1"
                }
            } else {
                // usdt
//                val maxTokenNum = BigDecimal.valueOf(userMaxRoundUsdtNum)
//                    .divide(BigDecimal.valueOf(currentPrice), 6, RoundingMode.HALF_UP)
                val maxTokenNum = BigDecimal.valueOf(tokenRoundHardCap * 1)
                val availableTokenNum = BigDecimal.valueOf(amount)
                    .divide(BigDecimal.valueOf(currentPrice), 6, RoundingMode.HALF_UP)
                println("current price is ${currentInfo.price} actualTokenNum is $actualTokenNum and availableTokenNum is $availableTokenNum and maxTokenNum is $maxTokenNum ")
                if (availableTokenNum <= maxTokenNum) {
                    actualTokenNum = availableTokenNum
                } else {
                    println("Reach the user's purchase limit")
                    return "-1"
                }
            }

            // gen sign
            if (actualTokenNum > BigDecimal.ZERO) {
                val newId = idGenerator.getNewId().toBigInteger()
                println("newId is $newId")
                val cals = PurchaseCalculation(
                    user = address,
                    amount = amount.toBigInteger(),
                    currency = currency,
                    tokensToReceive = actualTokenNum.multiply(BigDecimal.TEN.pow(9)).toBigInteger(),
                    currentPrice = BigInteger.valueOf(currentPrice),
                    currentRound = currentRound.toBigInteger(),
                    timestamp = (System.currentTimeMillis() / 1000).toBigInteger(),
                    nonce = newId,
                    usdtEquivalentAmount = actualTokenNum.setScale(6, RoundingMode.HALF_UP)
                        .multiply(BigDecimal.valueOf(currentPrice)).toBigInteger()
                )
                println("purchaseCalculation: $cals")
                val nonceSign = genSign(cals)
                val buyTokenResponse = BuyTokenResponse(
                    purchaseCalculation = cals,
                    sign = nonceSign
                )
                return buyTokenResponse
            }
        }
        return null
    }

    data class BuyTokenResponse(
        val purchaseCalculation: PurchaseCalculation,
        val sign: String
    ) {
    }

    /**
     * 创建更新轮次的消息
     */
    private fun createUpdateRoundMessage(newPrice: BigInteger, roundNumber: Long): Cell {
        return CellBuilder.beginCell()
            .storeUint(OP_UPDATE_ROUND, 32) // op code
            .storeUint(0, 64) // query_id
            .storeCoins(newPrice) // new_price
            .storeUint(roundNumber, 32) // round_number
            .endCell()
    }

    /**
     * 更新合约轮次信息 - 使用 ton4j 的简化 API
     */
    private fun updateContractRound(roundNumber: Long, tonPrice: BigDecimal): Boolean {
        if (ownerPrivateKey.isEmpty() || contractAddress.isEmpty()) {
            println("合约配置缺失，跳过合约更新")
            return false
        }

        return runCatching {
            // 获取当前轮次价格（从数据库）
            val currentRoundData = ontonFairLaunchRep.findByRoundNum(roundNumber)
                ?: throw IllegalStateException("未找到轮次 $roundNumber 的数据")

            // 价格已经是以 1_000_000 为单位存储的
            val newPrice = BigInteger.valueOf(currentRoundData.price)

            // 创建钱包 - 使用 ton4j 的简化方式
            val privateKeyBytes = HexFormat.of().parseHex(ownerPrivateKey)
            val keyPair = Utils.generateSignatureKeyPairFromSeed(privateKeyBytes)

            val wallet = WalletV4R2.builder()
                .walletId(42)
                .keyPair(keyPair)
                .build()

            val seqno = getWalletSeqno(wallet.address.toRaw())

            // 创建更新轮次消息
            val updateRoundMessage = createUpdateRoundMessage(newPrice, roundNumber)

            // 使用 ton4j 的 prepareTransferMsg 方法 - 这是推荐的方式
            val transferMessage = wallet.prepareTransferMsg(
                seqno,
                Address.of(contractAddress),
                Utils.toNano(0.02),
                updateRoundMessage,
                3 // sendMode
            )

            // 发送交易
            val sendResult = sendTransaction(transferMessage.toCell())

            if (sendResult) {
                // 等待交易确认
                waitForTransactionConfirmation(wallet.address.toRaw(), seqno)
            } else {
                false
            }
        }.fold(
            onSuccess = { result ->
                println("合约轮次 $roundNumber 更新${if (result) "成功" else "失败"}")
                result
            },
            onFailure = { exception ->
                println("更新合约轮次失败: ${exception.message}")
                exception.printStackTrace()
                false
            }
        )
    }

    /**
     * 等待交易确认
     */
    private fun waitForTransactionConfirmation(
        walletAddress: String,
        expectedSeqno: Long,
        maxWaitSeconds: Int = 30
    ): Boolean {
        repeat(maxWaitSeconds) {
            try {
                Thread.sleep(1000)
                val newSeqno = getWalletSeqno(walletAddress)
                if (newSeqno > expectedSeqno) {
                    println("交易确认成功，seqno 从 $expectedSeqno 更新到 $newSeqno")
                    return true
                }
            } catch (e: Exception) {
                println("检查交易确认时出错: ${e.message}")
            }
        }
        println("交易确认超时，等待了 $maxWaitSeconds 秒")
        return false
    }
    
    /**
     * 获取钱包 seqno
     */
    private fun getWalletSeqno(address: String): Long {
        try {
            val requestJson = JSONObject().apply {
                put("address", address)
                put("method", "seqno")
                put("stack", JSONArray())
            }
            
            val response = webClient
                .post()
                .uri("$tonApiEndpoint/api/v2/runGetMethod")
                .header("X-API-Key", tonApiKey)
                .header("Content-Type", "application/json")
                .bodyValue(requestJson.toString())
                .retrieve()
                .bodyToMono(String::class.java)
                .block()
                
            val jsonObject = JSONObject(response)
            val result = jsonObject.getJSONObject("result")
            val stack = result.getJSONArray("stack")
            
            if (stack.length() > 0) {
                val seqnoArray = stack.getJSONArray(0)
                val seqnoObj = seqnoArray.getJSONObject(1)
                return seqnoObj.getString("number").toLong()
            }
            
            return 0L
        } catch (e: Exception) {
            println("获取 seqno 失败: ${e.message}")
            return 0L
        }
    }
    
    private fun sendTransaction(message: Cell): Boolean {
        try {
            val bocBase64 = Utils.bytesToBase64SafeUrl(message.toBoc())
            
            val requestJson = JSONObject().apply {
                put("boc", bocBase64)
            }
            
            val response = webClient
                .post()
                .uri("$tonApiEndpoint/api/v2/sendBoc")
                .header("X-API-Key", tonApiKey)
                .header("Content-Type", "application/json")
                .bodyValue(requestJson.toString())
                .retrieve()
                .bodyToMono(String::class.java)
                .block()
                
            val jsonObject = JSONObject(response)
            return jsonObject.getBoolean("ok")
            
        } catch (e: Exception) {
            println("发送交易失败: ${e.message}")
            e.printStackTrace()
            return false
        }
    }

}

