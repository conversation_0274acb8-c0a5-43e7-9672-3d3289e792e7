package com.rewardoor.app.services

import com.rewardoor.app.dao.*
import com.rewardoor.app.dao.WiseScoreInviteRepository.InviteeWithDate
import com.rewardoor.enums.ActIdInfo
import com.rewardoor.enums.InviteCodeCheckedType
import org.apache.commons.lang3.RandomStringUtils
import com.rewardoor.model.*
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.time.Instant
import com.rewardoor.enums.TBookInviteSbtLevel
import org.springframework.core.env.Environment

@Service
@Transactional
class WiseInviteService(
    private val wiseInviteRepo: WiseScoreInviteRepository,
    private val sbtWhiteListRepo: SBTWhiteListRepository,
    private val wiseScoreService: WiseScoreService,
    private val userService: UserService,
    private val userLuckyDrawRepository: UserLuckyDrawRepository,
    private val tPointsVanguardRepo: TPointsVanguardRepository,
    private val telegramService: TelegramService,
    private val sbtRewardRepo: SBTRewardRepository,
    private val participantRepo: ParticipantRepository,
    private val tonSocietySyncRepository: TonSocietySyncRepository,
    private val userWiseScoreRepo: UserWiseScoreRepository,
    private val env: Environment,
) {
    fun addInviteCode(userId: Long, eventStage: Int): String {
        var code = genNewCode()
        while (true) {
            val cnt = wiseInviteRepo.addInviteCode(userId, eventStage, code)
            if (cnt > 0) break
            code = genNewCode()
        }
        return code
    }

    fun getOrAddInviteCode(userId: Long, eventStage: Int): WiseInviteCode {
        val current = wiseInviteRepo.getInviteCode(userId, eventStage)
        if (current != null) return current
        val code = addInviteCode(userId, eventStage)
        return WiseInviteCode(userId, eventStage, code, 3, 0)
    }

    fun getInviteCode(userId: Long, eventStage: Int): WiseInviteCode? {
        return wiseInviteRepo.getInviteCode(userId, eventStage)
    }

    fun addInvitee(code: String, eventStage: Int, invitee: User): InviteResult {
        val inviteCode = getInviteCode(invitee.userId, eventStage)?.inviteCode
        if (inviteCode == code) {
            return InviteResult(null, InviteCodeCheckedType.INVITE_MY_SELF)
        }
        val invite = wiseInviteRepo.getInviteByCode(code.uppercase())
        // still generate wiseScore without invite score
        if (invite == null) {
            println("No invite code for user ${invitee.userId} and the code is ${code.uppercase()}")
            val newWiseScore = wiseScoreService.addScoreResult(invitee.userId)
            return InviteResult(newWiseScore, InviteCodeCheckedType.NO_INVITE)
        }
        // still generate wiseScore without invite score
        if (invite.usedTimes >= invite.totalTimes) {
            println("Invite code $code has been used up")
            val newWiseScore = wiseScoreService.addScoreResult(invitee.userId)
            return InviteResult(newWiseScore, InviteCodeCheckedType.SUCCESS)
        }
        val inviteTgName = telegramService.getUserInfo(invite.userId)?.username ?: ""

        wiseInviteRepo.addInvitee(invite.userId, inviteTgName, code.uppercase(), eventStage, invitee.userId)

        // init wiseScoreResult
        var wiseScoreResult: UserWiseScore? = null

        // no wiseScore => init wiseScore
        val wiseScore = wiseScoreService.getScoreById(invite.userId)
        if (wiseScore == null) {
            wiseScoreResult = wiseScoreService.addBlankScoreResult(invitee)
        }
        // or get wiseScore
        wiseScoreResult = wiseScoreService.getUserWiseScore(invite.userId)

        wiseInviteRepo.updateInviteTimes(invite.userId, invite.inviteCode, invite.usedTimes + 1)
        val vanguard = tPointsVanguardRepo.getTPointsVanguardById(invite.userId)
        val points = if (vanguard == null) inviteTPoints else ambInviteTPoints
        val luckyDrawResult = LuckyDrawResult(
            userId = invite.userId,
            fissionLevel = -2,
            tPointsNum = points,
            isEligibleToGenerateWiseScore = 0,
            isEligibleToGenerateSBT = 0,
            createTime = Instant.now(),
            updateTime = Instant.now()
        )
        userLuckyDrawRepository.createUserLuckyDraw(luckyDrawResult)
        return InviteResult(wiseScoreResult, InviteCodeCheckedType.SUCCESS)
    }

    @Transactional
    fun addInviteeStage2(code: String, eventStage: Int, invitee: User): InviteResult {

        // Check is a new user(No record in tb_user_wise_score)
        val inviteeWiseScore = wiseScoreService.getScoreById(invitee.userId)
        if (inviteeWiseScore != null) {
            println("InviteCodeCheckedType.INVITE_REPEAT")
            return InviteResult(null, InviteCodeCheckedType.INVITE_REPEAT)
        }

        // Error: 邀请自己
        val inviteCode = getInviteCode(invitee.userId, eventStage)?.inviteCode
        if (inviteCode == code) {
            return InviteResult(null, InviteCodeCheckedType.INVITE_MY_SELF)
        }

        // Error: 这个码找不到邀请人
        val inviter = wiseInviteRepo.getInviteByCode(code.uppercase())
        if (inviter == null) {
            println("[addInviteeForSbt] No invite code for user ${invitee.userId} and the code is ${code.uppercase()}")
            val newWiseScore = wiseScoreService.addScoreResult(invitee.userId)
            return InviteResult(newWiseScore, InviteCodeCheckedType.NO_INVITE)
        }

        // Error: 被邀请人已经用过这个码了，tb_wise_invite_record 里有记录
        val invitees = wiseInviteRepo.getInvitees(inviter.userId)
        if (invitees.contains(invitee.userId)) {
            println("[addInviteeForSbt] User ${invitee.userId} has already used invite code ${code.uppercase()}, found record in tb_wise_invite_record")
            return InviteResult(null, InviteCodeCheckedType.INVITE_REPEAT_CODE)
        }

        // Success
        val inviteTgName = telegramService.getUserInfo(inviter.userId)?.username ?: ""
        wiseInviteRepo.addInvitee(inviter.userId, inviteTgName, code.uppercase(), eventStage, invitee.userId)

        wiseInviteRepo.updateInviteTimes(inviter.userId, inviter.inviteCode, inviter.usedTimes + 1)

        // Check if user meets next new SBT and inser inviter's SBT claim record

        val inviteesCount = wiseInviteRepo.getInviteesCountAll(inviter.userId)
        val intoNextLevel = TBookInviteSbtLevel.checkIsIntoNextLevel(inviter.userId, inviteesCount)
        val canLevelUp = intoNextLevel.canLevelUp

        if (canLevelUp) {
            updateInviteUserSbtList(inviter.userId)
        }

        // init wiseScoreResult
        var wiseScoreResult: UserWiseScore? = null

        // no wiseScore => init wiseScore
        val wiseScore = wiseScoreService.getScoreById(invitee.userId)
        if (wiseScore == null) {
            // init a new
            wiseScoreService.addBlankScoreResult(invitee)
        }

        return InviteResult(wiseScoreResult, InviteCodeCheckedType.SUCCESS)
    }

    fun checkAndAddSbtForStag1(userId: Long) {
        val inviteeCount = wiseInviteRepo.getInviteesCountAll(userId)
        println("checkAndAddSbtForStag1 - ${inviteeCount} - ${userId}")
        val sbtList = TBookInviteSbtLevel.getHistorySbtListByStage1(inviteeCount)

        val (activityIds, activityInfoMap) = TBookInviteSbtLevel.getActivityIdsByEnv(env)

        sbtList.forEach { sbtLevel ->

            val actInfo = activityInfoMap.values.find { it.level == sbtLevel.level } ?: return@forEach

            // update or create sbt_list
            createSbtListForInvite(userId, actInfo)
            // update or create user_reward
            createUserRewardForInvite(userId, actInfo)

        }
    }

    fun updateInviteUserSbtList(userId: Long) {
        val inviteeIds = getInviteesWithDateAll(userId)

        val actInfo = TBookInviteSbtLevel.getActivityIdByInvites(inviteeIds.size, env)

        val user = userService.getUserById(userId)
        val tonAddress = user?.ton?.tonWallet
        if (!tonAddress.isNullOrEmpty()) {
            val existingRecord = actInfo?.let {
                sbtWhiteListRepo.getUserSbtByUidAddressActivityId(
                    userId,
                    tonAddress,
                    activityId = it.id
                )
            }
            println("existingRecord - ${existingRecord?.userId} - claimedType: ${existingRecord?.claimedType}")

            if (existingRecord != null && existingRecord.claimedType!! < 3) {
                println("updateUserSBTClaimedType")
                sbtWhiteListRepo.updateUserSBTClaimedType(userId, tonAddress, actInfo.id, 2)
            } else {
                println("createUserSBT")
                val tonSyncSbt = actInfo?.let { tonSocietySyncRepository.getTonSyncHistoryBySBTId(it.sbtId) }!!
                val userSBTList = UserSBTList(
                    userId = userId,
                    address = tonAddress,
                    addressType = 1,
                    avatar = user.displayAvatar,
                    activityId = actInfo.id,
                    sbtLink = tonSyncSbt.activityUrl,
                    claimedType = 2
                )
                sbtWhiteListRepo.createUserSBT(userSBTList)
            }

            println("sbtRewardRepo.getSBTById(actInfo.sbtId)?.groupId? - ${sbtRewardRepo.getSBTById(actInfo.sbtId)?.groupId}")
            sbtRewardRepo.getSBTById(actInfo.sbtId)?.groupId?.let { groupId ->
                val userReward = UserReward(
                    rewardId = actInfo.sbtId,
                    rewardType = 3,
                    userId = userId,
                    groupId = groupId,
                    claimType = 2
                )

                val exists = participantRepo.getUserSbtReward(actInfo.sbtId, userId, groupId) != null
                if (exists) {
                    println("updateUserReward")
                    participantRepo.updateUserReward(userReward)
                } else {
                    println("addUserRewardResult")
                    participantRepo.addUserRewardResult(userReward)
                }
            }
        }
    }

    fun createSbtListForInvite(userId: Long, actInfo: ActIdInfo) {

        val user = userService.getUserById(userId)
        val tonAddress = user?.ton?.tonWallet

        if (tonAddress != null) {
            val existingRecord = sbtWhiteListRepo.getUserSbtByUidAddressActivityId(userId, tonAddress, actInfo.id)

            if (existingRecord == null) {
                val tonSyncSbt = actInfo.let { tonSocietySyncRepository.getTonSyncHistoryBySBTId(it.sbtId) }
                    ?: return

                val userSBTList = UserSBTList(
                    userId = userId,
                    address = tonAddress,
                    addressType = 1,
                    avatar = user.displayAvatar,
                    activityId = actInfo.id,
                    sbtLink = tonSyncSbt.activityUrl,
                    claimedType = 2
                )
                sbtWhiteListRepo.createUserSBT(userSBTList)
            }
        }
    }

    fun createUserRewardForInvite(userId: Long, actInfo: ActIdInfo) {
        val user = userService.getUserById(userId)
        val tonAddress = user?.ton?.tonWallet

        if (tonAddress != null) {

            // Handle reward creation/update
            sbtRewardRepo.getSBTById(actInfo.sbtId)?.groupId?.let { groupId ->
                val userReward = UserReward(
                    rewardId = actInfo.sbtId,
                    rewardType = 3,
                    userId = userId,
                    groupId = groupId,
                    claimType = 2
                )

                val exists = participantRepo.getUserSbtReward(actInfo.sbtId, userId, groupId) != null
                if (!exists) {
                    participantRepo.addUserRewardResult(userReward)
                }
            }
        }
    }

    fun getSbtListByActivityIds(activityList: List<Int>, userId: Long): List<SBTReward> {
        // get sbt claimedType info by userId
        val sbtMap = sbtWhiteListRepo.getUserSbtListByUseId(userId)?.associateBy { it.activityId }

        // get sbt info
        return activityList.flatMap { activityId ->
            sbtRewardRepo.getSBTByActivityId(activityId).onEach { sbtReward ->
                sbtMap?.get(activityId)?.let { userSbt ->
                    sbtReward.claimedType = userSbt.claimedType ?: 0
                }
            }
        }
    }

    fun calcCurrentAddScore(index: Int): Int {
        val inviteAddScore = when {
            index < 3 -> 1000
            index in 3..19 -> 500
            index in 20..49 -> 200
            index in 50..99 -> 100
            else -> 50
        }
        return inviteAddScore
    }

    fun getInvitees(userId: Long): List<Long> {
        return wiseInviteRepo.getInvitees(userId)
    }

    fun getInviteesWithDate(userId: Long, eventStage: Int = 1): List<InviteeWithDate> {
        return wiseInviteRepo.getInviteesWithDate(userId, eventStage)
    }

    fun getInviteesWithDateAll(userId: Long): List<InviteeWithDate> {
        return wiseInviteRepo.getInviteesWithDateAll(userId)
    }

    fun getInviterUid(userId: Long): Long {
        val inviterUid = wiseInviteRepo.getInviterUid(userId) ?: 0
        return inviterUid
    }

    fun getInviterCode(userId: Long): String {
        val inviterCode = wiseInviteRepo.getInviterCode(userId) ?: ""
        return inviterCode
    }

    fun getInviterTgName(userId: Long): String {
        val inviterTgName = wiseInviteRepo.getInviterTgName(userId) ?: ""
        return inviterTgName
    }

    private fun genNewCode(): String {
        return RandomStringUtils.random(6, true, true).uppercase()
    }

    fun updateInviteCode(userId: Long, eventStage: Int, newInviteTimes: Int): WiseInviteCode {
        val wiseInviteCode =
            getInviteCode(userId, eventStage) ?: throw IllegalArgumentException("No invite code for user")
        wiseInviteRepo.updateInviteTotalTimes(userId, wiseInviteCode.inviteCode, newInviteTimes)
        return wiseInviteCode
    }


    fun checkInviteResultEvents(userId: Long, credential: Credential): Boolean {
        var isVerify = false
        val groupId = credential.groupId
        val sbtRewards = sbtRewardRepo.getSBTByGroupId(groupId)
        if (sbtRewards.isNotEmpty()) {
            val rewardInfo = sbtRewards[0]
            val userReward = participantRepo.getUserSbtReward(rewardInfo.sbtId, userId, groupId)
            if(userReward != null && userReward.claimType >= 2) {
                isVerify = true
            }

        }
        return isVerify
    }

    companion object {
        private val inviteTPoints = 5000
        private val ambInviteTPoints = 6000
    }

    data class InviteResult(
        val wiseScore: UserWiseScore?,
        val codeCheckedType: InviteCodeCheckedType
    )
}

