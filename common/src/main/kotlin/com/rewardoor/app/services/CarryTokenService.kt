package com.rewardoor.app.services

import com.google.gson.Gson
import com.rewardoor.app.dao.TokenAirDropRepository
import com.rewardoor.model.TokenAirDrop
import com.rewardoor.model.TokenResponse
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import org.springframework.web.reactive.function.BodyInserters
import org.springframework.web.reactive.function.client.ExchangeStrategies
import org.springframework.web.reactive.function.client.WebClient
import java.math.BigDecimal
import java.net.URI

@Service
@Transactional
class CarryTokenService(
    val userService: UserService,
    val tokenAirDropRepo: TokenAirDropRepository
) {
    private val apiKey = "BQYcZ3KaDm7i54LWYWfUkyoMU1CvldGD"
    private val bearValue =
        "Bearer ory_at_GwTvfMjTHVUJ0FsKhSKs-Rlji8TYpwKjL8ceqC7bud0.phmjDPC4DcguRg964Yamc017o1cP9WqYmx0pNciJPMc"
    private val tokenContract = "0x115eC79F1de567eC68B7AE7eDA501b406626478e" // Carry token
    private val snapShotDate = "2024-05-25"
    private final val strategies = ExchangeStrategies.builder()
        .codecs { codecs -> codecs.defaultCodecs().maxInMemorySize(20 * 1024 * 1024) }
        .build()
    private val webClient = WebClient.builder().exchangeStrategies(strategies).build()
    private val bitQueryUri = URI("https://streaming.bitquery.io/graphql")

    //holders with more than 1 CRE
    fun getTokenAirDropInfo() {
        val response = webClient.post()
            .uri(bitQueryUri)
            .header("Content-Type", "application/json")
            .header("X-API-KEY", apiKey)
            .header(
                "Authorization",
                bearValue
            )
            .body(BodyInserters.fromValue("{\"query\":\"query(\$currency: String! \$date: String!) {\\n  EVM(dataset: archive) {\\n    TokenHolders(\\n      tokenSmartContract: \$currency\\n      date: \$date\\n  where: {Balance: {Amount: {gt: \\\"1\\\"}}}\\n orderBy: { descending: Balance_Amount }\\n ) {\\n      Balance {\\n        Amount\\n      }\\n      Holder {\\n        Address\\n      }\\n   }\\n  }\\n}\",\"variables\":\"{\\n  \\\"currency\\\": \\\"$tokenContract\\\",\\n  \\\"date\\\": \\\"$snapShotDate\\\"\\n}\"}"))
            .retrieve()
            .bodyToMono(String::class.java)
            .block()
        val gson = Gson()
        val tokenResponse = gson.fromJson(response, TokenResponse::class.java)
        if (tokenResponse.data.EVM != null) {
            val tokenHolders = tokenResponse.data.EVM.TokenHolders
            var tokenAirDrops = mutableListOf<TokenAirDrop>()
            for (tokenHolder in tokenHolders) {
                val holderAddress = tokenHolder.Holder.Address
                val holderAmount = tokenHolder.Balance.Amount.toDouble()
                val airDropAmount = holderAmount * 0.064259
//                val user = userService.getUserByAddress(holderAddress)
                val tokenAirDrop = TokenAirDrop(
                    tokenName = "GAME",
                    address = holderAddress,
                    userId = 0,
                    amount = airDropAmount.toString()
                )
                tokenAirDrops.add(tokenAirDrop)
            }
            println("token airdrop size " + tokenAirDrops.size)
            tokenAirDropRepo.insertTokenAirDrops(tokenAirDrops)
        }

    }

    fun getUserTokenAllocation(address: String): BigDecimal {
        val airDrop = tokenAirDropRepo.getByAddress(address) ?: return BigDecimal(0)
        return airDrop.amount.toBigDecimal()
    }

}