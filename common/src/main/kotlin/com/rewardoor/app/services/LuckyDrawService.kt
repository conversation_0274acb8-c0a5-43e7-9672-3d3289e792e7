package com.rewardoor.app.services

import com.rewardoor.app.dao.UserLuckyDrawRepository
import com.rewardoor.app.dao.UserTonRepository
import com.rewardoor.model.LuckyDrawResult
import com.rewardoor.model.UserSBTList
import org.springframework.http.HttpStatus
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import org.springframework.web.server.ResponseStatusException
import java.time.Instant
import kotlin.random.Random

@Service
@Transactional
class LuckyDrawService(
    val wiseScoreService: WiseScoreService,
    val luckyDrawRepo: UserLuckyDrawRepository,
    val userService: UserService,
    val userTonRepo: UserTonRepository,
    val telegramService: TelegramService,
    val tgLuckyDrawTimesService: TgInvitationService
) {

    //haven't generate wise score : level = 1, haven't in wise SBT WhiteList : level = 2, already in wise SBT WhiteList : level = 3
    fun getFissionLevel(userId: Long): Int {
        val wiseScore = wiseScoreService.getScoreById(userId)
        val sbtWhiteList = wiseScoreService.getSBTById(userId)
        if (wiseScore == null) {
            return 1
        } else if (sbtWhiteList == null) {
            return 2
        } else {
            return 3
        }
    }

    fun getIsInSBTList(userId: Long): Boolean {
        return wiseScoreService.getSBTById(userId) != null
    }

    fun drawResult(userId: Long): LuckyDrawResult {
        val cnt = tgLuckyDrawTimesService.getLuckyDrawCnt(userId).totalTimes
        if (cnt <= 0) {
            throw ResponseStatusException(HttpStatus.BAD_REQUEST, "No lucky draw times left")
        }
        val fissionLevel = getFissionLevel(userId)
        val tPoints = drawTPoints()
        val luckyDrawResult = LuckyDrawResult(
            userId = userId,
            fissionLevel = fissionLevel,
            createTime = Instant.now(),
            updateTime = Instant.now()
        )
        when (fissionLevel) {
            1 -> {
                if (tPoints == 500) { // qualified to generate wise score
                    luckyDrawResult.isEligibleToGenerateWiseScore = 1
                } else {
                    luckyDrawResult.tPointsNum = tPoints
                }
            }

            2 -> {
                if (tPoints == 500) { //qualified to wise SBT WhiteList
                    luckyDrawResult.isEligibleToGenerateSBT = 1
                    joinSBTWhiteList(userId)
                } else {
                    luckyDrawResult.tPointsNum = tPoints
                }
            }

            3 -> {
                luckyDrawResult.tPointsNum = tPoints
            }
        }
        addUserLuckyDrawResult(luckyDrawResult)
        tgLuckyDrawTimesService.deduceLuckDrawCnt(userId)
        return luckyDrawResult
    }

    fun drawTPoints(): Int {
        val chance = Random.nextInt(100)
        return when {
            chance < 30 -> 0      // 30% chance of not winning
            chance < 40 -> 10     // 10% chance of getting 10 TPoints
            chance < 50 -> 25     // 10% chance of getting 25 TPoints
            chance < 60 -> 50     // 10% chance of getting 50 TPoints
            chance < 70 -> 100    // 10% chance of getting 100 TPoints
            else -> 500           // 30% chance of getting 500 TPoints ||  generate wise score  || wise SBT WhiteList
        }
    }

    fun addUserLuckyDrawResult(luckyDrawResult: LuckyDrawResult): Int {
        return luckyDrawRepo.createUserLuckyDraw(luckyDrawResult)
    }

    fun getUserTPoints(userId: Long): Int? {
        return luckyDrawRepo.getUserTPoints(userId)
    }

    fun getAllUserIds(): List<Long> {
        return luckyDrawRepo.getAllTPointsUserId()
    }

    fun getAllUserTPoints(): Map<Long, Int> {
        return luckyDrawRepo.getTotalPointsForAllUsers()
    }

    fun joinSBTWhiteList(userId: Long): Int {
        val user = userService.getUserById(userId)!!
        val evmUser = userService.getEvmUserById(userId)
        val tonUser = userTonRepo.getTonUserWalletByUserId(userId)
        val tgUser = telegramService.getUserInfo(userId)
        val resultAddress = when {
            tonUser != null -> tonUser.tonWallet!!
            evmUser != null -> evmUser.evmWallet!!
            else -> tgUser?.username
        }
        val resultAddressType = when {
            tonUser != null -> 1
            evmUser != null -> 0
            else -> 2
        }
        val userSBTWhiteList = UserSBTList(
            userId = userId,
            address = resultAddress,
            addressType = resultAddressType,
            avatar = user.displayAvatar,
            updateTime = Instant.now()
        )
        return wiseScoreService.addUserSBTWhiteList(userSBTWhiteList)
    }

}