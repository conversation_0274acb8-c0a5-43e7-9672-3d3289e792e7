package com.rewardoor.app.services

import com.rewardoor.app.dao.DummyIdRepository
import com.rewardoor.app.dao.ParticipantRepository
import com.rewardoor.app.dao.UserNFTRepository
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
@Transactional
class UserNFTService(
    val userNFTRepository: UserNFTRepository,
    val campaignService: CampaignService,
    val nftService: NFTService,
    val participantRepos: ParticipantRepository,
    val dummyIdRepo: DummyIdRepository
) {
    fun addUserNFT(
        userId: Long,
        nftId: Long,
        groupId: Long,
        dummyId: Long,
        signature: String,
    ) {
        userNFTRepository.addUserNFT(userId, nftId, dummyId, signature, "")
    }

    fun updateUserNFTTx(nftId: Long, userId: Long, groupId: Long, dummyId: Long, tx: String) {
        campaignService.addUserReward(1, nftId, userId, groupId, 4)
        userNFTRepository.updateUserNFTTx(dummyId, tx)
    }

    //判断已claim的nft数量 是否 等于nft设定的mintCap
    fun isNftClaimAble(nftId: Long, groupId: Long): Boolean {
        val nft = nftService.getNFTById(nftId)!!
        if (nft.unlimited) {
            return true
        }
        val mintCap = nft.mintCap
        val claimedNftNum = participantRepos.getClaimedUserCount(nftId, groupId, 4)
        return claimedNftNum < mintCap
    }

    fun getDummyId(userId: Long, groupId: Long): Long {
        return dummyIdRepo.getDummyId(userId, groupId)
    }
}