package com.rewardoor.app.services

import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.rewardoor.app.dao.*
import org.json.JSONArray
import org.json.JSONObject
import java.math.BigDecimal
import org.springframework.beans.factory.annotation.Value
import org.springframework.core.env.Environment
import org.springframework.data.redis.core.StringRedisTemplate
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import org.springframework.web.reactive.function.client.ExchangeStrategies
import org.springframework.web.reactive.function.client.WebClient


data class VerificationResult(
    val isVerified: <PERSON>olean,
    val totalTvl: Double
)
typealias EventVerifier = (JSONArray, String) -> VerificationResult


@Service
@Transactional
class TaskVerifyService(
    val participantRepo: ParticipantRepository,
    val telegramService: TelegramService,
    val userService: UserService,
    val redisTemplate: StringRedisTemplate,
    val env: Environment,
    @Value("\${tg.x-api-key}") val xApiKey: String,
//    @Value("\${etherScan.api-key}") val ethApiKey: String,
    @Value("\${twitter.ut_key}") val utoolKey: String,
    @Value("\${tg.ton-x-api-key}") val tonXApiKey: String,
    @Value("\${tg.ton-x-partner-id}") val tonXPartnerId: String,
    @Value("\${coinMarket.api_key}") val coinMarketApiKey: String,
    private val credentialRepository: CredentialRepository,
) {
    private val log = mu.KotlinLogging.logger { }
    private final val strategies = ExchangeStrategies.builder()
        .codecs { codecs -> codecs.defaultCodecs().maxInMemorySize(20 * 1024 * 1024) }
        .build()
    private val webClient = WebClient.builder().exchangeStrategies(strategies).build()
    private val mapper = jacksonObjectMapper()
    private val tonTokenIds =
        "30846,28704,31061,30117,9103,29704,31698,27894,25672,31697,28850,29705,30116,31252,27311,30118,29069,31267,23156,32106,28792"  // crypto IDs, id from https://api.coinmarketcap.com/data-api/v3/cryptocurrency/market-pairs/latest?slug=bemo-staked-ton&start=1&limit=10&category=spot&centerType=all&sort=cmc_rank_advanced&direction=desc&spotUntracked=true
    private val tonConsoleBearer = "AFXEZ3ACHWCQPGIAAAACDBX3ACNADH4DWOIU3DOWOQMPYF7M5QIP5ZZFE5WI227KRIKIQ2Y"

    data class Activity(val id: String, val EQ: String, val UQ: String)

    // Check any coin hold
    fun checkJettonCoinHold(
        userId: Long,
        coinAddress: String,
        symbolName: String,
        jettonMinimumAmount: Long? = null
    ): Boolean {
        val tonWallet = userService.getTonWalletByUserId(userId).tonWallet ?: return false
        try {
            val response = webClient
                .get()
                .uri(
                    "https://tonapi.io/v2/accounts/${tonWallet}/jettons"
                )
                .header("Authorization", "Bearer $tonConsoleBearer")
                .retrieve()
                .bodyToMono(String::class.java)
                .block();
            if (!response.isNullOrEmpty()) {
                val jsonObject = JSONObject(response)
                val balancesArray = jsonObject.getJSONArray("balances")
                for (i in 0 until balancesArray.length()) {
                    val balanceObject = balancesArray.getJSONObject(i)
                    val jettonObject = balanceObject.getJSONObject("jetton")
                    val address = jettonObject.getString("address")
                    val decimals = jettonObject.getInt("decimals")
                    if (address == coinAddress) {
                        val balance = balanceObject.getString("balance").toBigInteger()
                        val decimalsCount = BigDecimal.TEN.pow(decimals)
                        val jettonMinimumAmountAfterDecimals = jettonMinimumAmount?.times(decimalsCount.toLong())
                        return when {
                            jettonMinimumAmount == null || jettonMinimumAmount == 0L -> {
                                println("user is ${symbolName} holder，wallet = $tonWallet，balance = $balance")
                                true
                            }


                            else -> balance.toLong() > (jettonMinimumAmountAfterDecimals!!)
                        }
                    }
                }
            }
            return false
        } catch (e: Exception) {
            println(" check ${symbolName} error $e")
            return false
        }
    }

    // labelType: 69 HOLDING_JETTON
    fun checkHoldCustomJettonEvents(userId: Long, credentialId: Long, jsonObject: JSONObject): Boolean {
        val jettonSymbolName = jsonObject.getString("jettonSymbolName")
        val jettonAddress = jsonObject.getString("jettonAddress")
        val jettonMinimumAmount = jsonObject.optLong("jettonMinimumAmount")
        val isVerify = checkJettonCoinHold(userId, jettonAddress, jettonSymbolName, jettonMinimumAmount)
        return isVerify
    }

    private fun fetchTonAPIEvents(
        uri: String
    ): JSONArray {
        val response = webClient
            .get()
            .uri(uri)
            .header("Authorization", "Bearer $tonConsoleBearer")
            .retrieve()
            .bodyToMono(String::class.java)
            .block()
        return JSONObject(response).getJSONArray("events")
    }

    private fun checkCommonTaskForMulti(
        userId: Long,
        credentialId: Long,
        contractAddress: String,
        eventVerifier: EventVerifier,
        eventType: String,
        startDate: Long? = null,
        endDate: Long? = null,
        maxAttempts: Int = 1,
    ): Boolean {
        val tonWallet = userService.getTonWalletByUserId(userId).tonWallet
        val eventsLimit = 100

        var totalTonTVL = 0.0
        var foundVerifiedEvent = false

        try {
            var currentEndDate = endDate
            var attemptCount = 0

            while (attemptCount < maxAttempts) {
                val baseUrl = "https://tonapi.io/v2/accounts/${tonWallet}/events?limit=${eventsLimit}&initiator=true"
                val url = buildString {
                    append(baseUrl)
                    if (startDate != null) {
                        append("&start_date=$startDate")
                    }
                    if (currentEndDate != null) {
                        append("&end_date=$currentEndDate")
                    }
                }

                val events = fetchTonAPIEvents(url)

                if (events.length() == 0) {
                    break
                }

                val verificationResult = eventVerifier(events, contractAddress)
                if (verificationResult.isVerified) {
                    foundVerifiedEvent = true
                    if (verificationResult.totalTvl > 0) {
                        totalTonTVL += verificationResult.totalTvl
                    }
                    break
                }

                val firstEventTimestamp = events.getJSONObject(events.length() - 1).getLong("timestamp")
                if (endDate != null && firstEventTimestamp >= endDate) {
                    break
                }
                currentEndDate = firstEventTimestamp
                attemptCount++
            }
        } catch (e: Exception) {
            println("check user $eventType events error : $e")
        }

        if (foundVerifiedEvent && totalTonTVL > 0) {
            participantRepo.updateCredentialTVLAmount(userId, credentialId, totalTonTVL.toLong())
        }
        return foundVerifiedEvent
    }

    // DEMO ======
    private fun checkEventsForDemo(events: JSONArray, hipoAddess: String): VerificationResult {
        var totalTonTVL = 0.0

        for (i in 0 until events.length()) {
            val event = events.getJSONObject(i)
            val actions = event.getJSONArray("actions")

            for (j in 0 until actions.length()) {
                val action = actions.getJSONObject(j)
                if (action.getString("type") == "SmartContractExec" && action.optString("status") == "ok") {
                    val smartContractExec = action.getJSONObject("SmartContractExec")
                    val contract = smartContractExec.getJSONObject("contract")
                    val tonAttached = smartContractExec.optLong("ton_attached")
                    if (contract.optString("name") == "Hipo Treasury" && contract.optString("address") == hipoAddess) {
                        if (tonAttached > 0L) {
                            totalTonTVL += tonAttached
                        }
                        return VerificationResult(
                            isVerified = true,
                            totalTvl = totalTonTVL
                        )
                    }
                }
            }
        }
        return VerificationResult(isVerified = false, totalTvl = 0.0)
    }

    fun checkDemoEvents(userId: Long, credentialId: Long): Boolean {
        return checkCommonTaskForMulti(
            userId = userId,
            credentialId = credentialId,
            contractAddress = "0:8bc991cfe177bc7e9721433efa3befd199485a55cffd040a06c89af026b71bcf",
            eventVerifier = ::checkEventsForDemo,
            eventType = "Demo stake ton",
            startDate = 1,
            endDate = 2,
        )
    }
    // DEMO ======

    // LabelType 82 BEET_ROOT_DEFI_USDT_TRANSFER
    private fun checkEventsForBeetrootUsdtTransfer(events: JSONArray, contractAddress: String): VerificationResult {

        for (i in 0 until events.length()) {
            val event = events.getJSONObject(i)
            val actions = event.getJSONArray("actions")

            for (j in 0 until actions.length()) {
                val action = actions.getJSONObject(j)
                if (action.getString("type") == "JettonTransfer" && action.optString("status") == "ok") {
                    val jettonTransfer = action.getJSONObject("JettonTransfer")
                    val recipient = jettonTransfer.getJSONObject("recipient")
                    val jetton = jettonTransfer.getJSONObject("jetton")
                    val jettonSymbol = jetton.getString("symbol")
                    if (recipient.optString("address") == contractAddress && jettonSymbol == "USD₮") {
                        return VerificationResult(
                            isVerified = true,
                            totalTvl = 0.0
                        )
                    }
                }
            }
        }
        return VerificationResult(isVerified = false, totalTvl = 0.0)
    }

    fun checkBeetrootUsdtEvent(userId: Long, credentialId: Long): Boolean {
        return if (checkCommonTaskForMulti(
                userId = userId,
                credentialId = credentialId,
                contractAddress = "0:c2f0c639b58e6b3cce8a145c73e7c7cc5044baa92b05c62fcf6da8a0d50b8edc",
                eventVerifier = ::checkEventsForBeetrootUsdtTransfer,
                eventType = "BEETROOT USDT TRANSFER",
                startDate = null,
                endDate = null,
                maxAttempts = 1,
            )
        ) {
            true
        } else {
            false
        }
    }


    // LabelType 83 AKEDO_GAME_TRANSFER
    private fun checkEventsForAkedoGameTransfer(events: JSONArray, contractAddress: String): VerificationResult {

        for (i in 0 until events.length()) {
            val event = events.getJSONObject(i)
            val actions = event.getJSONArray("actions")

            for (j in 0 until actions.length()) {
                val action = actions.getJSONObject(j)
                if (action.getString("type") == "TonTransfer" && action.optString("status") == "ok") {
                    val tonTransfer = action.getJSONObject("TonTransfer")
                    val recipient = tonTransfer.getJSONObject("recipient")
                    val amount = tonTransfer.getLong("amount")
                    if (recipient.optString("address") == contractAddress && amount >= 2990000000) {
                        return VerificationResult(
                            isVerified = true,
                            totalTvl = 0.0
                        )
                    }
                }
            }
        }
        return VerificationResult(isVerified = false, totalTvl = 0.0)
    }

    // LabelType 84 ANY_CRAFT_TRADE
    private fun checkEventsForAnyCraftTrade(events: JSONArray, contractAddress: String): VerificationResult {

        for (i in 0 until events.length()) {
            val event = events.getJSONObject(i)
            val actions = event.getJSONArray("actions")

            for (j in 0 until actions.length()) {
                val action = actions.getJSONObject(j)
                if (action.getString("type") == "TonTransfer" && action.optString("status") == "ok") {
                    val tonTransfer = action.getJSONObject("TonTransfer")
                    val recipient = tonTransfer.getJSONObject("recipient")
                    val amount = tonTransfer.getLong("amount")
                    if (recipient.optString("address") == contractAddress) {
                        return VerificationResult(
                            isVerified = true,
                            totalTvl = 0.0
                        )
                    }
                }
            }
        }
        return VerificationResult(isVerified = false, totalTvl = 0.0)
    }

    // LabelType 85 BAZA_TRADE
    private fun checkEventsForBazaTrade(events: JSONArray, contractAddress: String): VerificationResult {

        for (i in 0 until events.length()) {
            val event = events.getJSONObject(i)
            val actions = event.getJSONArray("actions")

            for (j in 0 until actions.length()) {
                val action = actions.getJSONObject(j)
                if (action.getString("type") == "TonTransfer" && action.optString("status") == "ok") {
                    val tonTransfer = action.getJSONObject("TonTransfer")
                    val recipient = tonTransfer.getJSONObject("recipient")
                    val amount = tonTransfer.getLong("amount")
                    if (recipient.optString("address") == contractAddress) {
                        return VerificationResult(
                            isVerified = true,
                            totalTvl = 0.0
                        )
                    }
                }
            }
        }
        return VerificationResult(isVerified = false, totalTvl = 0.0)
    }



    fun checkAkedoGameEvent(userId: Long, credentialId: Long): Boolean {
        return if (checkCommonTaskForMulti(
                userId = userId,
                credentialId = credentialId,
                contractAddress = "0:4f7d4390b8430c5f889dacf5804e543520a99b3c579898559c26e9380ae5c78d",
                eventVerifier = ::checkEventsForAkedoGameTransfer,
                eventType = "Akedo Game Transfer",
                startDate = null,
                endDate = null,
                maxAttempts = 1,
            )
        ) {
            true
        } else {
            false
        }
    }

    fun checkAnyCraftEvent(userId: Long, credentialId: Long): Boolean {
        return if (checkCommonTaskForMulti(
                userId = userId,
                credentialId = credentialId,
                contractAddress = "0:2748a758b466901905ae5047ea7e095b7f6b57fc9827cc159d0814ef1af00ef6",
                eventVerifier = ::checkEventsForAnyCraftTrade,
                eventType = "Any Craft Trade",
                startDate = null,
                endDate = null,
                maxAttempts = 1,
            )
        ) {
            true
        } else {
            false
        }
    }

    fun checkBAZAEvent(userId: Long, credentialId: Long): Boolean {
        return if (checkCommonTaskForMulti(
                userId = userId,
                credentialId = credentialId,
                contractAddress = "0:f1f510cd32e96acd9ddcfbe53afd3ba7cba1c5639492d4667a58846c06af88fa",
                eventVerifier = ::checkEventsForBazaTrade,
                eventType = "Any Craft Trade",
                startDate = null,
                endDate = null,
                maxAttempts = 1,
            )
        ) {
            true
        } else {
            false
        }
    }
}
