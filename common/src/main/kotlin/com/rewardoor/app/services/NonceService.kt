package com.rewardoor.app.services

import org.springframework.data.redis.core.StringRedisTemplate
import org.springframework.stereotype.Service

@Service
class NonceService(private val redis: StringRedisTemplate) {
    fun addNonce(address: String, nonce: String) {
        redis.opsForValue().set("nonce:$address", nonce)
    }

    fun getAndInvalidateNonce(address: String): String? {
        return redis.opsForValue().getAndDelete("nonce:$address")
    }
}