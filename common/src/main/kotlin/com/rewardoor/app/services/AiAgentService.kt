package com.rewardoor.app.services

import com.fasterxml.jackson.databind.ObjectMapper
import com.rewardoor.IdGenerator
import com.rewardoor.app.dao.AiAgentRepository
import com.rewardoor.model.AiAgent
import com.rewardoor.model.DailyUserCount
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
class AiAgentService(val idGenerator: IdGenerator, val aiAgentRepo: AiAgentRepository) {

    private val mapper = ObjectMapper()
    private val logger = mu.KotlinLogging.logger {}

    @Transactional
    fun createAiAgent(aiAgent: AiAgent): AiAgent {
        val agentId = idGenerator.getNewId()
        aiAgent.agentId = agentId

        try {
            aiAgentRepo.createAiAgent(aiAgent)
            val tgClientConfig = aiAgent.tgClientConfig
            if (tgClientConfig != null) {
                val tgClientConfigId = idGenerator.getNewId()
                tgClientConfig.tgClientConfigId = tgClientConfigId
                tgClientConfig.agentId = agentId
                aiAgentRepo.createTgClientConfig(tgClientConfig)
                aiAgent.tgClientConfig = tgClientConfig
            }

        } catch (e: Exception) {
            logger.error("createCampaignNew error", e)
            throw e
        }
        return aiAgent
    }

    @Transactional
    fun getAiAgent(agentId: Long): AiAgent? {
        val aiAgent = aiAgentRepo.getAiAgentByAgentId(agentId)
        val tgClientConfig = aiAgentRepo.getTgClientConfigByAgentId(agentId)
        if (aiAgent != null && tgClientConfig != null) {
            aiAgent.tgClientConfig = tgClientConfig
            aiAgent.totalMemoriesCnt = 1048
            aiAgent.totalUsersCnt = 480
            val dailyUserList = listOf(
                DailyUserCount("2023-02-22", 35),
                DailyUserCount("2023-02-23", 75),
                DailyUserCount("2023-02-24", 80),
                DailyUserCount("2025-02-25", 101)
            )
            aiAgent.dailyUserCnt = dailyUserList
        }
        return aiAgent
    }

    @Transactional
    fun updateAiAgent(aiAgent: AiAgent): AiAgent? {
        val agentId = aiAgent.agentId
        val oldAgent = getAiAgent(agentId)
        if (oldAgent == null) {
            return null
        }
        aiAgentRepo.updateAiAgent(aiAgent)
        val tgClientConfig = oldAgent.tgClientConfig
        val oldTgClientConfig = aiAgentRepo.getTgClientConfigByAgentId(agentId)
        if (oldTgClientConfig != null && tgClientConfig != null) {
            aiAgentRepo.updateTgClientConfig(tgClientConfig)
        }
        return getAiAgent(agentId)
    }

    @Transactional
    fun getAiAgentList(): List<AiAgent>? {
        // 获取 aiAgentList 并转换为可变列表
        val aiAgentList = aiAgentRepo.getAiAgentList()?.toMutableList()
        if (aiAgentList != null) {
            for (i in aiAgentList.indices) {
                val agentId = aiAgentList[i].agentId
                val updatedAgent = getAiAgent(agentId)
                if (updatedAgent != null) {
                    aiAgentList[i] = updatedAgent
                }
            }
        }
        return aiAgentList
    }
}