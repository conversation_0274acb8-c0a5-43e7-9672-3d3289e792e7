package com.rewardoor.app.services

import com.rewardoor.app.dao.TelegramInvitationRepository
import com.rewardoor.app.dao.TgLuckyDrawTimesRepository
import com.rewardoor.app.dao.UserLuckyDrawRepository
import com.rewardoor.model.LuckyDrawResult
import com.rewardoor.model.TelegramInvitee
import com.rewardoor.model.TelegramLuckyInfo
import org.springframework.data.redis.core.StringRedisTemplate
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.time.Duration
import java.time.Instant
import java.time.LocalDateTime
import java.time.ZoneOffset
import java.time.format.DateTimeFormatter
import java.time.temporal.ChronoUnit

@Service
@Transactional
class TgInvitationService(
    private val invitationRepo: TelegramInvitationRepository,
    private val luckyDrawRepo: TgLuckyDrawTimesRepository,
    private val tgUserService: TelegramService,
    private val redisTemplate: StringRedisTemplate,
    private val userLuckyDrawRepo: UserLuckyDrawRepository
) {
    private val dailyInitialTimes = 5
    private val dailyTotalMax = 10
    private val dailyBonusStep = 1
    private val nextDistTime = Duration.ofMinutes(10)
    private val inviteBonus = 5
    private val premiumInviteBonus = 15
    private fun dailyKey(userId: Long): String {
        val date = LocalDateTime.now().format(DateTimeFormatter.ofPattern("YYYY-MM-dd"))
        return "daily_add_times_${date}_$userId"
    }

    private fun todayStr(): String {
        return Instant.now().atOffset(ZoneOffset.UTC).format(DateTimeFormatter.ofPattern("YYYY-MM-dd"))
    }

    fun addInvitation(
        userId: Long, inviterTgId: Long, inviteeTgId: Long,
        inviteeFirst: String, inviteeLast: String, username: String, premium: Boolean
    ): Int {
        if (luckyDrawRepo.getByTgId(inviteeTgId) != null) return 0

        val iCnt = invitationRepo.addInvitation(
            userId, inviterTgId,
            inviteeTgId, inviteeFirst, inviteeLast, username, premium
        )
        if (iCnt > 0) {
            addInitialCountOrIncreaseOfInvite(userId, inviterTgId, if(premium) premiumInviteBonus else inviteBonus)
            val luckyDrawResult = LuckyDrawResult(
                userId = userId,
                fissionLevel = -1, //invite friend
                tPointsNum = if (premium) 2500 else 500,
                isEligibleToGenerateWiseScore = 0,
                isEligibleToGenerateSBT = 0,
                createTime = Instant.now(),
                updateTime = Instant.now()
            )
            userLuckyDrawRepo.createUserLuckyDraw(luckyDrawResult)
        }
        return iCnt
    }

    fun addInitialCountOrIncreaseOfInvite(userId: Long, tgUserId: Long, count: Int){
        val iCount = luckyDrawRepo.addInitialLuckyDrawCnt(userId, tgUserId, count)
        if (iCount <= 0) {
            luckyDrawRepo.addLuckDrawCnt(userId, count)
        }
    }

    fun getLuckyDrawCnt(userId: Long): TelegramLuckyInfo {
        val current = luckyDrawRepo.getLuckyDrawTimesModel(userId)
        val persistentTimes = current?.times?:0
        val daily = luckyDrawRepo.getDailyTimeItem(userId, todayStr())
        if (daily == null) {
            luckyDrawRepo.addDailyInitialCount(userId, todayStr(), dailyInitialTimes)
            return TelegramLuckyInfo(
                dailyInitialTimes + persistentTimes,
                Instant.now().plus(Duration.ofDays(1)).truncatedTo(ChronoUnit.DAYS).toEpochMilli(),
                dailyInitialTimes,
                dailyTotalMax - dailyInitialTimes,
                dailyInitialTimes,
                dailyInitialTimes,
                dailyBonusStep,
                dailyTotalMax - dailyInitialTimes
            )
        } else {
            var todayTimes = daily.times
            var next = daily.nextAddAt
            if (daily.usedTimes < dailyTotalMax && daily.times == 0 && daily.nextAddAt.isBefore(Instant.now())) {
                next = Instant.now().plus(Duration.ofDays(1)).truncatedTo(ChronoUnit.DAYS)
                luckyDrawRepo.updateDailyItemWithNext(userId, todayStr(),
                    dailyBonusStep, daily.usedTimes, next)
                todayTimes += dailyBonusStep
            }
            val todayTotal = daily.usedTimes + daily.times
            val initialRemains = dailyInitialTimes - daily.usedTimes

            val bonusUnused = if (initialRemains <= 0) daily.times else 0
            return TelegramLuckyInfo(todayTimes + persistentTimes, next.toEpochMilli(),
                todayTotal, dailyTotalMax - todayTotal,
                dailyInitialTimes,
                if (initialRemains > 0) initialRemains else 0,
                dailyTotalMax - dailyInitialTimes, dailyBonusStep, bonusUnused)
        }
    }

    fun getTodayAdded(userId: Long): Int {
        val key = dailyKey(userId)
        val cnt = redisTemplate.opsForValue().get(key)
        return cnt?.toInt() ?: 0
    }

    fun setTodayAddTimes(userId: Long, count: Int) {
        val key = dailyKey(userId)
        redisTemplate.opsForValue().set(key, count.toString(), 1, java.util.concurrent.TimeUnit.DAYS)
    }

    fun addInitialLuckyDrawCnt(userId: Long, count: Int): Int {
        val tgUserId = tgUserService.getUserInfo(userId)?.tgId!!
        return luckyDrawRepo.addInitialLuckyDrawCnt(userId, tgUserId, count)
    }

    fun deduceLuckDrawCnt(userId: Long) {
        val daily = luckyDrawRepo.getDailyTimeItem(userId, todayStr())
        if (daily == null) {
            luckyDrawRepo.addDailyInitialCount(userId, todayStr(), dailyInitialTimes)
        }
        val dailyItem = luckyDrawRepo.getDailyTimeWithLock(userId, todayStr())!!
        if (dailyItem.times >= 1) {
            val newCnt = dailyItem.times - 1
            val used = dailyItem.usedTimes + 1
            if (newCnt <= 0) {
                val next = if (used < dailyTotalMax) {
                    Instant.now().plus(nextDistTime)
                } else {
                    Instant.now().plus(Duration.ofDays(1)).truncatedTo(ChronoUnit.DAYS)
                }
                luckyDrawRepo.updateDailyItemWithNext(userId, todayStr(), newCnt, used, next)
            } else {
                luckyDrawRepo.updateDailyItem(userId, todayStr(), newCnt, used)
            }
        } else {
            luckyDrawRepo.addLuckDrawCnt(userId, -1)
        }
    }

    fun getInviteCnt(userId: Long): Long {
        return invitationRepo.getInviteCnt(userId)
    }

    fun getInviteCntWithPremium(userId: Long, premium: Boolean): Long {
        return invitationRepo.getInviteCntWithPremium(userId, premium)
    }

    fun getInviteeDetail(userId: Long, limit: Int): List<TelegramInvitee> {
        return invitationRepo.getInvitee(userId, limit)
    }

    fun getInviteeDetailWithPremium(userId: Long, limit: Int, premium: Boolean): List<TelegramInvitee> {
        return invitationRepo.getInviteeWithPremium(userId, limit, premium)
    }
}