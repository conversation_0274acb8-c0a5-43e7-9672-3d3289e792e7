package com.rewardoor.app.services

import com.google.gson.Gson
import org.json.JSONObject
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import org.springframework.web.reactive.function.client.ExchangeStrategies
import org.springframework.web.reactive.function.client.WebClient
import java.net.URI
import com.rewardoor.model.CtaLinkResult
import reactor.core.publisher.Mono
import java.text.DecimalFormat
import java.net.URLEncoder
import java.nio.charset.StandardCharsets

@Service
@Transactional
class CustomCredentialService {

    private final val strategies = ExchangeStrategies.builder()
        .codecs { codecs -> codecs.defaultCodecs().maxInMemorySize(20 * 1024 * 1024) }
        .build()
    private val webClient = WebClient.builder().exchangeStrategies(strategies).build()

    data class VerifyApiResponse(
        val code: Int,
        val status: Any?,
        val message: String,
        val isVerified: Boolean
    )

    data class CtaLinkApiResponse(
        val code: Int,
        val status: String,
        val message: String,
        val link: String
    )

    fun verifyCustomCredentialByApi(apiLink: String, credentialId: Long, condition: Int, tonAddress: String, telegramId: Long, ): Boolean {

        val params = if (condition == 1) {
            mapOf(
                "credentialId" to credentialId.toString(),
                "tonAddress" to tonAddress
            )
        } else if (condition == 2) {
            mapOf(
                "telegram_id" to telegramId.toString()
            )
        } else {
            mapOf(
                "credentialId" to credentialId.toString()
            )
        }

        val url = buildUrl(apiLink, params)

        try {
            val requestSpec = webClient.get().uri(url)

            // hard code for Booms IO
            if (apiLink.contains("api.booms.io")) {
                requestSpec.header("API-Key", "66c69a6e-b4d2-46a9-aed6-56bb3d173032")
            }
            if (apiLink.contains("b2b.hubz.io")) {
                requestSpec.header("x-api-key", "PrH36ROel03gb3iSEEQ712oVhy8Cf19e4ABXrGyk")
            }

            val response = requestSpec
                .retrieve()
                .bodyToMono(String::class.java)
                .block();
            val apiResponse = Gson().fromJson(response, VerifyApiResponse::class.java)

            // Special handling for api.booms.io
            if (apiLink.contains("api.booms.io")) {
                // 处理 status 为 String 或 Boolean 的情况
                return when (apiResponse.status) {
                    is Boolean -> apiResponse.status
                    is String -> apiResponse.status == "true"
                    else -> false
                }
            }

            val code = apiResponse.code
            val status = apiResponse.status
            val isVerified = apiResponse.isVerified
            if (code == 200 && status == "success") {
                return isVerified
            } else {
                println("apiResponse is not valid : $url + ${apiResponse.code} + ${apiResponse.status} + ${apiResponse.message} + ${apiResponse.isVerified}")
            }
        } catch (e: Exception) {
            println("verify custom api error $e")
        }
        return false
    }


    fun getCtaLinkByApi(apiLink: String): CtaLinkResult {
//        val params = mapOf(
//            "credentialId" to credentialId.toString()
//        )
//        val url = buildUrl(apiLink, params)
        val url = apiLink
        try {
            val response = webClient
                .get()
                .uri(url)
                .retrieve()
                .bodyToMono(String::class.java)
                .block();
            val apiResponse = Gson().fromJson(response, CtaLinkApiResponse::class.java)
            val code = apiResponse.code
            val status = apiResponse.status
            if (code == 200 && status == "success") {
                return CtaLinkResult(link = apiResponse.link)
            } else {
                val errorMsg =
                    "get CTA link api response is not valid: $url + code:${apiResponse.code}、status:${apiResponse.status}、message:${apiResponse.message}、link:${apiResponse.link};"
                println(errorMsg)
                return CtaLinkResult(errorMessage = errorMsg)
            }
        } catch (e: Exception) {
            val errorMsg = "get CTA link api error $e"
            println(errorMsg)
            return CtaLinkResult(errorMessage = errorMsg)
        }
    }

    // For Backend
    fun checkCustomCredentialApi(apiVerifyLink: String, condition: Number?): CtaLinkResult {
        try {
            var url = ""
            if(condition == 1) {
                url = if (!apiVerifyLink.contains("tonAddress=")) {
                    val params = mapOf(
                        "tonAddress" to "UQCxRWiWTz8QSlLme1RnI6to0ZZcnV1ctdsGf3EyP7iY0Rdo" // demo kelly's ton address
                    )
                    buildUrl(apiVerifyLink, params)
                } else {
                    apiVerifyLink
                }
            } else {
                url = if (!apiVerifyLink.contains("telegram_id=")) {
                    val params = mapOf(
                        "telegram_id" to "7049598999" // demo kelly's tgid
                    )
                    buildUrl(apiVerifyLink, params)
                } else {
                    apiVerifyLink
                }
            }

            val requestSpec = webClient.get().uri(url)

            // hard code for Booms IO
            if (apiVerifyLink.contains("api.booms.io")) {
                requestSpec.header("API-Key", "66c69a6e-b4d2-46a9-aed6-56bb3d173032")
            }
            if (apiVerifyLink.contains("b2b.hubz.io")) {
                requestSpec.header("x-api-key", "PrH36ROel03gb3iSEEQ712oVhy8Cf19e4ABXrGyk")
            }

            val response = requestSpec
                .retrieve()
                .onStatus({ status -> status.isError }) { response ->
                    Mono.empty()
                }
                .bodyToMono(String::class.java)
                .block();
            val jsonObject = JSONObject(response)

            // hard code for booms io
            if(apiVerifyLink.contains("api.booms.io")) {
                if(jsonObject.has("status")) {
                    return CtaLinkResult(
                        isAPILinkCanVerify = true
                    )
                }
            }

            val requiredFields = listOf("status", "message", "code", "isVerified")
            val missingFields = requiredFields.filter { !jsonObject.has(it) }

            if (missingFields.isNotEmpty()) {
                return CtaLinkResult("Missing fields: ${missingFields.joinToString(", ")}")
            }

            val status = jsonObject.getString("status")
            val code = jsonObject.getInt("code")
            val message = jsonObject.getString("message")
            val isVerifiedString = jsonObject.get("isVerified").toString()

            if ((isVerifiedString == "true" || isVerifiedString == "false")) {
                return CtaLinkResult(isAPILinkCanVerify = true)
            } else {
                return CtaLinkResult(
                    isAPILinkCanVerify = false,
                    errorMessage = "verify api response is not valid:$url、code:${code}、status:${status}、msg:${message}、isVerified:${isVerifiedString}"
                )
            }
        } catch (e: Exception) {
            val errorMsg = "check verify custom credential api error $e"
            return CtaLinkResult(errorMessage = errorMsg)
        }

    }

    fun buildUrl(apiLink: String, params: Map<String, String>): String {
        val urlWithScheme = if (!apiLink.startsWith("http://") && !apiLink.startsWith("https://")) {
            "https://$apiLink"
        } else {
            apiLink
        }
        println(urlWithScheme)

        val uri = URI(urlWithScheme)
        val baseUrl = StringBuilder()

        baseUrl.append(uri.scheme).append("://").append(uri.host)

        baseUrl.append(uri.path.ifEmpty { "/" })

        val queryParams = mutableListOf<String>()

        uri.query?.split("&")?.forEach {
            val param = it.trim()
            if (param.isNotEmpty()) {
                queryParams.add(param)
            }
        }

        params.forEach { (key, value) ->
            val encodedKey = URLEncoder.encode(key, StandardCharsets.UTF_8)
            val encodedValue = URLEncoder.encode(value, StandardCharsets.UTF_8)
            queryParams.add("$encodedKey=$encodedValue")
        }

        if (queryParams.isNotEmpty()) {
            baseUrl.append("?").append(queryParams.joinToString("&"))
        }

        return baseUrl.toString()
    }
}