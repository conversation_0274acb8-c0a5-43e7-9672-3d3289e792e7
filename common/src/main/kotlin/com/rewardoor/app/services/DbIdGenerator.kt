package com.rewardoor.app.services

import com.rewardoor.IdGenerator
import com.rewardoor.app.dao.SequenceRepository
import org.springframework.stereotype.Component
import java.time.LocalDate
import java.time.ZoneId

@Component
class DbIdGenerator(val sequenceRepository: SequenceRepository): IdGenerator {
    companion object {
        val START = LocalDate.of(2022, 12, 1).atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli()
    }
    override fun getNewId(): Long {
        val prefix = (System.currentTimeMillis() - START) / 1000
        val id = sequenceRepository.getNewId();
        return "${prefix}${"%04d".format(id)}".toLong()
    }
}
