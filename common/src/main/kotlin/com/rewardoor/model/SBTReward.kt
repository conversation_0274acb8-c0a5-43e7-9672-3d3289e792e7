package com.rewardoor.model

import java.time.Instant

class SBTReward(
    var sbtId: Long = 0,
    var name: String = "",
    var category: Int = 0, // 0 - default , 1 - TBook OG SBTs, 2 - DeFi SBTs, 3 - Meme SBTs, 4 - Game SBTs, 5-premium SBTs, 6 - ton pioneer SBTs
    var activityId: Int = 0, //sbt activity id
    var activityUrl: String = "", // sbt activity url
    var picUrl: String = "", // media file url
    var number: Long = 0,
    var methodType: Int = 0, // 0 - default, 1 - FCFS, 2 - lucky draw
    var unlimited: Boolean = true, //unlimited
    var rewardNum: Long = 0,
    var projectId: Long,
    var creatorId: Long = 0,
    var groupId: Long = 0,
    var claimedType: Int = 0, // 0 - 任务未完成认证 ， 1 - 已完成还没生成unique link，2 - eligible, 3 - minting, 4 - claimed ， 5 - missed未获取
    var claimedDate: Instant? = null,
    var campaignId: Long = 0,
    var campaignName: String = "",
    var uniqueLink: String? = ""
) {
    override fun toString(): String {
        return "SBTReward(sbtId=$sbtId, number=$number, unlimited=$unlimited,methodType=$methodType, rewardNum=$rewardNum, projectId=$projectId, creatorId=$creatorId, groupId=$groupId, name=$name)"
    }
}

object SBTRewardClaimType {
    const val DEFAULT = 0
    const val NOT_GENERATE_LINK = 1
    const val ELIGIBLE = 2
    const val MINTING = 3
    const val CLAIMED = 4
    const val MISSED = 5
}