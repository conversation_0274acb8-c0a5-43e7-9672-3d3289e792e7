package com.rewardoor.model

import java.time.Instant

class TonSyncHistory(
    var projectId: Long = 0L,
    var campaignId: Long = 0L,
    var groupId: Long = 0L,
    val title: String = "",
    val subtitle: String = "",
    val description: String = "",
    val startDate: Instant,
    val endDate: Instant,
    val buttonLabel: String = "",
    var buttonLink: String = "",
    val sbtCollectionTitle: String = "",
    val sbtCollectionDesc: String = "",
    val sbtItemTitle: String = "",
    val sbtImage: String = "",
    val sbtVideo: String = "",
    val sbtDesc: String = "",
    val syncAt: Instant,
    var activityId: Long = 0L,
    var activityUrl: String = "",
    var sbtId: Long = 0,
    var category: Int? = 0, // 0 - default , 1 - TBook OG SBTs, 2 - DeFi SBTs, 3 - Meme SBTs, 4 - Game SBTs, 5-premium SBTs, 6 - ton pioneer SBTs
    var taskCategory: List<Int>? = emptyList(), // 0 - default , 1- social contributions, 2 - defi tasks, 3 - In-game levels, 4 - credit score greater than a certain level, 5 - other tasks
    var checkStatus: Int = 0, // 0 - under Review , 1 - passed , 2 - rejected
    var projectName: String? = "",
    val networkId: Int? = 0 // ton = 0,sui = 1
) {
    override fun toString(): String {
        return "TonSyncHistory(projectId=$projectId, campaignId=$campaignId, title=$title, subtitle=$subtitle, description=$description, startDate=$startDate, endDate=$endDate, buttonLink=$buttonLink, sbtCollectionTitle=$sbtCollectionTitle, sbtCollectionDesc=$sbtCollectionDesc, sbtImage=$sbtImage, sbtDesc=$sbtDesc, syncAt=$syncAt, activityId=$activityId, activityUrl=$activityUrl)"
    }
}