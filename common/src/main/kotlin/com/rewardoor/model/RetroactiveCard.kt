package com.rewardoor.model

import com.rewardoor.app.dao.tables.TBRetroactiveCardInventory
import com.rewardoor.app.dao.tables.TBRetroactiveCardLedger
import org.jetbrains.exposed.sql.ResultRow
import java.time.Instant

data class RetroactiveCardInventory(
    val inventoryId: Long,
    val userId: Long,
    val totalCards: Int,
    val balanceCards: Int,
    val usedCards: Int,
    val createdAt: Instant = Instant.now(),
    val updatedAt: Instant = Instant.now()
) {
    constructor(r: ResultRow): this(
        inventoryId = r[TBRetroactiveCardInventory.inventoryId],
        userId = r[TBRetroactiveCardInventory.userId],
        totalCards = r[TBRetroactiveCardInventory.totalCards],
        balanceCards = r[TBRetroactiveCardInventory.balanceCards],
        usedCards = r[TBRetroactiveCardInventory.usedCards],
        createdAt = r[TBRetroactiveCardInventory.createdAt],
        updatedAt = r[TBRetroactiveCardInventory.updatedAt]
    )
}

data class RetroactiveCardLedger(
    val ledgerId: Long,
    val userId: Long,
    val outId: String,
    val usedAmount: Long,
    val addedAmount: Long,
    val fromAmount: Long,
    val toAmount: Long,
    val createdAt: Instant = Instant.now(),
    val updatedAt: Instant = Instant.now()
) {
    constructor(r: ResultRow): this(
        ledgerId = r[TBRetroactiveCardLedger.ledgerId],
        userId = r[TBRetroactiveCardLedger.userId],
        outId = r[TBRetroactiveCardLedger.outId],
        usedAmount = r[TBRetroactiveCardLedger.usedAmount],
        addedAmount = r[TBRetroactiveCardLedger.addedAmount],
        fromAmount = r[TBRetroactiveCardLedger.fromAmount],
        toAmount = r[TBRetroactiveCardLedger.toAmount],
        createdAt = r[TBRetroactiveCardLedger.createdAt],
        updatedAt = r[TBRetroactiveCardLedger.updatedAt]
    )
}