package com.rewardoor.model

import io.swagger.v3.oas.annotations.media.Schema

class Project(
    @Schema(description = "project id，新建时不需要")
    var projectId: Long = 0,
    var companyId: Long = 0,
    val projectName: String = "",
    val projectUrl: String = "",
    @Schema(description = "项目logo地址")
    val avatarUrl: String = "",
    val creatorId: Long = 0L,
    @Schema(description = "项目标签")
    val tags: List<String> = emptyList(),
    val theme: Int = 0,// project theme : 0 -default , 1- light
    val projectDescription: String = "",
    val websiteUrl: String = "",
    val telegramUrl: String = "",
    var twitterLink: String = "",
    var telegramLink: String = "",
    var discordLink: String = "",
    var banner: String = "",
    var emptyCampaignText: String = "",
    val projectCustomItemA: String = "",
    var layerOneList: List<Long> = emptyList(),
    @Schema(description = "所属链")
    var chain: String = "",
    @Schema(description = "project goal")
    var goal: String = "",
    var tgHandle: String = "",
    var checkStatus: Int = 0, // 0 - under Review , 1 - passed , 2 - rejected
    @Schema(description = "project email，新建时必填")
    var projectEmail: String = "",
    var evmRequire: Boolean = false,
    var tonRequire: Boolean = false,
    var creatorAddress: String = ""
)