package com.rewardoor.model

import java.time.Instant

class AirDrop(
    val airDropId: Long,
    val chainId: Int = 0, // -1 : ton
    val chainName: String = "",
    val projectName: String,
    val projectDesc: String = "",
    val socialLink: String = "",
    val airDropDesc: String = "",
    // update
    val bannerUrl: String = "",
    val title: String = "",
    val description: String = "",
    val details: String = "",

    val tokenName: String,
    val tokenContractAddress: String = "",
    val airdropContractAddress: String = "",
    val gas: String = "",
    val status: Int = 0, // 0 - not started, 1 - checking , 2 - claiming  ,3 - ended
    val checkStartTime: Instant? = null,
    val checkCntStartTime: Instant? = null,
    val claimStartTime: Instant? = null,
    val endTime: Instant? = null
) {
}