package com.rewardoor.model

import java.time.Instant
import java.time.LocalDateTime

class TelegramLuckyDrawTimes(
    var userId: Long,
    var tgUserId: Long,
    var times: Int,
    var lastDailyAddAt: LocalDateTime,
    var todayAdded: Int = 0,
    var todayRemains: Int = 0
) {
}

data class DailyTimeBonus(val totalAdded: Int, val unClaimed: Int, val unused: Int,
                          val nextDistribution: Long, val step: Int, val max: Int)

data class DailyFree(val remains: Int, val max: Int)

class TelegramLuckyInfo(
    var totalTimes: Int,
    var nextDistribution: Long = -1, //Instant.now().plus(Duration.ofDays(1)).truncatedTo(ChronoUnit.DAYS).toEpochMilli(),
    var todayAdded: Int = 0,
    var todayRemains: Int = 0,
    var dailyInitialCount: Int = 0,
    var dailyInitialRemains: Int = 0,
    var dailyTimeBonusMax: Int = 0,
    var dailyTimeBonusStep: Int = 0,
    var dailyTimeBonusUnused: Int = 0
) {
    val dailyTimeBonus = DailyTimeBonus(todayAdded - dailyInitialCount,
        todayRemains, dailyTimeBonusUnused, nextDistribution, dailyTimeBonusStep, dailyTimeBonusMax)
    val dailyFree = DailyFree(dailyInitialRemains, dailyInitialCount)
}

class DailyLuckyDraw(
    var userId: Long,
    var times: Int,
    var usedTimes: Int,
    var dateStr: String,
    var nextAddAt: Instant,
    var lastDailyUseAt: Instant,
) {
}