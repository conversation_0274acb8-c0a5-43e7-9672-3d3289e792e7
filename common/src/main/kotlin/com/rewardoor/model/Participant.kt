package com.rewardoor.model

import com.fasterxml.jackson.annotation.JsonFormat
import java.time.Instant
import java.time.LocalDateTime

class Participant(
    var userId: Long = 0,
    var campaignId: Long = 0,
    var wallet: String,
    var nfts: List<Long> = emptyList(),
    var points: List<Long> = emptyList(),
    var sbts: List<Long> = emptyList(),
    var suiSbts: List<Long> = emptyList(),
    var pointNum: Long = 0,
    var isJoin: Boolean = false,
    var isVisit: Boolean = false,
    var credentials: List<Credential> = emptyList(),
    var verifiedCredentials: List<Credential> = emptyList(),
    @JsonFormat(shape = JsonFormat.Shape.NUMBER, without = [JsonFormat.Feature.WRITE_DATE_TIMESTAMPS_AS_NANOSECONDS])
    var participantDate: Instant? = null,
    var isTwitterLogin: Boolean = false,
    var twitterName: String = "",
    var isTgLogin: Boolean = false,
    var tgName: String = ""
) {
}