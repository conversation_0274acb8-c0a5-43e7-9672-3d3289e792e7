package com.rewardoor.model

import java.time.Instant

class Token(
    var tokenId: Long = 0,
    var name:String = "", // USDT or etc.
    var netWork:String = "", // Optimism or etc.
    var number: Long = 0, // token amount for each winner
    var contract: String = "",
    var chainId: Int = 1,
    var methodType: Int = 0, // 0 - default, 1 - FCFS, 2 - lucky draw
    var rewardNum: Long = 0, //amount of winners
    var projectId: Long,
    var creatorId: Long = 0,
    var groupId: Long = 0,
    var claimedType: Int = 0, // 0 - 任务未完成认证 ， 1 - 已完成等待抽奖，2 - waiting, 3 - claimable, 4 - claimed ， 5 - missed未获取
    var claimedDate: Instant? = null,
    var campaignId: Long = 0,
    var campaignName: String = ""
) {
}