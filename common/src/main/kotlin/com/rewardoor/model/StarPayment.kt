package com.rewardoor.model

import com.fasterxml.jackson.annotation.JsonIgnore
import com.rewardoor.app.dao.tables.TBStarInvoices
import com.rewardoor.app.dao.tables.TBStarPayments
import org.jetbrains.exposed.sql.ResultRow
import java.time.Instant

data class StarInvoice(
    val invoiceId: Long,
    val payload: String,
    val userId: Long,
    val amount: Int,
    val product: String
) {
    constructor(r: ResultRow): this(
        invoiceId = r[TBStarInvoices.invoiceId],
        payload = r[TBStarInvoices.invoiceId].toString(),
        userId = r[TBStarInvoices.userId],
        amount = r[TBStarInvoices.amount],
        product = r[TBStarInvoices.product]
    )
}

data class StarPayment(
    val paymentId: Long,
    @JsonIgnore
    val telegramChargeId: String,
    val userId: Long,
    val starAmount: Int,
    val productAmount: Int,
    @JsonIgnore
    val invoiceId: Long,
    val createdAt: Instant = Instant.now(),
) {
    constructor(r: ResultRow): this(
        paymentId = r[TBStarPayments.paymentId],
        telegramChargeId = r[TBStarPayments.telegramChargeId],
        userId = r[TBStarPayments.userId],
        starAmount = r[TBStarPayments.starAmount],
        productAmount = r[TBStarPayments.productAmount],
        invoiceId = r[TBStarPayments.invoiceId],
        createdAt = r[TBStarPayments.createdAt]
    )
}
