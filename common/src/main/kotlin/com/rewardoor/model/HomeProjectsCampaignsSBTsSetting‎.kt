package com.rewardoor.model

import org.springframework.boot.Banner

class HomeProjectsCampaignsSBTsSetting(
    val banners: List<BannerSetting> = emptyList(),
    val campaignTitle: String,
    val campaigns: List<CampaignSetting> = emptyList(),
    val sbtTitle: String,
    val sbts: List<SbtSetting> = emptyList(),
    val projectTitle: String,
    val projects: List<ProjectSetting> = emptyList()
) {
}

data class BannerSetting(
    val bannerImageUrl: String,
    val bannerLink: String
)

data class CampaignSetting(
    val campaignId: Long,
    var campaignTitle: String = "",
    val imageUrl: String,
    var projectUrl: String = "",
    val campaignDescription: String = ""
)

data class SbtSetting(
    val sbtId: Long,
    var sbtTitle: String = "",
    val sbtActivityId: Int,
    var sbtName: String = "",
    var picUrl: String = "",
    var activityUrl: String = "",
    val campaignId: Long = 0,
    val projectUrl: String = "",
    val claimType: Int = 0, // only for users
    var sbtDescription: String = "",
    var streakDays: Int = 0, // only for daily check in
    var credentialId: Long = 0
)

data class ProjectSetting(
    val projectId: Long,
    var projectTitle: String = "",
    var projectUrl: String = "",
    var picUrl: String = "",
    var tags: List<String> = emptyList(),
    var projectName: String = ""
)