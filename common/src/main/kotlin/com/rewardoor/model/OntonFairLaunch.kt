package com.rewardoor.model
import java.math.BigDecimal
import java.time.Instant

data class OntonRoundRecordData(
    val roundNumber: Long,
    val startTime: Instant,
    val endTime: Instant,
    val price: Long,
    val totalRaisedTon: Long,
    val totalRaisedUsdt: Long,
    val tokensSold: Long,
    val purchaseCount: Long,
    val uniqueUsers: Long,
    val refundCount: Long,
    val refundedAmountTon: Long,
    val refundedAmountUsdt: Long,
    val tonPrice: BigDecimal,
    val totalRaised: BigDecimal,
    val isRoundEnded: Int = 0,        // 0=未结束，1=已结束
    val isHardCapReached: Int = 0,    // 0=未达到硬顶，1=已达到硬顶
)

data class OntonRoundContractUpdateData(
//    val price: Long,
    val totalRaisedTon: Long,
    val totalRaisedUsdt: Long,
    val tokensSold: Long,
    val purchaseCount: Long,
    val uniqueUsers: Long,
    val refundCount: Long,
    val refundedAmountTon: Long,
    val refundedAmountUsdt: Long,
    val tonPrice: BigDecimal,
    val totalRaised: BigDecimal,
//    val isRoundEnded: Int = 0,        // 0=未结束，1=已结束
//    val isHardCapReached: Int = 0,    // 0=未达到硬顶，1=已达到硬顶
)


data class CurrentRoundInfo(
    val launchStartTime : Instant?,
    val roundNumber: Long,
    val roundUnitPrice: Double,
    val tonPrice: BigDecimal,
    val startTime: Instant,
    val endTime: Instant,
    val inSettling: Boolean = false,
    val settlingDurationSecond: Long,
    val roundDurationSecond: Long,
    val totalRounds: Int,
    val isTotalEnd: Int,
    val lastRound: Long,
    val lastUnitPrice: Double,
)


data class RoundRecord(
    val roundNumber: Long,
    val roundUnitPrice: Long,
    val tonPrice: BigDecimal,
    val totalRaised: BigDecimal,
    val tokensSold: Long,
    val startTime: Instant,
    val endTime: Instant
)

data class GetCurrentRoundResponse(
    val current: CurrentRoundInfo?,
    val records: List<RoundRecord>
)

data class GetBasicInfoResponse(
    val firstRoundPrice: Double,
    val tokenHardCap: Long,
    val softCap: Long,
    val hardCap: Long,
    val startTime: Instant,
    val endTime: Instant
)
