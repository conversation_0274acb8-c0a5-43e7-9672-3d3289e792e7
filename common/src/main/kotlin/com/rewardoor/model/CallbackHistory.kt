package com.rewardoor.model

class CallbackHistory(
    var projectId: Long,
    var credentialId: Long,
    var userId: Long,
    var callbackUrl: String,
    var status: String,
    var times: Int,
    var content: String
) {
    override fun toString(): String {
        return "CallbackHistory(projectId=$projectId, credentialId=$credentialId, userId=$userId, callbackUrl='$callbackUrl', status='$status', times=$times, content='$content')"
    }
}