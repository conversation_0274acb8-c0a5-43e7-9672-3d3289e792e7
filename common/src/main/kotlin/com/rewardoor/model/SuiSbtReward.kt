package com.rewardoor.model

import java.time.Instant

class SuiSbtReward(
    var suiSbtId: Long = 0L,
    var suiSbtActivityId: Long = 0L,
    val sbtName: String = "",
    val sbtDesc: String = "",
    val sbtUrl: String = "",
    val contractAddress: String = "",
    val objectId: String = "",
    val projectId: Long = 0L,
    val creatorId: Long = 0L,
    val groupId: Long = 0L,
    var category: Int? = 0, // 0 - default , 1 - TBook OG SBTs, 2 - DeFi SBTs, 3 - Meme SBTs, 4 - Game SBTs, 5-premium SBTs, 6 - ton pioneer SBTs
    var taskCategory: List<Int>? = emptyList(), // 0 - default , 1- social contributions, 2 - defi tasks, 3 - In-game levels, 4 - credit score greater than a certain level, 5 - other tasks
    var checkStatus: Int = 0, // 0 - under review , 1- passed , 2 - rejected
    var methodType: Int = 1, // 0 - default, 1 - FCFS, 2 - lucky draw
    var unlimited: Boolean = true, //unlimited
    var claimedType: Int = 0, // 0 - 任务未完成认证 ， 1 - 已完成还没生成unique link，2 - eligible, 3 - minting, 4 - claimed ， 5 - missed未获取
    var claimedDate: Instant? = null,
    var campaignId: Long = 0L,
    var suiSbtObjectId: String = "",
    var txHash: String = "",
    var holderCnt: Int = 0,
    var projectUrl: String = "",
    var projectAvatarUrl: String = "",
    var projectName: String = "",
    var holderPicList: List<String>? = emptyList()
) {}