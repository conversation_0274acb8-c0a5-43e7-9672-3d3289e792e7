package com.rewardoor.model

import java.time.Instant

class UserSBTList(
    val userId: Long = 0,
    var address: String? = "",
    var addressType: Int = 0, // 0 - evm address , 1 - ton address , 2 - tg id
    var avatar: String = "",
    var activityId: Int? = 371, //default = 371
    var sbtLink: String = "",
    var claimedType: Int? = 0, // 0 - 任务未完成认证 ， 1 - 已完成还没生成unique link，2 - eligible, 3 - minting, 4 - claimed ， 5 - missed未获取
    var createTime: Instant? = null,
    var updateTime: Instant? = null
) {
    override fun toString(): String {
        return "UserSBTList(userId=$userId, address=$address, addressType=$addressType,avatar=$avatar, activityId=$activityId, sbtLink=$sbtLink)"
    }
}