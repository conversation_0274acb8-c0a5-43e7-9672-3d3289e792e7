package com.rewardoor.model

class Participation(
    var campaignId: Long = 0,
    var participantNum: Long = 0,
    var credentialNum: Long = 0L, //
    var pointNum: Long = 0, //
    var nftNum: Long = 0,
    var sbtClaimedNum: Long = 0,

    var nftList: List<NFT> = emptyList(),
    var pointList: List<Point> = emptyList(),
    var sbtList: List<SBTReward> = emptyList(),
    var suiSbtList: List<SuiSbtReward> = emptyList(),
    var credentialList: List<Credential> = emptyList(),
    var participantList: List<Participant> = emptyList(),

    var projectId: Long
) {
}