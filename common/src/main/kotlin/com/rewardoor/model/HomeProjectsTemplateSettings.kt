package com.rewardoor.model

import java.time.Instant

class HomeProjectsTemplateSettings(
    val templateName: String = "",
    val banner: Banner? = null,
    val middle: Middle? = null,
    val tonHero: TonHero? = null,
    val campaignIds: List<Long> = emptyList(),
    val campaigns: List<CampaignSetting> = emptyList(),
    val projectTitle: String = "",
    val projects: List<ProjectSetting> = emptyList()
) {
}

data class Banner(
    val title: String = "",
    val subTitle: String = "",
    val picUrl: String = "",
    val sbtIds: List<Long> = emptyList(),
    val sbts: List<SbtSetting> = emptyList()
)

data class Middle(
    val title: String = "",
    val description: String = ""
)

data class TonHero(
    val campaignId: Long = 0,
    val title: String = "",
    val description: String = "",
    val sbtPicUrl: String = "",
    val sbtNum: Int = 0,
    val sbtReward: SbtSetting? = null,
    val campaignStatus: Int = 0,
    val campaignEndAt: Instant? = null
)

data class HomeProjectTemplate(
    val templateName: String = "",
    val bannerTitle: String = "",
    val bannerPicUrl: String = "",
    val bannerSbtIds: List<Long> = emptyList(),
    val bannerSubTitle: String = "",
    val middleTitle: String = "",
    val middleDescription: String = "",
    val tonHeroCampaignId: Long = 0,
    val tonHeroSbtPicUrl: String = "",
    val tonHeroTitle: String = "",
    val tonHeroDescription: String = "",
    val campaignIds: List<Long> = emptyList()
)