package com.rewardoor.model

import com.fasterxml.jackson.annotation.JsonCreator
import com.fasterxml.jackson.annotation.JsonValue

enum class WiseScoreShareType(
    @JsonValue val value: Int,
    val type: String
) {
    TONCOIN(1, "TONCOIN"),
    NOTCOIN(2, "NOTCOIN"),
    ETH(3, "ETH"),
    TON_STAKE(4, "TON_STAKE"),
    TON_LP(5, "TON_LP"),
    ;

    fun toTaskName(): String {
        return "SHARE_TASK:${type}"
    }

    companion object {
        @JvmStatic
        @JsonCreator
        fun fromValue(value: Int): WiseScoreShareType {
            return values().first { it.value == value }
        }
    }
}