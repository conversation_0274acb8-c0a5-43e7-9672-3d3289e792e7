package com.rewardoor.model

class CredentialForm(
    val name: String,
    val label: String,
    val component: String,
    val componentProps: ComponentProps,
    val rules: List<Rule>,
    val html: String? = ""
)

data class ComponentProps(
    val placeholder: String
)

data class Rule(
    val required: Boolean,
    val message: String,
    val type: String? = null
)

data class FormList(
    val list: List<CredentialForm>
) {
}