package com.rewardoor.model

import java.time.Instant

//用户已经claim的reward
class UserReward(
    val rewardId: Long = 0, //对应nft id或point id
    val rewardType: Int = 0, //1:nft 2:point,3:ton SBT, 4: sui sbt
    val suiSbtObjectId: String = "", //sui sbt object id
    val txHash: String = "", // tx digest id
    val userId: Long = 0, //用户id
    val groupId: Long = 0, //所属group
    val claimType: Int = 0, // 0 - 任务未完成认证 ， 1 - 已完成等待抽奖，2 - waiting, 3 - claimable, 4 - claimed ， 5 - missed未获取
    var user: User? = null,
    var participantDate: Instant? = null
) {
}