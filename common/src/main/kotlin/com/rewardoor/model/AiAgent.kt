package com.rewardoor.model

import java.time.Instant

class AiAgent(
    // basic info
    var agentId: Long,
    val agentName: String = "",
    val agentImage: String = "",
    val bio: String = "",
    val systemPrompt: String = "",
    val lore: String = "",
    val status: Int = 0, // 0 -default , 1 - deployed/start 2 - stopped
    // intelligence
    val knowledgeList: List<String> = emptyList(),
    val topicsList: List<String> = emptyList(),
    // style setting
    val adjectives: List<String> = emptyList(),
    val generalStyleRules: String = "",
    // model setting
    val modelProvider: String = "",
    val modelName: String = "",
    val voiceModel: String = "",
    val createTime: Instant? = null,
    val updateTime: Instant? = null,
    var tgClientConfig: TgClientConfig? = null,
    var totalMemoriesCnt: Int = 0,
    var totalUsersCnt: Int = 0,
    var dailyUserCnt: List<DailyUserCount> = emptyList()
) {
}

data class TgClientConfig(
    var tgClientConfigId: Long = 0,
    var agentId: Long = 0,
    val chatStyles: String = "",
    val tgBotToken: String = "",
    val conversationExamples: List<Conversation> = emptyList()
) {
}

data class Conversation(
    val roleType: String,
    val content: String = ""
) {
}

data class DailyUserCount(
    val date: String,   // 日期，例如 "2025-02-23"
    val userCount: Int  // 对应的用户数
)
