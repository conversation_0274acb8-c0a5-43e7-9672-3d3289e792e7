package com.rewardoor.model

import com.fasterxml.jackson.annotation.JsonCreator
import com.fasterxml.jackson.annotation.JsonValue

enum class SimpleStatus(@JsonValue val value: Int, val desc: String) {
    NORMAL(0, "Normal"),
    DELETED(1, "Deleted");

    companion object {
        @JvmStatic @JsonCreator
        fun fromValue(value: Int): SimpleStatus {
            return values().first { it.value == value }
        }
    }
}