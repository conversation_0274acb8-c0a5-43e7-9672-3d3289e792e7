package com.rewardoor.model

import java.time.Instant

class WiseScoreUserStreak(
    val userId: Long, // 用户 ID
    val currentStreak: Int = 0, // 当前连续打卡天数
    val maxStreak: Int = 0, // 历史最长连续打卡天数
    val maxStreakBeginDate: Instant? = null, // 历史最长连续打卡天数的起始日期
    val maxStreakEndDate: Instant? = null, // 历史最长连续打卡天数的结束日期
    val lastCheckInDate: Instant? = null, // 最后一次打卡的日期
    val availableRetroactiveCards: Int = 0,     // 当前可用的补签卡数量
    val purchasableRetroactiveCards: Int = 0,    // 当前还可购买的补签卡数量
    var totalSignCardsUsed: Int? = 0, // 累计使用的补签卡数量
    val checkInRecord: List<Instant>? = emptyList()
) {
}