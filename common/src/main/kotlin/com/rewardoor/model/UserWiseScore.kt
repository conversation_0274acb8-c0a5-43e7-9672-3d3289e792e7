package com.rewardoor.model

import org.ton.java.tonlib.types.ShortTxId
import java.net.URL
import java.time.Instant

class UserWiseScore(
    val userId: Long = 0,
    var address: String? = "",
    var addressType: Int = 0, // 0 - evm address , 1 - ton address , 2 - tg id, 3- sui address
    var avatar: String = "",
    var totalScore: Int = 0,
    var wealthScore: WealthScore? = null,
    var identityScore: IdentityScore? = null,
    var socialScore: SocialScore? = null,
    var engagementScore: EngagementScore? = null,
    var wiseScoreSBTPerkList: List<WiseScoreSBTPerk> = emptyList(),
    val engagementSBTList: List<WiseScoreSBTPerk> = emptyList(),
    var isNotCoinHolder: Int = 0,
    var hasNotCoinTransaction: Int = 0,
    var hasTonStake: Int = 0,
    var hasTonLiquidityProvide: Int = 0,
    var rank: Int = 0,
    var userDcTgShareLink: List<UserDcTgShareLink> = emptyList(),
    var isFirstCreate: Boolean = false,
    var createTime: Instant? = null,
    var updateTime: Instant? = null
) {
}

class WealthScore(
    val score: Int = 0,
    val ethWealthScore: Int = 0,
    val tonWealthScore: Int = 0,
    val suiWealthScore: Int = 0,
    val jettonsWealthScore: Map<String, Int> = emptyMap()
) {
}

class IdentityScore(
    val score: Int = 0,
    val ethAddressScore: Int = 0,
    val tonAddressScore: Int = 0,
    val suiAddressScore: Int = 0,
    val socialScore: Int = 0,
    val xBindScore: Int = 0,
    val tgBindScore: Int = 0,
    val dcBindScore: Int = 0,
    val tgPremiumScore: Int = 0,
    val notCoinHolderScore: Int = 0
//    val availableScore: Int = 0
) {
}

class SocialScore(
    var score: Int = 0,
    val twitterFollowerScore: Int = 0,
    val tgUserScore: Int = 0,
    val dcUserScore: Int = 0,
    val wiseInviteScore: Int = 0
) {
}

class EngagementScore(
    val score: Int = 0,
    val credentialScore: Int = 0,
    val sbtScore: Int = 0,
    val basicSbtScore: Int = 0,
    val premiumSbtScore: Int = 0,
    val evmTransactionsScore: Int = 0,
    val tonTransactionsScore: Int = 0,
    val notCoinTransactionScore: Int = 0,
    val tonStakeScore: Int = 0,
    val tonLiquidityProvideScore: Int = 0
//    val premiumAvailableScore: Int = 0
) {
}

class WiseScoreSBTPerk(
    val projectUrl: String,
    val credential: Credential,
    val suiSbtId: Long,
    val suiSbtActivityId: Long,
    val sbtUrl: String,
    val sbtName: String,
    val sbtDesc: String,
    val suiSbtObjectId: String,
    val rewardType: Int, // 4
    val claimedType: Int,
    val requiredScore: Int = 0,
)