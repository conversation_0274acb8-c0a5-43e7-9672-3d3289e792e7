package com.rewardoor.model

import java.time.Instant

class NFT(
    var nftId: Long = 0,
    var name: String = "",
    var symbol: String = "",
    var contract: String = "",
    var chainId: Int = 1,
    var coverUrl: String = "",
    var picUrl: String = "",
    var mintCap: Long = 0,
    var unlimited: Boolean = true,
    var projectId: Long = 0,
    var creatorId: Long = 0,
    var groupId: Long = 0,
    var methodType: Int = 0, // 0 - default, 1 - FCFS, 2 - lucky draw
    var claimedType: Int = 0, // 0 - 任务未完成认证 ， 1 - 已完成等待抽奖，2 - waiting, 3 - claimable, 4 - claimed ， 5 - missed未获取
    var claimedCount: Int = 0,
    var claimedDate: Instant? = null,
    var campaignId: Long = 0,
    var campaignName: String = ""
) {
    override fun toString(): String {
        return "NFT(nftId=$nftId, name='$name', symbol='$symbol', contract='$contract', chainId=$chainId, coverUrl='$coverUrl',projectId=$projectId,creatorId=$creatorId,groupId=$groupId)"
    }

    companion object {
        val fakeNfts = listOf(
            NFT(
                nftId = 1,
                name = "TBook onboarding NFT",
                symbol = "TBook",
                mintCap = 10,
                picUrl = "https://rd-worker.xgamma.workers.dev/Leader.png",
                coverUrl = "https://rd-worker.xgamma.workers.dev/Leader.png",
                contract = "******************************************",
                methodType = 4,
                claimedCount = 3
            ),
            NFT(
                nftId = 2,
                name = "TBook Community NFT #1",
                symbol = "TBook",
                mintCap = 15,
                picUrl = "https://rd-worker.xgamma.workers.dev/Media.png",
                coverUrl = "https://rd-worker.xgamma.workers.dev/Media.png",
                contract = "******************************************",
                methodType = 2,
                claimedType = 4,
                claimedCount = 4
            )
        )

        val fakeGiveaways = listOf(
            NFTGiveaway(
                nftId = 1,
                address = "******************************************",
                nftNo = 154,
                mintDate = "03/09/2023",
            ),
            NFTGiveaway(
                nftId = 1,
                address = "******************************************",
                nftNo = 158,
                mintDate = "04/09/2023",
            ),
            NFTGiveaway(
                nftId = 1,
                address = "******************************************",
                nftNo = 167,
                mintDate = "04/09/2023",
            ),

            NFTGiveaway(
                nftId = 2,
                address = "******************************************",
                nftNo = 23,
                mintDate = "04/09/2023",
            ),
            NFTGiveaway(
                nftId = 2,
                address = "******************************************",
                nftNo = 29,
                mintDate = "06/09/2023",
            ),
            NFTGiveaway(
                nftId = 2,
                address = "0x17ceaeA322517b438560a9796935Ed79Fa16D661",
                nftNo = 35,
                mintDate = "06/09/2023",
            ),
            NFTGiveaway(
                nftId = 2,
                address = "0x63246cAc41f68Ef343e4dA8EaAC55785734cE4b3",
                nftNo = 36,
                mintDate = "06/09/2023",
            ),
        ).groupBy { it.nftId }
    }
}

class NFTGiveaway(
    var nftId: Long = 0,
    var address: String = "",
    var nftNo: Long = 0,
    var mintDate: String = ""
)