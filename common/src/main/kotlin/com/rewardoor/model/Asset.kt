package com.rewardoor.model

class Asset(
    val projectId: Long = 0,
    var nfts: List<NFT> = emptyList(),
    var credentials: List<Credential> = emptyList(),
    var userPoints: List<UserProject> = emptyList(),
    var userSBTs: List<UserSbt> = emptyList(),
    var points: List<PointInfo> = emptyList(),
    var checkSBTs: List<UserSbt> = emptyList()
) {
}

data class UserSbt(
    val netWorkId: Int = 0, // 0 - ton, 1 - sui
//    val sbtId: Long = 0,
//    val activityId: Int = 0,
//    val activityUrl: String = "",
//    val name: String = "",
//    val picUrl: String = "",
//    val description: String = "",
//    val totalParticipantNum: Int = 0,
    val claimStatus: Int = 0, // 0 - under review , 1 - claimable
    val totalClaimedNum: Long = 0,
    val tonSyncHistory: TonSyncHistory? = null,
    val campaignIdList: List<Long> = emptyList()
) {
}

data class PointInfo(
    val netWorkId: Int = 0, // 0 - ton, 1 - sui
    val pointId: Long = 0,
    val unlimited: Boolean = false,
    val rewardNum: Int = 0, // 发多少个,如果unlimited = true，代表发无限个
    val number: Int = 0, //每人发多少Point
    val claimStatus: Int = 0, // 0 - default , 1 - claimable
    val totalClaimedNum: Long = 0,
    val campaignId: Long = 0
) {

}