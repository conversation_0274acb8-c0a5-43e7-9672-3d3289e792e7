package com.rewardoor.model

import com.fasterxml.jackson.annotation.JsonCreator
import com.fasterxml.jackson.annotation.JsonFormat
import com.fasterxml.jackson.annotation.JsonValue
import io.swagger.v3.oas.annotations.media.Schema
import java.time.Instant

class Campaign(
    var campaignId: Long = 0,
    var title: String = "",
    var name: String = "",
    var picUrl: String,
    var description: String,
    var shareText: String = "",
    @JsonFormat(shape = JsonFormat.Shape.NUMBER, without = [JsonFormat.Feature.WRITE_DATE_TIMESTAMPS_AS_NANOSECONDS])
    var startAt: Instant? = null,
    @JsonFormat(shape = JsonFormat.Shape.NUMBER, without = [JsonFormat.Feature.WRITE_DATE_TIMESTAMPS_AS_NANOSECONDS])
    var endAt: Instant? = null,
    var status: CampaignStatus = CampaignStatus.DRAFT,
    var reward: String = "",
    var rewardAction: String = "",
    var projectId: Long,
    var creatorId: Long = 0,
    var points: Int? = null,
    var credentialId: Long? = null,
    var nft: Long? = null,
    var createTime: Instant? = null,
    var credentials: List<Credential> = emptyList(),
    var spaceId: Long = 0,
    var participantNum: Long = 0,
    var projectName: String = "",
    var projectUrl: String = "",
    var projectLogoUrl: String = "",
    var hasSBTReward: Boolean = false
) {
    override fun toString(): String {
        return "Campaign(campaignId=$campaignId, name='$name', picUrl='$picUrl', description='$description', startAt=$startAt, endAt=$endAt, status=$status, rewardAction='$rewardAction', projectId=$projectId, creatorId=$creatorId, points=$points, credentialId=$credentialId, nft=$nft,spaceId=$spaceId)"
    }

    fun getUniName(): String {
        return title.ifEmpty { name }
    }
}

@Schema(enumAsRef = true, description = "0: 草稿, 1：进行中, 2：计划中，3: 已完成, 4:暂停中, 5：审核中, 16: 已删除")
enum class CampaignStatus(@JsonValue val value: Int) {
    DRAFT(0),
    ON_GOING(1),
    SCHEDULED(2),
    COMPLETED(3),
    SUSPENDED(4),
    UNDER_REVIEWED(5),
    REJECTED(-1),
    DELETED(16);

    companion object {
        @JvmStatic
        @JsonCreator
        fun fromValue(value: Int): CampaignStatus {
            return values().first { it.value == value }
        }
    }
}

class TVLAddressInfo(
    val address: String,
    val userId: Long
)