package com.rewardoor.model

class CredentialGroup(
    var groupType: Int = 0, // 0 - default，1 - Governance，2 - community, 3 - Trade , 4 - ProductTest, 5 -My, 6 -Activity , 7-Submit
    // 8 - Liquidity Staking, 9 - Lending Protocol, 10 - DEXs, 11 - Storm Trade
    var name: String = "",
    var id: Long = 0,
    var credentialList: List<Credential> = emptyList(),
    var projectId: Long,
    var creatorId: Long = 0,
    var campaignId: Long,
    var status: Int = 0, //status 中，0: 草稿, 1：进行中, 2：计划中，3: 已完成, 16: 已删除
    var nftList: List<NFT> = emptyList(),
    var pointList: List<Point> = emptyList(),
    var tokenList: List<Token> = emptyList(),
    var sbtList: List<SBTReward> = emptyList(),
    var sbtCollection: TonSyncHistory? = null,
    var existActivityId: Int = 0,
    var suiSbtReward: SuiSbtReward? = null,
    var suiSbtSync: SuiSbtSync? = null,
    var existSuiSbtActivityId: Long = 0L,
    var suiSbtList:List<SuiSbtReward> = emptyList()
) {
    override fun toString(): String {
        return "CredentialGroup(groupType=$groupType, name='$name', id=$id, credentialList='$credentialList', projectId=$projectId, creatorId=$creatorId, campaignId=$campaignId,status=$status,nftList='$nftList',pointList='$pointList')"
    }

}