package com.rewardoor.model

class CampaignCard(
    val campaignId: Long,
    var title: String = "",
    var name: String = "",
    var picUrl: String,
    var description: String,
    var status: CampaignStatus = CampaignStatus.ON_GOING,
    var campaignCategory: Int = 0, // 1 - socialFi , 2 - Infra, 3 - Security， 4-gameFi， 5-Community
    val project: Project,
    val users: List<User> = emptyList(),
    val points: List<Point> = emptyList(),
    val nfts: List<NFT> = emptyList(),
    val tokens: List<String> = emptyList(),
) {
}