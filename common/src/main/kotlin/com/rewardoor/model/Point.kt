package com.rewardoor.model

import java.time.Instant

class Point(
    var pointId: Long = 0,
    var number: Long = 0,
    var methodType: Int = 0, // 0 - default, 1 - FCFS, 2 - lucky draw
    var unlimited: Boolean = true, //unlimited
    var rewardNum: Long = 0,
    var projectId: Long,
    var creatorId: Long = 0,
    var groupId: Long = 0,
    var claimedType: Int = 0, // 0 - 任务未完成认证 ， 1 - 已完成等待抽奖，2 - waiting, 3 - claimable, 4 - claimed ， 5 - missed未获取
    var claimedDate: Instant? = null,
    var campaignId: Long = 0,
    var campaignName: String = "",
    var claimedNum: Long = 0
) {
    override fun toString(): String {
        return "Point(pointId=$pointId, number=$number, unlimited=$unlimited,methodType=$methodType, rewardNum=$rewardNum, projectId=$projectId, creatorId=$creatorId, groupId=$groupId)"
    }

}