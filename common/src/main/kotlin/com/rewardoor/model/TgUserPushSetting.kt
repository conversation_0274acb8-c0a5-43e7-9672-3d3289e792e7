package com.rewardoor.model

import com.rewardoor.app.services.TgUserPushService
import java.time.Instant

class TgUserPushSetting(
    var tgPushId: Long = 0,
    val pushType: String = "", // eg: Exchange 80K TPoints for SBT and rewards
    val userType: Int = 0, // 0 - ALL USER, 1 - SBT holder ,  2 - top wise score user , 3 - wise score limit, 4 - white list
    val activityId: Int? = 0, // 当userType = 1时才有值
    val topWiseScoreType: Int? = 0, // 0 - default , 1 - top 1000, 2 - top 5000, 3 - top 10000, 4 - top 50000, 5 - top 100000
    val wiseScoreType: Int? = 0, // 0 - default , 1 - 大于, 2 - 大于等于, 3 - 小于, 4 - 小于等于
    val wiseScoreLimit: Int? = 0, // 分数限制
    val whiteList: List<Long>? = emptyList(),
    val hasGroups: Int? = 0, // 0 - 不分组，1 - 分10组
    var pushBasicInfoList: List<PushBasicInfo>? = emptyList(),
    val hasPushed: Int = 0, // 0 - 还未推送， 1- 已推送
    val createdAt: Instant? = null,
    var pushResultInfo: PushResultInfo? = null
) {
}

data class PushBasicInfo(
    var tgPushSettingId: Long? = 0,
    val imgUrl: String = "",
    val content: String = "",
    val buttonName: String = "",
    val buttonUrl: String = "",
    val pushDate: Instant? = null
) {
}

data class PushResultInfo(
    val tgPushId: Long,
    val groupId: Int = 0,
    val totalPushCount: Long = 0,
    val succeedPushCount: Long = 0,
    val failedPushCount: Long = 0,
    val pushStartDate: Instant? = null,
    val hasPushed: Int? = 0
) {
}