package com.rewardoor.model

class TPointsVanguardConfig(
    val level: Int,
    val pointsNum: Int,
    val nextLevelPointsNum: Int,
    val vanguardPrivilege: VanguardPrivilege? = null
) {
}

class VanguardPrivilege(
    val wiseInviteNum: Int,
    val onePointTwoTPoints: Int = 0, // 1.2* TPoints，0-false, 1-true
    val onePointTwoWiseScore: Int = 0, // 1.2* WiseScore，0-false, 1-true
    val inviteVanguardNum: Int = 0,  // number of invite other to be vanguard
    val exclusiveSBT: Int = 0, // 0-false, 1-true
) {
}