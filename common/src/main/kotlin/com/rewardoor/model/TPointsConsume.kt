package com.rewardoor.model

import java.time.Instant

class TPointsConsume(
    val userId: Long = 0L,
    var consumeType: Int = 0, // 1- increase card upper limit , 2 - increase card growth speed , 3 - buy cards , -2 - use 80K TPoints
    var level: Int = 0, // consumeType = 1 : level 0-10 , consumeType = 2 : level 0-8, consumeType = 3 : level 0- 3, -2 - use 80K TPoints
    var tPointsNum: Int = 0,
    var createTime: Instant? = null,
    var updateTime: Instant? = null
) {
}