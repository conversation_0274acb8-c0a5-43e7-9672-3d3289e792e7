package com.rewardoor.model

import io.swagger.v3.oas.annotations.media.Schema

class Company(
    @Schema(description = "company id，新建时不需要")
    var companyId: Long = 0,
    val companyName: String = "",
    @Schema(description = "项目logo地址")
    val creatorId: Long = 0L,
    @Schema(description = "项目标签")
    val companyDescription: String = "",
    val logo: String = "",
    val channelName: String = "",
    val channelLink: String = "",
    val twitterLink: String = "",
    val webUrl: String = "",
    val aboutBgImage: String = "",
    var homePoster: String = "",
    var homePosterLink: String ?= "",
    var pointBgImage: String ?= "",
)

class LayerOne(
    var layerOneId: Long = 0,
    val name: String = "",
    val icon: String = "",
    val url: String = "",
    var status: Int? = 0,
)

class CompanyLayerOneRelation(
    var companyId: Long = 0,
    var layerOneId: Long = 0,
    var status: Int = 0,
)

class CompanyWithProjects(
    val company: Company,
    val layerOneList: List<LayerOne> = listOf(),
    val projects: List<Project>
)

class CompanyLeaderboard(
    val companyId: Long = 0,
    val companyName: String = "",
    val leaderboard: List<UserCompany>
)

class UserCompany(
    val rank: Int = 0,
    val userId: Long,
    val projectId: Long,
    val address: String,
    val addressType: Int = 0, // 0 - default, 1 - evm , 2 - ton
    val pointNum: Long
)

class CompanyConfig(
    val companyId: Long = 0,
    val configKey: String,
    val configValue: String
)

class CompanyWithConfig(
    val company: Company,
    val companyId: Long = 0,
    val configKey: String,
    val configValue: String
)