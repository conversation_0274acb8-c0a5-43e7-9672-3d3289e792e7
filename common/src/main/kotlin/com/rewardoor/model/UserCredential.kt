package com.rewardoor.model

import com.fasterxml.jackson.annotation.JsonFormat
import java.time.Instant

class UserCredential(
    var userId: Long = 0,
    var address: String = "",
    var credentialId: Long = 0,
    var campaignId: Long = 0,
    var status: Int = 0, // 0 - default,1 - 有效， 2 - 无效
    @JsonFormat(shape = JsonFormat.Shape.NUMBER, without = [JsonFormat.Feature.WRITE_DATE_TIMESTAMPS_AS_NANOSECONDS])
    var participantDate: Instant? = null,
    var isTwitterLogin: Boolean = false,
    var socialId: String = "",
    var socialType: Int = 0,  // 0-default, 1-twitter/X , 2-telegram , 3-discord
    var labelType: Int = 0,  // 0 - default, 1 - Twitter Like, 2 - ReTweet Link, 3 - <PERSON> Spaces, 4 - Join Discord Service, 5 - Verify Discord role, 6 - Join Telegram Group, 7 - Join Telegram Channel，8 - Visit a Page or Site, 9 - Register by Twitter, 11 - Follow twitter, 12 - Snapshot, 13 - Airdrop Address Aggregation
    var amount: Long? = 0,
    var amount3: Long? = 0,
    var amount4: Long? = 0,
) {
}