package com.rewardoor.model

class CampaignTotal(
    var campaign: Campaign,
    var groups: List<CredentialGroup> = emptyList(),
    var participation: Participation? = null,
    var participantNum: Int = 0
//    var govGroups: String = "",// List<CredentialGroup> = emptyList(),
//    var comGroups: String = "",//List<CredentialGroup> = emptyList(),
//    var tradeGroups: String = "",//List<CredentialGroup> = emptyList(),
//    var productGroups: String = "",//List<CredentialGroup> = emptyList(),
//    var myGroups: String = "", //List<CredentialGroup> = emptyList(),
) {

}