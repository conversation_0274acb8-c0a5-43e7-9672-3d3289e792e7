package com.rewardoor.model

class CampaignStats(
    var campaign: Campaign,
    var nftReward: <PERSON>olean,
    var credentialReward: Boolean,
    var pointsReward: Boolean,
    var participation: Long,
    var qualified: Long,
    var claimed: Long
){}

class ProjectOverview(
    var project: Project,
    var actionCount: Long,
    var credentialCount: Long,
    var nftCount: Long,
    var campaignCount: Long,
    var campaigns: List<CampaignStats>
) {
}