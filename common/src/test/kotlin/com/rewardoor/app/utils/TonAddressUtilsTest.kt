package com.rewardoor.app.utils

import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.Test

class TonAddressUtilsTest {

    @Test
    fun testToHexAddress() {
        // 测试user-friendly格式转hex
        val userFriendlyAddress = "EQDEd4jsQ0WJWnrPWIHxyiitVzjse5Nw3QJUiI8Bp_1Kjdgd"
        val expectedHex = "0:c47788ec4345895a7acf5881f1ca28ad5738ec7b9370dd0254888f01a7fd4a8d"
        
        val result = TonAddressUtils.toHexAddress(userFriendlyAddress)
        assertEquals(expectedHex, result)
    }

    @Test
    fun testToHexAddressWithNonBounceable() {
        // 测试non-bounceable格式转hex
        val nonBounceableAddress = "UQDEd4jsQ0WJWnrPWIHxyiitVzjse5Nw3QJUiI8Bp_1KjYXY"
        val expectedHex = "0:c47788ec4345895a7acf5881f1ca28ad5738ec7b9370dd0254888f01a7fd4a8d"
        
        val result = TonAddressUtils.toHexAddress(nonBounceableAddress)
        assertEquals(expectedHex, result)
    }

    @Test
    fun testToHexAddressWithRawHex() {
        // 测试已经是hex格式的地址
        val hexAddress = "0:c47788ec4345895a7acf5881f1ca28ad5738ec7b9370dd0254888f01a7fd4a8d"
        
        val result = TonAddressUtils.toHexAddress(hexAddress)
        assertEquals(hexAddress, result)
    }

    @Test
    fun testIsValidTonAddress() {
        // 测试有效地址
        assertTrue(TonAddressUtils.isValidTonAddress("EQDEd4jsQ0WJWnrPWIHxyiitVzjse5Nw3QJUiI8Bp_1Kjdgd"))
        assertTrue(TonAddressUtils.isValidTonAddress("UQDEd4jsQ0WJWnrPWIHxyiitVzjse5Nw3QJUiI8Bp_1KjYXY"))
        assertTrue(TonAddressUtils.isValidTonAddress("0:c47788ec4345895a7acf5881f1ca28ad5738ec7b9370dd0254888f01a7fd4a8d"))
        
        // 测试无效地址
        assertFalse(TonAddressUtils.isValidTonAddress("invalid_address"))
        assertFalse(TonAddressUtils.isValidTonAddress(""))
        assertFalse(TonAddressUtils.isValidTonAddress("0x1234567890abcdef"))
    }

    @Test
    fun testIsSameAddress() {
        val userFriendlyAddress = "EQDEd4jsQ0WJWnrPWIHxyiitVzjse5Nw3QJUiI8Bp_1Kjdgd"
        val nonBounceableAddress = "UQDEd4jsQ0WJWnrPWIHxyiitVzjse5Nw3QJUiI8Bp_1KjYXY"
        val hexAddress = "0:c47788ec4345895a7acf5881f1ca28ad5738ec7b9370dd0254888f01a7fd4a8d"
        
        // 测试不同格式的同一地址
        assertTrue(TonAddressUtils.isSameAddress(userFriendlyAddress, nonBounceableAddress))
        assertTrue(TonAddressUtils.isSameAddress(userFriendlyAddress, hexAddress))
        assertTrue(TonAddressUtils.isSameAddress(nonBounceableAddress, hexAddress))
        
        // 测试不同地址
        val differentAddress = "EQCxRWiWTz8QSlLme1RnI6to0ZZcnV1ctdsGf3EyP7iY0Uqt"
        assertFalse(TonAddressUtils.isSameAddress(userFriendlyAddress, differentAddress))
    }

    @Test
    fun testHexToUserFriendly() {
        val hexAddress = "0:c47788ec4345895a7acf5881f1ca28ad5738ec7b9370dd0254888f01a7fd4a8d"
        val result = TonAddressUtils.hexToUserFriendly(hexAddress)

        // 结果应该是有效的TON地址
        assertTrue(TonAddressUtils.isValidTonAddress(result))
        
        // 转换后的地址应该与原hex地址相同
        assertTrue(TonAddressUtils.isSameAddress(hexAddress, result))
    }

    @Test
    fun testHexToNonBounceable() {
        val hexAddress = "0:c47788ec4345895a7acf5881f1ca28ad5738ec7b9370dd0254888f01a7fd4a8d"
        val expectedNonBounceable = "UQDEd4jsQ0WJWnrPWIHxyiitVzjse5Nw3QJUiI8Bp_1KjYXY"
        
        val result = TonAddressUtils.hexToNonBounceable(hexAddress)
        assertEquals(expectedNonBounceable, result)
    }

    @Test
    fun testInvalidAddressThrowsException() {
        assertThrows(IllegalArgumentException::class.java) {
            TonAddressUtils.toHexAddress("invalid_address")
        }
        
        assertThrows(IllegalArgumentException::class.java) {
            TonAddressUtils.hexToUserFriendly("invalid_hex")
        }
        
        assertThrows(IllegalArgumentException::class.java) {
            TonAddressUtils.hexToNonBounceable("invalid_hex")
        }
    }
}
