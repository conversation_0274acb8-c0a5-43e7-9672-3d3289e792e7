package com.rewardoor.app.services

import org.junit.jupiter.api.Test
import org.junit.jupiter.api.Assertions.*
import org.ton.java.cell.CellBuilder
import org.ton.java.address.Address
import org.ton.java.utils.Utils
import java.math.BigInteger

/**
 * OntonFairLaunchService 的单元测试
 * 主要测试 TON 合约集成相关功能
 */
class OntonFairLaunchServiceTest {

    @Test
    fun testCreateUpdateRoundMessage() {
        // 测试创建更新轮次消息的功能
        val newPrice = BigInteger.valueOf(5000000) // 5.0 USDT in micro units
        val roundNumber = 2L
        val opCode = 0xaa8b8b89L

        // 手动创建预期的消息结构
        val expectedMessage = CellBuilder.beginCell()
            .storeUint(opCode, 32)        // op code
            .storeUint(0, 64)             // query_id
            .storeCoins(newPrice)         // new_price
            .storeUint(roundNumber, 32)   // round_number
            .endCell()

        // 验证消息结构正确
        assertNotNull(expectedMessage)
        assertTrue(expectedMessage.bits.size > 0)
        
        println("✅ 更新轮次消息创建测试通过")
        println("消息哈希: ${expectedMessage.hash()}")
    }

    @Test
    fun testTonAddressValidation() {
        // 测试 TON 地址验证
        val validAddress = "EQCUmdTvMaj1JmC-YhsSfbJdWsi_--8urSYN-DIpXYi_hgvu"
        
        try {
            val address = Address.of(validAddress)
            assertNotNull(address)
            println("✅ TON 地址验证测试通过")
            println("地址: ${address.toRaw()}")
        } catch (e: Exception) {
            fail("TON 地址验证失败: ${e.message}")
        }
    }

    @Test
    fun testTonAmountConversion() {
        // 测试 TON 金额转换
        val amount = 0.02
        val nanoAmount = Utils.toNano(amount)
        
        assertTrue(nanoAmount > BigInteger.ZERO)
        assertEquals(BigInteger.valueOf(20000000), nanoAmount) // 0.02 TON = 20,000,000 nanoTON
        
        println("✅ TON 金额转换测试通过")
        println("$amount TON = $nanoAmount nanoTON")
    }

    @Test
    fun testCellBuilderBasicOperations() {
        // 测试 Cell 构建基本操作
        val testCell = CellBuilder.beginCell()
            .storeBit(false)
            .storeBit(true)
            .storeUint(42, 8)
            .storeCoins(BigInteger.valueOf(1000000))
            .endCell()

        assertNotNull(testCell)
        assertTrue(testCell.bits.size > 0)
        
        println("✅ Cell 构建基本操作测试通过")
        println("Cell 位数: ${testCell.bits.size}")
    }

    @Test
    fun testMessageStructureForContract() {
        // 测试完整的合约消息结构
        val contractAddress = "EQCUmdTvMaj1JmC-YhsSfbJdWsi_--8urSYN-DIpXYi_hgvu"
        val walletAddress = "EQDKbjIcfM6ezt8KjKJJLshZJJSqX7XOA4ff-W72r5gqPrHF"
        val amount = Utils.toNano(0.02)
        val opCode = 0xaa8b8b89L
        val newPrice = BigInteger.valueOf(5000000)
        val roundNumber = 2L

        // 创建更新轮次消息
        val updateRoundMessage = CellBuilder.beginCell()
            .storeUint(opCode, 32)
            .storeUint(0, 64)
            .storeCoins(newPrice)
            .storeUint(roundNumber, 32)
            .endCell()

        // 创建内部消息
        val internalMessage = CellBuilder.beginCell()
            .storeBit(false)                           // int_msg_info$0
            .storeBit(true)                            // ihr_disabled
            .storeBit(false)                           // bounce
            .storeBit(false)                           // bounced
            .storeAddress(Address.of(walletAddress))   // src
            .storeAddress(Address.of(contractAddress)) // dest
            .storeCoins(amount)                        // value
            .storeBit(false)                           // extra currencies
            .storeBit(false)                           // ihr_fee
            .storeBit(false)                           // fwd_fee
            .storeUint(0, 64)                         // created_lt
            .storeUint(0, 32)                         // created_at
            .storeBit(false)                          // init (maybe)
            .storeBit(true)                           // body (maybe)
            .storeRef(updateRoundMessage)             // body reference
            .endCell()

        assertNotNull(internalMessage)
        assertTrue(internalMessage.bits.size > 0)
        
        println("✅ 合约消息结构测试通过")
        println("内部消息哈希: ${internalMessage.hash()}")
        println("更新轮次消息哈希: ${updateRoundMessage.hash()}")
    }

    @Test
    fun testPriceConversion() {
        // 测试价格转换逻辑
        val priceInUsdt = 5.0 // 5 USDT
        val priceInMicroUnits = (priceInUsdt * 1_000_000).toLong()
        val priceAsBigInteger = BigInteger.valueOf(priceInMicroUnits)
        
        assertEquals(5000000L, priceInMicroUnits)
        assertEquals(BigInteger.valueOf(5000000), priceAsBigInteger)
        
        println("✅ 价格转换测试通过")
        println("$priceInUsdt USDT = $priceInMicroUnits micro units")
    }
}
