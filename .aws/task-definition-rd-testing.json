{"family": "rewardoor-api-stag", "containerDefinitions": [{"name": "rewardoor-service-testing", "image": "824745172792.dkr.ecr.ap-southeast-1.amazonaws.com/rewardoor-service-testing:latest", "cpu": 0, "links": [], "portMappings": [{"name": "tbook-8080-tcp", "containerPort": 8080, "hostPort": 8080, "protocol": "tcp", "appProtocol": "http"}], "essential": true, "entryPoint": [], "command": [], "environment": [{"name": "contract_caller_secret", "value": ""}, {"name": "contract_caller_url", "value": ""}], "environmentFiles": [], "mountPoints": [], "volumesFrom": [], "secrets": [{"name": "r2_secret_key", "valueFrom": "arn:aws:ssm:ap-southeast-1:824745172792:parameter/r2Secret"}, {"name": "r2_access_key", "valueFrom": "arn:aws:ssm:ap-southeast-1:824745172792:parameter/r2AccessKey"}, {"name": "ton_airdrop_secret_key", "valueFrom": "arn:aws:ssm:ap-southeast-1:824745172792:parameter/testTonAirdropSecretKey"}, {"name": "sui_verify_host", "valueFrom": "arn:aws:ssm:ap-southeast-1:824745172792:parameter/sui_verify_host"}, {"name": "sui_verify_auth", "valueFrom": "arn:aws:ssm:ap-southeast-1:824745172792:parameter/sui_verify_auth"}], "dnsServers": [], "dnsSearchDomains": [], "extraHosts": [], "dockerSecurityOptions": [], "dockerLabels": {}, "ulimits": [], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-create-group": "true", "awslogs-group": "/ecs/rewardoor-service-stag", "awslogs-region": "ap-southeast-1", "awslogs-stream-prefix": "ecs"}, "secretOptions": []}, "systemControls": []}], "taskRoleArn": "arn:aws:iam::824745172792:role/ecsTaskRole", "executionRoleArn": "arn:aws:iam::824745172792:role/ecsTaskExecutionRole", "networkMode": "awsvpc", "requiresCompatibilities": ["FARGATE"], "cpu": "1024", "memory": "2048", "runtimePlatform": {"cpuArchitecture": "X86_64", "operatingSystemFamily": "LINUX"}, "tags": [{"key": "ecs:taskDefinition:createdFrom", "value": "ecs-console-v2"}]}