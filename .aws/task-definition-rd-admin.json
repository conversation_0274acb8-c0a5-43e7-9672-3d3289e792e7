{"family": "rd-admin-api", "containerDefinitions": [{"name": "rd-admin-service", "image": "824745172792.dkr.ecr.ap-southeast-1.amazonaws.com/rd-admin-service:latest", "cpu": 0, "portMappings": [{"name": "rd-admin-api-8090-tcp", "containerPort": 8090, "hostPort": 8090, "protocol": "tcp", "appProtocol": "http"}], "essential": true, "environment": [{"name": "datasource_master_url", "value": "*********************************************************************************************************************************"}, {"name": "SPRING_PROFILES_ACTIVE", "value": "prod"}, {"name": "contract_caller_url", "value": ""}, {"name": "contract_caller_secret", "value": ""}], "secrets": [{"name": "r2_secret_key", "valueFrom": "arn:aws:ssm:ap-southeast-1:824745172792:parameter/r2Secret"}, {"name": "r2_access_key", "valueFrom": "arn:aws:ssm:ap-southeast-1:824745172792:parameter/r2AccessKey"}, {"name": "manager_private_key", "valueFrom": "arn:aws:ssm:ap-southeast-1:824745172792:parameter/manager_private_key"}, {"name": "database_username", "valueFrom": "arn:aws:ssm:ap-southeast-1:824745172792:parameter/database_username"}, {"name": "database_password", "valueFrom": "arn:aws:ssm:ap-southeast-1:824745172792:parameter/database_password"}, {"name": "tg_bot_token", "valueFrom": "arn:aws:ssm:ap-southeast-1:824745172792:parameter/tg_bot_token"}, {"name": "tg_check_bot_token", "valueFrom": "arn:aws:ssm:ap-southeast-1:824745172792:parameter/tg_check_bot_token"}, {"name": "tg_mini_app_token", "valueFrom": "arn:aws:ssm:ap-southeast-1:824745172792:parameter/tg_mini_app_token"}, {"name": "sui_verify_host", "valueFrom": "arn:aws:ssm:ap-southeast-1:824745172792:parameter/sui_verify_host"}, {"name": "sui_verify_auth", "valueFrom": "arn:aws:ssm:ap-southeast-1:824745172792:parameter/sui_verify_auth"}, {"name": "ton_airdrop_secret_key", "valueFrom": "arn:aws:ssm:ap-southeast-1:824745172792:parameter/tonAirdropSecretKey"}], "environmentFiles": [], "mountPoints": [], "volumesFrom": [], "ulimits": [], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/rd-admin-service", "mode": "non-blocking", "awslogs-create-group": "true", "max-buffer-size": "25m", "awslogs-region": "ap-southeast-1", "awslogs-stream-prefix": "ecs"}, "secretOptions": []}, "systemControls": []}], "taskRoleArn": "arn:aws:iam::824745172792:role/ecsTaskRole", "executionRoleArn": "arn:aws:iam::824745172792:role/ecsTaskExecutionRole", "networkMode": "awsvpc", "revision": 1, "volumes": [], "status": "ACTIVE", "requiresAttributes": [{"name": "com.amazonaws.ecs.capability.logging-driver.awslogs"}, {"name": "ecs.capability.execution-role-awslogs"}, {"name": "com.amazonaws.ecs.capability.ecr-auth"}, {"name": "com.amazonaws.ecs.capability.docker-remote-api.1.19"}, {"name": "com.amazonaws.ecs.capability.docker-remote-api.1.28"}, {"name": "com.amazonaws.ecs.capability.task-iam-role"}, {"name": "ecs.capability.execution-role-ecr-pull"}, {"name": "com.amazonaws.ecs.capability.docker-remote-api.1.18"}, {"name": "ecs.capability.task-eni"}, {"name": "com.amazonaws.ecs.capability.docker-remote-api.1.29"}], "placementConstraints": [], "compatibilities": ["EC2", "FARGATE"], "requiresCompatibilities": ["FARGATE"], "cpu": "8192", "memory": "16384", "runtimePlatform": {"cpuArchitecture": "X86_64", "operatingSystemFamily": "LINUX"}, "tags": []}