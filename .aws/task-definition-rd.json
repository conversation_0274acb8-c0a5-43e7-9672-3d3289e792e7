{"family": "reward<PERSON>-api", "containerDefinitions": [{"name": "rewardoor-service", "image": "824745172792.dkr.ecr.ap-southeast-1.amazonaws.com/rewardoor-service:latest", "cpu": 0, "links": [], "portMappings": [{"name": "tbook-8080-tcp", "containerPort": 8080, "hostPort": 8080, "protocol": "tcp", "appProtocol": "http"}], "essential": true, "entryPoint": [], "command": [], "environment": [{"name": "datasource_master_url", "value": "*********************************************************************************************************************************"}, {"name": "SPRING_PROFILES_ACTIVE", "value": "prod"}, {"name": "contract_caller_url", "value": ""}, {"name": "contract_caller_secret", "value": ""}], "environmentFiles": [], "mountPoints": [], "volumesFrom": [], "secrets": [{"name": "r2_secret_key", "valueFrom": "arn:aws:ssm:ap-southeast-1:824745172792:parameter/r2Secret"}, {"name": "r2_access_key", "valueFrom": "arn:aws:ssm:ap-southeast-1:824745172792:parameter/r2AccessKey"}, {"name": "manager_private_key", "valueFrom": "arn:aws:ssm:ap-southeast-1:824745172792:parameter/manager_private_key"}, {"name": "database_username", "valueFrom": "arn:aws:ssm:ap-southeast-1:824745172792:parameter/database_username"}, {"name": "database_password", "valueFrom": "arn:aws:ssm:ap-southeast-1:824745172792:parameter/database_password"}, {"name": "tg_bot_token", "valueFrom": "arn:aws:ssm:ap-southeast-1:824745172792:parameter/tg_bot_token"}, {"name": "tg_check_bot_token", "valueFrom": "arn:aws:ssm:ap-southeast-1:824745172792:parameter/tg_check_bot_token"}, {"name": "tg_mini_app_token", "valueFrom": "arn:aws:ssm:ap-southeast-1:824745172792:parameter/tg_mini_app_token"}, {"name": "ton_airdrop_secret_key", "valueFrom": "arn:aws:ssm:ap-southeast-1:824745172792:parameter/tonAirdropSecretKey"}, {"name": "sui_verify_host", "valueFrom": "arn:aws:ssm:ap-southeast-1:824745172792:parameter/sui_verify_host"}, {"name": "sui_verify_auth", "valueFrom": "arn:aws:ssm:ap-southeast-1:824745172792:parameter/sui_verify_auth"}], "dnsServers": [], "dnsSearchDomains": [], "extraHosts": [], "dockerSecurityOptions": [], "dockerLabels": {}, "ulimits": [], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-create-group": "true", "awslogs-group": "/ecs/rewardoor-service", "awslogs-region": "ap-southeast-1", "awslogs-stream-prefix": "ecs"}, "secretOptions": []}, "systemControls": []}, {"name": "aws-otel-collector", "image": "public.ecr.aws/aws-observability/aws-otel-collector:v0.38.1", "cpu": 0, "portMappings": [{"containerPort": 2000, "hostPort": 2000, "protocol": "udp"}, {"containerPort": 4317, "hostPort": 4317, "protocol": "tcp"}, {"containerPort": 8125, "hostPort": 8125, "protocol": "udp"}], "essential": true, "command": ["--config=/etc/ecs/ecs-cloudwatch-xray.yaml"], "environment": [], "mountPoints": [], "volumesFrom": [], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-create-group": "true", "awslogs-group": "/ecs/ecs-aws-otel-sidecar-collector", "awslogs-region": "ap-southeast-1", "awslogs-stream-prefix": "ecs"}}, "systemControls": [], "secrets": [{"name": "AOT_CONFIG_CONTENT", "valueFrom": "arn:aws:ssm:ap-southeast-1:824745172792:parameter/otel-collector-config"}]}], "taskRoleArn": "arn:aws:iam::824745172792:role/ecsTaskRole", "executionRoleArn": "arn:aws:iam::824745172792:role/ecsTaskExecutionRole", "networkMode": "awsvpc", "requiresAttributes": [{"name": "com.amazonaws.ecs.capability.logging-driver.awslogs"}, {"name": "ecs.capability.execution-role-awslogs"}, {"name": "com.amazonaws.ecs.capability.ecr-auth"}, {"name": "com.amazonaws.ecs.capability.docker-remote-api.1.19"}, {"name": "ecs.capability.execution-role-ecr-pull"}, {"name": "ecs.capability.secrets.ssm.environment-variables"}, {"name": "com.amazonaws.ecs.capability.docker-remote-api.1.18"}, {"name": "ecs.capability.task-eni"}, {"name": "com.amazonaws.ecs.capability.docker-remote-api.1.29"}], "compatibilities": ["EC2", "FARGATE"], "requiresCompatibilities": ["EC2"], "cpu": "8192", "memory": "16384", "runtimePlatform": {"cpuArchitecture": "X86_64", "operatingSystemFamily": "LINUX"}, "tags": [{"key": "ecs:taskDefinition:createdFrom", "value": "ecs-console-v2"}]}