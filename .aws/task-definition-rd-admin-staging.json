{"family": "rd-admin-staging", "containerDefinitions": [{"name": "rd-admin-staging", "image": "824745172792.dkr.ecr.ap-southeast-1.amazonaws.com/rd-admin-staging:latest", "cpu": 0, "portMappings": [{"name": "rd-admin-staging-8090-tcp", "containerPort": 8090, "hostPort": 8090, "protocol": "tcp", "appProtocol": "http"}], "essential": true, "environment": [{"name": "contract_caller_url", "value": ""}, {"name": "contract_caller_secret", "value": ""}], "secrets": [{"name": "r2_secret_key", "valueFrom": "arn:aws:ssm:ap-southeast-1:824745172792:parameter/r2Secret"}, {"name": "r2_access_key", "valueFrom": "arn:aws:ssm:ap-southeast-1:824745172792:parameter/r2AccessKey"}, {"name": "ton_airdrop_secret_key", "valueFrom": "arn:aws:ssm:ap-southeast-1:824745172792:parameter/testTonAirdropSecretKey"}, {"name": "sui_verify_host", "valueFrom": "arn:aws:ssm:ap-southeast-1:824745172792:parameter/sui_verify_host"}, {"name": "sui_verify_auth", "valueFrom": "arn:aws:ssm:ap-southeast-1:824745172792:parameter/sui_verify_auth"}], "environmentFiles": [], "mountPoints": [], "volumesFrom": [], "ulimits": [], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/rd-admin-staging", "mode": "non-blocking", "awslogs-create-group": "true", "max-buffer-size": "25m", "awslogs-region": "ap-southeast-1", "awslogs-stream-prefix": "ecs"}, "secretOptions": []}, "systemControls": []}], "taskRoleArn": "arn:aws:iam::824745172792:role/ecsTaskRole", "executionRoleArn": "arn:aws:iam::824745172792:role/ecsTaskExecutionRole", "networkMode": "awsvpc", "revision": 1, "volumes": [], "status": "ACTIVE", "requiresAttributes": [{"name": "com.amazonaws.ecs.capability.logging-driver.awslogs"}, {"name": "ecs.capability.execution-role-awslogs"}, {"name": "com.amazonaws.ecs.capability.ecr-auth"}, {"name": "com.amazonaws.ecs.capability.docker-remote-api.1.19"}, {"name": "com.amazonaws.ecs.capability.docker-remote-api.1.28"}, {"name": "com.amazonaws.ecs.capability.task-iam-role"}, {"name": "ecs.capability.execution-role-ecr-pull"}, {"name": "com.amazonaws.ecs.capability.docker-remote-api.1.18"}, {"name": "ecs.capability.task-eni"}, {"name": "com.amazonaws.ecs.capability.docker-remote-api.1.29"}], "placementConstraints": [], "compatibilities": ["EC2", "FARGATE"], "requiresCompatibilities": ["FARGATE"], "cpu": "2048", "memory": "4096", "runtimePlatform": {"cpuArchitecture": "X86_64", "operatingSystemFamily": "LINUX"}, "registeredAt": "2024-11-05T14:39:43.179Z", "registeredBy": "arn:aws:iam::824745172792:root", "tags": []}