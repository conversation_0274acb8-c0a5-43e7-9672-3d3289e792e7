<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>TBook</title>
</head>
<body>
<h3 id="msg">Redirecting……</h3>
<script>
    const url = new URL(window.location.href)
    let hs = url.host
    let authResult
    if (url.searchParams['tgAuthResult']) {
        authResult = url.searchParams['tgAuthResult']
    } else {
        authResult = url.hash.split('=')[1]
    }
    const segments = url.pathname.split("/")
    const projectName = segments[segments.length - 1]

    location.href = `https://${projectName}.tbook.com/tg_callback?tgAuthResult=${authResult}`
</script>
</body>
</html>