<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>TBook</title>
</head>
<body>
<h3 id="msg">Redirecting……</h3>
<script>
    const url = new URL(window.location.href)
    let hs = url.host
    let authResult
    if (url.searchParams['tgAuthResult']) {
        authResult = url.searchParams['tgAuthResult']
    } else {
        authResult = url.hash.split('=')[1]
    }
    const data = new URLSearchParams()
    data.append('originAuthResult', authResult)
    fetch(`https://${hs}/tg/callback`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
        },
        credentials: 'include',
        body: data
    }).then(r => r.json())
        .then(d => {
            if (d.code != 200) {
                alert(d.message)
                document.querySelector('#msg').innerText = d.message
                return
            }
            document.querySelector('#msg').innerText = 'Login success, Please close this page.'
            window.close()
        })
</script>
</body>
</html>