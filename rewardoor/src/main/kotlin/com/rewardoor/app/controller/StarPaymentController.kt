package com.rewardoor.app.controller

import com.pengrad.telegrambot.TelegramBot
import com.pengrad.telegrambot.model.request.LabeledPrice
import com.pengrad.telegrambot.request.CreateInvoiceLink
import com.pengrad.telegrambot.request.SendInvoice
import com.rewardoor.app.services.RetroactiveService
import com.rewardoor.app.services.StarPaymentService
import com.rewardoor.app.services.TelegramService
import com.rewardoor.app.services.UserService
import org.springframework.beans.factory.annotation.Value
import org.springframework.data.redis.core.StringRedisTemplate
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.web.bind.annotation.*

@RestController
@RequestMapping("/starPayment")
class StarPaymentController(
    private val starPaymentService: StarPaymentService,
    private val userService: UserService,
    private val telegramService: TelegramService,
    private val redisTemplate: StringRedisTemplate,
    private val retroactiveService: RetroactiveService,
    @Value("\${tg.mini_app.token}") private val tgBotToken: String,
) {
    private val logger = mu.KotlinLogging.logger {}
    private val bot = TelegramBot(tgBotToken)

    @PostMapping("/purchaseCompensateCard")
    fun createInvoice(@RequestBody req: CompensateBuyRequest): Any {
        val principal = SecurityContextHolder.getContext().authentication
        val user = userService.getUserByPrincipal(principal.principal.toString())!!

        val unitPrice = redisTemplate.opsForValue().get("retroactive_card_price")?.toIntOrNull() ?: 60
        val invoice = starPaymentService.createPaymentInvoice(user.userId, req.amount)
        val createLink = CreateInvoiceLink("Retroactive Card", "TBook Retroactive Card", invoice.payload, "XTR",
            LabeledPrice("Retroactive Card", req.amount * unitPrice)
        )
        try {
            val link = bot.execute(createLink)
            logger.info { "Create Invoice link succeed: $link" }
            return mapOf(
                "code" to 200,
                "message" to "Success",
                "link" to link.result()
            )
        } catch (e: Exception) {
            logger.error(e) { "Failed to create invoice link" }
            return mapOf(
                "code" to 500,
                "message" to "Failed to create invoice link"
            )
        }
    }

    @PostMapping("/purchaseCompensateCard/bot")
    fun createInvoiceInBot(@RequestBody req: CompensateBuyRequest): Any {
        val principal = SecurityContextHolder.getContext().authentication
        val user = userService.getUserByPrincipal(principal.principal.toString())!!
        val tgUser = telegramService.getUserInfo(user.userId)
            ?: return mapOf(
                "code" to 400,
                "message" to "User don't have telegram account"
            )
        val unitPrice = redisTemplate.opsForValue().get("retroactive_card_price")?.toIntOrNull() ?: 60
        val invoice = starPaymentService.createPaymentInvoice(user.userId, req.amount)
        bot.execute(SendInvoice(tgUser.tgId, "Retroactive Card", "TBook Retroactive Card", invoice.payload, "XTR",
            LabeledPrice("Retroactive Card", req.amount * unitPrice)
        ))
        return mapOf(
            "code" to 200,
            "message" to "Success"
        )
    }

    @GetMapping("/purchaseHistory")
    fun getPurchaseHistory(): Any {
        val principal = SecurityContextHolder.getContext().authentication
        val user = userService.getUserByPrincipal(principal.principal.toString())!!
        val payments = starPaymentService.getPaymentsByUserId(user.userId)
        return mapOf(
            "code" to 200,
            "message" to "Success",
            "payments" to payments
        )
    }

    // @PostMapping("/consumeTest")
    fun consumeTest(@RequestBody req: CompensateBuyRequest): Any {
        val principal = SecurityContextHolder.getContext().authentication
        val user = userService.getUserByPrincipal(principal.principal.toString())!!
        val invoice = retroactiveService.consumeRetroactiveCard(user.userId, req.amount, "")
        return mapOf(
            "code" to 200,
            "message" to "Success",
            "invoice" to invoice,
        )
    }
}

class CompensateBuyRequest(
    val amount: Int
)
