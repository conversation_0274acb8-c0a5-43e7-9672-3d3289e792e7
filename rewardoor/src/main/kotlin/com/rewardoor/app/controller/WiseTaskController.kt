package com.rewardoor.app.controller

import com.rewardoor.app.services.TelegramService
import com.rewardoor.app.services.UserService
import com.rewardoor.app.services.WiseTaskService
import com.rewardoor.model.User
import com.rewardoor.model.WiseScoreShareType
import org.springframework.beans.factory.annotation.Value
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.web.bind.annotation.*

@RestController
@RequestMapping("/wise-task")
class WiseTaskController(
    private val userService: UserService,
    private val taskService: WiseTaskService,
    private val telegramService: TelegramService,
    @Value("\${wise_task.group}") private val wiseTaskGroup: String,
    @Value("\${wise_task.channel}") private val wiseTaskChannel: String) {

    @PostMapping("/check-task")
    fun checkTask(req: CheckTaskRequest): CheckTaskResponse {
        val principal = SecurityContextHolder.getContext().authentication.principal.toString()
        val user = userService.getUserByPrincipal(principal)!!
        if (req.taskName.startsWith("tg:premium")) {
            return checkPremiumTask(req, user)
        }

        if (req.taskName.startsWith("join:")) {
            return checkJoinTask(req, user)
        }

        if (req.taskName.startsWith("boost")) {
            return checkBoostTask(req, user)
        }

        if (!req.taskName.startsWith("share:")) {
            return CheckTaskResponse(req.taskName, false)
        }
        val segs = req.taskName.split(":")
        if (segs.size < 3) {
            return CheckTaskResponse(req.taskName, false)
        }
        val type = segs[2].toIntOrNull() ?: return CheckTaskResponse(req.taskName, false)

        val name = WiseScoreShareType.fromValue(type).toTaskName()
        taskService.addTask(user.userId, name, 500)
        return CheckTaskResponse(req.taskName, true)
    }

    fun checkBoostTask(req: CheckTaskRequest, user: User): CheckTaskResponse {
        val boost = telegramService.verifyUserBoostInChannel(user.userId, "@$wiseTaskChannel")
        if (boost) {
            taskService.addTask(user.userId, req.taskName, 2000)
        }
        return CheckTaskResponse(req.taskName, boost)
    }

    fun checkPremiumTask(req: CheckTaskRequest, user: User): CheckTaskResponse {
        val premium = telegramService.getUserTgInfo(user.userId)?.isPremium?:false
        if (premium) {
            taskService.addTask(user.userId, req.taskName, 1000)
        }
        return CheckTaskResponse(req.taskName, premium)
    }

    fun checkJoinTask(req: CheckTaskRequest, user: User): CheckTaskResponse {
        val task = req.taskName.substring(5)
        val result = when (task) {
            "group:tb" -> {
                telegramService.verifyUserInChannel(user.userId, "@$wiseTaskGroup")
            }
            "channel:tb" -> {
                telegramService.verifyUserInChannel(user.userId, "@$wiseTaskChannel")
            }
            else -> {
                return CheckTaskResponse(req.taskName, false)
            }
        }
        if (result) {
            taskService.addTask(user.userId, req.taskName, 2000)
        }
        return CheckTaskResponse(req.taskName, result)
    }

    @GetMapping("/finished-tasks")
    fun getFinishedTasks(): List<String> {
        val principal = SecurityContextHolder.getContext().authentication.principal.toString()
        val user = userService.getUserByPrincipal(principal)!!
        return taskService.getUserTasks(user.userId)
    }
}

class CheckTaskRequest(
    val taskName: String
)

class CheckTaskResponse(
    val taskName: String,
    val finished: Boolean
)