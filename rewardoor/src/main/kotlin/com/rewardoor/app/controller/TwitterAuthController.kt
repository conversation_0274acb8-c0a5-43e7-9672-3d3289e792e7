package com.rewardoor.app.controller

import com.fasterxml.jackson.databind.ObjectMapper
import com.github.scribejava.core.oauth.AccessTokenRequestParams
import com.rewardoor.app.auth.AddressAuthentication
import com.rewardoor.app.auth.Jwt
import com.rewardoor.app.dao.UserRepository
import com.rewardoor.app.dto.SocialAccountBound
import com.rewardoor.app.dto.TwitterInfoFromUrlRequest
import com.rewardoor.app.services.DiscordService
import com.rewardoor.app.services.TelegramService
import com.rewardoor.app.services.UserService
import com.rewardoor.app.services.UserTwitterService
import com.rewardoor.app.utils.isDuplicate
import com.rewardoor.model.PassportAccountPair
import com.rewardoor.model.PassportAccounts
import com.rewardoor.model.UserTwitterInfo
import com.twitter.clientlib.TwitterCredentialsBearer
import com.twitter.clientlib.TwitterCredentialsOAuth2
import com.twitter.clientlib.api.TwitterApi
import com.twitter.clientlib.auth.TwitterOAuth20Service
import jakarta.servlet.http.HttpServletRequest
import org.apache.commons.lang3.RandomStringUtils
import org.apache.http.client.utils.URIBuilder
import org.springframework.beans.factory.annotation.Value
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.security.access.annotation.Secured
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.web.bind.annotation.*
import org.springframework.web.server.ResponseStatusException
import java.net.URI
import java.time.format.DateTimeFormatter


@RestController()
@RequestMapping("/twitter")
class TwitterAuthController(
    @Value("\${twitter.api.id}") val clientId: String,
    @Value("\${twitter.api.secret}") val clientSecret: String,
    @Value("\${twitter.api.bearer}") val bearerToken: String,
    val userTwitterService: UserTwitterService,
    val userService: UserService,
    val dcService: DiscordService,
    val tgService: TelegramService,
    private val userRepository: UserRepository
) {
    private val mapper = ObjectMapper()
    private val logger = mu.KotlinLogging.logger {}
    private val defaultScope = "tweet.read users.read follows.read space.read like.read list.read"

    @GetMapping("auth")
    @Secured
    fun twitterAuth(request: HttpServletRequest): Map<String, String> {
        val redirectUri = getCallbackUrl(request.getHeader("referer"))
        val principal = SecurityContextHolder.getContext().authentication as AddressAuthentication
        val challenge = RandomStringUtils.random(10, true, true)
        val state = RandomStringUtils.random(10, true, true)
        val uri = URIBuilder().setScheme("https").setHost("twitter.com").setPath("/i/oauth2/authorize")
            .addParameter("response_type", "code")
            .addParameter("client_id", clientId)
            .addParameter("redirect_uri", redirectUri)
            .addParameter("scope", defaultScope)
            .addParameter("state", state)
            .addParameter("code_challenge", challenge)
            .addParameter("code_challenge_method", "plain")
            .build()
        userTwitterService.addStateChallengeCache(state, challenge)
        return mapOf("url" to uri.toString())
    }

    @PostMapping("callback")
    fun callback(
        @RequestParam("state") state: String,
        @RequestParam("code") code: String,
        request: HttpServletRequest
    ): Any {
        logger.info { "Twitter callback, state: $state, code: $code" }
        val challenge = userTwitterService.getChallengeByState(state)
        if (challenge == null) {
            logger.info { "Invalid state: $state" }
            throw ResponseStatusException(HttpStatus.BAD_REQUEST, "Invalid state")
        }
        val principal = SecurityContextHolder.getContext().authentication as AddressAuthentication
        val userInfo = userService.getUserByPrincipal(principal.principal)!!
        val utInfo = UserTwitterInfo(userId = userInfo.userId)
        val redirectUri = getCallbackUrl(request.getHeader("referer"))
        val (user, token) = try {
            val param = AccessTokenRequestParams.create(code).pkceCodeVerifier(challenge)
                .scope(defaultScope)
            val twitter20Service = TwitterOAuth20Service(clientId, clientSecret, redirectUri, defaultScope)
            val token = twitter20Service.getAccessToken(param)

            val credential = TwitterCredentialsOAuth2(clientId, clientSecret, token.accessToken, token.refreshToken)
            val apiInstance = TwitterApi(credential)

            val user = apiInstance.users().findMyUser().execute()
            user to token
        } catch (e: Exception) {
            logger.error(e) { "fetch Twitter account info fail" }
            return mapOf("code" to 401, "message" to "Get Twitter account info fail")
        }
        with(utInfo) {
            accessToken = token.accessToken
            refreshToken = token.refreshToken ?: ""
            twitterScreenName = user.data?.name.toString()
            twitterName = user.data?.username.toString()
            twitterId = user.data?.id.toString()
            connected = true
        }
        return try {
            userTwitterService.createTwitterUserInfo(utInfo)
            mapOf(
                "code" to 200,
                "socialName" to utInfo.twitterName,
                "info" to userTwitterService.getUserInfo(principal.userId)
            )
        } catch (e: Exception) {
            if (e.isDuplicate()) {
                logger.error(e) { "Twitter account already connected" }
                val principal = SecurityContextHolder.getContext().authentication.principal.toString()
                val addressUser = userService.getUserByPrincipal(principal)!!
                val passportA = PassportAccounts(
                    userId = addressUser.userId,
                    evmAddress = addressUser.evm.evmWallet ?: "",
                    tonAddress = addressUser.ton.tonWallet ?: "",
                    suiAddress = addressUser.suiAddress,
                    twitterName = addressUser.twitterName,
                    dcName = addressUser.dcName,
                    tgName = addressUser.tgName
                )
                val curUser = userTwitterService.getUserInfoByTwitterId(utInfo.twitterId)!!
                val twitterUser = userService.getUserById(curUser.userId)!!
                val address = userService.getEvmUserById(curUser.userId)?.evmWallet
                val passportB = PassportAccounts(
                    userId = twitterUser.userId,
                    evmAddress = twitterUser.evm.evmWallet ?: "",
                    tonAddress = twitterUser.ton.tonWallet ?: "",
                    suiAddress = twitterUser.suiAddress,
                    twitterName = utInfo.twitterName,
                    dcName = twitterUser.dcName,
                    tgName = twitterUser.tgName
                )
                val isAbleToMerge = isPassportsAbleToMerge(passportA, passportB)
                if (!isAbleToMerge) { // The user's Twitter account has already been linked to an address.
                    SocialAccountBound(
                        "Twitter account already connected",
                        utInfo.twitterName, address ?: "", passportA = passportA, passportB = passportB
                    )
                } else { // The user's Twitter account has not been linked to an address,merge passport.
                    val passportA = PassportAccounts(
                        userId = userInfo.userId,
                        evmAddress = userInfo.evm.evmWallet ?: "",
                        tonAddress = userInfo.ton.tonWallet ?: "",
                        suiAddress = userInfo.suiAddress,
                        twitterName = userInfo.twitterName,
                        dcName = userInfo.dcName,
                        tgName = userInfo.tgName
                    )
                    val twitterId = user.data?.id.toString()
                    val twitterUserId = userTwitterService.getUserInfoByTwitterId(twitterId)!!.userId
                    val tUser = userService.getUserById(twitterUserId)!!
                    val passportB = PassportAccounts(
                        userId = twitterUserId,
                        evmAddress = tUser.evm.evmWallet ?: "",
                        tonAddress = tUser.ton.tonWallet ?: "",
                        suiAddress = tUser.suiAddress,
                        twitterName = userTwitterService.getUserInfoByTwitterId(twitterId)!!.twitterName,
                        dcName = tUser.dcName,
                        tgName = tUser.tgName
                    )
                    mapOf(
                        "code" to 400,
                        "passportA" to passportA,
                        "passportB" to passportB
                    )
                }
            } else {
                logger.error(e) { "Database error" }
                mapOf("code" to 500, "message" to "Internal error")
            }
        }
    }

    @PostMapping("mergePassport")
    fun mergePassport(
        @RequestBody passports: PassportAccountPair
    ): Any {
        val principal = SecurityContextHolder.getContext().authentication as AddressAuthentication
        val curUser = userService.getUserById(principal.userId)!!
        val passportA = passports.passportA!!
        val passportB = passports.passportB!!
        if (curUser.userId != passportA.userId) {
            return ResponseEntity.ok(
                mapOf(
                    "code" to 400,
                    "message" to "merge failed.Passport A is not your current passport.",
                    "address" to passports.passportA?.evmAddress,
                )
            )
        }
        val isAbleToMerge = isPassportsAbleToMerge(passportA, passportB)
        if (!isAbleToMerge) {
            return ResponseEntity.ok(
                mapOf(
                    "code" to 400,
                    "message" to "merge failed.There are conflicts between these two passports. You can not merge for now.",
                    "passportA" to passportA,
                    "passportB" to passportB
                )
            )
        }
//        val addressUserId = if (passportA.evmAddress != "") passportA.userId else passportB.userId
        var addressUserId = 0L
        var basedPassport = "A"
        if (passportA.tonAddress != "") {
            addressUserId = passportA.userId
        } else if (passportB.tonAddress != "") {
            addressUserId = passportB.userId
            basedPassport = "B"
        } else if (passportA.suiAddress != "") {
            addressUserId = passportA.userId
        } else if (passportB.suiAddress != "") {
            addressUserId = passportB.userId
            basedPassport = "B"
        } else if (passportA.evmAddress != "") {
            addressUserId = passportA.userId
        } else if (passportB.evmAddress != "") {
            addressUserId = passportB.userId
            basedPassport = "B"
        } else if (passportA.twitterName != "") {
            addressUserId = passportA.userId
        } else if (passportB.twitterName != "") {
            addressUserId = passportB.userId
            basedPassport = "B"
        }
//        if (addressUserId == 0L) {
//            return ResponseEntity.ok(
//                mapOf(
//                    "code" to 400,
//                    "message" to "merge failed.Neither of the two passports has an EVM or TON address.",
//                    "passportA" to passportA,
//                    "passportB" to passportB
//                )
//            )
//        }
        val (oldUserId, newUserId) = if (basedPassport == "A") Pair(passportB.userId, passportA.userId) else Pair(
            passportA.userId,
            passportB.userId
        )
        try {
            userRepository.updateEvmUserId(oldUserId, newUserId)
            userRepository.updateSuiUserId(oldUserId, newUserId)
            userService.updateTwiUserId(oldUserId, newUserId)
            dcService.updateDcUserId(oldUserId, newUserId)
            tgService.updateTgUserId(oldUserId, newUserId)
            userService.updateTonUserId(oldUserId, newUserId)
            userService.transVerifiedCredentialsAndRewards(oldUserId, newUserId)
            val mergedUser = userService.getUserById(newUserId)
            val cookie = Jwt.buildCookie(newUserId)
            val setCk = cookie.toString().split(";")[0] + ";"
            return if (principal.userId != newUserId) {
                ResponseEntity.ok().header("Set-Cookie", cookie.toString()).body(
                    mapOf(
                        "code" to 200,
                        "mergedUser" to mergedUser
//                        "ck" to setCk
                    )
                )
            } else {
                ResponseEntity.ok(
                    mapOf(
                        "code" to 200,
                        "mergedUser" to mergedUser
                    )
                )
            }

        } catch (e: Exception) {
            print("exception : $e")
            return ResponseEntity.ok(
                mapOf(
                    "code" to 400,
                    "message" to "merge failed",
                    "passportA" to passportA,
                    "passportB" to passportB
                )
            )
        }
    }

    @PostMapping("merge")
    fun mergeAccounts(
        @RequestParam("twitterId") twitterId: String,
        @RequestParam("address") address: String
    ): Any {
        val principal = SecurityContextHolder.getContext().authentication as AddressAuthentication
        val utInfo = userTwitterService.getUserInfoByTwitterId(twitterId)!!
        val twitterUser = userService.getUserById(principal.userId)
//        if (mergeType == "1") {  // twitter login, merge evm address
        val addressUser = userService.getUserByAddress(address)!!
        val addressUserId = addressUser.userId
        val isAbleToMerge = isSocialAndTonAbleToMerge(utInfo.userId, addressUserId)
        println(" cur userId is " + principal.userId + " address User Id is" + addressUserId)
        if (!isAbleToMerge) {
            return ResponseEntity.ok(
                mapOf(
                    "code" to 400,
                    "message" to "merge failed.There are conflicts between these two passports. You can not merge for now.",
                    "address" to address,
                )
            )
        }
        if (utInfo != null && isAbleToMerge) {
            userService.updateTwitterUserId(utInfo, addressUserId)
            dcService.updateDcUserId(utInfo.userId, addressUserId)
            tgService.updateTgUserId(utInfo.userId, addressUserId)
            userService.updateTonUserId(utInfo.userId, addressUserId)
            userService.transVerifiedCredentialsAndRewards(utInfo.userId, addressUserId)
//            if (twitterUser != null) {
//                userRepo.markMergeDeletedUser(principal.userId)
//            }
        }
        val mergeTwitterInfo = userTwitterService.getUserInfoByTwitterId(twitterId)!!
        val cookie = Jwt.buildCookie(mergeTwitterInfo.userId)
        val setCk = cookie.toString().split(";")[0] + ";"
        return if (mergeTwitterInfo.userId == addressUserId) {
            ResponseEntity.ok().header("Set-Cookie", cookie.toString()).body(
                mapOf(
                    "code" to 200,
                    "mergeTwitterInfo" to mergeTwitterInfo,
                    "address" to address
//                    "ck" to setCk
                )
            )
        } else {
            ResponseEntity.ok(
                mapOf(
                    "code" to 400,
                    "message" to "merge failed",
                    "address" to address
//                    "ck" to setCk
                )
            )
        }
//        }
//        else if (mergeType == "2") { //twitter login, merge ton address
//            val tonUser = userService.getTonWalletByAddress(address)
//            if (tonUser == null) {
//                return ResponseEntity.ok(
//                    mapOf(
//                        "code" to 400,
//                        "message" to "merge failed,invalid ton address",
//                        "address" to address
//                    )
//                )
//            } else {
//                val tonUserId = tonUser.userId
//                val isAbleToMerge = isSocialAndTonAbleToMerge(utInfo.userId, tonUserId)
//                if (!isAbleToMerge) {
//                    return ResponseEntity.ok(
//                        mapOf(
//                            "code" to 400,
//                            "message" to "merge failed.There are conflicts between these two passports. You can not merge for now.",
//                            "address" to address,
//                        )
//                    )
//                }
//                if (utInfo != null && isAbleToMerge) {
//                    userService.updateTwitterUserId(utInfo, tonUserId)
//                    dcService.updateDcUserId(utInfo.userId, tonUserId)
//                    tgService.updateTgUserId(utInfo.userId, tonUserId)
//                    transVerifiedCredentialsAndRewards(utInfo.userId, tonUserId)
//                    val mergeTwitterInfo = userTwitterService.getUserInfoByTwitterId(twitterId)!!
//                    val cookie = Jwt.buildCookie(mergeTwitterInfo.userId)
//                    return if (mergeTwitterInfo.userId == tonUserId) {
//                        ResponseEntity.ok().header("Set-Cookie", cookie.toString()).body(
//                            mapOf(
//                                "code" to 200,
//                                "mergeTwitterInfo" to mergeTwitterInfo,
//                                "address" to address,
//                                "cookie" to cookie.toString()
//                            )
//                        )
//                    } else {
//                        ResponseEntity.ok(
//                            mapOf(
//                                "code" to 400,
//                                "message" to "merge failed",
//                                "address" to address,
//                                "cookie" to cookie.toString()
//                            )
//                        )
//                    }
//                }
//            }
//        }
//        return ResponseEntity.ok(
//            mapOf(
//                "code" to 400,
//                "message" to "unknown merge type",
//                "address" to address,
//            )
//        )
    }

    @PostMapping("unbind")
    fun unbindAccount(
        @RequestParam("twitterId") twitterId: String
    ): Int {
        val principal = SecurityContextHolder.getContext().authentication as AddressAuthentication
        val addressUserId = principal.userId
        val utInfo = userTwitterService.getUserInfoByTwitterId(twitterId)!!
        val curUserId = utInfo.userId
        if (utInfo != null) {
            userService.unbindDcUser(curUserId)
            userService.unbindTgUser(curUserId)
            return userService.unbindTwitterUser(twitterId)
        }
        return 0
    }

    @PostMapping("checkAction")
    fun checkAction(
        @RequestParam("action") action: String,
        @RequestParam("target") target: String
    ): Map<String, Any> {
        val principal = SecurityContextHolder.getContext().authentication as AddressAuthentication
        val utInfo = userTwitterService.getUserInfo(principal.userId)!!
        if (!utInfo.connected) {
            return mapOf("code" to 401, "message" to "Twitter account is not connected")
        }
        val credential = TwitterCredentialsBearer(bearerToken)
        userTwitterService.checkUserActivity(utInfo, credential, action, target)
        return emptyMap()
    }

    @PostMapping("getInfo")
    fun getInfoByUrl(@RequestBody req: TwitterInfoFromUrlRequest): Map<String, Any?> {
        val url = URI(req.url)
        val segments = url.path.split("/")
        if (url.path.contains("spaces")) {
            val apiInstance = TwitterApi(TwitterCredentialsBearer(bearerToken))

            val response = apiInstance.spaces().findSpaceById(segments[3])
                .spaceFields(setOf("scheduled_start,started_at"))
                .expansions(setOf("creator_id"))
                .execute()
            return mapOf(
                "userName" to response.includes?.users?.firstOrNull()?.name,
                "link" to req.url,
                "startAt" to response.data?.startedAt?.format(DateTimeFormatter.ofPattern("MM dd"))
            )
        } else {
            return mapOf("userName" to segments[1])
        }
    }

    private fun getCallbackUrl(referer: String): String {
        val requestURI = URI(referer)
        return URIBuilder().setScheme("https")
            .setHost(requestURI.host)
            .setPath("twitter/callback").build().toString()
    }

    private fun isSocialAndTonAbleToMerge(userId: Long, addressUserId: Long): Boolean {
        val dcUser = dcService.getUserDcInfo(userId)
        val tgUser = tgService.getUserInfo(userId)
        val tonUser = userService.getTonWalletByUserId(userId)
        val addressDcUser = dcService.getUserDcInfo(addressUserId)
        val addressTgUser = tgService.getUserInfo(addressUserId)
        val addressTonUser = userService.getTonWalletByUserId(addressUserId)
        return when {
            (dcUser != null && addressDcUser != null) || (tgUser != null && addressTgUser != null) || (tonUser != null && addressTonUser != null) -> false
            else -> true
        }
    }

    private fun isPassportsAbleToMerge(passportA: PassportAccounts, passportB: PassportAccounts): Boolean {
        return when {
            (passportA.evmAddress != "" && passportB.evmAddress != "") || (passportA.tonAddress != "" && passportB.tonAddress != "")
                    || (passportA.twitterName != "" && passportB.twitterName != "") || (passportA.dcName != "" && passportB.dcName != "")
                    || (passportA.tgName != "" && passportB.tgName != "") -> false

            else -> true
        }
    }

    private fun isSocialAbleToMerge(userId: Long, addressUserId: Long): Boolean {
        val dcUser = dcService.getUserDcInfo(userId)
        val tgUser = tgService.getUserInfo(userId)
        val addressDcUser = dcService.getUserDcInfo(addressUserId)
        val addressTgUser = tgService.getUserInfo(addressUserId)
        return when {
//            dcUser != null && addressDcUser == null && tgUser != null && addressTgUser == null -> true
//            dcUser != null && addressDcUser == null && tgUser == null && addressTgUser == null -> true
//            tgUser != null && addressTgUser == null && dcUser == null && addressDcUser == null -> true
//            tgUser == null && addressTgUser == null && dcUser == null && addressDcUser == null -> true
//            else -> false
            (dcUser != null && addressDcUser != null) || (tgUser != null && addressTgUser != null) -> false
            else -> true
        }
    }
}