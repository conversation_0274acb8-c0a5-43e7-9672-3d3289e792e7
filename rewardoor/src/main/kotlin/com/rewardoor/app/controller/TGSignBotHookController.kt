package com.rewardoor.app.controller

import com.pengrad.telegrambot.TelegramBot
import com.pengrad.telegrambot.impl.TelegramBotClient
import com.pengrad.telegrambot.model.ChatMember
import com.pengrad.telegrambot.model.ChatMemberUpdated
import com.pengrad.telegrambot.request.BaseRequest
import com.pengrad.telegrambot.request.ExportChatInviteLink
import com.pengrad.telegrambot.request.SetWebhook
import com.pengrad.telegrambot.response.StringResponse
import com.pengrad.telegrambot.utility.BotUtils
import com.rewardoor.app.services.TelegramService
import com.rewardoor.model.TgPrivateGroupInfo
import org.springframework.beans.factory.annotation.Value
import org.springframework.boot.context.event.ApplicationReadyEvent
import org.springframework.context.ApplicationListener
import org.springframework.web.bind.annotation.*
import org.springframework.web.server.ResponseStatusException


@RestController
@RequestMapping("/tg_sign")
class TGSignBotHookController(
    @Value("\${tg.bot_token}") private val tgBotToken: String,
    @Value("\${tg.sign_hook_url}") private val webhookUrl: String,
    private val telegramService: TelegramService,
): ApplicationListener<ApplicationReadyEvent> {
    private val log = mu.KotlinLogging.logger { }
    private val bot = TelegramBot(tgBotToken)
    private val secretToken = "d45r5ae9"
    private val botId = tgBotToken.split(":")[0].toLong()

    override fun onApplicationEvent(event: ApplicationReadyEvent) {
        //bot.execute(SetWebhook().secretToken(secretToken).url(webhookUrl))
    }

    @PostMapping("webhook")
    fun webhook(@RequestBody updateString: String,
                @RequestHeader("X-Telegram-Bot-Api-Secret-Token") secretToken: String) {
        if (secretToken != this.secretToken) {
            log.warn("unknown secret token: {}", secretToken)
            throw ResponseStatusException(org.springframework.http.HttpStatus.FORBIDDEN, "Invalid secret token")
        }
        log.info("update received: {}", updateString)
        try {
            val update = BotUtils.parseUpdate(updateString)

            if (update.myChatMember() != null) {
                handleMyChatMember(update.myChatMember())
            } else {
                log.info { "skip non-myChatMember update" }
            }

        } catch (ex: Exception) {
            log.error(ex) { "handle message failed" }
        }
    }

    fun handleMyChatMember(myChatMember: ChatMemberUpdated) {
        val chatId = myChatMember.chat()?.id() ?: return
        val status = myChatMember.newChatMember()?.status() ?: return
        if (myChatMember.newChatMember().user()?.id() != botId) {
            log.info { "skip non-bot newChatMember update" }
            return
        }
        if (status == ChatMember.Status.kicked || status == ChatMember.Status.left) {
            telegramService.getTgPrivateGroupInfoByTgId(chatId)?.let {
                telegramService.updateTgPrivateGroupInfo(it.copy(status = status.toString(), operationAt = myChatMember.date().toLong()))
            }
            return
        } else if (status != ChatMember.Status.administrator && status != ChatMember.Status.creator) {
            log.info { "skip non-administrator newChatMember update" }
            return
        }

        val inviteLink = bot.execute(ExportChatInviteLink(chatId)).result()
        log.info { "invite link: $inviteLink" }
        val hash = TelegramService.extractPrivateLink(inviteLink)
        val current = telegramService.getTgPrivateGroupInfoByTgId(chatId)
        val opAt = myChatMember.date().toLong()
        if (current != null) {
            telegramService.updateTgPrivateGroupInfo(current.copy(
                privateHash = hash.orEmpty(),
                status = status.toString(),
                operationAt = opAt
            ))
        } else {
            telegramService.addTgPrivateGroupInfo(TgPrivateGroupInfo(chatId, hash.orEmpty(),
                status.toString(), opAt))
        }
    }
}

class CheckChatInvite(hash: String) :
    BaseRequest<CheckChatInvite, StringResponse?>(StringResponse::class.java) {
    init {
        add("hash", hash)
    }
}
