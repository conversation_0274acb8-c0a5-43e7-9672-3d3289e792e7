package com.rewardoor.app.controller

import com.rewardoor.app.services.TelegramService
import com.rewardoor.app.services.UserService
import com.rewardoor.app.services.WiseInviteService
import com.rewardoor.app.services.WiseScoreService
import com.rewardoor.enums.InviteCodeCheckedType
import com.rewardoor.enums.TBookInviteSbtLevel
import com.rewardoor.enums.CombinedSbtInfo
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import org.springframework.core.env.Environment
import java.time.Instant

@RestController
@RequestMapping("/wise-score-invite-sbt")
class WiseScoreInviteSbtController(
    private val wiseInviteService: WiseInviteService,
    private val wiseScoreService: WiseScoreService,
    private val userService: UserService,
    private val telegramService: TelegramService,
    private val env: Environment,
) {
    @GetMapping("my-code")
    fun myInviteCode(): SimpleResponseEntity<WiseInviteSbtCodeResponse> {
        val principal = SecurityContextHolder.getContext().authentication.principal.toString()
        val user = userService.getUserByPrincipal(principal)!!

        val code = wiseInviteService.getOrAddInviteCode(user.userId, 2)

        // Give the sbt_list & user_reward to the stage 1 user
        wiseInviteService.checkAndAddSbtForStag1(user.userId)

        wiseScoreService.addScoreResult(user.userId)

        val inviteeIdsWithDate = wiseInviteService.getInviteesWithDateAll(user.userId)
        val inviteeIdsList = inviteeIdsWithDate.map { it.inviteeId }
        val inviteeTgInfo = telegramService.getUserInfos(inviteeIdsList).associateBy { it.userId }
        val invitees = inviteeIdsWithDate.mapIndexed { index, inviteeWithDate ->
            val inviteAddScore = when {
                index < 3 -> 1000
                index in 3..19 -> 500
                index in 20..49 -> 200
                index in 50..99 -> 100
                else -> 50
            }
            InviteeDto(
                inviteeWithDate.inviteeId,
                inviteeTgInfo[inviteeWithDate.inviteeId]?.firstName ?: "",
                inviteAddScore = inviteAddScore,
                inviteeWithDate.createdAt
            )
        }

        val (activityIds, activityIdMap) = TBookInviteSbtLevel.getActivityIdsByEnv(env)
        val sbts = wiseInviteService.getSbtListByActivityIds(activityIds, userId = user.userId)
        val combinedSbts = TBookInviteSbtLevel.combineSbtInfo(sbts, activityIdMap)

        combinedSbts.forEachIndexed { index, sbt ->
            println("sbt[$index] type[${sbt.claimedType}] - activityId: ${sbt.activityId}, sbtId: ${sbt.sbtId}, activityUrl: ${sbt.activityUrl}, picUrl: ${sbt.picUrl}")
        }

        return SimpleResponseEntity.success(
            "",
            WiseInviteSbtCodeResponse(
                user.userId,
                referralCode = code.inviteCode,
                currentLevel = TBookInviteSbtLevel.getLevelByInvites(invitees.size),
                sbts = combinedSbts,
                invitees = invitees,
            )
        )
    }

    @PostMapping("apply-code")
    fun applyInvite(code: String): SimpleResponse {
        val principal = SecurityContextHolder.getContext().authentication.principal.toString()
        val user = userService.getUserByPrincipal(principal)!!

        return try {
            val result = wiseInviteService.addInviteeStage2(code, 2, user)

            val noInviteMsg = "The invitation code does not exist. \n" + "Please try another one."
            val inviteMySelfMsg = "You've entered your own invitation code.\n" + "Please try another one."
            val inviteRepeatCodeMsg = "You're in TBook! Invite friends now to mint TBook OG SBTs together!"
            val inviteRepeatMsg = "You're in TBook! Invite friends now to mint TBook OG SBTs together!"
            val successMsg = "🌟🌟Successful to submit inviter's code!🌟🌟"

            when (result.codeCheckedType) {
                InviteCodeCheckedType.NO_INVITE -> SimpleResponse.failed(noInviteMsg)
                InviteCodeCheckedType.INVITE_MY_SELF -> SimpleResponse.failed(inviteMySelfMsg)
                InviteCodeCheckedType.INVITE_REPEAT -> SimpleResponse.success(inviteRepeatMsg)
                InviteCodeCheckedType.INVITE_REPEAT_CODE -> SimpleResponse.success(inviteRepeatCodeMsg)
                InviteCodeCheckedType.SUCCESS -> SimpleResponse.success(successMsg)
            }

        } catch (e: IllegalArgumentException) {
            SimpleResponse.failed(e.message ?: "Failed to apply invite code")
        }
    }
}

class WiseInviteSbtCodeResponse(
    val userId: Long,
    val referralCode: String,
    val currentLevel: Int,
    val sbts: List<CombinedSbtInfo>,
    val invitees: List<InviteeDto>,
)
