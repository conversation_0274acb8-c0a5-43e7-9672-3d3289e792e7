package com.rewardoor.app.controller

import com.rewardoor.app.services.ProjectService
import com.rewardoor.app.services.RetroactiveService
import com.rewardoor.app.services.UserService
import com.rewardoor.app.services.WiseScoreUserStreakService
import com.rewardoor.enums.WiseScoreUserStreakSbt
import com.rewardoor.model.SbtSetting
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController
import java.time.Instant

@RestController
@RequestMapping("/wise-score")
class WiseScoreUserStreakController(
    private val userStreakService: WiseScoreUserStreakService,
    private val userService: UserService,
    private val projectService: ProjectService
) {
    @GetMapping("/check-in")
    fun checkIn(): Any? {
        val principal = SecurityContextHolder.getContext().authentication.principal.toString()
        val user = userService.getUserByPrincipal(principal)!!
        val checkCnt = userStreakService.userCheckIn(user.userId)
        if (checkCnt == 0) {
            return mapOf(
                "code" to 400,
                "user" to user.userId,
                "message" to "The user has already checked in today and cannot check in again."
            )
        } else {
            return mapOf(
                "code" to 200,
                "user" to user.userId,
                "message" to "Check in success."
            )
        }
    }

    @GetMapping("/back-check-in")
    fun checkIn(@RequestParam date: Instant): Any? {
        val principal = SecurityContextHolder.getContext().authentication.principal.toString()
        val user = userService.getUserByPrincipal(principal)!!
        val userId = user.userId
        val checkCnt = userStreakService.userBackFillCheckIn(userId, date)
        return when (checkCnt) {
            -1 -> mapOf(
                "code" to 404,
                "user" to userId,
                "message" to "Back-date is not allowed."
            )

            -2 -> mapOf(
                "code" to 404,
                "user" to userId,
                "message" to "User has no retroactive card"
            )

            -3 -> mapOf(
                "code" to 404,
                "user" to userId,
                "message" to "User has insufficient retroactive card"
            )

            0 -> mapOf(
                "code" to 400,
                "user" to userId,
                "message" to "The user has already checked in this day and cannot check in again."
            )

            else -> mapOf(
                "code" to 200,
                "user" to userId,
                "message" to "Make up for a missed sign-in success."
            )
        }
    }

    @GetMapping("/streak-info")
    fun getRecord(): Any? {
        val principal = SecurityContextHolder.getContext().authentication.principal.toString()
        val user = userService.getUserByPrincipal(principal)!!
        val userStreak = userStreakService.addOrUpdateUserStreak(user.userId)
        return mapOf(
            "code" to 200,
            "user" to user.userId,
            "userStreak" to userStreak
        )
    }

    @GetMapping("/streak-sbt")
    fun getWiseScoreUserStreakSbtList(): Any? {
        val principal = SecurityContextHolder.getContext().authentication.principal.toString()
        val user = userService.getUserByPrincipal(principal)!!
        val streakSbts = WiseScoreUserStreakSbt.values().toList()
        val streakSbtSettings = mutableListOf<SbtSetting>()
        for (streakSbt in streakSbts) {
            val sbtId = streakSbt.sbtId
            val sbtSetting = projectService.getSbtSettingById(sbtId, user.userId)
            if (sbtSetting != null) {
                sbtSetting.streakDays = streakSbt.streakDays
                sbtSetting.credentialId = streakSbt.credentialId
                streakSbtSettings.add(sbtSetting)
            }
        }
        return mapOf(
            "code" to 200,
            "user" to user.userId,
            "streakSbtSettings" to streakSbtSettings
        )
    }

    @GetMapping("/reward")
    fun getUserReward(): Any? {
        val principal = SecurityContextHolder.getContext().authentication.principal.toString()
        val user = userService.getUserByPrincipal(principal)!!
        val userStreakRewards = userStreakService.getUserRewards(user.userId)
        return mapOf(
            "code" to 200,
            "user" to user.userId,
            "userStreakRewards" to userStreakRewards
        )
    }

}