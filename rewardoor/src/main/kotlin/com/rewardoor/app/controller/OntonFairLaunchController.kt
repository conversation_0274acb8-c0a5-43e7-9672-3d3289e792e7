package com.rewardoor.app.controller

import com.rewardoor.app.dto.*
import com.rewardoor.app.services.*
import com.rewardoor.model.*
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.web.bind.annotation.*


@RestController
@RequestMapping("onton")
class OntonFairLaunchController(
    val userService: UserService,
    val ontonFairLaunchService: OntonFairLaunchService
) {

    // Controller 接口
    @GetMapping("/basic-info")
    fun getBasicInfo(): SimpleResponseEntity<GetBasicInfoResponse> {
        return try {
            val response = ontonFairLaunchService.getBasicInfo()
            SimpleResponseEntity.success("Get basic info success", response)
        } catch (e: Exception) {
            SimpleResponseEntity.failed("Failed to get basic info: ${e.message}")
        }
    }

    @GetMapping("/rolling-round")
    fun getRollingRound(
        @RequestHeader("Authorization") authorizationKey: String, // 从请求头中读取 Authorization Key
        @RequestParam(required = false) simulateTime: String? = null
    ): SimpleResponseEntity<GetCurrentRoundResponse> {
        return try {
            if (authorizationKey != "tbk_authorization_2dc079390") {
                return SimpleResponseEntity.failed("Invalid Authorization Key")
            }
            val response = ontonFairLaunchService.getCurrentRoundInfo(simulateTime)
            SimpleResponseEntity.success("Get current round success", response)
        } catch (e: Exception) {
            SimpleResponseEntity.failed("Failed to get current round: ${e.message}")
        }
    }


    @GetMapping("/current-round")
    fun getCurrentRound(
        @RequestParam(required = false) simulateTime: String? = null
    ): SimpleResponseEntity<GetCurrentRoundResponse> {
        return try {
            val response = ontonFairLaunchService.getCurrentRoundInfo(simulateTime)
            SimpleResponseEntity.success("Get current round success", response)
        } catch (e: Exception) {
            SimpleResponseEntity.failed("Failed to get current round: ${e.message}")
        }
    }

    class BuyTokenRequest(
        val address: String,
        val currency: Int,
        val amount: Long
    )

    @PostMapping("/user/token-amount")
    fun getUserTokenAmount(@RequestBody buyTokenRequest: BuyTokenRequest): SimpleResponseEntity<Any?> {
        return try {
            val responseSign = ontonFairLaunchService.getUserBuyTokenAmount(
                buyTokenRequest.address,
                buyTokenRequest.currency,
                buyTokenRequest.amount
            )
            if (responseSign == "-1") {
                SimpleResponseEntity.failed("The user has reached the purchase limit.")
            } else if (responseSign == null) {
                SimpleResponseEntity.failed("Failed to generate signature.")
            } else {
                SimpleResponseEntity.success("Get token num success", responseSign)
            }
        } catch (e: Exception) {
            println("exception: ${e.message}")
            SimpleResponseEntity.failed("Failed to get token num")
        }
    }

}