package com.rewardoor.app.controller

import com.rewardoor.app.dto.CampaignReportDto
import com.rewardoor.app.services.ProjectService
import com.rewardoor.app.services.UserService
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestHeader
import org.springframework.web.bind.annotation.RestController

@RestController
class CampaignReportController(
    val projectService: ProjectService,
    val userService: UserService) {

    @PostMapping("/campaign/report")
    fun getReport(@RequestBody campaignReportDto: CampaignReportDto,
                  @RequestHeader("x-auth-token") auth: String): ResponseEntity<String> {
        val projectTokens = projectService.getProjectTokens(campaignReportDto.projectId)
        if (!projectTokens.contains(auth)) {
            return ResponseEntity.status(401).body("Unauthorized")
        }
        projectService.addResult(campaignReportDto, auth)
        return ResponseEntity.ok("[DONE]")
    }
}