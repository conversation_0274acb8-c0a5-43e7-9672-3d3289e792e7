package com.rewardoor.app.controller

import com.amazonaws.services.s3.AmazonS3
import com.amazonaws.services.s3.model.ObjectMetadata
import com.amazonaws.services.s3.model.PutObjectRequest
import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.databind.ObjectMapper
import com.rewardoor.app.dao.ParticipantRepository
import com.rewardoor.app.dto.ContractDto
import com.rewardoor.app.dto.NFTClaimDto
import com.rewardoor.app.dto.NFTGiveawayDto
import com.rewardoor.app.services.*
import com.rewardoor.app.utils.Signs
import com.rewardoor.model.NFT
import com.rewardoor.model.NFTGiveaway
import com.rewardoor.model.NFTMetaData
import mu.KotlinLogging
import org.springframework.beans.factory.annotation.Value
import org.springframework.http.HttpStatus
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.web.bind.annotation.*
import org.springframework.web.client.HttpClientErrorException
import org.springframework.web.server.ResponseStatusException
import java.io.ByteArrayInputStream

@RestController
@RequestMapping("nft")
class NFTController(
    val nftService: NFTService,
    val userService: UserService,
    val idGenerator: GeneratorService,
    val campaignService: CampaignService,
    val participantRepo: ParticipantRepository,
    val credentialGroupService: CredentialGroupService,
    val s3: AmazonS3,
    val userNFTService: UserNFTService,
    val contractService: ContractService,
    @Value("\${manager.private_key}") val privateKey: String,
    private val privilegeCheck: PrivilegeCheck
) {

    private val logger = KotlinLogging.logger {}
    val mapper = ObjectMapper()

    @PostMapping("preCreate")
    fun preCreateNFT(@RequestBody nft: NFT): NFT {
        logger.info { "createNFT : $nft" }
        val principal = SecurityContextHolder.getContext().authentication.principal.toString()
        val nftId = idGenerator.getNewId()
        privilegeCheck.checkUserId(nft.creatorId, principal)

        putMeta("${nftId}/meta", "0", NFTMetaData(nft.name, "Rewardoor", "Rewardoor Badger NFT", nft.coverUrl))
        val user = userService.getUserByPrincipal(principal)!!
        nft.nftId = nftId
        nft.creatorId = user.userId
        nft.chainId = 10
        nft.contract = ""
        return nftService.createNFT(nft)
    }

    @PostMapping("finishCreate")
    fun finishCreateNFT(
        @RequestParam("nftId") nftId: Long,
        @RequestParam("contractAddress") contractAddress: String
    ): NFT {
        val principal = SecurityContextHolder.getContext().authentication.principal.toString()
        val user = userService.getUserByPrincipal(principal)!!
        val nft = nftService.getNFTById(nftId) ?: throw HttpClientErrorException(HttpStatus.NOT_FOUND, "NFT not found")
        if (nft.creatorId != user.userId) {
            throw HttpClientErrorException(HttpStatus.FORBIDDEN, "You are not the creator of this NFT")
        }
        if (nft.contract.isNotEmpty()) {
            throw HttpClientErrorException(HttpStatus.BAD_REQUEST, "NFT is already created with address")
        }
        return nftService.createNFT(nft)
    }

    @GetMapping("/{nftId}")
    fun getNFTById(@PathVariable("nftId") nftId: Long): NFT? {
        return nftService.getNFTById(nftId)
    }

    @GetMapping("/creator/{creatorId}")
    fun getNFTByCreatorId(@PathVariable("creatorId") creatorId: Long): List<NFT> {
        return nftService.getNFTByCreatorId(creatorId)
    }

    @PostMapping("/claim/{nftId}/group/{groupId}")
    fun claimNFT(
        @PathVariable("nftId") nftId: Long,
        @PathVariable("groupId") groupId: Long
    ): NFTClaimDto? {
        val principal = SecurityContextHolder.getContext().authentication.principal.toString()
        val user = userService.getUserByPrincipal(principal) ?: throw ResponseStatusException(
            HttpStatus.NOT_FOUND,
            "User not found"
        )
        val group = credentialGroupService.getCredentialGroupById(groupId) ?: throw ResponseStatusException(
            HttpStatus.NOT_FOUND,
            "Group not found"
        )
        val nft = nftService.getNFTById(nftId) ?: throw ResponseStatusException(HttpStatus.NOT_FOUND, "NFT not found")
        // TODO check if user finished campaign
        println(" group is " + group.id + " campaign is " + group.campaignId + " user is " + user.userId)
        println(" credentials  " + campaignService.getUserCredentialsByCampaign(user.userId, group.campaignId).size)
        val finished =
            campaignService.getUserCredentialsByCampaign(user.userId, group.campaignId).associateBy { it.credentialId }
        for (credential in group.credentialList) {
            if (finished.containsKey(credential.credentialId)) {
                credential.isVerified = 1
            }
        }
        val isGroupVerified =
            campaignService.getIsGroupVerified(group, group.credentialList)
        println(
            " isgroup verified " + isGroupVerified + user.userId + " group id is  " + group.id + " nft id is" + nftId + "  " + userNFTService.isNftClaimAble(
                nftId,
                groupId
            )
        )
        if (isGroupVerified && userNFTService.isNftClaimAble(nftId, groupId)) {
            val dummyId = userNFTService.getDummyId(user.userId, group.id)
            val contractInfo = contractService.getContractInfoByChain(nft.chainId)
            val signature =
                Signs.hashNFTSpaceStation(
                    privateKey,
                    group.id,
                    nft.contract,
                    dummyId,
                    groupId,
                    user.wallet,
                    contractInfo.chainId,
                    contractInfo.stationContractAddress,
                    contractInfo.domainName
                )

            userNFTService.addUserNFT(user.userId, nftId, groupId, signature.second, signature.first)
            return NFTClaimDto(
                groupId.toString(),
                nft.contract,
                signature.second.toString(),
                groupId.toString(),
                user.wallet,
                signature.first
            )
        } else {
            return null
        }
    }

    @PostMapping("/claimed/{nftId}/group/{groupId}")
    fun updateClaimed(
        @PathVariable("nftId") nftId: Long,
        @PathVariable("groupId") groupId: Long,
        @RequestParam("tx") tx: String,
        @RequestParam("dummyId") dummyId: Long
    ): ResponseEntity<String> {
        val principal = SecurityContextHolder.getContext().authentication.principal.toString()
        val user = userService.getUserByPrincipal(principal) ?: throw ResponseStatusException(
            HttpStatus.NOT_FOUND,
            "User not found"
        )
        val group = credentialGroupService.getCredentialGroupById(groupId) ?: throw ResponseStatusException(
            HttpStatus.NOT_FOUND,
            "Group not found"
        )
        userNFTService.updateUserNFTTx(nftId, user.userId, group.id, dummyId, tx)
        return ResponseEntity.ok("Success")
    }

    fun putMeta(bucketName: String, nftId: String, meta: NFTMetaData): String {
        val name = "${nftId}.json"

        val objectMeta = ObjectMetadata()
        objectMeta.contentType = MediaType.APPLICATION_JSON_VALUE
        val request = PutObjectRequest(
            bucketName, name,
            ByteArrayInputStream(mapper.valueToTree<JsonNode>(meta).toString().toByteArray()),
            objectMeta
        )
        val response = s3.putObject(request)
        logger.info("put meta response: {}", response)
        return name
    }

    fun createBucket(bucketName: String) {
        if (!s3.doesBucketExistV2(bucketName)) {
            s3.createBucket(bucketName)
        }
    }

    @GetMapping("/supportedChains")
    fun chains(): List<ContractDto> {
        return contractService.getAll().values.map {
            ContractDto(it.stationContractAddress, it.factoryAddress, it.chainId, it.chainName, it.icon)
        }
    }
}
