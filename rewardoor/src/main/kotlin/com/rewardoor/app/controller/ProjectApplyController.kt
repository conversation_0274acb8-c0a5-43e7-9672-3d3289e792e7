package com.rewardoor.app.controller

import com.rewardoor.app.dto.StringPayloadRequest
import com.rewardoor.app.services.*
import com.rewardoor.model.ProjectApply
import org.apache.commons.validator.routines.EmailValidator
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

@RestController
@RequestMapping("projectApply")
class ProjectApplyController(
    private val userService: UserService,
    private val projectService: ProjectService,
    private val adminService: AdminService,
    private val mailService: MailService,
    private val projectApplyService: ProjectApplyService
) {

    @PostMapping("sendCaptcha")
    fun sendEmailCaptcha(@RequestBody request: StringPayloadRequest): SimpleResponseEntity<Any> {
        val mail = request.payload
        if (EmailValidator.getInstance().isValid(mail).not()) {
            return SimpleResponseEntity.failed("Invalid email address")
        }
        val principal = SecurityContextHolder.getContext().authentication.principal.toString()
        val user = userService.getUserByPrincipal(principal)!!
        val key = mailService.sendMailCaptcha(user.userId, mail)
        return SimpleResponseEntity.success("SUCCESS", mapOf("key" to key))
    }

    @PostMapping("")
    fun applyPrivilege(@RequestBody request: ProjectApplyRequest): SimpleResponseEntity<String> {
        val principal = SecurityContextHolder.getContext().authentication.principal.toString()
        val user = userService.getUserByPrincipal(principal)!!
        request.userId = user.userId
        val captchaValid = mailService.verifyCaptcha(user.userId, request.key, request.captcha)
        if (captchaValid.not()) {
            return SimpleResponseEntity.failed("Invalid captcha")
        }

        projectApplyService.saveApply(request)
        return SimpleResponseEntity.success("SUCCESS", "Apply success")
    }

    @PostMapping("code")
    fun applyInviteCode(@RequestBody request: StringPayloadRequest): SimpleResponseEntity<String> {
        val principal = SecurityContextHolder.getContext().authentication.principal.toString()
        val user = userService.getUserByPrincipal(principal)!!
        val code = request.payload

        if (!projectApplyService.verifyInviteCode(user.userId, code)) {
            return SimpleResponseEntity.failed("Invalid Application Code")
        }
        projectApplyService.addPrivilege(user.userId)
        return SimpleResponseEntity.success("SUCCESS", "Apply success")
    }
}

class ProjectApplyRequest(var key: String,
                          var captcha: String,
                          projectName: String = "",
                          category: String = "",
                          tmaLink: String = "",
                          email: String = "",
                          applyReason: String = "",
                          estimatedParticipants: Int = 0,
                          socialPlatforms: String = "",
                          additionalSocialInfo: String = "",
                          userId: Long = 0
): ProjectApply(projectName, category, tmaLink, email, applyReason, estimatedParticipants, socialPlatforms, additionalSocialInfo, userId)
