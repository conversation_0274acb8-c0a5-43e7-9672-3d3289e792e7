package com.rewardoor.app.controller

import com.rewardoor.app.dto.*
import com.rewardoor.app.services.*
import com.rewardoor.model.*
import org.springframework.beans.factory.annotation.Value
import org.springframework.data.redis.core.StringRedisTemplate
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.web.bind.annotation.*

@RestController
@RequestMapping("company")
class CompanyController(
    val companyService: CompanyService,
    val projectService: ProjectService,
    val campaignService: CampaignService,
    val userService: UserService,
) {

    @GetMapping("{companyId}")
    fun getCompanyInfo(
        @PathVariable("companyId") companyId: Long,
        @RequestParam("layerOneId", required = false) layerOneId: Long?
    ):  ResponseEntity<Map<String, Any>> {
        return try {
            val companyInfo = companyService.getCompanyWithProjects(companyId, layerOneId)
            val responseBody = mapOf(
                "code" to 200,
                "message" to "success",
                "data" to companyInfo
            ) as Map<String, Any>
            ResponseEntity.ok(responseBody)
        } catch (e: Exception) {
            val errorResponse = mapOf(
                "code" to 500,
                "message" to "Company not found: $companyId"
            )
            println("Error: ${e.message}")
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse)
        }
    }

    @GetMapping("leaderboard/{companyId}")
    fun getCompanyLeaderboard(@PathVariable("companyId") companyId: Long): ResponseEntity<Map<String, Any>> {
        return try {
            val companyLeaderboard = companyService.getCompanyLeaderboard(companyId)
            val responseBody = mapOf(
                "code" to 200,
                "message" to "success",
                "data" to companyLeaderboard
            ) as Map<String, Any>
            if(companyLeaderboard != null) {
                ResponseEntity.ok(responseBody)
            } else {
                val emptyResponse = mapOf(
                    "code" to 200,
                    "message" to "Company not found: $companyId",
                    "data" to emptyList<Any>()
                )
                ResponseEntity.status(HttpStatus.NOT_FOUND).body(emptyResponse)
            }
        } catch (e: Exception) {
            println(e.message)
            val errorResponse = mapOf(
                "code" to 500,
                "message" to "An error occurred while fetching the leaderboard"
            )
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse)
        }
    }

    @GetMapping("/{companyId}/onboardCampaign")
    fun getCompanyOnboardCampaign(@PathVariable("companyId") companyId: Long): ResponseEntity<Map<String, Any>> {
        return try {
            val address = SecurityContextHolder.getContext().authentication.principal.toString()
            val user = userService.getUserByPrincipal(address)!!
            val companyLeaderboard = companyService.getCompanyOnboardCampaign(companyId, user, address)
            if(companyLeaderboard?.campaign?.campaignId != null) {
                companyLeaderboard.participantNum = campaignService.getParticipantNumsByCampaignId(companyLeaderboard.campaign.campaignId)
            }
            val responseBody = mapOf(
                "code" to 200,
                "message" to "success",
                "data" to companyLeaderboard
            ) as Map<String, Any>
            if(companyLeaderboard != null) {
                ResponseEntity.ok(responseBody)
            } else {
                val emptyResponse = mapOf(
                    "code" to 200,
                    "message" to "Onboard Campaign not found: $companyId",
                    "data" to emptyList<Any>()
                )
                ResponseEntity.status(HttpStatus.NOT_FOUND).body(emptyResponse)
            }
        } catch (e: Exception) {
            println(e.message)
            val errorResponse = mapOf(
                "code" to 500,
                "message" to "An error occurred while fetching the onboard campaign"
            )
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse)
        }
    }
}