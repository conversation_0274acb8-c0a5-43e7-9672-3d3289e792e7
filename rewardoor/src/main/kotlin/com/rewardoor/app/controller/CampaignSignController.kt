package com.rewardoor.app.controller

import com.rewardoor.app.services.CredentialService
import com.rewardoor.app.services.CredentialSignService
import com.rewardoor.app.services.UserService
import com.rewardoor.app.utils.Signs
import org.springframework.http.HttpStatus
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.web.bind.annotation.*
import org.springframework.web.server.ResponseStatusException

@RestController
@RequestMapping("campaignSign")
class CampaignSignController(
    val userService: UserService,
    val credentialService: CredentialService,
    val credentialSignService: CredentialSignService
) {
    private val log = mu.KotlinLogging.logger {}
    @GetMapping("/{id}")
    fun getSignRawData(@PathVariable("id") id: Long): Map<String, Any> {
        val address = SecurityContextHolder.getContext().authentication.principal.toString()
        val user = userService.getUserByPrincipal(address)!!
        val credential = credentialService.getCredentialById(id)!!
        val currentSign = credentialSignService.getCredentialSign(user.userId, id)
        if (currentSign?.verified == true) {
            return mapOf("verified" to 1, "code" to 1)
        }
        val rawData = "Sign this message to participate in the campaign: ${credential.name}. \n\n" +
                "credential id: ${credential.credentialId} \n" +
                "address: $address \n" +
                "timestamp: ${System.currentTimeMillis()}"
        credentialSignService.addCredentialRawData(user.userId, id, rawData)
        return mapOf("data" to rawData, "code" to 0)
    }

    @PostMapping("/{id}/verify")
    fun verifySign(@PathVariable("id") id: Long, @RequestParam("sign")sign: String): Map<String, Any> {
        val address = SecurityContextHolder.getContext().authentication.principal.toString()
        val user = userService.getUserByPrincipal(address)!!
        val currentSign = credentialSignService.getCredentialSign(user.userId, id)?:throw ResponseStatusException(HttpStatus.BAD_REQUEST, "no raw data")
        if (currentSign.verified) {
            return mapOf("success" to true)
        }
        val rawData = currentSign.rawData
        val recovered = Signs.getAddressUsedToSignHashedMessage(sign, rawData)
        log.info("recovered: $recovered, address: $address, credentialId: $id")
        return if (address.equals(recovered, true)) {
            credentialSignService.updateCredentialVerified(user.userId, id, sign)
            mapOf("success" to true)
        } else {
            mapOf("error" to "invalid sign", "success" to false)
        }
    }
}