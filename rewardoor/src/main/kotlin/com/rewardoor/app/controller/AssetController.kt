package com.rewardoor.app.controller

import com.rewardoor.app.services.CampaignService
import com.rewardoor.app.services.UserService
import com.rewardoor.model.NFT
import com.rewardoor.model.UserAsset
import com.rewardoor.model.UserCampaigns
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

@RestController
@RequestMapping("")
class AssetController(
    val campaignService: CampaignService, val userService: UserService
) {
    @GetMapping("/user/{projectId}/assets")
    fun getUserAsset(@PathVariable("projectId") projectId: Long): UserAsset {
        val address = SecurityContextHolder.getContext().authentication.principal.toString()
        val user = userService.getUserByPrincipal(address)!!
        val userId = user.userId
        return campaignService.getUserAsset(userId, projectId)
    }

    @GetMapping("/company/user/{companyId}/assets")
    fun getUserAssetByCompany(@PathVariable("companyId") companyId: Long): UserAsset {
        val address = SecurityContextHolder.getContext().authentication.principal.toString()
        val user = userService.getUserByPrincipal(address)!!
        val userId = user.userId
        return campaignService.getUserAssetByCompany(userId, companyId)
    }

    @GetMapping("/user/{groupId}/nftInfo/{nftId}")
    fun getNftInfo(@PathVariable("groupId") groupId: Long, @PathVariable("nftId") nftId: Long): NFT {
        val address = SecurityContextHolder.getContext().authentication.principal.toString()
        val user = userService.getUserByPrincipal(address)!!
        val userId = user.userId
        return campaignService.getUserGroupNft(groupId, userId, nftId)
    }

    @GetMapping("/user/{projectId}/campaigns")
    fun getUserCampaigns(@PathVariable("projectId") projectId: Long): UserCampaigns {
        val address = SecurityContextHolder.getContext().authentication.principal.toString()
        val user = userService.getUserByPrincipal(address)!!
        val userId = user.userId
        return campaignService.getUserCampaigns(userId, projectId)
    }

}