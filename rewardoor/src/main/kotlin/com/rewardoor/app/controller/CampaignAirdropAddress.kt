package com.rewardoor.app.controller

import com.rewardoor.app.services.CredentialAirdropAddressService
import com.rewardoor.model.CredentialAirdropAddress
import com.rewardoor.model.UserCampaigns
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import org.springframework.http.ResponseEntity
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.web.bind.annotation.*


@RestController
@RequestMapping("campaignAddress")
class CampaignAirdropAddress(
    private val privilegeCheck: PrivilegeCheck,
    private val credentialAirdropAddressService: CredentialAirdropAddressService) {
    @PostMapping("submitAddress")
    fun submitAddress(@RequestBody airdropAddress: CredentialAirdropAddress): SimpleResponseEntity<CredentialAirdropAddress> {
        val principal = SecurityContextHolder.getContext().authentication.principal.toString()
        privilegeCheck.checkUserId(airdropAddress.userId, principal)
        val address = airdropAddress.address
        if (!credentialAirdropAddressService.isEthereumAddressValid(address)) {
            return SimpleResponseEntity.failed("The format of your wallet address is incorrect.")
        } else if (credentialAirdropAddressService.getCredentialAddress(
                airdropAddress.userId,
                airdropAddress.credentialId
            ) != null
        ) {
            return SimpleResponseEntity.failed("The address cannot be submitted repeatedly.")
        }
        airdropAddress.verified = true
        return SimpleResponseEntity.success(
            "submit address success",
            credentialAirdropAddressService.addCredentialAir(airdropAddress)!!
        )
    }

    @PostMapping("/address")
    fun getUserCampaigns(@RequestBody airdropAddress: CredentialAirdropAddress): SimpleResponseEntity<CredentialAirdropAddress> {
        val principal = SecurityContextHolder.getContext().authentication.principal.toString()
        privilegeCheck.checkUserId(airdropAddress.userId, principal)
        val credentialAirdropAddress = credentialAirdropAddressService.getCredentialAddress(
            airdropAddress.userId,
            airdropAddress.credentialId
        )
        if (credentialAirdropAddress != null) {
            return SimpleResponseEntity.success(
                "submit address success",
                credentialAirdropAddress
            )
        }
        return SimpleResponseEntity.failed("The user has not yet submitted an address")
    }
}