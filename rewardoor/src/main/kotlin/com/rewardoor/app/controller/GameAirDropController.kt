package com.rewardoor.app.controller

import com.rewardoor.app.dto.StringPayloadRequest
import com.rewardoor.app.services.CarryTokenService
import com.rewardoor.app.services.UserService
import com.rewardoor.app.utils.Signs
import org.apache.commons.lang3.RandomStringUtils
import org.springframework.beans.factory.annotation.Value
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.web.bind.annotation.*
import org.springframework.web.server.ResponseStatusException
import java.math.BigDecimal
import java.time.Duration
import java.time.LocalDateTime
import java.time.ZoneOffset

@RestController
@RequestMapping("/game_airdrop")
class GameAirDropController(
    private val userService: UserService,
    private val carryTokenService: CarryTokenService,
    @Value("\${game_drop.private_key}") private val gameDropPrivateKey: String,
    @Value("\${game_drop.chain_id}") private val gameDropChainId: Int,
    @Value("\${game_drop.contract}") private val gameDropContract: String,
    @Value("\${game_drop.domain}") private val gameDropDomain: String
) {
    @PostMapping("/check")
    fun checkAmount(): AirdropAllocation {
        val idPrincipal = SecurityContextHolder.getContext().authentication.principal.toString()
        if (idPrincipal == "anonymousUser") return AirdropAllocation("", "0", "0","0", "0")

        val user = userService.getUserByPrincipal(idPrincipal)!!
        if (!user.evm.binded) return AirdropAllocation("", "0", "0","0", "0")

        val airdrop = carryTokenService.getUserTokenAllocation(user.evm.evmWallet!!)
        return calcAllocation(user.evm.evmWallet!!, airdrop)
    }

    @PostMapping("/claim")
    fun checkAirDrop(@RequestBody roundRequest: StringPayloadRequest): SimpleResponseEntity<GameAirDropSignResponse> {
        val idPrincipal = SecurityContextHolder.getContext().authentication.principal.toString()
        val user = userService.getUserByPrincipal(idPrincipal)!!
        if (!user.evm.binded) return SimpleResponseEntity.failed( "EVM Required", null)

        val round = roundRequest.payload
        val total = carryTokenService.getUserTokenAllocation(user.evm.evmWallet!!)
        if (total.compareTo(BigDecimal(0)) == 0) return SimpleResponseEntity.success("Success", GameAirDropSignResponse("", "0", "", round, gameDropDomain, "0"))
        val allocation = calcAllocation(user.evm.evmWallet!!, total)
        val amountStr = when (round) {
            "s1" -> allocation.s1Amount
            "s2" -> allocation.s2Amount
            "s3" -> allocation.s3Amount
            "s4" -> allocation.s4Amount
            else -> throw ResponseStatusException(org.springframework.http.HttpStatus.BAD_REQUEST, "Invalid Round")
        }
        val amount = (BigDecimal(amountStr) * BigDecimal(10).pow(18)).toBigInteger()
        val salt = RandomStringUtils.random(6, true, true)
        val sign = Signs.hashGameAirDrop(
            gameDropPrivateKey,
            user.evm.evmWallet!!, round,
            amount, salt, gameDropChainId,
            gameDropContract,
            "GameAirDrop"
        )
        return SimpleResponseEntity.success("Success", GameAirDropSignResponse(sign, amount.toString(), salt, round, gameDropDomain, amountStr))
    }

    private fun calcAllocation(addr: String, total: BigDecimal): AirdropAllocation {
        if (total.compareTo(BigDecimal(0)) == 0) return AirdropAllocation(addr, "0", "0", "0", "0")
        val l3 = total.divide(BigDecimal(4))
        val s1 = total.minus(l3.multiply(BigDecimal(3)))
        return AirdropAllocation(addr, s1.toString(), l3.toString(), l3.toString(), l3.toString())
    }
}

data class GameAirDropSignResponse(val sign: String, val amount: String, val salt: String,
                                   val round: String, val domain: String, val displayAmount: String)

data class OpeningTime(val start: Long, val end: Long)

val openStart = LocalDateTime.of(2024, 7, 1, 0, 0).toInstant(ZoneOffset.UTC).toEpochMilli()
val monthMillis = Duration.ofDays(30).toMillis()

data class AirdropAllocation(val address: String,
                             val s1Amount: String,
                             val s2Amount: String,
                             val s3Amount: String,
                             val s4Amount: String,
                             val s1Period: OpeningTime = OpeningTime(openStart, openStart + monthMillis),
                             val s2Period: OpeningTime = OpeningTime(openStart + monthMillis * 2, openStart + monthMillis * 3),
                             val s3Period: OpeningTime = OpeningTime(openStart + monthMillis * 4, openStart + monthMillis * 5),
                             val s4Period: OpeningTime = OpeningTime(openStart + monthMillis * 6, openStart + monthMillis * 7),
                             val gameLogo: String = "https://game.build/imgs/logo.png",
                             val symbol: String = "GAME",
                             val blockHeight: Int = 19919633
    )