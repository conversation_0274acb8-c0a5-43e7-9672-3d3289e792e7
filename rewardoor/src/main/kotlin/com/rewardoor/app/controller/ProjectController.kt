package com.rewardoor.app.controller

import com.rewardoor.app.dao.CredentialGroupRepository
import com.rewardoor.app.dao.ParticipantRepository
import com.rewardoor.app.dao.ProjectRepository
import com.rewardoor.app.dao.SuiSbtRepository
import com.rewardoor.app.dto.*
import com.rewardoor.app.services.*
import com.rewardoor.model.*
import com.rewardoor.model.CampaignStats
import org.springframework.beans.factory.annotation.Value
import org.springframework.core.env.Environment
import org.springframework.data.redis.core.StringRedisTemplate
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.web.bind.annotation.*
import org.springframework.web.server.ResponseStatusException
import java.net.URI
import kotlin.random.Random

@RestController
class ProjectController(
    val projectService: ProjectService,
    val campaignService: CampaignService,
    val credentialService: CredentialService,
    val resultStatsService: ResultStatsService,
    val userService: UserService,
    val projectApplyService: ProjectApplyService,
    val userTwitterService: UserTwitterService,
    val dcService: DiscordService,
    val telegramService: TelegramService,
    val adminService: AdminService,
    private val redisTemplate: StringRedisTemplate,
    @Value("\${home.admins}") val adminAddress: MutableList<String>,
    private val participantRepo: ParticipantRepository,
    private val env: Environment,
    private val suiSbtRepository: SuiSbtRepository,
    private val credentialGroupRepository: CredentialGroupRepository,
    private val projectRepository: ProjectRepository
) {

    @PostMapping("/project")
    fun createProject(@RequestBody project: Project): SimpleResponseEntity<Project> {
        val authentication = SecurityContextHolder.getContext().authentication
        val principal = SecurityContextHolder.getContext().authentication.principal.toString()
        val user = userService.getUserByPrincipal(principal)!!
        if (projectApplyService.getUserPrivilege(user.userId).not()) {
            return SimpleResponseEntity.failed("You are not allowed to create a project")
        }
        val chain = (authentication as? com.rewardoor.app.auth.AddressAuthentication)?.chain?.lowercase()
        if (!chain.isNullOrBlank()) {
            project.chain = chain
        }
        val isUrlExist = projectService.getProjectByUrl(project.projectUrl) != null
        val isNameExist = projectService.getProjectByName(project.projectName) != null
        if (project.layerOneList.isEmpty()) {
            project.layerOneList = emptyList()
        }
        if (isUrlExist) {
            return SimpleResponseEntity.failed("The project URL has been used")
        } else if (isNameExist) {
            return SimpleResponseEntity.failed("The project name has been used")
        } else {
            return SimpleResponseEntity.success(
                "Add project success",
                projectService.addProject(project, user.userId, chain ?: "")
            )
        }
    }

    @GetMapping("/project/{projectId}")
    fun getProject(@PathVariable("projectId") projectId: Long): Project? {
        return projectService.getProject(projectId)
    }

    @GetMapping("/project/byUrl/{url}")
    fun getProjectByUrl(@PathVariable("url") projectUrl: String): Project? {
        val project = projectService.getProjectByUrl(projectUrl)!!
        val ut = userTwitterService.getUserInfo(project.creatorId)
        if (ut != null && ut.connected) {
            project.twitterLink = "https://twitter.com/" + ut.twitterName
        }
        val tg = telegramService.getUserInfo(project.creatorId)
        if (tg != null && tg.connected) {
            project.telegramLink = "https://t.me/" + tg.username
        }
        return project
    }

    @GetMapping("/project/overviewByUrl/{url}")
    fun getProjectOverviewByUrl(@PathVariable("url") projectUrl: String): ResponseEntity<ProjectOverview> {
        val project = projectService.getProjectByUrl(projectUrl) ?: return ResponseEntity.notFound().build()
        return ResponseEntity.ok(getProjectPreview(project))
    }

    @GetMapping("/project/hot")
    fun getHotProjects(): ResponseEntity<Any> {
        val stagProjectIds = listOf(575950653874, 574055793867)
        val prodProjectIds = listOf(575950653874, 793559312598483, 786833142594752, 827646042615492)
        val hotProjects = mutableListOf<Project>()

        val stagSuiSbtIds = listOf(
//            772739796905, 772738156900, 772654886894,
            768665576787, 769250376807, 805468137310
        )
        val prodSuiSbtIds = listOf(575950653874, 772989062587290, 805687122604580, 805688972604587)
        val hotSBTs = mutableListOf<SuiSbtReward>()

        val p = if (env.activeProfiles.contains("prod")) "PROD" else "STAG"
        val projectIds = if (p == "PROD") prodProjectIds else stagProjectIds
        val suiSbtIds = if (p == "PROD") prodSuiSbtIds else stagSuiSbtIds
        for (projectId in projectIds) {
            val project = projectService.getProject(projectId)
            if (project != null) {
                hotProjects.add(project)
            }
        }

        for (suiSbtId in suiSbtIds) {
            val suiSbtReward = suiSbtRepository.getSuiSbtRewardById(suiSbtId)
            val holderCnt = participantRepo.getClaimedUserCountByRewardId(suiSbtId)
            if (suiSbtReward != null) {
                suiSbtReward.holderCnt = holderCnt
                val groupId = suiSbtReward.groupId
                val sbtCampaignId = credentialGroupRepository.getCredentialGroupById(groupId)!!.campaignId
                val sbtProjectId = suiSbtReward.projectId
                val project = projectRepository.getProjectById(sbtProjectId)!!
                val projectUrl = project.projectUrl
                val projectAvatarUrl = project.avatarUrl
                suiSbtReward.campaignId = sbtCampaignId
                suiSbtReward.projectUrl = projectUrl
                suiSbtReward.projectAvatarUrl = projectAvatarUrl
                suiSbtReward.projectName = project.projectName
                hotSBTs.add(suiSbtReward)
            }
        }

        return ResponseEntity.ok(
            mapOf("code" to 200, "hotProjects" to hotProjects, "hotSBTs" to hotSBTs)
        )
    }

    private fun getProjectPreview(project: Project): ProjectOverview {
        val campaigns = campaignService.getCampaignByProjectId(project.projectId)
        val credentials = credentialService.getCredentialByProjectId(project.projectId)
        val campaignStats = campaigns.map {
            val stats = campaignService.getCampaignByStats(it.campaignId)
            val size = stats.attendees.size.toLong()
            CampaignStats(
                it,
                it.nft != null,
                it.credentialId != null,
                it.points != null,
                if (size <= 0) 0 else Random.nextLong(0, size),
                if (size <= 0) 0 else Random.nextLong(0, size),
                if (size <= 0) 0 else Random.nextLong(0, size)
            )
        }
        val overview = ProjectOverview(
            project,
            actionCount = Random.nextLong(10, 1000),
            credentialCount = credentials.size.toLong(),
            nftCount = Random.nextLong(0, 1),
            campaignCount = campaigns.size.toLong(),
            campaignStats
        )
        return overview
    }

    @PostMapping("/project/{projectId}/genToken")
    fun generateAPIToken(@PathVariable("projectId") projectId: Long): String {
        val principal = SecurityContextHolder.getContext().authentication.principal.toString()
        val user = userService.getUserByPrincipal(principal)!!
        return projectService.generateAPIToken(projectId, user.userId)
    }

    @GetMapping("/project/stats/{id}/points")
    fun getCampaignPointsStats(@PathVariable("id") id: Long): PointStats {
        val points = resultStatsService.getProjectPoints(id).sortedByDescending { it.points }
        return PointStats(points.sumOf { it.points }, points)
    }

    @GetMapping("/project/explore")
    fun getExploreCampaigns(): List<CampaignCard> {
        return projectService.getAllScheduledAndOnGoingCampaigns()
    }

    @GetMapping("/project/home")
    fun getProjectsAndCampaigns(): Map<String, Any> {
        val homeSettings = projectService.getHomeProjectsAndCampaignsAndSBTs()
        return mapOf(
            "code" to 200,
            "homeSettings" to homeSettings
        )
    }

    @GetMapping("/project/home/<USER>")
    fun getProjectTemplates(@RequestParam name: String): Map<String, Any?> {
        val principal = SecurityContextHolder.getContext().authentication.principal.toString()
        if (principal == "anonymousUser") {
            val templateSettings = projectService.getHomeProjectsTemplates(name, 0)
            return mapOf(
                "code" to 200,
                "templateSettings" to templateSettings
            )
        }
        val user = userService.getUserByPrincipal(principal)!!
        val templateSettings = projectService.getHomeProjectsTemplates(name, user.userId)
        return mapOf(
            "code" to 200,
            "templateSettings" to templateSettings
        )
    }


    @GetMapping("/project/coreData")
    fun getCoreData(): Map<String, Any> {
        val evmUserCnt = userService.getEvmUserCnt()
        val tonUserCnt = userService.getTonUserCnt()
        val twitterUserCnt = userTwitterService.getTwitterUserCnt()
        val dcUserCnt = dcService.getDcUserCnt()
        val tgUserCnt = telegramService.getTgUserCnt()
        val credentialCnt = credentialService.getCredentialCntFromRedis()
        val pointsClaimedCnt = campaignService.getPointsClaimedCntFromRedis()
        val totalCnt = evmUserCnt + tonUserCnt + twitterUserCnt + dcUserCnt + tgUserCnt
        val rewardsClaimedCnt = participantRepo.getRewardsClaimedCnt()
        val onChainUserCnt = evmUserCnt + tonUserCnt
        return mapOf(
            "code" to 200,
            "totalCnt" to totalCnt,
            "credentialCnt" to credentialCnt,
            "rewardsClaimedCnt" to rewardsClaimedCnt,
            "pointsClaimedCnt" to pointsClaimedCnt,
            "onChainUserCnt" to onChainUserCnt
        )
    }

    @PostMapping("/project/{projectId}/key")
    fun generateKey(@PathVariable("projectId") projectId: Long): ProjectExternalConfigDto {
        val principal = SecurityContextHolder.getContext().authentication.principal.toString()
        val user = userService.getUserByPrincipal(principal)!!
        val creatorId = projectService.getProjectCreator(projectId) ?: throw ResponseStatusException(
            HttpStatus.NOT_FOUND,
            "Project not found"
        )
//        if (user.userId != creatorId) throw ResponseStatusException(
//            HttpStatus.FORBIDDEN,
//            "You are not the creator of this project"
//        )
        projectService.generateKey(projectId)
        return ProjectExternalConfigDto.fromModel(projectService.getProjectExternalConfig(projectId)!!)
    }

    @PostMapping("/project/{projectId}/callback")
    fun updateProjectCallbackUrl(
        @PathVariable("projectId") projectId: Long,
        @RequestBody callbackDto: SetCallbackDto
    ): ProjectExternalConfigDto {
        val principal = SecurityContextHolder.getContext().authentication.principal.toString()
        val creatorId = projectService.getProjectCreator(projectId) ?: throw ResponseStatusException(
            HttpStatus.NOT_FOUND,
            "Project not found"
        )
        val user = userService.getUserByPrincipal(principal)!!
        if (user.userId != creatorId) throw ResponseStatusException(
            HttpStatus.FORBIDDEN,
            "You are not the creator of this project"
        )
        try {
            val uri = URI.create(callbackDto.callbackUrl)
            if (!uri.scheme.startsWith("http")) throw ResponseStatusException(
                HttpStatus.BAD_REQUEST,
                "Invalid callback url"
            )
        } catch (e: Exception) {
            throw ResponseStatusException(HttpStatus.BAD_REQUEST, "Invalid callback url")
        }
        projectService.updateProjectCallbackUrl(projectId, callbackDto.callbackUrl)
        return ProjectExternalConfigDto.fromModel(projectService.getProjectExternalConfig(projectId)!!)
    }

    @PostMapping("/project/{projectId}/callback/status")
    fun updateProjectCallbackStatus(
        @PathVariable("projectId") projectId: Long,
        @RequestBody callbackDto: CallbackDto
    ): ProjectExternalConfigDto {
        val principal = SecurityContextHolder.getContext().authentication.principal.toString()
        val creatorId = projectService.getProjectCreator(projectId) ?: throw ResponseStatusException(
            HttpStatus.NOT_FOUND,
            "Project not found"
        )
        val user = userService.getUserByPrincipal(principal)!!
        if (user.userId != creatorId) throw ResponseStatusException(
            HttpStatus.FORBIDDEN,
            "You are not the creator of this project"
        )

        projectService.updateProjectCallbackStatus(projectId, callbackDto.enable)
        return ProjectExternalConfigDto.fromModel(projectService.getProjectExternalConfig(projectId)!!)
    }

    /*@GetMapping("testCallback")
     fun testCallback() {
         val config = projectService.getProjectExternalConfig(217262530358)!!
         val user = userService.getUserByAddress("0x3beff95bbb844015372075aae6fe8ff1e0de5d27")!!
         callbackService.callback(user, 217262530358, 1, System.currentTimeMillis())
     }*/

    fun checkIsCreator(principal: String, projectId: Long, user: User): Boolean {
        val creatorId = projectService.getProjectCreator(projectId) ?: throw ResponseStatusException(
            HttpStatus.NOT_FOUND,
            "Project not found"
        )
        if (user.userId != creatorId) throw ResponseStatusException(
            HttpStatus.FORBIDDEN,
            "You are not the creator of this project"
        ) else {
            return true
        }
    }

}