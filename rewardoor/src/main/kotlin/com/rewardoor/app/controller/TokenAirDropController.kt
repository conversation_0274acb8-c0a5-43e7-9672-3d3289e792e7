package com.rewardoor.app.controller

import com.rewardoor.app.services.CarryTokenService
import org.springframework.stereotype.Controller
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestMapping

@Controller
@RequestMapping("airDrop")
class TokenAirDropController(val carryTokenService: CarryTokenService) {

    @GetMapping("game")
    fun generateAirDropInfo() {
        carryTokenService.getTokenAirDropInfo()
    }
}