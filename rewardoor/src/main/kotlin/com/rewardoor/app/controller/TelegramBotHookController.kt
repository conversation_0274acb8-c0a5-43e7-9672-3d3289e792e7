package com.rewardoor.app.controller

import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.pengrad.telegrambot.TelegramBot
import com.pengrad.telegrambot.model.CallbackQuery
import com.pengrad.telegrambot.model.InlineQuery
import com.pengrad.telegrambot.model.User
import com.pengrad.telegrambot.model.request.InlineKeyboardButton
import com.pengrad.telegrambot.model.request.InlineKeyboardMarkup
import com.pengrad.telegrambot.model.request.InlineQueryResultArticle
import com.pengrad.telegrambot.model.request.ParseMode
import com.pengrad.telegrambot.request.*
import com.pengrad.telegrambot.response.BaseResponse
import com.pengrad.telegrambot.response.SendResponse
import com.pengrad.telegrambot.utility.BotUtils
import com.rewardoor.app.dao.TPointsConsumeRepository
import com.rewardoor.app.dao.TelegramUserPushRepository
import com.rewardoor.app.dto.TgAuthCallbackReq
import com.rewardoor.app.services.*
import com.rewardoor.app.utils.TgStarter
import com.rewardoor.model.*
import org.springframework.beans.factory.annotation.Value
import org.springframework.boot.context.event.ApplicationReadyEvent
import org.springframework.context.ApplicationListener
import org.springframework.data.redis.core.StringRedisTemplate
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.web.bind.annotation.*
import org.springframework.web.server.ResponseStatusException
import java.time.Instant
import java.util.*

@RestController
@RequestMapping("/tg")
class TelegramBotHookController(
    @Value("\${tg.mini_app.token}") private val tgBotToken: String,
    @Value("\${tg.mini_app.webhook_url}") private val webhookUrl: String,
    @Value("\${tg.mini_app.bot_name}") private val botName: String,
    @Value("\${tg.mini_app.app_name}") private val miniAppName: String,
    private val tgInvitationService: TgInvitationService,
    private val userTelegramService: TelegramService,
    private val userService: UserService,
    private val wiseTaskService: WiseTaskService,
    private val wiseInviteService: WiseInviteService,
    private val wiseScoreService: WiseScoreService,
    private val credentialGroupService: CredentialGroupService,
    private val tPointsService: TPointsService,
    private val tPointsConsumeRepo: TPointsConsumeRepository,
    private val redisTemplate: StringRedisTemplate,
    private val telegramUserPushRepository: TelegramUserPushRepository,
    private val starPaymentService: StarPaymentService
) : ApplicationListener<ApplicationReadyEvent> {
    private val log = mu.KotlinLogging.logger { }
    private val secretToken = "e1555ae1"
    private val bot = TelegramBot(tgBotToken)
    private val startImage = "https://static.tbook.vip/tb_intro.jpg"
    private val wiseScoreImage = "https://static.tbook.vip/wise_score.png"
    private val miniAppUrl = "https://t.me/$botName/$miniAppName"
    private lateinit var specialCampaignText: String

    override fun onApplicationEvent(event: ApplicationReadyEvent) {
        bot.execute(SetWebhook().secretToken(secretToken).url(webhookUrl))
        specialCampaignText = redisTemplate.opsForValue().get("ton_defi_campaign") ?: ""
        log.info { "special campaign text: $specialCampaignText" }
    }

    fun withFailedLog(ac: () -> BaseResponse) {
        val r = ac()
        if (!r.isOk) {
            log.warn { "[bot hook] send message fail: ${r.description()}" }
        }
    }

    private fun registerIfEmpty(from: User): UserTelegramInfo {
        val inviteeInfo = userTelegramService.getUserInfoByTgId(from.id())
        if (inviteeInfo != null) return inviteeInfo
        try {
            val tgInfo = TgAuthCallbackReq(
                from.id(),
                from.firstName().orEmpty(),
                from.lastName().orEmpty(),
                from.username().orEmpty()
            )
            userTelegramService.registerTgUser(tgInfo)
        } catch (e: Exception) {
            log.error(e) { "register user failed" }
        }
        return userTelegramService.getUserInfoByTgId(from.id())!!
    }

    @PostMapping("webhook")
    fun webhook(
        @RequestBody updateString: String,
        @RequestHeader("X-Telegram-Bot-Api-Secret-Token") secretToken: String
    ) {
        if (secretToken != this.secretToken) {
            log.warn("unknown secret token: {}", secretToken)
            throw ResponseStatusException(org.springframework.http.HttpStatus.FORBIDDEN, "Invalid secret token")
        }
        try {
            val update = BotUtils.parseUpdate(updateString)
            log.info("update received: {}", update)
            if (update.preCheckoutQuery() != null) {
                starPaymentService.handlePreCheckoutQuery(update.preCheckoutQuery())
                return
            }
            if (update.message() != null && update.message().successfulPayment() != null) {
                starPaymentService.handleSuccessfulPayment(update.message().successfulPayment(), update.message().chat().id())
                return
            }
            if (update.inlineQuery() != null) {
                handlerInlineQuery(update.inlineQuery())
                return
            }
            if (update.callbackQuery() != null) {
                println("call back query is ${update.callbackQuery().data()}")
                handlerCallbackQuery(update.callbackQuery())
                return
            }

            val message = update.message()
            if (message == null) {
                log.info("ignore non-message update")
                return
            }
            message.text()?.let {
                when {
                    it.startsWith("/start") -> {
                        val from = update.message().from()
                        val chatId = update.message().chat().id()
                        var arg = it.substringAfter("/start").trim()
                        log.info { "start with context: $arg" }

                        if (arg.isNotEmpty() && arg.startsWith("bind_")) {
                            val token = arg.substringAfter("bind_")
                            handleTelegramBinding(token, from, chatId)
                            return
                        }

                        val inviteeInfo = registerIfEmpty(from)
                        if (arg.isNotEmpty() && arg.startsWith("cr_")) {
                            val targetUrl = redisTemplate.opsForValue().get(TgStarter.formatKeyFromStartParam(arg))
                            withFailedLog {
                                bot.execute(
                                    SendMessage(
                                        chatId, """<a href='$targetUrl'>Click to join</a> the community
                                                            |
                                                            |$targetUrl
                                                """.trimMargin()
                                    ).parseMode(ParseMode.HTML)
                                )
                            }
                            return
                        }
                        if (arg.isNotEmpty() && arg.startsWith("camp_")) {
                            val cleanArgs = arg.substring(5)
                            var targetUrl = "$miniAppUrl?startapp=${cleanArgs}"
                            val content = if (cleanArgs.equals(specialCampaignText, true)) {
                                targetUrl = "$miniAppUrl?startapp=WzZd"
                                specialCampaignStartContent.format(targetUrl, targetUrl)
                            } else {
                                """<a href='$targetUrl'>Click to join</a> the campaign
                                                            |
                                                            |$targetUrl""".trimMargin()
                            }
                            withFailedLog {
                                bot.execute(
                                    SendMessage(chatId, content).parseMode(ParseMode.HTML)
                                )
                            }
                            return
                        }

                        var startParam = "WzNd"
                        val userId = arg.toLongOrNull()
                        if (arg.isNotEmpty() && userId == null) {
                            val ws = redisTemplate.opsForValue().get("tg:start:$arg")
                            log.info { "start with: $ws" }
                            if (!ws.isNullOrEmpty()) {
                                startParam = ws
                            }
                        }
                        var messageText = START_CONTENT_4
                        var parseMode = ParseMode.HTML
                        if (arg.isNotEmpty() && userId != null) {
                            val inviter = userTelegramService.getUserInfo(userId)!!
                            if (inviter.tgId != from.id()) {
                                val cnt = tgInvitationService.addInvitation(
                                    userId, inviter.tgId, from.id(),
                                    from.firstName().orEmpty(), from.lastName().orEmpty(),
                                    from.username().orEmpty(), from.isPremium
                                )
                                if (cnt > 0) {
                                    val un = inviter.username.replace("_", "\\_")
                                    messageText = START_BY_INVITE_CONTENT.format(un)
                                    parseMode = ParseMode.MarkdownV2
                                }
                            }
                        }

                        val buttons = mutableListOf(
                            arrayOf(
                                InlineKeyboardButton(
                                    """Launch app""",
                                    "https://t.me/tbook_incentive_bot/tbook"
                                )
                            ),
                            arrayOf(
                                InlineKeyboardButton(
                                    """Follow X""",
                                    "https://x.com/realtbook"
                                )
                            ),
                            arrayOf(
                                InlineKeyboardButton(
                                    """Join community""",
                                    "https://t.me/tbookincentive"
                                )
                            ),
                        )
                        val keyboard = InlineKeyboardMarkup(*buttons.toTypedArray())

                        withFailedLog {
                            bot.execute(
                                SendPhoto(chatId, startImage)
                                    .caption(messageText).parseMode(parseMode)
                                    .replyMarkup(keyboard)
                            )
                        }
                        //withFailedLog { bot.execute(SendMessage(chatId, START_CONTENT_2)) }
                    }

                    it.startsWith("/wise_score") -> {
                        val buttons = mutableListOf(
                            arrayOf(
                                InlineKeyboardButton(
                                    """Mint My TON Credit SBT""",
                                    "https://id.ton.org/wise-sbt-1"
                                )
                            ),
                            arrayOf(
                                InlineKeyboardButton(
                                    """Boost My WISE Credit Score""",
                                    "https://t.me/tbook_incentive_bot/tbook?startapp=WzNd"
                                )
                            )
                        )
                        val request = SendPhoto(update.message().chat().id(), wiseScoreImage)
                            .caption(SCORE_CONTENT)
                            .parseMode(ParseMode.MarkdownV2)
                            .replyMarkup(InlineKeyboardMarkup(*buttons.toTypedArray()))
                        withFailedLog { bot.execute(request) }
                    }

                    it.startsWith("/support") -> {
                        withFailedLog {
                            bot.execute(
                                SendMessage(
                                    update.message().chat().id(),
                                    SUPPORT_CONTENT
                                ).parseMode(ParseMode.MarkdownV2)
                            )
                        }
                    }

                    it.startsWith("/earn_sbt_assets") -> {
                        val buttons = mutableListOf(
                            arrayOf(
                                InlineKeyboardButton(
                                    """Unlock My SBT Assets""",
                                    "https://t.me/tbook_incentive_bot/tbook"
                                )
                            ))
                        withFailedLog {
                            bot.execute(
                                SendMessage(
                                    update.message().chat().id(),
                                    earnSBTContent
                                ).parseMode(ParseMode.MarkdownV2)
                                    .replyMarkup(InlineKeyboardMarkup(*buttons.toTypedArray()))
                            )
                        }
                    }

                    it.startsWith("/explore_campaigns") -> {
                        val request = SendMessage(update.message().chat().id(), CAMPAIGN_CONTENT)
                            .parseMode(ParseMode.MarkdownV2)
                            .replyMarkup(
                                InlineKeyboardMarkup(
                                    arrayOf(
                                        InlineKeyboardButton(
                                            "Explore Campaigns",
                                            "https://t.me/tbook_incentive_bot/tbook"
                                        )
                                    )
                                )
                            )
                        withFailedLog { bot.execute(request) }
                    }

                    else -> {
                        log.info { "unknown command, ignore ${update.message().text()}" }
                    }
                }
            }
        } catch (ex: Exception) {
            log.error(ex) { "handle message failed" }
        }
    }

    fun handlerCallbackQuery(callbackQuery: CallbackQuery) {
        val query = callbackQuery.data()
        val queryId = callbackQuery.id()
        if (query.startsWith("use tpoints")) {
            println(" start handle tpoints query")
            handleUse80kTPointsQuery(query, queryId)
            return
        }
        if (query.startsWith("merge:")) {
            handleMergeCallback(query, callbackQuery)
            return
        }
    }

    fun handlerInlineQuery(inlineQuery: InlineQuery) {
        val query = inlineQuery.query()
        log.info { "inline query received: [$query]" }
        if (query.startsWith("wise:invite:")) {
            handleWiseInviteQuery(query, inlineQuery.id())
            return
        }
        if (query.startsWith("share:")) {
            handleShareQuery(query, inlineQuery.id())
            return
        }

        if (!query.startsWith("invite:")) {
            log.info { "unknown query [$query], ignore" }
            return
        }
        val userId = query.split(":").last()
        val id = "invite:$userId"
        val link = "https://t.me/$botName?start=$userId"
        val content = INLINE_QUERY_CONTENT + "\n\n$link"
        val queryResult = InlineQueryResultArticle(
            id,
            """Hi friend, get your 5 scratch cards🎉""", content
        )
            .url(link)
            .hideUrl(false)
            .thumbnailUrl("https://static.tbook.vip/inline_query_logo.png")
        val answer = AnswerInlineQuery(inlineQuery.id(), queryResult)
        bot.execute(answer)
    }

    fun handleShareQuery(query: String, queryId: String) {
        val params = query.split(":")
        if (params.size < 3) {
            log.warn { "invalid share query: $query" }
            return
        }
        val userId = params[1].toLongOrNull()
        val type = params[2].toIntOrNull()
        if (userId == null || type == null) {
            return log.warn { "invalid share query: $query" }
        }
        val shareType = WiseScoreShareType.fromValue(type)
        wiseTaskService.addTask(userId, shareType.toTaskName(), 500)

        val startQuery = mapper.writeValueAsString(arrayOf(5, shareType.value))
        val link = "$miniAppUrl?startapp=${Base64.getEncoder().encodeToString(startQuery.toByteArray())}"
        val content = SHARE_QUERY_CONTENT + "\n\n$link"
        val id = "share:$userId:$type"
        val queryResult = InlineQueryResultArticle(
            id,
            """🎁I have obtained the WISE Credential and 🎉 improved my WISE Credit Score.""", content
        )
            .url(link)
            .hideUrl(false)
            .thumbnailUrl("https://static.tbook.vip/inline_query_logo.png")
        val answer = AnswerInlineQuery(queryId, queryResult)
        bot.execute(answer)
    }

    fun handleUse80kTPointsQuery(query: String, queryId: String) {
        val top500SbtId = 0L
        val top50SbtId = 1L
        val top500SbtLink =
            "https://t.me/tbook_incentive_bot/tbook?startapp=WyIxIiwidGJvb2staW5jZW50aXZlIiwiNjY1NjYyMTUyMzUwNTA3Il0"
        val top50SbtLink =
            "https://t.me/tbook_incentive_bot/tbook?startapp=WyIxIiwidGJvb2staW5jZW50aXZlIiwiNjY1NjU4NjAyMzUwMzQ5Il0"
        val params = query.split(":")
        if (params.size < 2) {
            log.warn { "invalid use TPoints query: $query" }
            return
        }
        println("id is " + params[1].trim().toLong())
        val tgId = params[1].trim().toLongOrNull() ?: return log.warn { "invalid use TPoints query: $query" }
        val user = userTelegramService.getUserInfoByTgId(tgId) ?: return log.warn { "invalid tg user query: $query" }
        val userTPoints = tPointsService.getUserTPointsNum(user.userId)
        val hasParticipated =
            telegramUserPushRepository.getTgUserPush(user.userId, "Exchange 80K TPoints for SBT and rewards")
        if (hasParticipated?.participateType == 1) {
            try {
                val answerCallbackQuery = AnswerCallbackQuery(queryId)
                    .text("Failed to use TPoints! You have already participated.")
                    .showAlert(true)
                bot.execute(answerCallbackQuery)
            } catch (e: Exception) {
                log.error { "execute answerCallbackQuery error: $e" }
            }
            return
        }
        if (userTPoints >= 80000) {
            val tPointsConsume = TPointsConsume(
                userId = user.userId,
                consumeType = -2,
                level = -2,
                tPointsNum = 80000,
                createTime = Instant.now(),
                updateTime = Instant.now()
            )
            try {
                val leftPoints = userTPoints - 80000
//                val top500Sbt = credentialGroupService.getSBTById(top500SbtId)!!
//                val top500ActivityId = top500Sbt.activityId
//                val top50Sbt = credentialGroupService.getSBTById(top50SbtId)!!
//                val top50ActivityId = top50Sbt.activityId
//                val top500UserLink =
//                    wiseScoreService.getUserSBTLink(user.userId, top500ActivityId, top500SbtId, top500Sbt.groupId)
//                if (telegramUserPushRepository.getTgUserPushByUidAndPushType(
//                        user.userId,
//                        "Top 50 - Exchange 80K TPoints for SBT and rewards"
//                    ) != null
//                ) {
//                    // top 50 user
//                    val top50UserLink =
//                        wiseScoreService.getUserSBTLink(user.userId, top50ActivityId, top50SbtId, top50Sbt.groupId)
//                }

                val answerCallbackQuery = AnswerCallbackQuery(queryId)
                    .text("Success to use TPoints ! You have $leftPoints points left.")
                    .showAlert(true)
                bot.execute(answerCallbackQuery)
                tPointsConsumeRepo.addTPointsConsume(tPointsConsume)
                telegramUserPushRepository.updateTgUserPushParticipateType(
                    user.userId,
                    "Exchange 80K TPoints for SBT and rewards",
                    1
                )
            } catch (e: Exception) {
                log.error { "execute answerCallbackQuery error: $e" }
            }
        } else {
            try {
                val answerCallbackQuery = AnswerCallbackQuery(queryId)
                    .text("TPoints used failed! You have $userTPoints points left.")
                    .showAlert(true)
                bot.execute(answerCallbackQuery)
            } catch (e: Exception) {
                log.error { "execute answerCallbackQuery error: $e" }
            }
        }
    }

    private fun handleTelegramBinding(token: String, from: User, chatId: Long) {
        val userId = userTelegramService.verifyBindToken(token)
        if (userId == null) {
            withFailedLog {
                bot.execute(
                    SendMessage(
                        chatId, "Invalid or expired binding token. Please request a new binding link."
                    )
                )
            }
            return
        }

        // Check if the Telegram user is already bound to another account
        val existingTgBinding = userTelegramService.getUserInfoByTgId(from.id())
        if (existingTgBinding != null && existingTgBinding.userId != userId) {
            // Get user information for both accounts to check if they can be merged
            val currentUser = userService.getUserById(userId)!!
            val existingUser = userService.getUserById(existingTgBinding.userId)!!
            
            val passportA = PassportAccounts(
                userId = currentUser.userId,
                evmAddress = currentUser.evm.evmWallet ?: "",
                tonAddress = currentUser.ton.tonWallet ?: "",
                suiAddress = currentUser.suiAddress,
                twitterName = currentUser.twitterName,
                dcName = currentUser.dcName,
                tgName = currentUser.tgName
            )
            
            val passportB = PassportAccounts(
                userId = existingUser.userId,
                evmAddress = existingUser.evm.evmWallet ?: "",
                tonAddress = existingUser.ton.tonWallet ?: "",
                suiAddress = existingUser.suiAddress,
                twitterName = existingUser.twitterName,
                dcName = existingUser.dcName,
                tgName = existingUser.tgName
            )
            
            val isAbleToMerge = isPassportsAbleToMerge(passportA, passportB)
            
            if (!isAbleToMerge) {
                withFailedLog {
                    bot.execute(
                        SendMessage(
                            chatId, "This Telegram account is already bound to another user account."
                        )
                    )
                }
                return
            } else {
                // Ask user if they want to merge accounts
                val keyboard = InlineKeyboardMarkup()
                    .addRow(
                        InlineKeyboardButton("Yes, merge accounts")
                            .callbackData("merge:${userId}:${existingTgBinding.userId}"),
                        InlineKeyboardButton("No, cancel")
                            .callbackData("cancel_merge")
                    )
                
                withFailedLog {
                    bot.execute(
                        SendMessage(
                            chatId, "This Telegram account is already bound to another user. Would you like to merge these accounts?"
                        ).replyMarkup(keyboard)
                    )
                }
                return
            }
        }

        // Check if the user already has a Telegram account bound
        val existingUserBinding = userTelegramService.getUserInfo(userId)
        if (existingUserBinding != null && existingUserBinding.tgId != from.id()) {
            withFailedLog {
                bot.execute(
                    SendMessage(
                        chatId, "Your account is already bound to another Telegram account."
                    )
                )
            }
            return
        }

        // Bind the Telegram account to the user
        try {
            val tgInfo = TgAuthCallbackReq(
                from.id(),
                from.firstName().orEmpty(),
                from.lastName().orEmpty(),
                from.username().orEmpty()
            )
            userTelegramService.addUserTgInfo(userId, tgInfo)
            withFailedLog {
                bot.execute(
                    SendMessage(
                        chatId, "Your Telegram account has been successfully bound to your user account!"
                    )
                )
            }
        } catch (e: Exception) {
            log.error(e) { "Error binding Telegram account" }
            withFailedLog {
                bot.execute(
                    SendMessage(
                        chatId, "An error occurred while binding your Telegram account. Please try again later."
                    )
                )
            }
        }
    }

    fun handleWiseInviteQuery(query: String, queryId: String) {
        val params = query.split(":")
        if (params.size < 3) {
            log.warn { "invalid wise invite query: $query" }
            return
        }
        val userId = params[2].toLongOrNull() ?: return log.warn { "invalid share query: $query" }

        var startQuery = ""
        val wiseCode = wiseInviteService.getInviteCode(userId, 1)
        if (wiseCode == null) {
            if (wiseScoreService.getScoreById(userId) != null) {
                val code = wiseInviteService.getOrAddInviteCode(userId, 1)
                startQuery = mapper.writeValueAsString(arrayOf(3, code.inviteCode))
            }
        } else {
            startQuery = mapper.writeValueAsString(arrayOf(3, wiseCode.inviteCode))
        }
        val link = "$miniAppUrl?startapp=${Base64.getEncoder().encodeToString(startQuery.toByteArray())}"
        val content = WISE_INVITE_CONTENT + "\n\n$link"
        val id = "wise:invite:$userId"
        val queryResult = InlineQueryResultArticle(
            id,
            """Hey, I have an excellent WISE Credit Score 🌟🌟🌟.""", content
        )
            .url(link)
            .hideUrl(false)
            .thumbnailUrl("https://static.tbook.vip/inline_query_logo.png")
        val answer = AnswerInlineQuery(queryId, queryResult)
        bot.execute(answer)
    }

    @GetMapping("invitations")
    fun getInvitationDetail(): Any {
        val principal = SecurityContextHolder.getContext().authentication.principal.toString()
        val user = userService.getUserByPrincipal(principal)!!
        val cnt = tgInvitationService.getInviteCnt(user.userId)
        val plainCnt = tgInvitationService.getInviteCntWithPremium(user.userId, false)
        val premiumCnt = tgInvitationService.getInviteCntWithPremium(user.userId, true)
        val plainInvitees = tgInvitationService.getInviteeDetailWithPremium(user.userId, 5, false)
        val premiumInvitees = tgInvitationService.getInviteeDetailWithPremium(user.userId, 5, true)
        return mapOf(
            "code" to 200,
            "data" to TgInvitationDetail(cnt, plainCnt, premiumCnt, plainInvitees, premiumInvitees)
        )
    }


    private fun isPassportsAbleToMerge(passportA: PassportAccounts, passportB: PassportAccounts): Boolean {
        return when {
            (passportA.evmAddress != "" && passportB.evmAddress != "") || (passportA.tonAddress != "" && passportB.tonAddress != "")
                    || (passportA.suiAddress != "" && passportB.suiAddress != "")
                    || (passportA.twitterName != "" && passportB.twitterName != "") || (passportA.dcName != "" && passportB.dcName != "")
                    || (passportA.tgName != "" && passportB.tgName != "") -> false

            else -> true
        }
    }

    private fun handleMergeCallback(callbackData: String, callbackQuery: CallbackQuery) {
        val chatId = callbackQuery.message().chat().id()

        if (callbackData == "cancel_merge") {
            withFailedLog {
                bot.execute(
                    EditMessageText(chatId, callbackQuery.message().messageId(), "Account merge cancelled.")
                )
            }
            return
        }

        val parts = callbackData.split(":")
        if (parts.size != 3 || parts[0] != "merge") {
            return
        }

        val newUserId = parts[1].toLongOrNull() ?: return
        val oldUserId = parts[2].toLongOrNull() ?: return

        try {
            // Perform the merge
            userService.updateTwiUserId(oldUserId, newUserId)
            userTelegramService.updateTgUserId(oldUserId, newUserId)
            userService.transVerifiedCredentialsAndRewards(oldUserId, newUserId)

            // Bind the Telegram account to the user
            val from = callbackQuery.from()
            val tgInfo = TgAuthCallbackReq(
                from.id(),
                from.firstName().orEmpty(),
                from.lastName().orEmpty(),
                from.username().orEmpty()
            )
            userTelegramService.addUserTgInfo(newUserId, tgInfo)

            withFailedLog {
                bot.execute(
                    EditMessageText(chatId, callbackQuery.message().messageId(),
                        "Accounts successfully merged! Your Telegram account has been bound to your user account."
                    )
                )
            }
        } catch (e: Exception) {
            log.error(e) { "Error merging accounts" }
            withFailedLog {
                bot.execute(
                    EditMessageText(chatId, callbackQuery.message().messageId(),
                        "An error occurred while merging accounts. Please try again later."
                    )
                )
            }
        }
    }

    companion object {
        private val mapper = jacksonObjectMapper()
        private val SHARE_CONTENT = """@%s Hi friend, 💅click to get your lucky cards. 🎉
            |🔥 The thrilling scratch competition is now in full bloom! 💥
            |🎁 Prize Pool: 💰Notcoin, 💲20,000U
        """.trimMargin()

        private val START_CONTENT_1 = """🔥 The thrilling scratch competition is now in full bloom\! 💥💥💥
        |💅Tap "Scratch and Win" to get your lucky cards and share with your friends to earn more\. 🎉 🎉
        """.trimMargin()

        private val START_CONTENT_3 = """🧧💰🥇🎉🎊🤑🏆🪙
You’ve got 5 free scratch cards\!

Scratch to earn 💵Notcoin 💰usdt  🎁TPoints and more"""

        private val START_CONTENT = """🎉Congrats\! You’ve got 5 free scratch cards\!

Scratch to earn 💎Notcoin 💰usdt  🎁TPoints and more"""

        private val START_CONTENT_2 = """Scroll up in our channel for more surprises!🧧🎊
https://t.me/tbookincentive"""

//        private val START_CONTENT_4 = """🥳Get Noticed! Get Paid!
//
//🚀 <b>Generate your TON WISE Credit,</b> mint your WISE Credit SBT and showcase your impact!
//🔥 <b>Late Night DeFi is LIVE!</b> Tap into top yields with <b>TON Society and TON’s 6 biggest DeFi.</b> Master DeFi strategies and start earning your rewards today！""".trimIndent()

        private val START_CONTENT_4 = """<b>Welcome to TBook!</b>

Earn Soulbound Token assets by completing tasks and unlock exclusive rewards and airdrops.

- Earn SBT and more assets
- level up TBook WISE credit score
- Incentive passport
- Invite friends
- ……
""".trimIndent()

        private val START_BY_INVITE_CONTENT = """🎁Invitation Accepted\!🎁
        |You've boosted for @%s\! 🚀
        |💅Tap "Scratch and Win" to get your lucky cards and share with your friends to earn more\. 🎉
        """.trimMargin()

        private val SCORE_CONTENT =
            """Mint your TON Credit SBT, elevate your WISE Credit score, which reflects your social influence, on\-chain activity and Web3 assets\. Backed by our Proof of Contribution mechanism and dynamic RWA mirroring, your impact is recorded as immutable SBTs, helping top projects recognize and reward trusted contributors\.
        """.trimMargin()

        private val SUPPORT_CONTENT =
            """Dear community, please feel free to contact us anytime while participating, attesting, and earning with TBook\.

🪪Incentive Passport
The Incentive Passport is a digital credential that serves as proof of contribution, participation, and engagement\. Powered by on\-chain and social attestations, it connects user achievements across multiple chains, fostering loyalty and enhancing ecosystem interoperability\.

🏅WISE Score
Mint your WISE SBT to boost your WISE Credit Score, which reflects your social influence, on\-chain activity, expertise, and Web3 assets\. Backed by our Proof of Contribution mechanism and dynamic RWA mirroring, your impact is recorded as immutable SBTs, helping top projects recognize and reward trusted contributors\.

🚀Incentive Hub
Explore and engage in the most potential projects and earn rewards\! 📖QA Doc For any additional questions, please consult our QA section first\.

📪Report
For any inquiries or feedback of TBook products, please don’t hesitate to reach out to us via Telegram\.

📣Community
Telegram Channel: [https://t\.me/tbookincentive](https://t.me/tbookincentive)
Twitter: [https://x\.com/realtbook](https://x.com/realtbook)
Website: [https://www\.tbook\.com](https://www.tbook.com)
        """.trimMargin()

        private const val CAMPAIGN_CONTENT = """🚀 Engage the best airdrops by tap on Explore Campaigns\."""

        private val INLINE_QUERY_CONTENT = """ Hi friend, get your 5 scratch cards🎉💅

        |Scratch to earn 🪙 Notcoin 💵20,000U 🏆TPoints
        """.trimMargin()

        private val SHARE_QUERY_CONTENT =
            """ 🎁I have obtained the WISE Credential  and 🎉 improved my WISE Credit Score.

🔥Come on to obtain yours!
        """.trimIndent()

        private val WISE_INVITE_CONTENT = """Checkout my WISE Credit Score! 🌟

Check yours and claim a WISE Credit SBT on Ton Society🤩

"""
        private val specialCampaignStartContent = """ 🔥<b>Join the Late Night DeFi Campaign on TON!</b>🔥

            |🥳Your chance to earn BIG is here!
            |💎Engage with <b>bemo, Tonstakers, STON.fi, DeDust, EVAA</b>, and <b>Storm Trade</b> to earn SBTs and claim your <b>TON Society airdrop!</b>

            |<a href="%s">💥Join the Campaign Now!⚡️</a>

            | %s
        """.trimMargin()
        private val earnSBTContent = """Your SBTs are critical assets that grant you premium benefits, tap to unlock your SBT assets and access exclusive airdrops\.
        """.trimMargin()
    }

}

data class TgInvitationDetail(
    val totalCnt: Long,
    val inviteCnt: Long,
    val premiumCnt: Long,
    val invitees: List<TelegramInvitee>,
    val premiumInvitees: List<TelegramInvitee>
)