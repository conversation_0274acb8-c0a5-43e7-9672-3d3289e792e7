package com.rewardoor.app.controller

import com.google.gson.Gson
import com.rewardoor.app.auth.AddressAuthentication
import com.rewardoor.app.dao.tables.TBUserCredentialNew
import com.rewardoor.app.dao.tables.TBUserDiscord
import com.rewardoor.app.dao.tables.TBUserTelegram
import com.rewardoor.app.dao.tables.TBUserTwitter
import com.rewardoor.app.dto.UrlReqDto
import com.rewardoor.app.services.*
import com.rewardoor.enums.SocialType
import com.rewardoor.model.Credential
import com.rewardoor.model.CredentialLabelType
import com.rewardoor.model.UnbindInfo
import org.jetbrains.exposed.sql.and
import org.jetbrains.exposed.sql.select
import org.jetbrains.exposed.sql.update
import org.json.JSONObject
import org.springframework.http.HttpStatus
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.web.reactive.function.client.WebClient
import org.springframework.transaction.annotation.Transactional
import org.springframework.transaction.support.TransactionTemplate
import org.springframework.web.bind.annotation.*
import org.springframework.web.reactive.function.client.ExchangeStrategies
import org.springframework.web.server.ResponseStatusException
import java.net.URI

@RestController
@RequestMapping("social")
class SocialController(
    val dcService: DiscordService,
    val telegramService: TelegramService,
    val twitterService: UserTwitterService,
    val credentialService: CredentialService,
    val campaignService: CampaignService,
    val userTwitterService: UserTwitterService,
    val userService: UserService,
    val userCredentialGroupService: CredentialGroupService,
    val transactionTemplate: TransactionTemplate,
    private val customCredentialService: CustomCredentialService
) {
    private val log = mu.KotlinLogging.logger {}
    private val tonStakerUrl = "https://t.me/tonstakers_bot?profile"
    private val bemoUrl = "https://t.me/bemo_finance_bot?profile"
    private val evaaUrl = "https://t.me/EvaaAppBot?profile"
    private val stonFiUrl = "https://t.me/ston_fi?profile"
    private val dedustUrl = "https://t.me/dedustBot?profile"
    private val stormTradeUrl = "https://t.me/StormTradeBot?profile"

    private final val strategies = ExchangeStrategies.builder()
        .codecs { codecs -> codecs.defaultCodecs().maxInMemorySize(20 * 1024 * 1024) }
        .build()
    private val webClient = WebClient.builder().exchangeStrategies(strategies).build()

    @PostMapping("getInfo")
    fun getInfo(@RequestBody req: UrlReqDto): Map<String, Any?> {
        if (req.credentialId == 8L) return mapOf("link" to "Visit")
        val uri = URI(req.url)
        val host = uri.host
        return when {
            Credential.isTwitterTask(req.labelType) -> {
                if (host.equals("twitter.com", true) || host.equals("x.com", true)) {
                    twitterService.getInfoByUrl(uri, req.labelType)
                } else {
                    mapOf("code" to 4001, "message" to "Invalid Twitter URL")
                }
            }

            Credential.isDiscordTask(req.labelType) -> {
                if (host.equals("discord.com", true) || host.equals("discord.gg", true)) {
                    val r = dcService.getInfoByUrl(req.url, req.roleId).toMutableMap()
                    r
                } else {
                    mapOf("code" to 4001, "message" to "Invalid Discord URL")
                }
            }

            Credential.isTelegramTask(req.labelType) -> {
                if (host.equals("t.me", true) || host.equals("telegram.org", true)) {
                    telegramService.getInfoByUrl(req.url)
                } else {
                    mapOf("code" to 4001, "message" to "Invalid Telegram URL")
                }
            }

            Credential.isVisitTask(req.labelType) -> {
                mapOf("code" to 200, "message" to "OK", "data" to mapOf("visitPageName" to req.visitPageName))
            }

            Credential.isRegisterTask(req.labelType) -> {
                mapOf("code" to 200, "message" to "OK", "data" to mapOf("eventName" to "Register an event"))
            }

            Credential.isSubmitTask(req.labelType) -> {
                mapOf("code" to 200, "message" to "OK", "data" to mapOf("title" to req.title))
            }

            req.labelType == CredentialLabelType.STAKE_SOME_TON_ON_TONSTAKERS -> {
                mapOf("code" to 200, "message" to "OK", "data" to mapOf("link" to tonStakerUrl))
            }

            req.labelType == CredentialLabelType.STAKE_SOME_TON_ON_BEMO -> {
                mapOf("code" to 200, "message" to "OK", "data" to mapOf("link" to bemoUrl))
            }

            req.labelType == CredentialLabelType.BORROW_USDT || req.labelType == CredentialLabelType.SUPPLY_STTON_OR_TSTON_IN_EVAA -> {
                mapOf("code" to 200, "message" to "OK", "data" to mapOf("link" to evaaUrl))
            }

            req.labelType == CredentialLabelType.PROVIDE_LIQUIDITY_ON_STON_FI -> {
                mapOf("code" to 200, "message" to "OK", "data" to mapOf("link" to stonFiUrl))
            }

            req.labelType == CredentialLabelType.PROVIDE_LIQUIDITY_ON_DEDUST -> {
                mapOf("code" to 200, "message" to "OK", "data" to mapOf("link" to dedustUrl))
            }

            req.labelType == CredentialLabelType.VAULT_USDT_OR_TON || req.labelType == CredentialLabelType.OPEN_TRADE_IN_TRADING_PAIR -> {
                mapOf("code" to 200, "message" to "OK", "data" to mapOf("link" to stormTradeUrl))
            }

            req.labelType == CredentialLabelType.SUBSCRIBE_TO_TELEGRAM_PREMIUM -> {
                mapOf("code" to 200, "message" to "OK", "data" to mapOf("link" to "https://t.me/premium"))
            }

            req.labelType == CredentialLabelType.STAKE_TON_ON_HIPO -> {
                mapOf(
                    "code" to 200,
                    "message" to "OK",
                    "data" to mapOf("link" to "https://t.me/HipoFinanceBot?profile")
                )
            }

            req.labelType == CredentialLabelType.DEPOSIT_tsTON_OR_stTON_AND_MINT_ON_AQUA -> {
                mapOf("code" to 200, "message" to "OK", "data" to mapOf("link" to "https://aquaprotocol.xyz/"))
            }

            req.labelType == CredentialLabelType.STAKE_STORM_ON_JVAULT -> {
                mapOf("code" to 200, "message" to "OK", "data" to mapOf("link" to "https://t.me/JVaultBot?profile"))
            }

            req.labelType == CredentialLabelType.DEPOSIT_TON_ON_DAOLAMA -> {
                mapOf("code" to 200, "message" to "OK", "data" to mapOf("link" to "https://app.daolama.co/yield"))
            }

            req.labelType == CredentialLabelType.DEPOSIT_USDT_ON_TONHEDGE -> {
                mapOf("code" to 200, "message" to "OK", "data" to mapOf("link" to "https://t.me/ton_hedge_bot?profile"))
            }

            req.labelType == CredentialLabelType.BUY_MID_RISK_INDEX_WITH_TON_ON_SETTLETON -> {
                mapOf("code" to 200, "message" to "OK", "data" to mapOf("link" to "https://t.me/settleton_bot?profile"))
            }

            req.labelType == CredentialLabelType.DEPOSIT_tsTON_OR_USDT_ON_PARRATON -> {
                mapOf("code" to 200, "message" to "OK", "data" to mapOf("link" to "https://t.me/parraton_bot?profile"))
            }

            req.labelType == CredentialLabelType.DEPOSIT_tsTON_OR_stTON_ON_TONSTABLE -> {
                mapOf("code" to 200, "message" to "OK", "data" to mapOf("link" to "https://t.me/TonStableBot?profile"))
            }

            req.labelType == CredentialLabelType.DEPOSIT_TON_ON_TONPOOLS -> {
                mapOf("code" to 200, "message" to "OK", "data" to mapOf("link" to "https://t.me/tonpools_bot?profile"))
            }

            req.labelType == CredentialLabelType.LONG_OR_SHORT_PERPS_ON_TRAPDOOR -> {
                mapOf(
                    "code" to 200,
                    "message" to "OK",
                    "data" to mapOf("link" to "https://t.me/tradoor_io_bot?profile")
                )
            }

            req.labelType == CredentialLabelType.TOKEN_TRANSFER_ON_GASPUMP -> {
                mapOf("code" to 200, "message" to "OK", "data" to mapOf("link" to "https://t.me/gasPump_bot?profile"))
            }

            req.labelType == CredentialLabelType.TOKEN_SWAP_ON_RAINBOWSWAP -> {
                mapOf(
                    "code" to 200,
                    "message" to "OK",
                    "data" to mapOf("link" to "https://t.me/rainbow_swap_bot?profile")
                )
            }

            req.labelType == CredentialLabelType.BOOST_BLUM_IN_OPEN_LEAGUE -> {
                mapOf("code" to 200, "message" to "OK", "data" to mapOf("link" to "https://t.me/BlumCryptoBot?profile"))
            }

            req.labelType == CredentialLabelType.COMPLETE_DAILY_CHECK_IN_ON_CATIZEN -> {
                mapOf("code" to 200, "message" to "OK", "data" to mapOf("link" to "https://t.me/catizenbot?profile"))
            }

            req.labelType == CredentialLabelType.COMPLETE_DAILY_CHECK_IN_ON_YESCOIN -> {
                mapOf(
                    "code" to 200,
                    "message" to "OK",
                    "data" to mapOf("link" to "https://t.me/theYescoin_bot?profile")
                )
            }

            req.labelType == CredentialLabelType.PROVIDE_LIQUIDITY_FOR_WAT_OR_TON_ON_GAMEE -> {
                mapOf("code" to 200, "message" to "OK", "data" to mapOf("link" to "https://t.me/gameechannel"))
            }

            req.labelType == CredentialLabelType.COMPLETE_A_TRADE_IN_ANY_NFT_COLLECTION_ON_GETGEMS -> {
                mapOf("code" to 200, "message" to "OK", "data" to mapOf("link" to "https://t.me/getgems"))
            }

            req.labelType == CredentialLabelType.BOOST_TON_STATION_IN_OPEN_LEAGUE -> {
                mapOf(
                    "code" to 200,
                    "message" to "OK",
                    "data" to mapOf("link" to "https://t.me/tonstationgames_bot?profile")
                )
            }

            req.labelType == CredentialLabelType.STAKE_SOME_TON_ON_TONSTAKERS_FOR_DEGEN -> {
                mapOf(
                    "code" to 200,
                    "message" to "OK",
                    "data" to mapOf("link" to "https://t.me/tonstakers_bot?profile")
                )
            }

            req.labelType == CredentialLabelType.HOLDING_CATI_OF_CITIZEN -> {
                mapOf("code" to 200, "message" to "OK", "data" to mapOf("link" to "https://t.me/catizenbot?profile"))
            }

            req.labelType == CredentialLabelType.HOLDING_FTON_OF_FANTON -> {
                mapOf("code" to 200, "message" to "OK", "data" to mapOf("link" to "https://t.me/fanton_nft_en"))
            }

            req.labelType == CredentialLabelType.HOLDING_RBTC_OF_ROCKY_RABBIT -> {
                mapOf("code" to 200, "message" to "OK", "data" to mapOf("link" to "https://t.me/rockyrabbitio"))
            }

            req.labelType == CredentialLabelType.HOLDING_FNZ_OF_FANZEE -> {
                mapOf("code" to 200, "message" to "OK", "data" to mapOf("link" to "https://t.me/fanzeelabs"))
            }

            req.labelType == CredentialLabelType.HOLDING_PUMP_OF_PUMPERS -> {
                mapOf("code" to 200, "message" to "OK", "data" to mapOf("link" to "https://t.me/pumpers"))
            }

            req.labelType == CredentialLabelType.HOLDING_RANDOM_OF_RANDOMTG -> {
                mapOf("code" to 200, "message" to "OK", "data" to mapOf("link" to "https://t.me/random"))
            }

            req.labelType == CredentialLabelType.HOLDING_GRAM_OF_GRAM -> {
                mapOf("code" to 200, "message" to "OK", "data" to mapOf("link" to "https://t.me/gramcoinorg"))
            }

            req.labelType == CredentialLabelType.HOLDING_JETTON_OF_JETTON -> {
                mapOf("code" to 200, "message" to "OK", "data" to mapOf("link" to "https://t.me/jetton_chat"))
            }

            req.labelType == CredentialLabelType.HOLDING_UP_OF_TONUP -> {
                mapOf("code" to 200, "message" to "OK", "data" to mapOf("link" to "https://t.me/TonUP_io"))
            }

            req.labelType == CredentialLabelType.HOLDING_CES_OF_SWAP_COFFEE -> {
                mapOf("code" to 200, "message" to "OK", "data" to mapOf("link" to "https://t.me/swap_coffee"))
            }

            req.labelType == CredentialLabelType.HOLDING_TON_OF_TON -> {
                mapOf("code" to 200, "message" to "OK", "data" to mapOf("link" to "https://t.me/tonblockchain"))
            }

            req.labelType == CredentialLabelType.AKEDO_GAME_TRANSFER -> {
                mapOf("code" to 200, "message" to "OK", "data" to mapOf("link" to "https://t.me/Akedo_Bot/AkedoPlatform?startapp=channelWPMGBbv5RGbq4pR5"))
            }

            req.labelType == CredentialLabelType.ANY_CRAFT_TRADE -> {
                mapOf("code" to 200, "message" to "OK", "data" to mapOf("link" to "https://t.me/anycraftbot/play?startapp=-sbt"))
            }

            req.labelType == CredentialLabelType.BAZA_TRADE -> {
                mapOf("code" to 200, "message" to "OK", "data" to mapOf("link" to "https://t.me/baza_mall_bot"))
            }

            req.labelType == CredentialLabelType.CHECK_IN_N_DAYS -> {
                val credential = credentialService.getCredentialById(req.credentialId)!!
                val options = credential.options
                val ctaLink = options.substringAfter("\"ctaLink\":\"").substringBefore("\"").trim()
                mapOf("code" to 200, "message" to "OK", "data" to mapOf("link" to ctaLink))
            }

            req.labelType == CredentialLabelType.OFF_CHAIN_CUSTOM_TASK -> {

                if (req.verifyApiLink.isNullOrBlank()) {
                    return mapOf(
                        "code" to 400,
                        "status" to "error",
                        "message" to "API is missing",
                    )
                }

                if (req.ctaLink.isNullOrBlank()) {
                    return mapOf(
                        "code" to 400,
                        "status" to "error",
                        "message" to "CTA Link is missing",
                    )
                }

                // 1. Check CTA API
//                val ctaApiData = customCredentialService.getCtaLinkByApi(req.ctaApiLink!!)

                // 2. Check API Verify Link
                val checkCustomCredentialApiData =
                    customCredentialService.checkCustomCredentialApi(req.verifyApiLink!!, req.condition)

                val isValid = checkCustomCredentialApiData.isAPILinkCanVerify

                return if (isValid) {
                    mapOf(
                        "code" to 200,
                        "status" to "success",
                        "message" to "OK",
                        "data" to mapOf(
                            "isValid" to true,
                            "ctaLink" to req.ctaLink
                        ),
                    )
                } else {
                    mapOf(
                        "code" to 400,
                        "status" to "error",
                        "message" to "The APl link is invalid.\n" +
                                "Please adjust the API format according to the API Standard and try again.",
                        "data" to mapOf(
                            "isValid" to checkCustomCredentialApiData.isAPILinkCanVerify,
                            "ctaLink" to req.ctaLink
                        ),
                    )
                }
            }

            else -> mapOf("code" to 200, "data" to mapOf("url" to req.url))
        }
    }

    @GetMapping("options")
    fun processData() {
        val campaignIdList = campaignService.getAllScheduledAndOnGoingCampaigns()?.map { it.campaignId }
        val credentialList = credentialService.getAllCredentials()
            .filter { it.options == "" || it.labelType == 8 || it.labelType == 10 || it.labelType == 13 || it.options == "null" || it.labelType == 4 || it.labelType == 5 }
        val filteredCredentialList = credentialList?.filter { credential ->
            campaignIdList?.contains(credential.campaignId) == true
        }!!
        for (credential in filteredCredentialList) {
            val req = UrlReqDto(
                url = credential.link,
                credentialId = credential.credentialId,
                roleId = credential.roleId,
                roleName = credential.roleName,
                labelType = credential.labelType,
                visitPageName = credential.visitPageName
            )
            val result = getInfo(req)
            val gson = Gson()
            val json = gson.toJson(result["data"])
            credential.options = json
            credentialService.updateCredential(credential)
        }
    }

    private fun getSocialType(labelType: Int): SocialType {
        return when {
            Credential.isTwitterTask(labelType) -> {
                SocialType.TWITTER
            }

            Credential.isDiscordTask(labelType) -> {
                SocialType.DISCORD
            }

            Credential.isTelegramTask(labelType) -> {
                SocialType.TELEGRAM
            }

            else -> SocialType.DEFAULT
        }
    }

    @GetMapping("resetCredentialNew")
    fun resetCredentialNew(@RequestHeader("Authorization") token: String) {
        if (token != "rB110w") {
            throw ResponseStatusException(HttpStatus.UNAUTHORIZED, "Unauthorized")
        }
        val allCredentials = credentialService.getAllCredentials().associateBy { it.credentialId }
        val userCredentialList = userCredentialGroupService.getAllUserCredentials().filter { it.labelType == 0 }
        val groupedUsers = userCredentialList.groupBy { getSocialType(allCredentials[it.credentialId]?.labelType ?: 0) }
            .mapValues { it.value.map { c -> c.userId }.distinct() }.toMap()
        val (twitterUsers, discordUsers, tgUsers) = transactionTemplate.execute {
            val twitterUserIds = groupedUsers[SocialType.TWITTER] ?: emptyList()
            val twitterUsers = TBUserTwitter.slice(TBUserTwitter.userId, TBUserTwitter.twitterId)
                .select { TBUserTwitter.userId inList twitterUserIds }
                .associate { it[TBUserTwitter.userId] to it[TBUserTwitter.twitterId] }

            val discordUserIds = groupedUsers[SocialType.DISCORD] ?: emptyList()
            val discordUsers = TBUserDiscord.slice(TBUserDiscord.userId, TBUserDiscord.dcId)
                .select { TBUserDiscord.userId inList discordUserIds }
                .associate { it[TBUserDiscord.userId] to it[TBUserDiscord.dcId] }

            val tgUserIds = groupedUsers[SocialType.TELEGRAM] ?: emptyList()
            val tgUsers = TBUserTelegram.slice(TBUserTelegram.userId, TBUserTelegram.tgId)
                .select { TBUserTelegram.userId inList tgUserIds }
                .associate { it[TBUserTelegram.userId] to it[TBUserTelegram.tgId] }

            Triple(twitterUsers, discordUsers, tgUsers)
        }!!

        log.info {
            "userCredential count: ${userCredentialList.size}, twitterUser Count: ${twitterUsers.size}, " +
                    "dcUsers count: ${discordUsers.size}, tgUsers count: ${tgUsers.size}"
        }

        var count = 0
        for (ucs in userCredentialList.groupBy { it.credentialId }) {
            val credentialId = ucs.key
            val credential = allCredentials[credentialId] ?: continue
            val socialType = getSocialType(credential.labelType)
            if (socialType == SocialType.DEFAULT) continue

            for (userCredentialWindowed in ucs.value.windowed(100, 100, true)) {
                transactionTemplate.execute {
                    for (userCredential in userCredentialWindowed) {
                        userCredential.socialType = socialType.code
                        userCredential.socialId = when (socialType) {
                            SocialType.TWITTER -> twitterUsers[userCredential.userId].toString()
                            SocialType.DISCORD -> discordUsers[userCredential.userId].toString()
                            SocialType.TELEGRAM -> tgUsers[userCredential.userId].toString()
                            else -> ""
                        }
                        userCredential.labelType = allCredentials[userCredential.credentialId]!!.labelType

                        TBUserCredentialNew.update({
                            (TBUserCredentialNew.userId eq userCredential.userId) and
                                    (TBUserCredentialNew.credentialId eq userCredential.credentialId)
                        }) {
                            it[TBUserCredentialNew.socialId] = userCredential.socialId
                            it[TBUserCredentialNew.socialType] = userCredential.socialType
                            it[TBUserCredentialNew.labelType] = userCredential.labelType
                        }

                        if (userCredential.socialId == "null" || userCredential.socialId.isEmpty()) {
                            log.info { "Empty socialId: ${userCredential.userId}, ${userCredential.socialId}" }
                            continue
                        }
//                        TBUserCredentialNew.update({
//                            (TBUserCredentialNew.userId eq userCredential.userId) and
//                                    (TBUserCredentialNew.credentialId eq userCredential.credentialId)
//                        }) {
//                            it[TBUserCredentialNew.socialId] = userCredential.socialId
//                            it[TBUserCredentialNew.socialType] = userCredential.socialType
//                            it[TBUserCredentialNew.labelType] = userCredential.labelType
//                        }
                        //userCredentialGroupService.updateUserCredential(userCredential)
                        count++
                        log.info { "Update userCredential finished: ${userCredential.userId}, ${userCredential.socialId}" }
                        log.info { "Total count: $count" }
                    }
                }
            }
        }
    }

    @PostMapping("unbind")
    fun unbindAccount(
        @RequestParam("id") id: String,
        @RequestParam("socialType") socialType: Int
    ): Map<String, Any?> {
        val principal = SecurityContextHolder.getContext().authentication as AddressAuthentication
        val addressUserId = principal.userId
        try {
            when (socialType) {
                SocialType.TWITTER.code -> {
                    val utInfo = userTwitterService.getUserInfoByTwitterId(id)!!
                    val curUserId = utInfo.userId
                    if (curUserId != addressUserId) {
                        return mapOf(
                            "code" to 400,
                            "message" to "You cannot unbind the social media account that is not associated with the current passport."
                        )
                    }
                    val userCredentials = userCredentialGroupService.getByUserId(curUserId)
                        .filter { it.socialType == SocialType.TWITTER.code }
                    if (userCredentials.isEmpty()) {
                        val unbindInfo = UnbindInfo(
                            userId = curUserId,
                            address = principal.address,
                            socialType = SocialType.TWITTER.code,
                            socialId = id,
                            credentialId = 0
                        )
                        userService.addUnbindInfo(unbindInfo)
                    }
                    if (utInfo != null) {
                        userService.unbindTwitterUser(id)
                        for (userCredential in userCredentials) {
                            val unbindInfo = UnbindInfo(
                                userId = curUserId,
                                address = principal.address,
                                socialType = SocialType.TWITTER.code,
                                socialId = id,
                                credentialId = userCredential.credentialId
                            )
                            userService.addUnbindInfo(unbindInfo)
                        }
                    }
                }

                SocialType.DISCORD.code -> {
                    val dcUser = dcService.getUserInfoByDcId(id)
                    if (dcUser?.userId != addressUserId) {
                        return mapOf(
                            "code" to 400,
                            "message" to "You cannot unbind the social media account that is not associated with the current passport."
                        )
                    }
                    if (dcUser != null) {
                        val userCredentials = userCredentialGroupService.getByUserId(dcUser.userId)
                            .filter { it.socialType == SocialType.DISCORD.code }
                        if (userCredentials.isEmpty()) {
                            val unbindInfo = UnbindInfo(
                                userId = dcUser.userId,
                                address = principal.address,
                                socialType = SocialType.DISCORD.code,
                                socialId = id,
                                credentialId = 0
                            )
                            userService.addUnbindInfo(unbindInfo)
                        }
                        userService.unbindDcUser(dcUser.userId)
                        for (userCredential in userCredentials) {
                            val unbindInfo = UnbindInfo(
                                userId = dcUser.userId,
                                address = principal.address,
                                socialType = SocialType.DISCORD.code,
                                socialId = id,
                                credentialId = userCredential.credentialId
                            )
                            userService.addUnbindInfo(unbindInfo)
                        }
                    }
                }

                SocialType.TELEGRAM.code -> {
                    val tgUser = telegramService.getUserInfoByTgId(id.toLong())
                    if (tgUser?.userId != addressUserId) {
                        return mapOf(
                            "code" to 400,
                            "message" to "You cannot unbind the social media account that is not associated with the current passport."
                        )
                    }
                    if (tgUser != null) {
                        val userCredentials = userCredentialGroupService.getByUserId(tgUser.userId)
                            .filter { it.socialType == SocialType.TELEGRAM.code }
                        if (userCredentials.isEmpty()) {
                            val unbindInfo = UnbindInfo(
                                userId = tgUser.userId,
                                address = principal.address,
                                socialType = SocialType.TELEGRAM.code,
                                socialId = id,
                                credentialId = 0
                            )
                            userService.addUnbindInfo(unbindInfo)
                        }
                        userService.unbindTgUser(tgUser.userId)
                        for (userCredential in userCredentials) {
                            val unbindInfo = UnbindInfo(
                                userId = tgUser.userId,
                                address = principal.address,
                                socialType = SocialType.TELEGRAM.code,
                                socialId = id,
                                credentialId = userCredential.credentialId
                            )
                            userService.addUnbindInfo(unbindInfo)
                        }
                    }
                }

            }
            return mapOf("code" to 200, "message" to "OK")
        } catch (e: Exception) {
            println("bind exception : $e")
            return mapOf("code" to 401, "message" to "FAILED")

        }
    }

}