package com.rewardoor.app.controller

import com.auth0.jwt.JWT
import com.fasterxml.jackson.databind.node.ArrayNode
import com.fasterxml.jackson.databind.node.ObjectNode
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.google.api.client.googleapis.auth.oauth2.GoogleIdTokenVerifier
import com.rewardoor.app.auth.Jwt
import com.rewardoor.app.dto.ZKNonceRequest
import com.rewardoor.app.dto.ZKPRequest
import com.rewardoor.app.services.ZKLoginService
import jakarta.servlet.http.HttpServletRequest
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.beans.factory.annotation.Value
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpStatus
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*
import org.springframework.web.reactive.function.client.WebClient
import org.springframework.web.reactive.function.client.WebClientResponseException
import org.springframework.web.server.ResponseStatusException
import java.time.Instant


@RestController
@RequestMapping("/zkproxy/v1")
class ZKProxyController(
    private val zkLoginService: ZKLoginService,
    @Qualifier("googleVerifier") private val googleVerifier: GoogleIdTokenVerifier,
    @Value("\${zklogin.enoki_url}") private val zkUrl: String,
    @Value("\${zklogin.enoki_api_key}") private val zkAppClientId: String
) {
    private val log = mu.KotlinLogging.logger {}
    private val client = WebClient.create()
    private val authHeaderValue = "Bearer $zkAppClientId"
    private val mapper = jacksonObjectMapper()

    @RequestMapping("zklogin", method = [RequestMethod.GET, RequestMethod.POST])
    fun zkLogin(@RequestHeader("zklogin-jwt") jwt: String): ResponseEntity<Any> {
        val token = JWT.decode(jwt)
        if (token.expiresAtAsInstant < Instant.now()) {
            throw ResponseStatusException(HttpStatus.UNAUTHORIZED, "token expired")
        }
        val idToken = googleVerifier.verify(jwt)
        val email = idToken.payload.email
        val response = client.get()
            .uri("${zkUrl}/zklogin")
            .header("zklogin-jwt", jwt)
            .header("Authorization", authHeaderValue)
            .retrieve()
            .bodyToMono(String::class.java).block() ?: "{}"
        val zkResponse = mapper.readTree(response) as ObjectNode
        val zkData = zkResponse.get("data") as ObjectNode
        val salt = zkData.get("salt").asText()
        val address = zkData.get("address").asText()
        val zk = zkLoginService.addZKLogin(token.issuer, token.subject, email, salt, address)
        val cookie = Jwt.buildCookie(zk.userId)
        val setCk = cookie.toString().split(";")[0] + ";"
        zkData.put("newUser", zk.newUser)
//        zkData.put("ck", setCk)
        zkResponse.replace("data", zkData)
        return ResponseEntity.ok().header("Set-Cookie", cookie.toString())
            .body(zkResponse)
    }

    @PostMapping("zklogin/nonce")
    fun zkLoginNonce(@RequestBody zkNonceRequest: ZKNonceRequest): String {
        return client.post()
            .uri("${zkUrl}/zklogin/nonce")
            .contentType(MediaType.APPLICATION_JSON)
            .header("Authorization", authHeaderValue)
            .bodyValue(mapper.writeValueAsString(zkNonceRequest))
            .retrieve()
            .bodyToMono(String::class.java)
            .doOnError {
                if (it is WebClientResponseException) {
                    val responseBody = it.responseBodyAsString
                    log.error("Error response: ${it.statusCode} $responseBody")
                } else {
                    log.error("Error: ${it.message}", it)
                }
            }
            .block() ?: "{}"
    }

    @RequestMapping("zklogin/zkp", method = [RequestMethod.POST])
    fun zkLoginZKP(
        @RequestHeader("zklogin-jwt") jwt: String,
        @RequestBody zkpRequest: ZKPRequest
    ): String {
        return client.post()
            .uri("${zkUrl}/zklogin/zkp")
            .contentType(MediaType.APPLICATION_JSON)
            .header("zklogin-jwt", jwt)
            .header("Authorization", authHeaderValue)
            .bodyValue(mapper.writeValueAsString(zkpRequest))
            .retrieve()
            .bodyToMono(String::class.java)
            .doOnError {
                if (it is WebClientResponseException) {
                    val responseBody = it.responseBodyAsString
                    log.error("Error response: ${it.statusCode} $responseBody")
                } else {
                    log.error("Error: ${it.message}", it)
                }
            }
            .block() ?: "{}"
    }

    @RequestMapping("app", method = [RequestMethod.GET, RequestMethod.POST])
    fun metaData(): String {
        return client.get()
            .uri("${zkUrl}/app")
            .header("Authorization", authHeaderValue)
            .retrieve()
            .bodyToMono(String::class.java).block() ?: "{}"
    }

    @PostMapping("/transaction-blocks/sponsor")
    fun sponsorTransaction(request: HttpServletRequest): ResponseEntity<Any> {
        val bodyValue = request.inputStream.use { it.reader().readText() }
        val bodyNode = mapper.readTree(bodyValue) as ObjectNode
        val allowedMoveCallTargets = arrayOf(
            "0xfa0e78030bd16672174c2d6cc4cd5d1d1423d03c28a74909b2a148eda8bcca16::clock::access",
            "0x1a3dbf55125880fede9a4264ced5ffd803548ed41818278b05882153c14131a4::test_sbt_on_sui::mint"
            )
        bodyNode.set<ArrayNode>("allowedMoveCallTargets", mapper.valueToTree(allowedMoveCallTargets))
        val zkJwtHeader = request.getHeader("zklogin-jwt")
        val response = client.post()
            .uri("${zkUrl}/transaction-blocks/sponsor")
            .contentType(MediaType.APPLICATION_JSON)
            .header("Authorization", authHeaderValue)
            .header("zklogin-jwt", zkJwtHeader)
            .bodyValue(bodyNode.toString())
            .retrieve()
            .bodyToMono(String::class.java)
            .doOnError { e ->
                if (e is WebClientResponseException) {
                    val responseBody = e.responseBodyAsString
                    log.error("Error response: ${e.statusCode} $responseBody")
                } else {
                    log.error("Error: ${e.message}", e)
                }
            }
            .block() ?: "{}"
        return ResponseEntity.ok(response)
    }

    @PostMapping("/transaction-blocks/sponsor/{sponsorId}")
    fun sponsorTransactionById(
        @PathVariable("sponsorId") sponsorId: String,
        request: HttpServletRequest
    ): ResponseEntity<Any> {
        val bodyValue = request.inputStream.use { it.reader().readText() }
        val zkJwtHeader = request.getHeader("zklogin-jwt")
        val response = client.post()
            .uri("${zkUrl}/transaction-blocks/sponsor/${sponsorId}")
            .contentType(MediaType.APPLICATION_JSON)
            .header("Authorization", authHeaderValue)
            .header("zklogin-jwt", zkJwtHeader)
            .bodyValue(bodyValue)
            .retrieve()
            .bodyToMono(String::class.java)
            .doOnError { e ->
                if (e is WebClientResponseException) {
                    val responseBody = e.responseBodyAsString
                    log.error("Error response: ${e.statusCode} $responseBody")
                } else {
                    log.error("Error: ${e.message}", e)
                }
            }
            .block() ?: "{}"
        return ResponseEntity.ok(response)
    }

    @RequestMapping("/{*path}")
    fun forward(
        @PathVariable("path") path: String,
        request: HttpServletRequest
    ): Any {
        val headers = HttpHeaders()
        request.headerNames.toList().forEach { name ->
            if (name.equals("Authorization", true)) {
                return@forEach
            }
            request.getHeaders(name).toList().forEach { value ->
                headers.add(name, value)
            }
        }

        return when (request.method) {
            "GET" -> {
                client.get()
                    .uri("${zkUrl}/${path}")
                    .header("Authorization", authHeaderValue)
                    .headers { it.addAll(headers) }
                    .retrieve()
                    .bodyToMono(String::class.java).block() ?: "{}"
            }

            "POST" -> {
                val bodyValue = request.inputStream.use { it.reader().readText() }
                client.post()
                    .uri("${zkUrl}/${path}")
                    .header("Authorization", authHeaderValue)
                    .headers { it.addAll(headers) }
                    .bodyValue(bodyValue)
                    .retrieve()
                    .bodyToMono(String::class.java).block() ?: "{}"
            }

            "OPTIONS" -> {
                ResponseEntity.ok()
            }

            else -> throw ResponseStatusException(HttpStatus.BAD_REQUEST, "invalid method")
        }
    }
}