package com.rewardoor.app.controller

import com.rewardoor.app.services.LuckyDrawService
import com.rewardoor.app.services.TPointsService
import com.rewardoor.app.services.TgInvitationService
import com.rewardoor.app.services.WiseScoreService
import com.rewardoor.model.LuckyDrawResult
import com.rewardoor.model.UserWiseScore
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

@RestController
@RequestMapping("luckyDraw")
class LuckyDrawController(
    val luckyDrawService: LuckyDrawService,
    val tgLuckyDrawTimesService: TgInvitationService,
    val wiseScoreService: WiseScoreService,
    val tPointsService: TPointsService
) {
    @GetMapping("/level/{userId}")
    fun getFissionLevel(@PathVariable("userId") userId: Long): Int {
        return luckyDrawService.getFissionLevel(userId)
    }

    @GetMapping("/{userId}")
    fun getLuckyDrawResult(@PathVariable("userId") userId: Long): LuckyDrawResult {
        return luckyDrawService.drawResult(userId)
    }

    @GetMapping("/tPoints/{userId}")
    fun getUserTPoints(@PathVariable("userId") userId: Long): Any {
        val info = tgLuckyDrawTimesService.getLuckyDrawCnt(userId)
        return mapOf(
            "code" to 200,
            "tPoints" to tPointsService.getUserTPointsNum(userId),
            "luckyDrawCnt" to info.totalTimes,
            "nextDistribution" to info.nextDistribution,
            "isInSBTWhiteList" to luckyDrawService.getIsInSBTList(userId)
        )
    }

    @GetMapping("/tPoints/top/{limit}")
    fun getUserTPointsTop(@PathVariable("limit") limit: Int): Any {
        val topResult = tPointsService.getTopLimitTPointsUser(limit)
        return mapOf(
            "code" to 200,
            "topResult" to topResult
        )
    }

    @GetMapping("/inSBTList/{userId}")
    fun isInSBTWhiteList(@PathVariable("userId") userId: Long): Boolean {
        return luckyDrawService.getIsInSBTList(userId)
    }

    @GetMapping("/joinSBTList/{userId}")
    fun jonSBTWhiteList(@PathVariable("userId") userId: Long): Any {
        val sbtList = wiseScoreService.getSBTById(userId)
        if (sbtList != null) {
            return mapOf(
                "code" to 404,
                "message" to "already in SBT white list"
            )
        }
        val inviteCnt = tgLuckyDrawTimesService.getInviteCnt(userId)
        if (inviteCnt < 5) {
            return mapOf(
                "code" to 404,
                "message" to "The number of invitees is less than 5"
            )
        }
        val joinCnt = luckyDrawService.joinSBTWhiteList(userId)
        if (joinCnt == 1) {
            return mapOf(
                "code" to 200,
                "message" to "join success"
            )
        } else {
            return mapOf(
                "code" to 404,
                "message" to "already in SBT white list"
            )
        }
    }


}