package com.rewardoor.app.controller

import com.rewardoor.app.auth.AddressAuthentication
import com.rewardoor.app.services.TPointsService
import com.rewardoor.app.services.WiseInviteService
import com.rewardoor.app.services.WiseScoreService
import com.rewardoor.enums.TPointsVanguardLevel
import com.rewardoor.model.TPointsVanguard
import com.rewardoor.model.TPointsVanguardConfig
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

@RestController
@RequestMapping("vanguard")
class VanguardController(
    val tPointsService: TPointsService,
    private val wiseInviteService: WiseInviteService,
    private val wiseScoreService: WiseScoreService
) {
    private val adminUserIds = listOf<Long>(
        53103538732184 // sue
        , 248067700078 //cyrus
        , 229933120808 //cyrus_test
    )

    @GetMapping("/level/{userId}")
    fun getUserLevel(@PathVariable("userId") userId: Long): Any? {

        val userVanguard = tPointsService.getUserVanguardById(userId)
        return if (userVanguard == null) {
            mapOf(
                "code" to 400,
                "message" to "you don't have access to Vanguard"
            )
        } else {
            // update user vanguard level & points
            val userWiseScoreNum = wiseScoreService.getScoreById(userId)?.totalScore ?: 0
            userVanguard.wiseScoreNum = userWiseScoreNum
            val userTPoints = tPointsService.getUserTPointsNum(userId)
            if (userTPoints != userVanguard.tPointsNum) {
                tPointsService.updateUserVanguard(userVanguard)
                userVanguard.tPointsNum = userTPoints
            }
            updateVanguardWiseInviteTimes(userId, userVanguard)
            mapOf(
                "code" to 200,
                "tPointsVanguard" to userVanguard
            )
        }
    }

    @GetMapping("/level/config")
    fun getLevelConfig(): List<TPointsVanguardConfig> {
        return TPointsVanguardLevel.values().map {
            TPointsVanguardConfig(
                level = it.code,
                pointsNum = it.pointsNum,
                nextLevelPointsNum = it.nextLevelPointsNum,
                vanguardPrivilege = it.vanguardPrivilege
            )
        }
    }

    @GetMapping("/create/{userId}")
    fun createUserVanguard(@PathVariable("userId") userId: Long): Any? {
//        val principal = SecurityContextHolder.getContext().authentication as AddressAuthentication
//        if (!adminUserIds.contains(principal.userId)) {
//            return mapOf(
//                "code" to 400,
//                "message" to "you are not authorized to create a user",
//            )
//        }
        val userVanguard = tPointsService.getUserVanguardById(userId)
        if (userVanguard != null) {
            return mapOf(
                "code" to 400,
                "message" to "Vanguard is already created",
            )
        }
        val userTPoints = tPointsService.getUserTPointsNum(userId)
        for (level in TPointsVanguardLevel.values().reversed()) {
            if (userTPoints >= level.pointsNum) {
                val tPointsVanguard = TPointsVanguard(
                    userId = userId,
                    level = level.code,
                    tPointsNum = userTPoints
                )
                tPointsService.createUserVanguard(tPointsVanguard)
                return mapOf(
                    "code" to 200,
                    "tPointsVanguard" to tPointsVanguard
                )
            }
        }
        return mapOf(
            "code" to 4004,
            "level" to "unknown level",
            "tPoints" to userTPoints
        )
    }

    private fun updateVanguardWiseInviteTimes(userId: Long, tPointsVanguard: TPointsVanguard) {
        val newInviteTimes = TPointsVanguardLevel.getByCode(tPointsVanguard.level).vanguardPrivilege?.wiseInviteNum ?: 0
        val wiseInviteCode = wiseInviteService.getOrAddInviteCode(userId, 1)
        if (newInviteTimes > wiseInviteCode.totalTimes) {
            wiseInviteService.updateInviteCode(userId, 1, newInviteTimes)
        }
    }
}