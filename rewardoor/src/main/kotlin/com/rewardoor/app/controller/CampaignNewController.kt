package com.rewardoor.app.controller

import com.rewardoor.app.dao.ParticipantRepository
import com.rewardoor.app.services.*
import com.rewardoor.model.*
import org.springframework.core.env.Environment
import org.springframework.http.HttpStatus
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.web.bind.annotation.*
import org.springframework.web.server.ResponseStatusException
import java.time.Instant

@RestController
@RequestMapping("campaignNew")
class CampaignNewController(
    val credentialService: CredentialService,
    val credentialGroupService: CredentialGroupService,
    val userService: UserService,
    val campaignService: CampaignService,
    val userTwitterService: UserTwitterService,
    val nftService: NFTService,
    val callbackService: CallbackService,
    val projectService: ProjectService,
    private val discordService: DiscordService,
    service: DiscordService,
    private val telegramService: TelegramService,
    val wiseScoreService: WiseScoreService,
    private val env: Environment,
    private val privilegeCheck: PrivilegeCheck,
    private val participantRepository: ParticipantRepository,
) {
    val prodCampaignIds = listOf(
        0,               //normie badge default
        59750443878720,  // Guardian of BLUM
        59751238878734,  // Guardian of Catizen
        59801027878955,  // Guardian of Gamee
        59801219878962,  // Guardian of Yescoin
        59836088879476,  // Guardian of TON Station
        59801416878968,  // Guardian of GetGems
        59801647878973,  // FTON Holder
        59801772878980,  // RBTC Holder
        59802064878986,  // FNZ Holder
        59802292878997,  // PUMP Holder
        59802707879004,  // RANDOM Holder
        59802860879009,  // GRAM Holder
        59803039879016,  // JETTON Holder
        59803176879022,  // UP Holder
        59803299879029,  // CES Holder
        59803420879034  // Toncoin Holder
    )

    @GetMapping("/{id}")
    fun getCam(@PathVariable("id") id: Long): CampaignTotal? {
        val principal = SecurityContextHolder.getContext().authentication.principal.toString()
        if (principal == "anonymousUser") {
            val campaignTotal = campaignService.getVerifiedCamByCredential(id, 0, "") ?: return null
            for (group in campaignTotal.groups) {
                val credentials = group.credentialList
                for (credential in credentials) {
                    // 当credential.labelType是wise score积分加入tg group时，返回时不带上options里的inviteLink
                    if (credential.labelType == CredentialLabelType.WISE_SCORE_JOIN_PRIVATE_GROUP) {
                        val options = credential.options
                        if (options.contains("\"inviteLink\":\"")) {
                            val inviteLink = options.substringAfter("\"inviteLink\":\"").substringBefore("\"").trim()
                            if (inviteLink.isNotEmpty() && !inviteLink.contains("{") && !inviteLink.contains("}")) {
                                credential.options = options.replace(inviteLink, "")
                            }
                        }
                    }
                }
            }
            campaignTotal.participation = campaignService.getParticipationById(id)
            return campaignTotal
        }
        val user = userService.getUserByPrincipal(principal)!!
        val userId = user.userId

        println("userId is " + userId + " wallet is " + user.wallet)
        val campaignTotal = campaignService.getVerifiedCamByCredential(id, userId, user.wallet) ?: return null
        for (group in campaignTotal.groups) {
            val credentials = group.credentialList
            for (credential in credentials) {
                // 当credential.labelType是wise score积分加入tg group时，如果用户没有验证通过，返回时不带上options里的inviteLink
                if (credential.labelType == CredentialLabelType.WISE_SCORE_JOIN_PRIVATE_GROUP) {
                    val userCredentialResult = participantRepository.getResult(userId, credential.credentialId)
                    if (userCredentialResult == null) {
                        val options = credential.options
                        if (options.contains("\"inviteLink\":\"")) {
                            val inviteLink = options.substringAfter("\"inviteLink\":\"").substringBefore("\"").trim()
                            if (inviteLink.isNotEmpty() && !inviteLink.contains("{") && !inviteLink.contains("}")) {
                                credential.options = options.replace(inviteLink, "")
                            }
                        }
                    }
                }
            }
        }
        campaignTotal.participantNum = campaignService.getParticipantNumsByCampaignId(id)
//        campaignTotal.participation = campaignService.getParticipationById(id)
        return campaignTotal
    }

    @GetMapping("/consumer/{id}")
    fun getUserCam(@PathVariable("id") id: Long): Any? {
        val principal = SecurityContextHolder.getContext().authentication.principal.toString()
        if (campaignService.getCampaignById(id) == null) {
            return mapOf("code" to 404, "message" to "The campaign is not exist")
        }
        if (principal == "anonymousUser") {
            val campaignTotal = campaignService.getVerifiedCamByCredential(id, 0, "") ?: return null
            for (group in campaignTotal.groups) {
                val credentials = group.credentialList
                for (credential in credentials) {
                    // 当credential.labelType是wise score积分加入tg group时，返回时不带上options里的inviteLink
                    if (credential.labelType == CredentialLabelType.WISE_SCORE_JOIN_PRIVATE_GROUP) {
                        val options = credential.options
                        if (options.contains("\"inviteLink\":\"")) {
                            val inviteLink = options.substringAfter("\"inviteLink\":\"").substringBefore("\"").trim()
                            if (inviteLink.isNotEmpty() && !inviteLink.contains("{") && !inviteLink.contains("}")) {
                                credential.options = options.replace(inviteLink, "")
                            }
                        }
                    }
                }
            }
            campaignTotal.participantNum = campaignService.getParticipantNumsByCampaignId(id)
            if (campaignTotal.campaign.status == CampaignStatus.DELETED || campaignTotal.campaign.status == CampaignStatus.REJECTED) {
                return mapOf("code" to 204, "campaignTotal" to campaignTotal)
            }
            return mapOf("code" to 200, "campaignTotal" to campaignTotal)
        }
        val user = userService.getUserByPrincipal(principal)!!
        val userId = user.userId

        val campaignTotal = campaignService.getVerifiedCamByCredential(id, userId, user.wallet) ?: return null
        for (group in campaignTotal.groups) {
            val credentials = group.credentialList
            for (credential in credentials) {
                // 当credential.labelType是wise score积分加入tg group时，如果用户没有验证通过，返回时不带上options里的inviteLink
                if (credential.labelType == CredentialLabelType.WISE_SCORE_JOIN_PRIVATE_GROUP) {
                    val userCredentialResult = participantRepository.getResult(userId, credential.credentialId)
                    if (userCredentialResult == null) {
                        val options = credential.options
                        if (options.contains("\"inviteLink\":\"")) {
                            val inviteLink = options.substringAfter("\"inviteLink\":\"").substringBefore("\"").trim()
                            if (inviteLink.isNotEmpty() && !inviteLink.contains("{") && !inviteLink.contains("}")) {
                                credential.options = options.replace(inviteLink, "")
                            }
                        }
                    }
                }
            }
        }
        campaignTotal.participantNum = campaignService.getParticipantNumsByCampaignId(id)
//        campaignTotal.participation = campaignService.getParticipationById(id)
        if (campaignTotal.campaign.status == CampaignStatus.DELETED || campaignTotal.campaign.status == CampaignStatus.REJECTED) {
            return mapOf("code" to 204, "campaignTotal" to campaignTotal)
        }
        return mapOf("code" to 200, "campaignTotal" to campaignTotal)
    }

    @GetMapping("claim/{groupId}")
    fun claimReward(@PathVariable("groupId") groupId: Long): CredentialGroup? {
        val principal = SecurityContextHolder.getContext().authentication.principal.toString()
        val group = credentialGroupService.getCredentialGroupById(groupId)!!
        val campaign = campaignService.getCampaignById(group.campaignId)!!
        if (principal == "anonymousUser") {
            println(" not login " + groupId)
            return group
        }
        val user = userService.getUserByPrincipal(principal)!!
        val userId = user.userId
        val points = campaignService.updatePointClaimedType(group, userId, campaign)
        group.pointList = points
        if (group.sbtList.isNotEmpty()) {
            val sbts = campaignService.updateSBTClaimedType(group, userId, campaign)
            group.sbtList = sbts
        }
        return group
    }

    @GetMapping("claimSBT/{sbtId}")
    fun mintSBT(@PathVariable("sbtId") sbtId: Long): Any? {
        val principal = SecurityContextHolder.getContext().authentication.principal.toString()
        val sbt = credentialGroupService.getSBTById(sbtId)!!
        val group = credentialGroupService.getCredentialGroupById(sbt.groupId)!!
        val activityId = sbt.activityId
        if (principal == "anonymousUser") {
            println(" not login " + group.id)
            return group
        }
        val user = userService.getUserByPrincipal(principal)!!
        val userId = user.userId
        try {
            for (credential in group.credentialList) {
                if (credential.labelType == 66) {
                    credentialGroupService.verifyCredential(user, null, credential)
                }
                if (credential.labelType in listOf(
                        CredentialLabelType.T_POINTS_EXCHANGE,
                        CredentialLabelType.T_POINTS_EXCHANGE_TOP_50,
                        CredentialLabelType.CHECK_IN_N_DAYS
                    )
                ) {
                    val isCredentialVerified = credentialGroupService.verifyCredential(user, null, credential)
                    if (!isCredentialVerified) {
                        return mapOf(
                            "code" to 400,
                            "status" to "error",
                            "message" to "Task Not Finished"
                        )
                    }
                }
            }
        } catch (e: Exception) {
            println("verify credential labelType 66 error:$e")
        }
        val userLink = wiseScoreService.getUserSBTLink(userId, activityId, sbtId, sbt.groupId)
        if (userLink["status"] == "error") {
            if (userLink["link"] == "reward link with such activity id and wallet address already created") {
                print("user $userId for sbt $sbtId is already created")
            }
            return mapOf(
                "code" to 400,
                "status" to "error",
                "message" to userLink["link"]
            )
        } else if (userLink["status"] == "hasMinted") {
            return mapOf(
                "code" to 4004,
                "status" to "hasMinted",
                "message" to "the user has minted sbt",
                "link" to userLink["link"]
            )
        } else if (userLink["link"] == "") {
            return mapOf(
                "code" to 40004,
                "status" to "error",
                "message" to "Get mint url failed"
            )
        } else {
            return mapOf(
                "code" to 200,
                "status" to "minting",
                "message" to "Get mint url success",
                "link" to userLink["link"]
            )
        }

    }
//    @GetMapping("claimSBTTest/{userId}")
//    fun mintSBTTest(@PathVariable("userId") userId: Long) {
//        val userLink = wiseScoreService.getUserSBTLink(userId, 563)
//        println(userLink)
//    }

    @GetMapping("defi")
    fun getDefiCampaign(): Campaign? {
        // todo : prod campaignId
        val defiCampaignId = if (env.activeProfiles.contains("prod")) 56120827779159 else 556762923578
        val campaign = campaignService.getCampaignById(defiCampaignId)
        if (campaign != null) {
            val project = projectService.getProject(campaign.projectId)!!
            campaign.projectName = project.projectName
            campaign.projectUrl = project.projectUrl
        }
        return campaign
    }

    @GetMapping("claimNft/{nftId}")
    fun claimNft(@PathVariable("nftId") nftId: Long): CredentialGroup? {
        val principal = SecurityContextHolder.getContext().authentication.principal.toString()
        val nft = nftService.getNFTById(nftId)!!
        val group = credentialGroupService.getCredentialGroupById(nft.groupId)!!
        if (principal == "anonymousUser") {
            println(" not login " + group.id)
            return group
        }
        val user = userService.getUserByPrincipal(principal)!!
        val userId = user.userId
        return campaignService.updateClaimType(1, nftId, userId, group.id, 4)
    }

//    @GetMapping("/user/{campaignId}")
//    fun getUserCamInfo(
//        @PathVariable("campaignId") campaignId: Long,
//        @PathVariable("spaceId") spaceId: String
//    ): CampaignTotalWithUser? {
//        val campaignTotal = campaignService.getCampaignTotalById(campaignId)!!
//        val address = SecurityContextHolder.getContext().authentication.principal.toString()
//        val user = userService.getUserByAddress(address)!!
//        val userId = user.userId
//        val participant = campaignService.getParticipantByUserId(userId, campaignId)!!
//        //更新C端登录participant状态
//        campaignService.updateParticipantBySpace(userId, campaignId, spaceId)
//        return CampaignTotalWithUser(
//            campaignTotal, participant
//        )
//    }

    @GetMapping("/user/{credentialId}")
    fun getUserVerifiedInfo(
        @PathVariable("campaignId") campaignId: Long
    ): CampaignTotal? {
        val address = SecurityContextHolder.getContext().authentication.principal.toString()
        val user = userService.getUserByPrincipal(address)!!
        val userId = user.userId
        return campaignService.getVerifiedCamByCredential(campaignId, userId, address)
    }

    @GetMapping("/normie/campaigns")
    fun getNormieCampaigns(): List<Long> {
        return prodCampaignIds
    }

    @PostMapping("/verify/normie/{campaignId}")
    fun verifyNormieCampaign(@PathVariable("campaignId") campaignId: Long): Any {
        val address = SecurityContextHolder.getContext().authentication.principal.toString()
        val user = userService.getUserByPrincipal(address)!!
        if (!user.ton.binded) {
            return mapOf(
                "code" to 400,
                "message" to "user need to bind ton wallet"
            )
        }
        val normieVerifyResult = credentialGroupService.verifyNormieCampaign(user, campaignId)
        var tonHolderSBTResult: CredentialGroupService.NormieVerifyResult? = null
        if (campaignId == prodCampaignIds.last()) {
            tonHolderSBTResult = if (normieVerifyResult.last().labelType == 51) normieVerifyResult.last() else null
        }
        return mapOf(
            "code" to 200,
            "data" to mapOf(
                "normieVerifyResult" to normieVerifyResult,
                "tonHoldlerSBT" to tonHolderSBTResult
            )
        )
    }

    @GetMapping("/verify/normie")
    fun verifyNormie(): Any {
        var user: User? = null
        val address = SecurityContextHolder.getContext().authentication.principal.toString()
        if (address == "") {
            user = userService.getUserById(57220106837736)!!
        } else {
            user = userService.getUserByPrincipal(address)!!
        }
        if (!user.ton.binded) {
            return mapOf(
                "code" to 400,
                "user" to user,
                "message" to "user need to bind ton wallet"
            )
        }
        // todo : update 15 normie campaign ids
        val campaignIds = if (env.activeProfiles.contains("prod")) prodCampaignIds else emptyList()
        val normieVerifyResult = credentialGroupService.verifyWholeNormieCredentials(user, campaignIds)
        val tonHolderSBTResult = if (normieVerifyResult.last().labelType == 51) normieVerifyResult.last() else null
        return mapOf(
            "code" to 200,
            "user" to user,
            "normieVerifyResult" to if (tonHolderSBTResult != null) normieVerifyResult.dropLast(1) else normieVerifyResult,
            "tonHoldlerSBT" to tonHolderSBTResult
        )
    }

    @PostMapping("/credential/{credentialId}/verify")
    fun verifyCredential(@PathVariable("credentialId") credentialId: Long): Credential {
        val address = SecurityContextHolder.getContext().authentication.principal.toString()
        val user = userService.getUserByPrincipal(address)!!
        val utInfo = userTwitterService.getUserInfo(user.userId)
        val credential = credentialService.getCredentialById(credentialId) ?: throw ResponseStatusException(
            HttpStatus.NOT_FOUND,
            "credential not found"
        )
        if (projectService.projectNeedEvm(credential.projectId) && !user.evm.binded) {
            throw ResponseStatusException(HttpStatus.NOT_ACCEPTABLE, "EVM account is not connected")
        }
        if (projectService.projectNeedTon(credential.projectId) && !user.ton.binded) {
            throw ResponseStatusException(HttpStatus.NOT_ACCEPTABLE, "TON is not connected")
        }
        val isVerified = if (credential.labelType <= 3) {
            if (utInfo?.connected != true) {
                throw ResponseStatusException(HttpStatus.NOT_ACCEPTABLE, "Twitter account is not connected")
            }
            credentialGroupService.verifyCredential(user, utInfo, credential)
        } else {
            credentialGroupService.verifyCredential(user, utInfo, credential)
        }
        if (isVerified) {
            if (Credential.isDiscordTask(credential.labelType)) {
                callbackService.callback(
                    user,
                    credential.projectId,
                    credentialId,
                    Instant.now().toEpochMilli(),
                    userDiscord = discordService.getUserDcInfo(user.userId)
                )
            } else if (Credential.isTwitterTask(credential.labelType)) {
                callbackService.callback(
                    user,
                    credential.projectId,
                    credentialId,
                    Instant.now().toEpochMilli(),
                    userTwitterInfo = utInfo
                )
            } else if (Credential.isTelegramTask(credential.labelType)) {
                callbackService.callback(
                    user,
                    credential.projectId,
                    credentialId,
                    Instant.now().toEpochMilli(),
                    userTelegramInfo = telegramService.getUserInfo(user.userId)
                )
            } else {
                callbackService.callback(user, credential.projectId, credentialId, Instant.now().toEpochMilli())
            }
        }
        return credentialGroupService.getCredentialByUser(user, credentialId)
    }

    @PostMapping("/credential/{credentialId}/visitPageVerify")
    fun updateCredential(@PathVariable("credentialId") credentialId: Long): UserCredential {
        val address = SecurityContextHolder.getContext().authentication.principal.toString()
        val user = userService.getUserByPrincipal(address)!!
        val credential = credentialService.getCredentialById(credentialId) ?: throw ResponseStatusException(
            HttpStatus.NOT_FOUND,
            "credential not found"
        )
        if (credential.labelType == 8) {
            val userCredential = UserCredential(
                userId = user.userId,
                address = user.wallet,
                credentialId = credential.credentialId,
                campaignId = credential.campaignId,
                status = 1,
                participantDate = Instant.now(),
                labelType = credential.labelType
            )
            credentialGroupService.addUserCredential(userCredential)
            callbackService.callback(user, credential.projectId, credentialId, Instant.now().toEpochMilli())
            return userCredential
        } else
            throw ResponseStatusException(
                HttpStatus.NOT_FOUND,
                "credential not found"
            )
    }

    @PostMapping("participant")
    fun addParticipant(
        @RequestBody loggedInUser: LoggedInUser
    ): Participant {
        val principal = SecurityContextHolder.getContext().authentication.principal.toString()
        privilegeCheck.checkUserId(loggedInUser.userId, principal)
        val participant =
            campaignService.getParticipantByUserIdAndCampaignId(loggedInUser.userId, loggedInUser.campaignId)
        if (participant == null) {
            val participant = Participant(
                userId = loggedInUser.userId,
                campaignId = loggedInUser.campaignId,
                wallet = loggedInUser.address,
                nfts = emptyList(),
                points = emptyList(),
                pointNum = 0L,
                isJoin = false,
                isVisit = false,
                credentials = emptyList(),
                verifiedCredentials = emptyList(),
                participantDate = Instant.now(),
                isTwitterLogin = loggedInUser.isTwitterLogin
            )
            campaignService.addParticipant(participant)
            return participant
        }
        return participant
    }

    @GetMapping("/project/consumer/{projectId}")
    fun getGroupByProjectId(@PathVariable("projectId") projectId: Long): List<CampaignTotal>? {
//        val address = SecurityContextHolder.getContext().authentication.principal.toString()
//        val user = userService.getUserByPrincipal(address)!!
//        val creatorId = user.userId
        val campaignTotals = campaignService.getCampaignTotalsByProjectId(projectId)!!
            .filter { it.campaign.status == CampaignStatus.ON_GOING || it.campaign.status == CampaignStatus.SCHEDULED }
        for (campaignTotal in campaignTotals) {
            campaignTotal.participantNum =
                campaignService.getParticipantNumsByCampaignId(campaignTotal.campaign.campaignId)
        }
//        val eligibles = credentialService.getCredentialEligibleCount(crs.map { it.credentialId })
//        crs.forEach { it.eligibleCount = eligibles[it.credentialId]?:0 }
        return campaignTotals
    }

    // Get campaign totals by project ID with optional status filter (ON_GOING/SCHEDULED/COMPLETED)
    @GetMapping("/project/consumer/{projectId}/v2")
    fun getGroupByProjectIdV2(
        @PathVariable("projectId") projectId: Long,
        @RequestParam(required = false) status: List<Int>?
    ): List<CampaignTotal>? {
        val statusList = status?.mapNotNull { statusValue ->
            when (statusValue) {
                1 -> CampaignStatus.ON_GOING
                2 -> CampaignStatus.SCHEDULED
                3 -> CampaignStatus.COMPLETED
                else -> null
            }
        } ?: listOf(CampaignStatus.ON_GOING, CampaignStatus.SCHEDULED)

        val campaignTotals = campaignService.getCampaignTotalsByProjectId(projectId)!!
            .filter { it.campaign.status in statusList }

        for (campaignTotal in campaignTotals) {
            campaignTotal.participantNum =
                campaignService.getParticipantNumsByCampaignId(campaignTotal.campaign.campaignId)
        }
        return campaignTotals
    }
}