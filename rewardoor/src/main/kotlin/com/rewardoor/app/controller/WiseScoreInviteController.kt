package com.rewardoor.app.controller

import com.rewardoor.app.dto.UserInfoDto
import com.rewardoor.app.services.TelegramService
import com.rewardoor.app.services.UserService
import com.rewardoor.app.services.WiseInviteService
import com.rewardoor.app.services.WiseScoreService
import com.rewardoor.enums.InviteCodeCheckedType
import com.rewardoor.enums.WiseScoreLevel
import com.rewardoor.model.User
import com.rewardoor.model.WiseInviteCode
import org.jetbrains.exposed.sql.javatime.timestampWithTimeZoneParam
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import java.time.Instant

@RestController
@RequestMapping("/wise-score-invite")
class WiseScoreInviteController(
    private val wiseInviteService: WiseInviteService,
    private val wiseScoreService: WiseScoreService,
    private val userService: UserService,
    private val telegramService: TelegramService
) {
    @GetMapping("my-code")
    fun myInviteCode(): SimpleResponseEntity<WiseInviteCodeResponse> {
        val principal = SecurityContextHolder.getContext().authentication.principal.toString()
        val user = userService.getUserByPrincipal(principal)!!
        val wiseScore = wiseScoreService.getScoreById(user.userId)
            ?: return SimpleResponseEntity.failed("You do not have WISE score.")
        val code = wiseInviteService.getOrAddInviteCode(user.userId, 1)
        val inviterId = wiseInviteService.getInviterUid(user.userId)
        val inviterCode = wiseInviteService.getInviterCode(user.userId)
        var inviterTgName = ""
        var photoUrl = ""
        inviterTgName = wiseInviteService.getInviterTgName(user.userId)
        if (inviterTgName.isEmpty()) {
            val userInfo = userService.getUserById(inviterId)
            inviterTgName = userInfo?.tgName ?: ""
            photoUrl = userInfo?.avatar ?: ""
        }
        val inviteeIdsWithDate = wiseInviteService.getInviteesWithDate(user.userId, 1)
        val inviteeIdsList = inviteeIdsWithDate.map { it.inviteeId }
        val inviteeTgInfo = telegramService.getUserInfos(inviteeIdsList).associateBy { it.userId }
        val invitees = inviteeIdsWithDate.mapIndexed { index, inviteeWithDate ->
            val inviteAddScore = wiseInviteService.calcCurrentAddScore(index)
            InviteeDto(
                inviteeWithDate.inviteeId,
                inviteeTgInfo[inviteeWithDate.inviteeId]?.firstName ?: "",
                inviteAddScore,
                inviteeWithDate.createdAt
            )
        }
        return SimpleResponseEntity.success(
            "",
            WiseInviteCodeResponse(
                user.userId,
                inviterTgName,
                photoUrl,
                inviterCode,
                code.inviteCode,
                code.totalTimes,
                code.usedTimes,
                invitees
            )
        )
    }

    @GetMapping("has-code")
    fun checkInviteCode(): Any {
        val principal = SecurityContextHolder.getContext().authentication.principal.toString()
        val user = userService.getUserByPrincipal(principal)!!
        val code = wiseInviteService.getInviteCode(user.userId, 1) ?: return mapOf("hasCode" to false)
        return mapOf("hasCode" to true, "code" to code.inviteCode)
    }

    @PostMapping("apply-code")
    fun applyInvite(code: String): SimpleResponse {
        val principal = SecurityContextHolder.getContext().authentication.principal.toString()
        val user = userService.getUserByPrincipal(principal)!!
        return try {
            val result = wiseInviteService.addInvitee(code, 1, user)
            val noInviteMsg = "The invitation code does not exist. \n" + "Please try another one."
            val inviteMySelfMsg = "You've entered your own invitation code.\n" + "Please try another one."
            val inviteRepeatCodeMsg = "You have already been successfully invited by same code"
            val inviteRepeatMsg = "You are not a new user"
            val successMsg = "🌟🌟Successful to submit inviter's code!🌟🌟"

            when (result.codeCheckedType) {
                InviteCodeCheckedType.NO_INVITE -> SimpleResponse.failed(noInviteMsg)
                InviteCodeCheckedType.INVITE_MY_SELF -> SimpleResponse.failed(inviteMySelfMsg)
                InviteCodeCheckedType.INVITE_REPEAT -> SimpleResponse.failed(inviteRepeatMsg)
                InviteCodeCheckedType.INVITE_REPEAT_CODE -> SimpleResponse.failed(inviteRepeatCodeMsg)
                InviteCodeCheckedType.SUCCESS -> SimpleResponse.success(successMsg)
            }
        } catch (e: IllegalArgumentException) {
            SimpleResponse.failed(e.message ?: "Failed to apply invite code")
        }
    }
}

class InviteeDto(val userId: Long, val userName: String, val inviteAddScore: Int, val inviteDate: Instant) {
    val avatar: String =
        User.AVATAR_BASE + userId
}

class WiseInviteCodeResponse(
    val userId: Long,
    val inviterTgName: String,  //当前用户被谁邀请
    val avatar: String,
    val inviterCode: String,
    val code: String,
    val totalTimes: Int,
    val usedTimes: Int,
    val invitees: List<InviteeDto>
)