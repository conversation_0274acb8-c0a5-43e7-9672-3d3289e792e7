package com.rewardoor.app.controller

import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.rewardoor.app.services.CredentialService
import com.rewardoor.app.services.UserService
import com.rewardoor.model.Credential
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.web.bind.annotation.*

@RestController
@RequestMapping("credentials")
class CredentialController(
    val credentialService: CredentialService
) {
    @GetMapping("/all")
    fun getAllCredential(): List<Credential> {
        val crs = credentialService.getAllCredentials()
        val eligibles = credentialService.getCredentialEligibleCount(crs.map { it.credentialId })
        crs.forEach { it.eligibleCount = eligibles[it.credentialId]?:0 }
        return crs
    }

    @GetMapping("/creator/{creatorId}")
    fun getCredentialByCreatorId(@PathVariable("creatorId") creatorId: Long): List<Credential> {
        val crs = credentialService.getCredentialByCreatorId(creatorId)
        val eligibles = credentialService.getCredentialEligibleCount(crs.map { it.credentialId })
        crs.forEach { it.eligibleCount = eligibles[it.credentialId]?:0 }
        return crs
    }
}