package com.rewardoor.app.controller

import com.auth0.jwt.JWT
import com.github.scribejava.core.oauth.OAuth20Service
import com.google.api.client.googleapis.auth.oauth2.GoogleIdTokenVerifier
import com.rewardoor.app.auth.AddressAuthentication
import com.rewardoor.app.auth.Jwt
import com.rewardoor.app.dto.StringPayloadRequest
import com.rewardoor.app.dto.ZKProofRequest
import com.rewardoor.app.services.ZKLoginService
import com.rewardoor.app.utils.Hashs
import com.rewardoor.model.UserZK
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.beans.factory.annotation.Value
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController
import org.springframework.web.reactive.function.client.WebClient
import org.springframework.web.server.ResponseStatusException
import java.time.Instant

@RestController
@RequestMapping("zk")
class ZKController(
    private val zkLoginService: ZKLoginService,
    @Qualifier("googleOauthService") private val googleService: OAuth20Service,
    @Qualifier("googleVerifier") private val googleVerifier: GoogleIdTokenVerifier,
    @Value("\${zklogin.proof_service_url}") private val proofUrl: String
) {
    private val webClient = WebClient.create()

    @PostMapping("salt")
    fun getSalt(@RequestBody request: StringPayloadRequest): ResponseEntity<Any> {
        val token = JWT.decode(request.payload)
        if (token.expiresAtAsInstant < Instant.now()) {
            throw ResponseStatusException(HttpStatus.UNAUTHORIZED, "token expired")
        }
        val idToken = googleVerifier.verify(request.payload)
        val email = idToken.payload.email
        val salt = Hashs.hkdfJwt(token)
        val zk = zkLoginService.addZKLogin(token.issuer, token.subject, email, salt)
        val cookie = Jwt.buildCookie(zk.userId)
        val setCk = cookie.toString().split(";")[0] + ";"
        return ResponseEntity.ok().header("Set-Cookie", cookie.toString())
            .body(
                mapOf(
                    "code" to 200,
                    "socialName" to email,
                    "zk" to zk
                )
            )
    }

    @PostMapping("proof")
    fun zkProof(@RequestBody request: ZKProofRequest): ResponseEntity<Any> {
        val principal = SecurityContextHolder.getContext().authentication as AddressAuthentication
        val zk = zkLoginService.getZKLoginById(principal.userId) ?: throw ResponseStatusException(
            HttpStatus.UNAUTHORIZED,
            "not zk login"
        )
        val proof = webClient.post()
            .uri(proofUrl)
            .header("Content-Type", "application/json")
            .bodyValue(request.proof)
            .retrieve()
            .bodyToMono(String::class.java).block()
        zkLoginService.updateZKAddress(principal.userId, request.address)
        return ResponseEntity.ok().body(
            mapOf(
                "code" to 200,
                "socialName" to zk.identity,
                "issuer" to zk.displayIssuer(),
                "proof" to proof
            )
        )
    }

    @PostMapping("address")
    fun updateZKAddress(@RequestParam("address") address: String): UserZK {
        val principal = SecurityContextHolder.getContext().authentication as AddressAuthentication
        zkLoginService.updateZKAddress(principal.userId, address)
        return zkLoginService.getZKLoginById(principal.userId)!!
    }
}