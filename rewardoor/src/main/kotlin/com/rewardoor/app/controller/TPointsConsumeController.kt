package com.rewardoor.app.controller

import com.rewardoor.app.auth.AddressAuthentication
import com.rewardoor.app.services.TPointsService
import com.rewardoor.app.services.TgInvitationService
import com.rewardoor.enums.TPointsBuyCardsLevel
import com.rewardoor.enums.TPointsIncreaseGrowthLevel
import com.rewardoor.enums.TPointsIncreaseLimitLevel
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

@RestController
@RequestMapping("tPoints")
class TPointsConsumeController(
    val tPointsService: TPointsService,
    val tgLuckyDrawTimesService: TgInvitationService
) {

    private val increaseLimitMaxLevel = 10
    private val increaseGrowthSpeedMaxLevel = 8

    @GetMapping("/buyCards/{level}")
    fun buyCards(@PathVariable("level") level: Int): Any {
        val principal = SecurityContextHolder.getContext().authentication as AddressAuthentication
        val userId = principal.userId
        val isHasEnoughTPointsToBuyCards = tPointsService.isHasEnoughTPointsToBuyCards(userId, 3, level)
        val isAbleToBuyCards = tPointsService.isAbleToBuyCards(userId, level)
        if (!isHasEnoughTPointsToBuyCards) {
            return mapOf(
                "code" to 404,
                "message" to "You don't have enough TPoints to buy cards."
            )
        }
        if (!isAbleToBuyCards) {
            return mapOf(
                "code" to 4004,
                "message" to "Each user is limited to purchasing 10 cards per day."
            )
        }
        val insertCount = tPointsService.buyCards(userId, 3, level)
        if (insertCount == 1) {
            return mapOf(
                "code" to 200,
                "message" to "Success."
            )
        } else {
            return mapOf(
                "code" to 500,
                "message" to "Failed."
            )
        }
    }

    @GetMapping("/boost/consumeType/{consumeType}")
    fun increaseCardLimitOrGrowthSpeed(@PathVariable("consumeType") consumeType: Int): Any {
        val principal = SecurityContextHolder.getContext().authentication as AddressAuthentication
        val userId = principal.userId
        val consumeMaxLevel = tPointsService.getLimitOrGrowthMaxLevel(userId, consumeType)
        if (consumeType == 1 && consumeMaxLevel == increaseLimitMaxLevel) {
            return mapOf(
                "code" to 4004,
                "message" to "Max level is 10."
            )
        }
        if (consumeType == 2 && consumeMaxLevel == increaseGrowthSpeedMaxLevel) {
            return mapOf(
                "code" to 4004,
                "message" to "Max level is 8."
            )
        }
        val isHasEnoughTPointsToBuyCards =
            tPointsService.isHasEnoughTPointsToBuyCards(userId, consumeType, consumeMaxLevel + 1)
        if (!isHasEnoughTPointsToBuyCards) {
            if (consumeType == 1) {
                return mapOf(
                    "code" to 404,
                    "message" to "You don't have enough TPoints to increase card upper limit."
                )
            } else {
                return mapOf(
                    "code" to 404,
                    "message" to "You don't have enough TPoints to increase card growth speed."
                )
            }
        }
        val insertCount = tPointsService.buyCards(userId, consumeType, consumeMaxLevel + 1)
        if (insertCount == 1) {
            return mapOf(
                "code" to 200,
                "message" to "Success."
            )
        } else {
            return mapOf(
                "code" to 500,
                "message" to "Failed."
            )
        }
    }

    @GetMapping("/boost/status")
    fun getBuyCardsStatus(): Any {
        val principal = SecurityContextHolder.getContext().authentication as AddressAuthentication
        val userId = principal.userId
        val todayBuyCardsRecords = tPointsService.getTodayBuyCardsRecords(userId)
        var todayBuyCardsNum = 0
        for (record in todayBuyCardsRecords) {
            val cardNum = tPointsService.getLevelBuyCardNum(record.level)
            todayBuyCardsNum += cardNum
        }
        val isAbleToBuyCards = todayBuyCardsNum < 10
        val info = tgLuckyDrawTimesService.getLuckyDrawCnt(userId)
        return mapOf(
            "code" to 200,
            "isAbleToBuyCards" to isAbleToBuyCards,
            "userId" to userId,
            "todayBuyCardsNum" to todayBuyCardsNum,
            "dailyFree" to info.dailyFree,
            "dailyTimeBonus" to info.dailyTimeBonus
        )
    }

    @GetMapping("/buyCards/levelMap")
    fun getBuyCardsLevelMap(): Array<Map<String, Int>> {
        return TPointsBuyCardsLevel.values().map {
            mapOf("level" to it.ordinal + 1, "cardCnt" to it.cardCnt, "pointsNum" to it.pointsNum)
        }.toTypedArray()
    }

    @GetMapping("/boost/levelMap/{consumeType}")
    fun getBoostLevelMap(@PathVariable("consumeType") consumeType: Int): Array<Map<String, Int>> {
        when (consumeType) {
            1 -> {
                return TPointsIncreaseLimitLevel.values().map {
                    mapOf("level" to it.code, "pointsNum" to it.pointsNum)
                }.toTypedArray()
            }

            2 -> {
                return TPointsIncreaseGrowthLevel.values().map {
                    mapOf("level" to it.code, "pointsNum" to it.pointsNum)
                }.toTypedArray()
            }

            3 -> {
                return TPointsBuyCardsLevel.values().map {
                    mapOf("level" to it.ordinal + 1, "cardCnt" to it.cardCnt, "pointsNum" to it.pointsNum)
                }.toTypedArray()
            }

            else -> {
                return emptyArray<Map<String, Int>>()
            }
        }
    }

    @GetMapping("/boost/level/{consumeType}")
    fun getUserMaxLevel(@PathVariable("consumeType") consumeType: Int): Any {
        val principal = SecurityContextHolder.getContext().authentication as AddressAuthentication
        val userId = principal.userId
        val consumeMaxLevel = tPointsService.getLimitOrGrowthMaxLevel(userId, consumeType)
        return mapOf(
            "code" to 200,
            "userId" to userId,
            "maxLevel" to consumeMaxLevel
        )

    }
}