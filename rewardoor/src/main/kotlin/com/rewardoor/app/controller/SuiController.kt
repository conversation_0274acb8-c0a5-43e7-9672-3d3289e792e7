package com.rewardoor.app.controller

import com.fasterxml.jackson.annotation.JsonSetter
import com.fasterxml.jackson.annotation.Nulls
import com.rewardoor.app.auth.AddressAuthentication
import com.rewardoor.app.auth.Jwt
import com.rewardoor.app.dao.*
import com.rewardoor.app.dto.SocialAccountBound
import com.rewardoor.app.dto.UserSuiDto
import com.rewardoor.app.services.*
import com.rewardoor.enums.SocialType
import com.rewardoor.model.*
import io.swagger.v3.oas.annotations.Operation
import org.json.JSONObject
import org.springframework.beans.factory.annotation.Value
import org.springframework.core.env.Environment
import org.springframework.http.ResponseEntity
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.web.bind.annotation.*
import java.net.URI
import java.net.http.HttpClient
import java.net.http.HttpRequest
import java.net.http.HttpResponse

@RestController
@RequestMapping("/sui")
class SuiController(
    private val userService: UserService,
    private val nonceService: NonceService,
    private val suiService: SuiService,
    private val campaignService: CampaignService,
    @Value("\${airdrop.ton_secret_key}") private val secretKey: String,
    private val wiseScoreService: WiseScoreService,
    private val credentialGroupService: CredentialGroupService,
    private val credentialService: CredentialService,
    private val suiSbtRepository: SuiSbtRepository,
    private val userTwitterService: UserTwitterService,
    private val participantRepository: ParticipantRepository,
    private val credentialRepository: CredentialRepository,
    private val env: Environment,
    private val credentialGroupRepository: CredentialGroupRepository,
    private val projectRepository: ProjectRepository,
    private val campaignRepository: CampaignRepository,
    private val tonSocietySyncRepo: TonSocietySyncRepository
) {
    @GetMapping("nonce")
    @Operation(summary = "Get nonce for Sui wallet", description = "Get a nonce for Sui wallet authentication")
    fun getNonce(@RequestParam("address") address: String): ResponseEntity<Any> {
        val nonce = "Sign this message to authenticate with TBook: ${System.currentTimeMillis()}"
        nonceService.addNonce(address.lowercase(), nonce)
        return ResponseEntity.ok(nonce)
    }

    @PostMapping("login")
    @Operation(summary = "Login with Sui wallet", description = "Login with Sui wallet address and signature")
    fun login(@RequestBody request: SuiLoginRequest): Any {
        val nonce = nonceService.getAndInvalidateNonce(request.address.lowercase())
            ?: return ResponseEntity.badRequest().body(mapOf("message" to "Invalid nonce", "code" to 4001))

        val isSignatureValid = suiService.verifySuiSignature(
            request.address, request.publicKey, request.signature,
            nonce, request.zkLogin, request.network
        )
        if (!isSignatureValid) {
            return ResponseEntity.badRequest().body(mapOf("message" to "Invalid signature", "code" to 4002))
        }

        var isLogin = false
        val idPrincipal = SecurityContextHolder.getContext().authentication.principal.toString()
        if (idPrincipal == "anonymousUser") isLogin = true

        val userSui = userService.getSuiWalletByAddress(request.address)
        if (isLogin) {
            return withLogin(userSui, request.address, request.publicKey)
        }

        val user = userService.getUserByPrincipal(idPrincipal)!!
        if (user.userId == userSui?.userId) {
            return withLogin(userSui, request.address, request.publicKey)
        }
        if (userSui != null) {
            val passportA = PassportAccounts(
                userId = user.userId,
                evmAddress = user.evm.evmWallet ?: "",
                tonAddress = user.ton.tonWallet ?: "",
                suiAddress = user.sui.suiWallet ?: "",
                twitterName = user.twitterName,
                dcName = user.dcName,
                tgName = user.tgName
            )
            val suiUser = userService.getUserById(userSui.userId)!!
            val passportB = PassportAccounts(
                userId = suiUser.userId,
                evmAddress = suiUser.evm.evmWallet ?: "",
                tonAddress = suiUser.ton.tonWallet ?: "",
                suiAddress = suiUser.sui.suiWallet ?: "",
                twitterName = suiUser.twitterName,
                dcName = suiUser.dcName,
                tgName = suiUser.tgName
            )
            val isAbleToMerge = isPassportsAbleToMerge(passportA, passportB)
            if (!isAbleToMerge) {
                return ResponseEntity.ok(
                    SocialAccountBound(
                        "This wallet is already bound to another user",
                        "SUI",
                        request.address,
                        passportA,
                        passportB
                    )
                )
            } else {
                return mapOf(
                    "code" to 400,
                    "message" to "The address has been taken, you need to merge the accounts.",
                    "passportA" to passportA,
                    "passportB" to passportB
                )
            }
        }

        val bindResult = userService.bindSuiWallet(user.userId, request.address, request.publicKey)
        if (!bindResult) {
            return ResponseEntity.badRequest().body(mapOf("message" to "Failed to bind wallet", "code" to 4002))
        }

        return ResponseEntity.ok(mapOf("code" to 200, "user" to UserSuiDto(request.address, true)))
    }

    private fun isPassportsAbleToMerge(passportA: PassportAccounts, passportB: PassportAccounts): Boolean {
        return when {
            (passportA.evmAddress != "" && passportB.evmAddress != "") || (passportA.tonAddress != "" && passportB.tonAddress != "")
                    || (passportA.suiAddress != "" && passportB.suiAddress != "")
                    || (passportA.twitterName != "" && passportB.twitterName != "") || (passportA.dcName != "" && passportB.dcName != "")
                    || (passportA.tgName != "" && passportB.tgName != "") -> false

            else -> true
        }
    }

    private fun withLogin(current: UserSui?, wallet: String, publicKey: String): ResponseEntity<Any> {
        if (current != null) {
            val cookie = Jwt.buildCookie(current.userId)
            return ResponseEntity.ok().header("Set-Cookie", cookie.toString()).body(
                mapOf("code" to 200, "user" to UserSuiDto(current.suiWallet, true))
            )
        } else {
            val userId = userService.registerSuiUser(wallet, publicKey)
            val cookie = Jwt.buildCookie(userId)
            return ResponseEntity.ok().header("Set-Cookie", cookie.toString()).body(
                mapOf("code" to 200, "user" to UserSuiDto(wallet, true))
            )
        }
    }

    @GetMapping("sbt/info/{campaignId}")
    // 根据campaign id获取project下的其他sui sbt、以及整体的trending sbt
    fun getSuiSbtInfos(@PathVariable campaignId: Long): ResponseEntity<Any> {
        val address = SecurityContextHolder.getContext().authentication.principal.toString()
        val user = userService.getUserByPrincipal(address)!!
        val auth = SecurityContextHolder.getContext().authentication as? com.rewardoor.app.auth.AddressAuthentication
        val chain = auth?.chain
        val userSui = userService.getSuiWalletByUserId(user.userId)
        if (!userSui.binded) {
            return ResponseEntity.ok(
                mapOf("code" to 400, "message" to "Please bind your Sui wallet first")
            )
        }
        val campaign = campaignService.getCampaignById(campaignId)!!
        val projectId = campaign.projectId
        var otherSbts = suiSbtRepository.getSuiSbtRewardByProjectId(projectId)
            ?.filter { it.groupId != 0L }
        if (otherSbts != null) {
            otherSbts = otherSbts.filter { sbt ->
                val groupId = sbt.groupId
                val sbtCampaignId = credentialGroupRepository.getCredentialGroupById(groupId)!!.campaignId
                val campaignStatus = campaignRepository.getCampaignById(sbtCampaignId)!!.status
                val sbtProjectId = sbt.projectId
                val projectUrl = projectRepository.getProjectById(sbtProjectId)!!.projectUrl
                sbt.campaignId = sbtCampaignId
                sbt.projectUrl = projectUrl
                (sbtCampaignId != campaignId) && (campaignStatus == CampaignStatus.ON_GOING)
            }
        }
        val trendingTop7SbtInfos = participantRepository.getTop7ClaimedSuiRewards()
        println("trendingTop7SbtInfos: $trendingTop7SbtInfos and size is ${trendingTop7SbtInfos.size}")
        val trendingTop7Sbts = mutableListOf<SuiSbtReward>()
        for (sbt in trendingTop7SbtInfos) {
            val sbtId = sbt.first
            val sbtReward = suiSbtRepository.getSuiSbtRewardById(sbtId)
            if (sbtReward != null) {
                sbtReward.holderCnt = sbt.second.toInt()
                val sbtCampaignId = credentialGroupRepository.getCredentialGroupById(sbtReward.groupId)!!.campaignId
                if (sbtCampaignId != campaignId) {
                    val sbtCampaign = campaignRepository.getCampaignById(sbtCampaignId)!!
                    val sbtProjectId = sbtReward.projectId
                    val projectUrl = projectRepository.getProjectById(sbtProjectId)!!.projectUrl
                    sbtReward.campaignId = sbtCampaignId
                    sbtReward.projectUrl = projectUrl
                    if (sbtCampaign.status == CampaignStatus.ON_GOING) {
                        trendingTop7Sbts.add(sbtReward)
                    }
                }
            }
        }
        return ResponseEntity.ok(
            mapOf(
                "code" to 200, "sbtInfos" to SuiProjectSbtInfos(
                    otherSbts,
                    trendingTop7Sbts
                )
            )
        )
    }

//    @PostMapping("sbt/sync")
//    fun createSbt(@RequestBody request: SuiSbtSync): ResponseEntity<Any> {
//        val address = SecurityContextHolder.getContext().authentication.principal.toString()
//        val user = userService.getUserByPrincipal(address)!!
//        val userSui = userService.getSuiWalletByUserId(user.userId)
//        if (!userSui.binded) {
//            return ResponseEntity.ok(
//                mapOf("code" to 400, "message" to "Please bind your Sui wallet first")
//            )
//        }
//        return ResponseEntity.ok(
//            suiService.syncSuiSbt(request)
//        )
//    }

    @PostMapping("unbind")
    fun unbindSui(): ResponseEntity<Any> {
        val address = SecurityContextHolder.getContext().authentication.principal.toString()
        val user = userService.getUserByPrincipal(address)!!
        val auth = SecurityContextHolder.getContext().authentication as? com.rewardoor.app.auth.AddressAuthentication
        val chain = auth?.chain
//        if (chain == null || chain.lowercase() != "sui") {
//            return ResponseEntity.ok("Only sui wallet can unbind sui")
//        }
        val userSui = userService.getSuiWalletByUserId(user.userId)
        if (!userSui.binded) {
            return ResponseEntity.ok(
                mapOf("code" to 400, "message" to "Please bind your Sui wallet first")
            )
        }
        val deleteCnt = userService.unbindSuiUser(user.userId)
        return ResponseEntity.ok(
            mapOf("code" to 200, "message" to "Unbind success", "cnt" to deleteCnt)
        )
    }

    @PostMapping("sbt/claim")
    fun claimSbt(@RequestBody request: SuiClaimSbtRequest): ResponseEntity<Any> {
        val address = SecurityContextHolder.getContext().authentication.principal.toString()
        val user = userService.getUserByPrincipal(address)!!
        val userSui = userService.getSuiWalletByUserId(user.userId)
        if (!userSui.binded) {
            return ResponseEntity.ok(
                mapOf("code" to 400, "message" to "Please bind your Sui wallet first")
            )
        }
//        val name = "TBook SBT"
//        val description = "This is a claim for TBook SBT"
//        val url = "https://static.tbook.vip/tb_intro.jpg"
//        val sbtId = 32133L
        val suiSbtId = request.sbtId
        val suiSbt = suiSbtRepository.getSuiSbtRewardById(suiSbtId)!!
        val groupId = suiSbt.groupId
        val credentials = credentialRepository.getCredentialByGroupId(groupId)
        var isTaskFinished = true
        val utInfo = userTwitterService.getUserInfo(user.userId)
        for (credential in credentials) {
            val isCredentialVerified = credentialGroupService.verifyCredential(user, utInfo, credential)
            if (!isCredentialVerified) {
                isTaskFinished = false
                break
            }
        }
        if (!isTaskFinished) {
            return ResponseEntity.ok(
                mapOf(
                    "code" to 400,
                    "status" to "error",
                    "message" to "Task Not Finished"
                )
            )
        }
        val hasClaimed = participantRepository.getClaimTypeByUserId(suiSbtId, user.userId, groupId) == 4
        if (hasClaimed) {
            return ResponseEntity.ok(
                mapOf(
                    "code" to 400,
                    "status" to "error",
                    "message" to "It has been claimed"
                )
            )
        }
        val sbtClaimRes = signSbtClaim(suiSbt, userSui.suiWallet!!)
//        wiseScoreService.addUserReward(4, suiSbtId, user.userId, groupId, 4)
        return ResponseEntity.ok(
            mapOf(
                "code" to 200,
                "status" to "success",
                "sbtClaimResult" to sbtClaimRes
            )
        )
    }

    @PostMapping("sbt/claim/check")
    fun claimCheck(@RequestBody request: SuiClaimCheckRequest): ResponseEntity<Any> {
        val address = SecurityContextHolder.getContext().authentication.principal.toString()
        val user = userService.getUserByPrincipal(address)!!
        val userSui = userService.getSuiWalletByUserId(user.userId)
        if (!userSui.binded) {
            return ResponseEntity.ok(
                mapOf("code" to 400, "message" to "Please bind your Sui wallet first")
            )
        }
        val suiSbtId = request.sbtId
        val suiSbt = suiSbtRepository.getSuiSbtRewardById(suiSbtId)!!
        val groupId = suiSbt.groupId
        val credentials = credentialRepository.getCredentialByGroupId(groupId)
        var isTaskFinished = true
        val utInfo = userTwitterService.getUserInfo(user.userId)
        for (credential in credentials) {
            val isCredentialVerified = credentialGroupService.verifyCredential(user, utInfo, credential)
            if (!isCredentialVerified) {
                isTaskFinished = false
                break
            }
        }
        if (!isTaskFinished) {
            return ResponseEntity.ok(
                mapOf(
                    "code" to 400,
                    "status" to "error",
                    "message" to "Task Not Finished"
                )
            )
        }
        val hasClaimed = participantRepository.getClaimTypeByUserId(suiSbtId, user.userId, groupId) == 4
        if (hasClaimed) {
            return ResponseEntity.ok(
                mapOf(
                    "code" to 400,
                    "status" to "error",
                    "message" to "It has been claimed"
                )
            )
        }
        val tx = request.tx
        val sbtObjectId = getSuiTransactionDetailsObjectId(tx, userSui.suiWallet ?: "")
        if (!sbtObjectId.isNullOrEmpty()) {
            var isTxUsed = false
            if (tx != "") {
                isTxUsed = wiseScoreService.getSuiUserRewardByUidAndTx(user.userId, tx) != null
            }
            if (!isTxUsed) {
                wiseScoreService.addUserReward(4, suiSbtId, user.userId, groupId, 4, sbtObjectId, tx)
                return ResponseEntity.ok(
                    mapOf(
                        "code" to 200,
                        "status" to "success"
                    )
                )
            }
        }
        return ResponseEntity.ok(
            mapOf(
                "code" to 400,
                "status" to "fail",
                "message" to "tx is not valid"
            )
        )
    }

    @GetMapping("/getScore")
    fun getScore(): Any {
        val principal = SecurityContextHolder.getContext().authentication as AddressAuthentication
        val userId = principal.userId
        val userSui = userService.getSuiWalletByUserId(userId)
        if (!userSui.binded) {
            return mapOf("code" to 400, "message" to "Please bind your Sui wallet first")
        }
        val userWiseScore = wiseScoreService.addScoreResult(userId, "sui")
        val shareLinks = wiseScoreService.getUserShareLinks(userId)
        for (link in shareLinks) {
            if (link.socialType == SocialType.DISCORD.code) {
                wiseScoreService.appendDiscordInfo(userId, link)
            } else if (link.socialType == SocialType.TELEGRAM.code) {
                wiseScoreService.appendTelegramInfo(link)
            }
        }
        userWiseScore.userDcTgShareLink = shareLinks
        return mapOf(
            "code" to 200,
            "message" to "user has already generated  WiseScore",
            "userWiseScore" to userWiseScore
        )
    }

    @GetMapping("/getConfigSbtData")
    fun getConfigSbtData(): ResponseEntity<Map<String, Any>> {
        return try {
            val principal = SecurityContextHolder.getContext().authentication as AddressAuthentication
            val userId = principal.userId
            val userSui = userService.getSuiWalletByUserId(userId)

            if (!userSui.binded) {
                return ResponseEntity.badRequest().body(
                    mapOf(
                        "code" to 400,
                        "message" to "Please bind your Sui wallet first"
                    )
                )
            }

            // 获取配置的 SBT 数据，返回数组格式
            val sbtDataList = wiseScoreService.getSbtMapDataWithPosition()

            ResponseEntity.ok(
                mapOf(
                    "code" to 200,
                    "message" to "Successfully retrieved config SBT data",
                    "data" to sbtDataList // 返回 List<RewardInfo>
                )
            )
        } catch (e: Exception) {
            ResponseEntity.internalServerError().body(
                mapOf(
                    "code" to 500,
                    "message" to "Internal server error: ${e.message}"
                )
            )
        }
    }

    data class UpdateSBTCategoryRequest(
        val sbtId: Long,
        val category: Int
    )

    @PostMapping("updateSbtCategory")
    fun updateSBTCategory(@RequestBody req: UpdateSBTCategoryRequest): Any {
        val principal = SecurityContextHolder.getContext().authentication.principal.toString()
        val user = userService.getUserByPrincipal(principal)!!
        val userSui = userService.getSuiWalletByUserId(user.userId)
        if (!userSui.binded) {
            return SimpleResponseEntity.failed("Please bind your Sui wallet first", null)
        }
        val sbtCheckDetail = tonSocietySyncRepo.getTonSyncCheckBySBTId(req.sbtId)
        if (sbtCheckDetail != null) {
            val sbtSyncHistory = suiSbtRepository.getSuiSbtSyncBySbtId(req.sbtId)
            if (sbtSyncHistory != null) {
                val activityId = sbtSyncHistory.suiSbtActivityId
                val sbtList = suiSbtRepository.getSuiSbtRewardByActivityId(activityId)
                for (sbt in sbtList) {
                    suiSbtRepository.updateSuiSbtCategory(sbt.suiSbtId, req.category)
                }
                return SimpleResponseEntity.success("OK", sbtSyncHistory)
            } else {
                suiSbtRepository.updateSuiSbtCategory(req.sbtId, req.category)
                return SimpleResponseEntity.success("OK", sbtCheckDetail)
            }
        }
        return SimpleResponseEntity.failed("error", null)
    }

    fun getSuiTransactionDetailsObjectId(digest: String, suiAddress: String): String? {
//        val suiScanRpcUrl = "https://rpc-testnet.suiscan.xyz:443"
        val p = if (env.activeProfiles.contains("prod")) "PROD" else "STAG"
        var suiRpcUrl = "https://fullnode.testnet.sui.io"
        if (p == "PROD") {
            suiRpcUrl = "https://fullnode.mainnet.sui.io"
        }
        val client = HttpClient.newBuilder().build()
        var sbtObjectId = ""
        try {
            val requestBody = JSONObject()
                .put("jsonrpc", "2.0")
                .put("id", 1)
                .put("method", "sui_getTransactionBlock")
                .put(
                    "params", listOf(
                        digest, // 交易 Digest
                        JSONObject()
                            .put("showInput", true)
                            .put("showEffects", true)
                            .put("showEvents", true)
                            .put("showObjectChanges", true)
                            .put("showBalanceChanges", true)
                    )
                )
                .toString()
            val request = HttpRequest.newBuilder()
                .uri(URI.create(suiRpcUrl))
                .header("Content-Type", "application/json")
                .POST(HttpRequest.BodyPublishers.ofString(requestBody))
                .build()
            val response = client.send(request, HttpResponse.BodyHandlers.ofString())
            val responseBody = response.body()
            val jsonResponse = JSONObject(responseBody)
            println(jsonResponse)
            if (jsonResponse.has("result")) {
                val result = jsonResponse.getJSONObject("result")
                val transaction = result.getJSONObject("transaction")
                val objectChanges = result.getJSONArray("objectChanges")
                val sender = transaction.getJSONObject("data").getString("sender")
                if (sender.lowercase() == suiAddress.lowercase()) {
                    for (i in 0 until objectChanges.length()) {
                        val objectChange = objectChanges.getJSONObject(i)
                        val objectType = objectChange.getString("objectType")
                        if (objectType.contains("sbt_nft::SBT")) {
                            sbtObjectId = objectChange.getString("objectId")
                        }
                    }
                    return sbtObjectId
                }
            } else {
                return null
            }
        } catch (e: Exception) {
            println("Error: ${e.message}")
            return null
        }
        return null
    }


    private fun signSbtClaim(
        sbt: SuiSbtReward, address: String
    ): SuiClaimSbtResponse {
        val now = System.currentTimeMillis()
        val signature = suiService.signSbtClaim(sbt.objectId, sbt.suiSbtActivityId, address, now)
        return SuiClaimSbtResponse(sbt.objectId, sbt.suiSbtActivityId.toString(), now, signature)
    }
}

class SuiLoginRequest(
    val address: String,
    val signature: String,
    @JsonSetter(nulls = Nulls.AS_EMPTY)
    val publicKey: String = "",
    val zkLogin: Boolean = false,
    val network: String = "testnet"
)

class SuiClaimSbtRequest(
    val credentialId: Long,
    val sbtId: Long,
)

class SuiClaimSbtResponse(
    val objectId: String,
    val sbtId: String,
    val timestamp: Long,
    val signature: String,
)

class SuiClaimCheckRequest(
    val credentialId: Long,
    val sbtId: Long,
    val tx: String
)

class SuiProjectSbtInfos(
    val otherSbts: List<SuiSbtReward>? = emptyList(),
    val trendingSbts: List<SuiSbtReward>? = emptyList()
)