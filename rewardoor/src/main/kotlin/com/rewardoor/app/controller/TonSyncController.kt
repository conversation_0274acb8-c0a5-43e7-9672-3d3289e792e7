package com.rewardoor.app.controller

import com.amazonaws.services.s3.AmazonS3
import com.amazonaws.services.s3.model.ObjectMetadata
import com.rewardoor.app.services.CampaignService
import com.rewardoor.app.services.TonSyncService
import com.rewardoor.app.services.UserService
import com.rewardoor.model.TonSyncHistory
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.beans.factory.annotation.Value
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import java.time.Instant

@RestController
@RequestMapping("ton-sync")
class TonSyncController(
    private val tonSyncService: TonSyncService,
    private val campaignService: CampaignService,
    private val userService: UserService,
    @Qualifier("s3clientBasic") private val s3: AmazonS3
) {
    private val log = mu.KotlinLogging.logger { }
}
