package com.rewardoor.app.controller

import com.rewardoor.app.dto.*
import com.rewardoor.app.services.*
import com.rewardoor.model.User
import org.springframework.security.access.annotation.Secured
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RestController
import java.time.Duration
import java.time.Instant

@RestController
class UserController(
    private val userService: UserService,
    private val projectService: ProjectService,
    private val userTwitterService: UserTwitterService,
    private val telegramService: TelegramService,
    private val dcService: DiscordService,
    private val adminService: AdminService,
    private val projectApplyService: ProjectApplyService,
    private val userInfoService: UserInfoService
) {

    @GetMapping(path = ["/user/info", "/info"])
    @Secured
    fun getTIPRoles(): UserInfoDto {
        val principal = SecurityContextHolder.getContext().authentication.principal.toString()
        val user = userService.getUserByPrincipal(principal)!!
        val userDto = getUserInfoDto(user)
        userDto.canCreateProject = projectApplyService.getUserPrivilege(user.userId)
        return userDto
    }

    @GetMapping(path = ["/user/info", "/ownerInfo"])
    @Secured
    fun getOwnerInfo(): UserInfoDto {
        val principal = SecurityContextHolder.getContext().authentication.principal.toString()
        val user = userService.getUserByPrincipal(principal)!!
        val userDto = getUserInfoDto(user)
        userDto.canCreateProject = projectApplyService.getUserPrivilege(user.userId)
        return userDto
    }

    @PostMapping("markNewUser")
    fun markNewUser(): UserInfoDto {
        val principal = SecurityContextHolder.getContext().authentication.principal.toString()
        val user = userService.getUserByPrincipal(principal)!!
        userService.updateNewUserStatus(user.userId, false)
        return getUserInfoDto(userService.getUserById(user.userId)!!)
    }

    private fun getUserInfoDto(user: User): UserInfoDto {
        val projects = projectService.getUserProjects(user.userId).toMutableList()
        if (projects.isEmpty()) {  // project admin
            val evmAddress = user.wallet
            val tonAddress = user.ton.tonWallet ?: ""
            val tonHexAddress = user.ton.hexAddress ?: ""
            val suiAddress = user.suiAddress
            val evmAdmin = adminService.getAdminByWallet(evmAddress)
            val tonAdmin = adminService.getAdminByWallet(tonAddress)
            val tonHexAdmin = adminService.getAdminByWallet(tonHexAddress)
            val suiAdmin = adminService.getAdminByWallet(suiAddress)
            println("evmAddress: $evmAddress and tonAddress: $tonAddress and suiAddress: $suiAddress and ${tonAdmin?.projectId}")
            if (evmAdmin != null && evmAdmin.status == 1) { //admin is valid
                val project = projectService.getProject(evmAdmin.projectId)
                if (project != null) {
                    projects.add(project)
                }
            }
            if (tonAdmin != null && tonAdmin.status == 1) { //admin is valid
                val project = projectService.getProject(tonAdmin.projectId)
                if (project != null) {
                    projects.add(project)
                }
            }
            if (tonHexAdmin != null && tonHexAdmin.status == 1) { //admin is valid
                val project = projectService.getProject(tonHexAdmin.projectId)
                if (project != null) {
                    projects.add(project)
                }
            }
            if (suiAdmin != null && suiAdmin.status == 1) { //admin is valid
                val project = projectService.getProject(suiAdmin.projectId)
                if (project != null) {
                    projects.add(project)
                }
            }
        }
        val userInfoDto = userInfoService.getUserDtoInfo(user)
        userInfoDto.projects = projects
        return userInfoDto
    }
}