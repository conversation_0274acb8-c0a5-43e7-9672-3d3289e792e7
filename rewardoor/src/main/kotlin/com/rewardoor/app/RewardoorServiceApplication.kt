package com.rewardoor.app

import org.springframework.boot.autoconfigure.SpringBootApplication
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration
import org.springframework.boot.runApplication
import org.springframework.context.annotation.ComponentScan
import org.springframework.scheduling.annotation.EnableScheduling
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity

@SpringBootApplication(exclude = [DataSourceAutoConfiguration::class])
@ComponentScan(basePackages = ["com.rewardoor"])
@EnableWebSecurity
@EnableScheduling
class RewardoorServiceApplication

fun main(args: Array<String>) {
    runApplication<RewardoorServiceApplication>(*args)
}
