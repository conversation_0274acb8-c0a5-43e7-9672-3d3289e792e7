package com.rewardoor.app

import io.micrometer.core.aop.TimedAspect
import io.micrometer.core.instrument.MeterRegistry
import jakarta.annotation.PostConstruct
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import javax.sql.DataSource


@Configuration
class MetricConfiguration {
    @Autowired
    lateinit var dataSource: DataSource

    @Autowired
    lateinit var metricRegistry: MeterRegistry

    @PostConstruct
    fun setUpMetric() {
//        if (dataSource is HikariDataSource) {
//            (dataSource as HikariDataSource).metricRegistry = metricRegistry
//        }
    }

    @Bean
    fun timedAspect(registry: MeterRegistry): TimedAspect? {
        return TimedAspect(registry)
    }


}