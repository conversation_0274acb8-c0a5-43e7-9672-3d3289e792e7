package com.rewardoor.app.auth

import com.fasterxml.jackson.databind.ObjectMapper
import jakarta.servlet.http.HttpServletRequest
import jakarta.servlet.http.HttpServletResponse
import org.springframework.http.HttpStatus
import org.springframework.security.core.AuthenticationException
import org.springframework.security.web.authentication.AuthenticationFailureHandler

class CustomAuthenticationFailureHandler: AuthenticationFailureHandler {
    private val objectMapper = ObjectMapper()
    override fun onAuthenticationFailure(
        request: HttpServletRequest,
        response: HttpServletResponse,
        exception: AuthenticationException
    ) {
        response.status = HttpStatus.UNAUTHORIZED.value()
        val data = mapOf("code" to "401", "message" to "UNAUTHORIZED")

        response.outputStream
            .println(objectMapper.writeValueAsString(data))
    }
}
