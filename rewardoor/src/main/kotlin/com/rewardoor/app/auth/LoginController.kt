package com.rewardoor.app.auth

import com.rewardoor.app.services.NonceService
import com.rewardoor.app.services.UserService
import com.rewardoor.app.services.UserTwitterService
import com.rewardoor.model.PassportAccounts
import io.swagger.v3.oas.annotations.Operation
import jakarta.servlet.http.HttpServletResponse
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseCookie
import org.springframework.http.ResponseEntity
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController
import java.util.*
import java.util.concurrent.ConcurrentHashMap

@RestController
class LoginController(
    private val manager: AddressAuthManager,
    private val userService: UserService,
    private val nonceService: NonceService,
    private val userTwitterService: UserTwitterService
) {
    @PostMapping("/authenticate")
    @Operation(summary = "login using wallet", description = "login with address value and sign message")
    fun login(
        @RequestParam("address") address: String,
        @RequestParam("sign") sign: String,
        response: HttpServletResponse
    ): ResponseEntity<Any> {
        val (r, newUser) = tryAuthenticate(address, sign)
        return if (r.isSuccess) {
            val cookie = Jwt.buildCookie(r.getOrNull()!!)
            ResponseEntity.ok().header(HttpHeaders.SET_COOKIE, cookie.toString())
                .body(mapOf("code" to 200, "newUser" to newUser, "ck" to cookie))
        } else {
            ResponseEntity.status(HttpStatus.FORBIDDEN).body("login failed")
        }
    }

    private fun tryAuthenticate(address: String, sign: String): Pair<Result<AddressAuthentication>, Boolean> {
        val nonce = nonceService.getAndInvalidateNonce(address.lowercase())
        val authentication = AddressAuthentication(address, sign, 0, nonce.orEmpty())
        val authResult = manager.authenticate(authentication) as AddressAuthentication
        return if (authResult.isAuthenticated) {
            var newUser = false
            var user = userService.getUserByAddress(address)
            if (user == null) {
                user = userService.registerUserByWallet(address)
                newUser = true
            }
            authResult.userId = user.userId
            Result.success(authResult) to newUser
        } else {
            Result.failure<AddressAuthentication>(Exception()) to false
        }
    }

    @GetMapping(path = ["signout", "logout"])
    @Operation(summary = "退出登录", description = "")
    fun logout(response: HttpServletResponse): ResponseEntity<Any> {
        val cookie = ResponseCookie.from(Jwt.TOKEN_NAME, "").secure(true).sameSite("none")
            .httpOnly(true).maxAge(0).build()
        return ResponseEntity.ok().header(HttpHeaders.SET_COOKIE, cookie.toString())
            .body("logout success")
    }

    @PostMapping("bindEvm")
    fun bindEvmWallet(
        @RequestParam("address") address: String,
        @RequestParam("sign") sign: String
    ): ResponseEntity<Any> {
        val principal = SecurityContextHolder.getContext().authentication.principal
        val user = userService.getUserByPrincipal(principal.toString())!!
        val nonce = nonceService.getAndInvalidateNonce(address.lowercase())
        val authentication = AddressAuthentication(address, sign, user.userId, nonce.orEmpty())
        val authResult = manager.authenticate(authentication) as AddressAuthentication
        return if (authResult.isAuthenticated) {
            val ret = userService.bindEvmWallet(user, address)
            if (ret == -1) {
                val passportA = PassportAccounts(
                    userId = user.userId,
                    evmAddress = user.evm.evmWallet ?: "",
                    tonAddress = user.ton.tonWallet ?: "",
                    suiAddress = user.suiAddress,
                    twitterName = user.twitterName,
                    dcName = user.dcName,
                    tgName = user.tgName
                )
                val addressUserId = userService.getUserByAddress(address)!!.userId
                val addressUser = userService.getUserById(addressUserId)!!
                val passportB = PassportAccounts(
                    userId = addressUserId,
                    evmAddress = addressUser.evm.evmWallet ?: "",
                    tonAddress = addressUser.ton.tonWallet ?: "",
                    suiAddress = addressUser.suiAddress,
                    twitterName = addressUser.twitterName,
                    dcName = addressUser.dcName,
                    tgName = addressUser.tgName
                )
                ResponseEntity.ok(
                    mapOf(
                        "code" to 400,
                        "message" to "The address $address has been taken, you need to merge the accounts.",
                        "passportA" to passportA,
                        "passportB" to passportB
                    )
                )
            } else if (ret == 0) {
                val curUser = userService.getUserById(user.userId)!!
                val passportA = PassportAccounts(
                    userId = curUser.userId,
                    evmAddress = curUser.evm.evmWallet ?: "",
                    tonAddress = curUser.ton.tonWallet ?: "",
                    suiAddress = curUser.suiAddress,
                    twitterName = curUser.twitterName,
                    dcName = curUser.dcName,
                    tgName = curUser.tgName
                )
                val addressUserId = userService.getUserByAddress(address)!!.userId
                val addressUser = userService.getUserById(addressUserId)!!
                val passportB = PassportAccounts(
                    userId = addressUserId,
                    evmAddress = addressUser.evm.evmWallet ?: "",
                    tonAddress = addressUser.ton.tonWallet ?: "",
                    suiAddress = addressUser.suiAddress,
                    twitterName = addressUser.twitterName,
                    dcName = addressUser.dcName,
                    tgName = addressUser.tgName
                )
                ResponseEntity.ok(
                    mapOf(
                        "code" to 4004,
                        "message" to "The address $address has been taken, please change another one.",
                        "passportA" to passportA,
                        "passportB" to passportB,
                        "userId" to user.userId,
                        "address" to address
                    )
                )
            } else {
                ResponseEntity.ok(mapOf("code" to 200, "message" to "bind success"))
            }
        } else {
            ResponseEntity.ok(mapOf("code" to 300, "message" to "sign failed"))
        }
    }

    private fun isPassportsAbleToMerge(passportA: PassportAccounts, passportB: PassportAccounts): Boolean {
        return when {
            (passportA.evmAddress != "" && passportB.evmAddress != "") || (passportA.tonAddress != "" && passportB.tonAddress != "")
                    || (passportA.suiAddress != "" && passportB.suiAddress != "")
                    || (passportA.twitterName != "" && passportB.twitterName != "") || (passportA.dcName != "" && passportB.dcName != "")
                    || (passportA.tgName != "" && passportB.tgName != "") -> false

            else -> true
        }
    }

    @GetMapping("nonce")
    fun getNonce(@RequestParam("address") address: String): String {
        val message = "sign in to rewardoor, nonce: ${UUID.randomUUID()}, timestamp: ${System.currentTimeMillis()}"
        nonceService.addNonce(address.lowercase(), message)
        return message
    }
}
