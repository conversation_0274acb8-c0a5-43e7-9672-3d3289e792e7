package com.rewardoor.app.auth

import com.auth0.jwt.JWT
import com.auth0.jwt.algorithms.Algorithm
import com.auth0.jwt.exceptions.JWTVerificationException
import org.springframework.http.ResponseCookie
import org.springframework.security.core.Authentication
import java.time.Duration
import java.time.Instant

object Jwt {
    val EXPIRATION = Duration.ofDays(1).toMillis()
    val TOKEN_NAME = "jwt_token"
    val JWT_SECRET = "28f6a59bc2d0488583e4d2c795bcbfb8"

    fun generateToken(authentication: Authentication): String {
        val addressAuth = authentication as AddressAuthentication
        val algorithm = Algorithm.HMAC256(JWT_SECRET)
        return JWT.create()
            .withExpiresAt(Instant.now().plusMillis(EXPIRATION))
            .withClaim("address", addressAuth.address)
            .withClaim("userId", addressAuth.userId.toString())
            .sign(algorithm)
    }

    fun validateToken(token: String): Authentication {
        val algorithm = Algorithm.HMAC256(JWT_SECRET)
        try {
            val jwt = JWT.require(algorithm).build().verify(token)
            val address = jwt.getClaim("address").toString().trim('"')
            val userId = jwt.getClaim("userId").toString().trim('"')
            val auth = AddressAuthentication(address, userId = userId.toLong())
            auth.isAuthenticated = true
            return auth
        } catch (ex: JWTVerificationException) {
            val auth = AddressAuthentication("", "")
            auth.isAuthenticated = false
            return auth
        }
    }

    fun buildCookie(authentication: AddressAuthentication): ResponseCookie {
        val token = generateToken(authentication)
        return ResponseCookie.from(TOKEN_NAME, token).secure(true).sameSite("none")
            .httpOnly(true).maxAge(-1).build()
    }

    fun buildCookie(userId: Long): ResponseCookie {
        val token = generateToken(AddressAuthentication("", userId = userId))
        return ResponseCookie.from(TOKEN_NAME, token).secure(true).sameSite("None; Secure")
            .httpOnly(true).path("/").maxAge(-1).build()
    }
}
