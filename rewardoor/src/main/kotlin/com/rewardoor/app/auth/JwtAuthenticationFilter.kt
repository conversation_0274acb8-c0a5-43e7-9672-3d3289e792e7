package com.rewardoor.app.auth

import com.rewardoor.app.services.UserService
import jakarta.servlet.FilterChain
import jakarta.servlet.http.HttpServletRequest
import jakarta.servlet.http.HttpServletResponse
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.security.web.authentication.www.BasicAuthenticationFilter
import org.springframework.stereotype.Component


@Component
class JwtAuthenticationFilter(
    authManager: AddressAuthManager,
    private val userService: UserService
) : BasicAuthenticationFilter(authManager) {
    private val log = mu.KotlinLogging.logger {}

    override fun doFilterInternal(
        request: HttpServletRequest,
        response: HttpServletResponse,
        chain: FilterChain
    ) {
        val tokenCookie = request.cookies?.find {
            it.name.equals(Jwt.TOKEN_NAME)
        }
        var tokenStr = ""
        if (tokenCookie == null) {
            val authorizationHeader = request.getHeader("Authorization")
            tokenStr = authorizationHeader?.substringAfter("jwt_token=", "")?.substringBefore(";").toString()
        } else {
            tokenStr = tokenCookie.value
        }

        if (tokenStr.isNullOrEmpty()) {
            log.info("no cookie token found, path: {}", request.servletPath)
            chain.doFilter(request, response)
            return
        }

        val auth = Jwt.validateToken(tokenStr) as AddressAuthentication
        if (!auth.isAuthenticated) {
            log.info("invalid token, path: {}, auth result: {}", request.servletPath, auth)
            chain.doFilter(request, response)
            return
        }

        val path = request.servletPath

        SecurityContextHolder.getContext().authentication = auth
        chain.doFilter(request, response)
    }
}
