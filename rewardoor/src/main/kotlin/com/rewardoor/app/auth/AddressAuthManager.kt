package com.rewardoor.app.auth

import com.rewardoor.app.utils.Signs
import org.springframework.security.authentication.AuthenticationManager
import org.springframework.security.core.Authentication
import org.springframework.stereotype.Component

@Component
class AddressAuthManager: AuthenticationManager {
    override fun authenticate(authentication: Authentication): Authentication {
        val addAuth = authentication as AddressAuthentication
        val recoveredAddress = Signs.getAddressUsedToSignHashedMessage(addAuth.sign, addAuth.nonce)
        println("trying to authenticate: $authentication, recordered address: $recoveredAddress")
        addAuth.isAuthenticated = recoveredAddress.equals(addAuth.address, true)
        return addAuth
    }
}
