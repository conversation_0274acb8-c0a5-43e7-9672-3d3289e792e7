package com.rewardoor.app

import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import com.rewardoor.app.bean.ContractInfo
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.core.io.ClassPathResource

@Configuration
class ContractConfiguration {
    private val mapper = jacksonObjectMapper()

    @Bean("contracts")
    fun contracts(@Value("\${contracts.file}") configFile: String): Map<Int, ContractInfo> {
        val content = ClassPathResource(configFile).inputStream.use { it.reader().readText() }
        return mapper.readValue<List<ContractInfo>>(content).associateBy { it.chainId }
    }
}