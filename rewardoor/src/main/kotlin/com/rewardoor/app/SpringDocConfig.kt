package com.rewardoor.app

import io.swagger.v3.oas.annotations.OpenAPIDefinition
import io.swagger.v3.oas.annotations.servers.Server
import org.springdoc.core.models.GroupedOpenApi
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.context.annotation.Profile

@OpenAPIDefinition(servers = [Server(url = "/", description = "Default Server URL")])
@Configuration
@Profile("!prod")
class SpringDocConfig {
    @Bean
    fun api(): GroupedOpenApi? {
        return GroupedOpenApi.builder()
            .group("rewardoor")
            .pathsToMatch("/**")
            .build();
    }
}
