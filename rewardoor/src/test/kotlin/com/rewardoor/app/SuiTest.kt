package com.rewardoor.app

import org.json.JSONObject
import org.junit.jupiter.api.Test
import org.springframework.web.reactive.function.client.ExchangeStrategies
import org.springframework.web.reactive.function.client.WebClient
import java.net.HttpURLConnection
import java.net.URI
import java.net.URL

class SuiTest {

    private final val strategies = ExchangeStrategies.builder()
        .codecs { codecs -> codecs.defaultCodecs().maxInMemorySize(20 * 1024 * 1024) }
        .build()
    private val webClient = WebClient.builder().exchangeStrategies(strategies).build()

    @Test
    // 获取用户的 Sui 数量和价值
    fun getUserSuiBalanceAndValue() {
        val walletAddress = "0xd06498128c371d3ea0a3f09b7631408c06fdfc8abd80a968f97b4adea3edadc5"
        val suiRpcUrl = "https://fullnode.mainnet.sui.io"

        // 获取 Sui 余额
        val suiBalance = getSuiBalance(walletAddress, suiRpcUrl)

//        // 获取 Sui 的当前价格
//        val suiPrice = getSuiPrice()

        // 计算总价值
        val totalValue = suiBalance
        println("balance is $suiBalance, total value is $totalValue")
    }

    // 使用 Sui RPC 查询用户的余额
    fun getSuiBalance(walletAddress: String, rpcUrl: String): Double {
        val requestBody = """
            {
                "jsonrpc": "2.0",
                "method": "suix_getBalance",
                "params": ["$walletAddress"],
                "id": 1
            }
        """.trimIndent()

        return try {
            val response = webClient.post()
                .uri(rpcUrl)
                .header("Content-Type", "application/json")
                .bodyValue(requestBody)
                .retrieve()
                .bodyToMono(String::class.java)
                .block()
            println(response.toString())
            val json = JSONObject(response)
            val result = json.getJSONObject("result")
            val suiBalance = result.getLong("totalBalance") / 1000000000.0
            println("sui balance is $suiBalance")
            suiBalance
        } catch (e: Exception) {
            e.printStackTrace()
            0.0
        }
    }

}