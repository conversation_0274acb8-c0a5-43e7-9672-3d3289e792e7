package com.rewardoor.app

import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import org.junit.jupiter.api.Test
import org.springframework.http.MediaType
import org.springframework.web.reactive.function.BodyInserters
import org.springframework.web.reactive.function.client.WebClient

class SnapShotTest {
    @Test
    fun fetchSpaceData() {
        val apiKey = "74eda144b7ab8c8120193528c36779c62bfd1263d225d2b966d4ac641cff0756"
        val apiUrl = "https://hub.snapshot.org/graphql"
        val query = """
        {
            space(id: "snapshot.dcl.eth") {
                id
                name
                members
            }
        }
    """.trimIndent()

        val client = WebClient.create()
        val response = client.get()
            .uri(apiUrl)
            .header("content-type", "application/json")
            .header("x-api-key", apiKey)
            .retrieve()
            .bodyToMono(String::class.java)
            .block()

        // 处理 API 响应
        val mapper = jacksonObjectMapper()
        val data = mapper.readTree(response)["data"]
        println(data)
    }

}