package com.rewardoor.app

import com.rewardoor.model.PassportAccounts
import kotlinx.coroutines.runBlocking
import org.json.JSONObject
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test
import org.ton.java.address.Address
import org.ton.java.cell.CellBuilder
import org.ton.java.utils.Utils
import java.util.HexFormat
import org.ton.java.mnemonic.Ed25519
import java.net.URI
import java.net.http.HttpClient
import java.net.http.HttpRequest
import java.net.http.HttpResponse

class TonAddressTest {
    @Test
    fun testTonAddress() {
        val addr = Address.of("EQDEd4jsQ0WJWnrPWIHxyiitVzjse5Nw3QJUiI8Bp_1Kjdgd")
        val hex = addr.toHex()
        val nonBounceable = addr.toNonBounceable()
        Assertions.assertEquals("0:c47788ec4345895a7acf5881f1ca28ad5738ec7b9370dd0254888f01a7fd4a8d", "0:$hex")
        Assertions.assertEquals("UQDEd4jsQ0WJWnrPWIHxyiitVzjse5Nw3QJUiI8Bp_1KjYXY", nonBounceable)
    }

    @Test
    fun testSign() {
        val cell = CellBuilder.beginCell()
            .storeAddress(Address.of("EQBH9JBIcPSCALy9NYKe6sS6xWvTXGxcyD22TfvXQLUqvym2"))
            .storeInt(Utils.toNano(100), 64)
            .storeInt(**********, 64)
            .endCell()

        // 使用私钥对Cell的哈希值进行签名
        val hash = cell.hash()
        val privateKey = HexFormat.of().parseHex("")
        val signature: ByteArray = Ed25519.sign(privateKey, hash)
        println(HexFormat.of().formatHex(signature))

    }
    // ci trigger

    @Test
    fun isPassportsAbleToMerge() {
        val passportA = PassportAccounts(
            userId = ************,
            evmAddress = "",
            tonAddress = "",
            suiAddress = "",
            twitterName = "",
            dcName = "",
            tgName = ""
        )
        val passportB = PassportAccounts(
            userId = ************,
            evmAddress = "0x3555881459e5252d918a205b91f0900b1e11fe3f",
            tonAddress = "EQAnamJjpPst061pjqPr75kWY38CVjSsehQMaj2b9PKlPHYh",
            suiAddress = "",
            twitterName = "",
            dcName = "",
            tgName = "Keyla_SUE"
        )
        when {
            (passportA.evmAddress != "" && passportB.evmAddress != "") || (passportA.tonAddress != "" && passportB.tonAddress != "")
                    || (passportA.suiAddress != "" && passportB.suiAddress != "")
                    || (passportA.twitterName != "" && passportB.twitterName != "") || (passportA.dcName != "" && passportB.dcName != "")
                    || (passportA.tgName != "" && passportB.tgName != "") -> print(false)

            else -> print(true)
        }
    }

    @Test
    fun getTransactionDetails() {
        val digest = "********************************************"
//        val suiRpcUrl = "https://rpc-testnet.suiscan.xyz:443"
        val suiRpcUrl = "https://fullnode.testnet.sui.io"
        val client = HttpClient.newBuilder().build()
        try {
            // 构造 JSON-RPC 请求体
            val requestBody = JSONObject()
                .put("jsonrpc", "2.0")
                .put("id", 1)
                .put("method", "sui_getTransactionBlock")
                .put(
                    "params", listOf(
                        digest, // 交易 Digest
                        JSONObject() // 启用选项
                            .put("showInput", true)
                            .put("showEffects", true)
                            .put("showEvents", true)
                            .put("showObjectChanges", true)
                            .put("showBalanceChanges", true)
                    )
                ) // 参数数组
                .toString()

            // 构造 HTTP 请求
            val request = HttpRequest.newBuilder()
                .uri(URI.create(suiRpcUrl))
                .header("Content-Type", "application/json")
                .POST(HttpRequest.BodyPublishers.ofString(requestBody))
                .build()

            // 发送请求
            val response = client.send(request, HttpResponse.BodyHandlers.ofString())
            val responseBody = response.body()

            // 解析 JSON 响应
            val jsonResponse = JSONObject(responseBody)
            println(jsonResponse)
            if (jsonResponse.has("result")) {
                val result = jsonResponse.getJSONObject("result")
                val transaction = result.getJSONObject("transaction")
                val objectChanges = result.getJSONArray("objectChanges")
                for (i in 0 until objectChanges.length()) {
                    val objectChange = objectChanges.getJSONObject(i)
                    val objectType = objectChange.getString("objectType")
                    if (objectType.contains("sbt_nft::SBT")) {
                        val sbtObjectId = objectChange.getString("objectId")
                        println(sbtObjectId)
                    }
                }
                val sender = transaction.getJSONObject("data").getString("sender")
                println(sender)// 返回发起交易的账户地址
                if (sender.lowercase() == "0x5216ef1b602de1b1533333455ad61cf1c5d0be703645c2cb9cf50d935046df45".lowercase()) {
                    println("equal")
                }
            } else {
                // 如果没有结果，返回 null
            }
        } catch (e: Exception) {
            println("Error: ${e.message}")
        }
    }
}