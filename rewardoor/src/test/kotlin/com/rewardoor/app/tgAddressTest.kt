package com.rewardoor.app

import com.fasterxml.jackson.databind.node.ArrayNode
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.google.gson.Gson
import com.rewardoor.model.TokenHolder
import com.rewardoor.model.TokenResponse
import io.swagger.v3.core.util.Json
import org.apache.http.client.utils.URIBuilder
import org.apache.http.message.BasicNameValuePair
import org.json.JSONArray
import org.json.JSONObject
import org.junit.jupiter.api.Test
import org.springframework.web.reactive.function.BodyInserters
import org.springframework.web.reactive.function.client.ExchangeStrategies
import org.springframework.web.reactive.function.client.WebClient
import java.net.URI
import java.text.DecimalFormat
import java.time.*
import java.time.format.DateTimeFormatter

class TgAddressTest(
) {
    private final val strategies = ExchangeStrategies.builder()
        .codecs { codecs -> codecs.defaultCodecs().maxInMemorySize(20 * 1024 * 1024) }
        .build()
    private val webClient = WebClient.builder().exchangeStrategies(strategies).build()
    private val mapper = jacksonObjectMapper()

    private fun sendThirdAndCheck(uri: URI, target: String): Boolean {
        val response = webClient.get()
            .uri(uri)
            .retrieve()
            .bodyToMono(String::class.java)
            .block()
        val content = mapper.readTree(response)
        if (content["msg"].asText() != "SUCCESS") return false
        val data = content.get("data") ?: return false
        if (data.asText().equals("Not Found")) return false
        val ids = mapper.readTree(data.asText()).get("ids")?.toList()?.map { it.asLong() } ?: return false
        return ids.contains(target.toLong())
    }

    @Test
    fun getEvmBalanceResult() {
        val address = "******************************************"
        val response = WebClient.builder().build()
            .get()
            .uri(
                "https://api.etherscan.io/api?module=account&action=balance&address=${address}&tag=latest&apikey=B88663Z7CUY5JDUE12H7E8MWKP9WW5XFGI}"
            )
            .retrieve()
            .bodyToMono(String::class.java)
            .block();
        val jsonObject = JSONObject(response.toString())
        val result = jsonObject.getLong("result")
        val ethNum = result / 1_000_000_000_000_000_000.0
        val df = DecimalFormat("0.0000000")
        val ethStr = df.format(ethNum)
        println(ethStr)
    }

    @Test
    fun getCoinPrice() {
        val ethUri = URI("https://api.binance.com/api/v3/ticker/price?symbol=ETHUSDT")
        val futureUri = URI("https://fapi.binance.com/fapi/v1/ticker/price?symbol=TONUSDT")
        val response = WebClient.builder().build()
            .get()
            .uri(futureUri)
            .retrieve()
            .bodyToMono(String::class.java)
            .block();

        //  转化为 JSONObject
        val jsonObject = JSONObject(response.toString())
        println(jsonObject.toString())
        // 获取价格
        val price = jsonObject.getString("price")

        print(price.toDouble())
    }

    @Test
    fun getTwitterFollowingNum() {
        val uri = URIBuilder("https://twitter.good6.top/api/base/apitools/uerByIdOrNameLookUp")
            .setParameters(
                BasicNameValuePair(
                    "apiKey",
                    "o7ZFzpSQlSmRYHi612DGxgjsmhz263vDRbp35frwEcvyS|11704522-PShUS3dHZ8dhKds3z9mdzDNbQbSAvq08MRMbRtcHa"
                ), BasicNameValuePair("userId", "1478980168194555908")
            )
            .build()
        val response = webClient.get()
            .uri(uri)
            .retrieve()
            .bodyToMono(String::class.java)
            .block()
        val content = mapper.readTree(response)
        if (content["msg"].asText() != "SUCCESS") println("")
        val data = content.get("data")?.asText()
        if (data == "Not Found") println("")

        val dataArray = mapper.readValue(data, ArrayNode::class.java)
        val followerNum = dataArray.get(0).get("followers_count")?.asInt() ?: println("")
        println(followerNum)
    }


    @Test
    fun getBalanceResult() {
        val address = "Ef8zMzMzMzMzMzMzMzMzMzMzMzMzMzMzMzMzMzMzMzMzM0vF"
        val response = WebClient.builder().build()
            .get()
            .uri("https://toncenter.com/api/v2/getAddressBalance?address=${address}")
            .header("x-api-key", "63956d1b10345d7796c5526fffa73f486dd3547728bb6000ca630c6ed0dffb3b")
            .retrieve()
            .bodyToMono(String::class.java)
            .block()
        val jsonObject = JSONObject(response.toString())
        val result = jsonObject.getLong("result")
        val tons = result / 1_000_000_000.0
        val df = DecimalFormat("0.0000000")
        val tonsStr = df.format(tons)

        println(response.toString() + " " + tonsStr)
    }

    @Test
    fun getTokenPrice() {
        val response = WebClient.builder().build()
            .get()
            .uri("https://pro-api.coinmarketcap.com/v2/cryptocurrency/quotes/latest?id=11419")
            .header("X-CMC_PRO_API_KEY", "a12e9aab-3b62-45eb-80d1-94d58e7feaa0")
            .retrieve()
            .bodyToMono(String::class.java)
            .block()
        val jsonObject = JSONObject(response.toString())
        println(response.toString())
    }

    @Test
    fun getEthTransactions() {
        val apiKey = "B88663Z7CUY5JDUE12H7E8MWKP9WW5XFGI"
        val response = WebClient.builder().build()
            .get()
            .uri("https://api.etherscan.io/api?module=proxy&action=eth_getTransactionCount&address=******************************************&tag=latest&apikey=B88663Z7CUY5JDUE12H7E8MWKP9WW5XFGI")
            .retrieve()
            .bodyToMono(String::class.java)
            .block();
        val jsonObject = JSONObject(response.toString())
        var resultInHex = jsonObject.getString("result") // 获取"result"字段的值
        resultInHex = if (resultInHex.startsWith("0x")) resultInHex.substring(2) else resultInHex
        val resultAsDecimal = resultInHex.toLong(16) // 将十六进制的值转换为十进制
        println(resultAsDecimal)
    }

    @Test
    fun getTransactions() {
        val address = "Ef8zMzMzMzMzMzMzMzMzMzMzMzMzMzMzMzMzMzMzMzMzM0vF"
        val limit = 100
        val response = WebClient.builder().build()
            .get()
            .uri("https://toncenter.com/api/v2/getTransactions?address=${address}&limit=${100}")
            .header("x-api-key", "63956d1b10345d7796c5526fffa73f486dd3547728bb6000ca630c6ed0dffb3b")
            .retrieve()
            .bodyToMono(String::class.java)
            .block()

        val jsonObject = JSONObject(response.toString())
        val transactions = jsonObject.getJSONArray("result") // 获取 'result' 数组
        val transactionCount = transactions.length()

//        println(response.toString())

        println("Total number of transactions: $transactionCount")

    }

    @Test
    fun getTokenHolders() {
        val webClient =
            WebClient.builder().codecs { it.defaultCodecs().maxInMemorySize(30 * 1024 * 1024) }  // 提高到1MB build()
                .build()
        val currency = "0x115eC79F1de567eC68B7AE7eDA501b406626478e"
        val date = "2024-04-20"

        val response = webClient.post()
            .uri("https://streaming.bitquery.io/graphql")
            .header("Content-Type", "application/json")
            .header("X-API-KEY", "BQYcZ3KaDm7i54LWYWfUkyoMU1CvldGD")
            .header(
                "Authorization",
                "Bearer ory_at_6A5RfWk9cJxvcoACvZOHQny7iOZPadKWFkyUGsZ530Y.qhKEKCjvGAndYHiPWomvW0fUKWNmA772CQhZwLJiIx8"
            )
            .body(BodyInserters.fromValue("{\"query\":\"query(\$currency: String! \$date: String!) {\\n  EVM(dataset: archive) {\\n    TokenHolders(\\n      tokenSmartContract: \$currency\\n      date: \$date\\n  where: {Balance: {Amount: {gt: \\\"1\\\"}}}\\n limit: {count: 10}\\n orderBy: { descending: Balance_Amount }\\n ) {\\n      Balance {\\n        Amount\\n      }\\n      Holder {\\n        Address\\n      }\\n   }\\n  }\\n}\",\"variables\":\"{\\n  \\\"currency\\\": \\\"$currency\\\",\\n  \\\"date\\\": \\\"$date\\\"\\n}\"}"))
            .retrieve()
            .bodyToMono(String::class.java)
            .block()
        println(response.toString())
        val gson = Gson()
        val responseJson = gson.fromJson(response, TokenResponse::class.java)
        println(responseJson.data)
    }

    @Test
    fun getLastStreak() {
        val checkIns: List<Instant> = listOf(
            Instant.parse("2025-01-08T10:00:00Z"),
            Instant.parse("2025-01-09T10:00:00Z"),
            Instant.parse("2025-01-10T10:00:00Z"),
            Instant.parse("2025-01-11T10:00:00Z"),
            Instant.parse("2025-01-12T10:00:00Z"),
            Instant.parse("2025-01-16T10:00:00Z"),
            Instant.parse("2025-01-17T10:00:00Z"),
            Instant.parse("2025-01-18T00:00:00Z"),
            Instant.parse("2025-01-19T00:00:00Z")
//            Instant.parse("2025-01-20T00:00:00Z")
        )

        val today = Instant.now().atZone(ZoneId.systemDefault()).toLocalDate()
        var maxStreak = 0 // 历史最大连续天数
        var currentStreak = 1 // 当前连续天数，初始值为 1
        var maxStartDate: LocalDate? = null // 最大连续天数的起始日期
        var maxEndDate: LocalDate? = null // 最大连续天数的终止日期
        var currentStartDate: LocalDate? = null // 当前连续天数的起始日期
        var previousDate: LocalDate? = null
        var lastStreak = 0
        var isLastStreakActive = true // 标记是否正在计算最后一次连续打卡天数
        val lastCheckInDate = checkIns.last().atZone(ZoneId.systemDefault()).toLocalDate()

        // 遍历打卡记录（按日期降序）
        for (checkIn in checkIns.reversed()) {
            val checkInDate = checkIn.atZone(ZoneId.systemDefault()).toLocalDate()
            if (previousDate != null) {
                if (checkInDate == previousDate.minusDays(1)) {
                    // 如果是连续日期，增加当前连续天数
                    currentStreak++
                    currentStartDate = currentStartDate ?: checkInDate // 只在第一次连续时设置
                    // 如果正在计算最后一次连续打卡天数，增加 lastStreak
                    if (isLastStreakActive) {
                        lastStreak++
                    }
                } else if (checkInDate != previousDate) {

                    // 如果不连续，检查是否需要更新最大连续天数
                    if (currentStreak > maxStreak) {
                        maxStreak = currentStreak
                        maxStartDate = previousDate
                        maxEndDate = currentStartDate // 当前连续段的终止日期
                    }
                    // 重置当前连续天数
                    currentStreak = 1
                    currentStartDate = checkInDate

                    // 如果中断了连续性，停止计算最后一次连续打卡天数
                    isLastStreakActive = false
                }
            } else {
                // 第一次遍历时设置当前连续的起始日期
                currentStartDate = checkInDate
                // 初始化最后一次连续打卡天数
                if (checkInDate == today || checkInDate == today.minusDays(1)) {
                    lastStreak = 1
                }
            }
            // 更新上一个打卡日期
            previousDate = checkInDate
        }

        // 最后再检查一次，确保最后一段连续天数被记录
        if (currentStreak > maxStreak) {
            maxStreak = currentStreak
            maxStartDate = previousDate
            maxEndDate = currentStartDate // previousDate 是最后一段连续的结束日期
        }
        // 如果最后一次连续天数是当前连续天数，更新 lastStreak
        if (isLastStreakActive) {
            lastStreak = currentStreak
        }
        println("maxStreak is $maxStreak and currentStreak is $currentStreak and lastStreak is $lastStreak and maxBeginDate is $maxStartDate and maxEndDate is $maxEndDate and lastCheckInDate is $lastCheckInDate")
    }

    data class RoundSummary(
        val roundNumber: Long,
        val startTime: Long,
        val endTime: Long,
        val price: Long,
        val totalRaisedTon: Long,
        val totalRaisedUsdt: Long,
        val tokensSold: Long,
        val purchaseCount: Long,
        val uniqueUsers: Long,
        val refundCount: Long,
        val refundedAmountTon: Long,
        val refundedAmountUsdt: Long
    )

    @Test
    fun timeCheck(){
        val timestamp = "2025-07-23 09:00:00"
        val formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")
        val localDateTime = LocalDateTime.parse(timestamp, formatter)
        val utcZonedDateTime = localDateTime.atZone(ZoneId.of("UTC"))
        println(utcZonedDateTime.format(formatter))
    }

    @Test
    fun getTonContract() {
        val contractAddress = "kQCUmdTvMaj1JmC-YhsSfbJdWsi_--8urSYN-DIpXYi_hgvu"
        val apiKey = "095b88d26e59825a615f2a431c8d89e80448e721917a9ae44218aa9554829687" // test net
        // 创建请求 JSON 数据
        val requestJson = JSONObject().apply {
            put("address", contractAddress) // 合约地址
            put("method", "round_summary")       // 方法名称

            // 构建 [["num", "1"]] 作为嵌套数组
            val stackArray = JSONArray().apply {
                put(JSONArray().apply {
                    put("num")
                    put("1")
                })
            }

            put("stack", stackArray)
        }
        try {
            val response = WebClient.builder().build()
                .post()
                .uri("https://testnet.toncenter.com/api/v2/runGetMethod")
                .header("X-API-Key", apiKey)
                .header("Content-Type", "application/json")
                .bodyValue(requestJson.toString())
                .retrieve()
                .bodyToMono(String::class.java)
                .block()

            val jsonObject = JSONObject(response)
            val result = jsonObject.getJSONObject("result")
            println(result)
            try {

                // 获取 stack 数组
                val stackArray = result.getJSONArray("stack")

                // 获取第一个元素（["tuple", {...}]）
                val tupleObject = stackArray.getJSONArray(0).getJSONObject(1)

                // 获取 elements 数组
                val elementsArray = tupleObject.getJSONArray("elements")

                // 解析 elements 中的每个参数
                val roundNumber = elementsArray.getJSONObject(0).getJSONObject("number").getString("number").toLong()
                val startTime = elementsArray.getJSONObject(1).getJSONObject("number").getString("number").toLong()
                val endTime = elementsArray.getJSONObject(2).getJSONObject("number").getString("number").toLong()
                val price = elementsArray.getJSONObject(3).getJSONObject("number").getString("number").toLong()
                val totalRaisedTon = elementsArray.getJSONObject(4).getJSONObject("number").getString("number").toLong()
                val totalRaisedUsdt =
                    elementsArray.getJSONObject(5).getJSONObject("number").getString("number").toLong()
                val tokensSold = elementsArray.getJSONObject(6).getJSONObject("number").getString("number").toLong()
                val purchaseCount = elementsArray.getJSONObject(7).getJSONObject("number").getString("number").toLong()
                val uniqueUsers = elementsArray.getJSONObject(8).getJSONObject("number").getString("number").toLong()
                val refundCount = elementsArray.getJSONObject(9).getJSONObject("number").getString("number").toLong()
                val refundedAmountTon =
                    elementsArray.getJSONObject(10).getJSONObject("number").getString("number").toLong()
                val refundedAmountUsdt =
                    elementsArray.getJSONObject(11).getJSONObject("number").getString("number").toLong()

                val summary = RoundSummary(
                    roundNumber,
                    startTime,
                    endTime,
                    price,
                    totalRaisedTon,
                    totalRaisedUsdt,
                    tokensSold,
                    purchaseCount,
                    uniqueUsers,
                    refundCount,
                    refundedAmountTon,
                    refundedAmountUsdt
                )
                println(summary.toString())
                println("合约方法调用成功，结果：${result}")
            } catch (e: Exception) {
                e.printStackTrace()
                println("解析错误")
            }
        } catch (e: Exception) {
            println("合约方法调用失败，错误：${e.message}")
        }
    }

    @Test
    fun getRoundSummary() {
        val contractAddress = "EQCCjQ9e39-9eoBJdt_mXn2jftAWgNDhYaytVg4tFzEFmKTX" // 替换为你的地址
        val apiKey = "095b88d26e59825a615f2a431c8d89e80448e721917a9ae44218aa9554829687"

        val roundNumber = 1

        val requestJson = JSONObject().apply {
            put("address", contractAddress)
            put("method", "round_summary")
            put("stack", listOf(listOf("int", roundNumber.toString())))
        }

        try {
            val response = WebClient.builder().build()
                .post()
                .uri("https://testnet.toncenter.com/api/v2/runGetMethod")
                .header("X-API-Key", apiKey)
                .header("Content-Type", "application/json")
                .bodyValue(requestJson.toString())
                .retrieve()
                .bodyToMono(String::class.java)
                .block()

            val jsonObject = JSONObject(response)
            val result = jsonObject.getJSONObject("result")
            println("合约方法调用成功，结果：$result")

            // 可选：解析 result["stack"] 数组，提取字段
            val stack = result.getJSONArray("stack")
            for (i in 0 until stack.length()) {
                val item = stack.getJSONArray(i)
                val type = item.getString(0)
                val value = item.getString(1)
                val intValue = value.removePrefix("0x").toLong(16)
                println("字段 $i 类型: $type, 十进制值: $intValue")
            }

        } catch (e: Exception) {
            println("调用失败：${e.message}")
        }
    }

    @Test
    fun getTime() {
        val now = LocalDateTime.now(ZoneOffset.UTC)
        // 起始时间：2025-07-22 14:00 (UTC)
        val startTime = LocalDateTime.of(2025, 7, 20, 7, 0)

        // 每轮持续时间：130 分钟（2 小时 + 10 分钟）
        val roundDuration = Duration.ofMinutes(130)

        // 总轮数
        val totalRounds = 50

        // 计算当前时间与起始时间的时间差（单位：分钟）
        val timeElapsed = Duration.between(startTime, now).toMinutes()

        // 如果当前时间在起始时间之前，返回 0
        if (timeElapsed < 0) println(0)

        // 计算当前是第几轮
        val currentRound = (timeElapsed / roundDuration.toMinutes()) + 1

        // 如果超过总轮数范围，返回 0
        println(if (currentRound > totalRounds) 0 else currentRound.toInt())
    }
}