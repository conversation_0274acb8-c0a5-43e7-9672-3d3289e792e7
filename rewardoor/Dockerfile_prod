FROM amazoncorretto:17

#ADD https://github.com/aws-observability/aws-otel-java-instrumentation/releases/download/v1.32.1/aws-opentelemetry-agent.jar /app/aws-opentelemetry-agent.jar
#ENV JAVA_TOOL_OPTIONS "-javaagent:/app/aws-opentelemetry-agent.jar"

WORKDIR /app

COPY target/rewardoor-service-0.0.1-SNAPSHOT.jar rewardoor-service.jar

#G1GC
ENTRYPOINT exec java -XX:+UnlockExperimentalVMOptions -XX:+AlwaysPreTouch -Xms12G -Xmx12G -XX:MaxDirectMemorySize=1024m -XX:MaxMetaspaceSize=384m -XX:ReservedCodeCacheSize=256m -XX:MaxRAMPercentage=45 -XX:InitialRAMPercentage=45 -XX:MinHeapFreeRatio=0 -XX:MaxHeapFreeRatio=100 -XX:+UseAdaptiveSizePolicy -XX:MaxGCPauseMillis=2000 -XX:+DisableExplicitGC -XX:+UseG1GC -Djava.security.egd=file:/dev/./urandom -Xlog:async -Xlog:safepoint=info,classhisto*=info,age*=info,gc*=info:stdout -jar rewardoor-service.jar

# ZGC
# ENTRYPOINT exec java -XX:-UseBiasedLocking -XX:-ZProactive -XX:+UseLargePages -XX:ZCollectionInterval=120 -XX:+UnlockExperimentalVMOptions -XX:+AlwaysPreTouch -Xms12G -Xmx12G -XX:MaxDirectMemorySize=1024m -XX:MaxMetaspaceSize=384m -XX:ReservedCodeCacheSize=256m -XX:MaxRAMPercentage=45 -XX:InitialRAMPercentage=45 -XX:MinHeapFreeRatio=0 -XX:MaxHeapFreeRatio=100 -XX:+DisableExplicitGC -XX:+UseZGC -Djava.security.egd=file:/dev/./urandom -Xlog:async -Xlog:safepoint=info,classhisto*=info,age*=info,gc*=info:stdout -jar rewardoor-service.jar
