## Generate API Key

Use above `Generate` button to generate a new API Key.

## API Call

### Endpoint

POST /campaign/report

### Required Auth Header

name: x-auth-token

value: the api key


### Payload

Json, fields:

- campaignId: number
- projectId: number
- UserCredentialPair: object
  - address: the user address, string
  - credential: the credential id, number
- eligible: the eligible addresses, UserCredential array 

Example: 
```json
{
  "campaignId": 153899590085,
  "projectId": 156889540037,
  "eligible": [{"address": "0x98dc2a3b8d3642219427dc8278029b17a7df8908", "credential":  156839540258}]
}
```

## Example

```shell
curl 'https://https://rewardoor-api.fly.dev/campaign/report' \
  -H 'Connection: keep-alive' \
  -H 'content-type: application/json' \
  -H 'Accept: */*' \
  -H 'x-auth-token: 16ea413c-0d91-418b-9a03-a0110e1db73b' \
  --data-binary '{"campaignId": 153899590085, "projectId": 156889540037, "eligible": [{"address": "0x98dc2a3b8d3642219427dc8278029b17a7df8908", "credential":  156839540258}]}' \
  --compressed
```