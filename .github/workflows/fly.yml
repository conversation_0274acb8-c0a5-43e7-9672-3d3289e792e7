name: Fly Deploy
on:
  push:
    branches:
      - stag
jobs:
  deploy:
    name: Deploy app
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3

      - name: Set up JDK 17
        uses: actions/setup-java@v3
        with:
          java-version: '17'
          distribution: 'liberica'

      - name: Build with <PERSON>ven
        run: mvn clean package -DskipTests

      - uses: superfly/flyctl-actions/setup-flyctl@master
      - run: flyctl deploy --local-only
        env:
          FLY_API_TOKEN: ${{ secrets.FLY_API_TOKEN }}
